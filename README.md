```shell
# 忽略启动的相关配置文件
git update-index --assume-unchanged task-service-svc/src/main/resources/bootstrap.yml
git update-index --assume-unchanged task-service-svc/src/main/resources/application.yml
git update-index --assume-unchanged task-service-svc/src/main/resources/application-dev.yml
git update-index --assume-unchanged task-service-svc/src/test/resources/bootstrap.yml
git update-index --assume-unchanged task-service-svc/src/test/resources/application.yml

# 确认是否忽略成功
git ls-files -v | grep ^h
```