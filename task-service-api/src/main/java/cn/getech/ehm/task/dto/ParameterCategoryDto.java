package cn.getech.ehm.task.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;


/**
 * 继承类型的参数
 *
 * <AUTHOR>
 * @date 2020-07-09
 */
@Data
@ApiModel(value = "ParameterCategoryDto", description = "继承类型的参数")
public class ParameterCategoryDto {

    @ApiModelProperty(value = "测点参数id")
    private String measuringParameterId;

    @ApiModelProperty(value = "设备id")
    private String equipmentId;

    @ApiModelProperty(value = "类型测点参数id")
    private String categoryParamId;

    @ApiModelProperty(value = "参数最新值")
    private Double value;

    @ApiModelProperty(value = "uid")
    private String createBy;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "租户ID")
    private Map<String, Double> allParamValueMap;
}