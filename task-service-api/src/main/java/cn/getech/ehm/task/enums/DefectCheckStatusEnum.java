package cn.getech.ehm.task.enums;

/**
 * 工单App查询状态枚举
 * <AUTHOR>
 * @date 2020/3/3
 */
public enum DefectCheckStatusEnum {
    NOCHECK(0,"未验收"),
    CHECKED(1,"已验收"),
    CHECKFAIL(2,"验收不通过");

    DefectCheckStatusEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public boolean isSame(Integer type) {
        return null != type && this.value == type;
    }

    public static String getNameByValue(int value){
        switch (value){
            case 0: return DefectCheckStatusEnum.NOCHECK.getName();
            case 1: return DefectCheckStatusEnum.CHECKED.getName();
            case 2: return DefectCheckStatusEnum.CHECKFAIL.getName();
        }

        return null;
    }

    private int value;

    private String name;

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
