package cn.getech.ehm.task.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TodayTaskDetailDto {

    @ApiModelProperty(value = "总工单")
    private Integer allCount;

    @ApiModelProperty(value = "已完成工单")
    private Integer finishedCount;

    @ApiModelProperty(value = "未完成")
    private Integer notFinishedCount;

    @ApiModelProperty(value = "工单类型")
    private Integer taskType;

    @ApiModelProperty(value = "标签名称")
    private String label;

    @ApiModelProperty(value = "颜色")
    private String color;
}
