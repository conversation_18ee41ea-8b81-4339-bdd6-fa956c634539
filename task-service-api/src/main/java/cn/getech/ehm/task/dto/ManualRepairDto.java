package cn.getech.ehm.task.dto;

import cn.getech.poros.framework.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 人工报修
 */
@Data
@EqualsAndHashCode()
public class ManualRepairDto {

    @ApiModelProperty(value = "报修单id")
    private String id;

    @ApiModelProperty(value = "设备id")
    private String equipmentId;

    @Excel(name = "客户名称", cellType = Excel.ColumnType.STRING)
    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @Excel(name = "故障设备名称", cellType = Excel.ColumnType.STRING)
    @ApiModelProperty(value = "设备名称")
    private String equipmentName;

    @Excel(name = "提报时间", cellType = Excel.ColumnType.STRING, dateFormat = "yyyy-MM-dd HH:mm")
    @ApiModelProperty(value = "报修/提报时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "状态(-1作废0待维修/待诊断1已维修/已完成)")
    private Integer status;

    @Excel(name = "处理状态", cellType = Excel.ColumnType.STRING)
    private String statusString;

    @Excel(name = "故障内容", cellType = Excel.ColumnType.STRING)
    @ApiModelProperty(value = "故障描述")
    private String faultDesc;

    @Excel(name = "解决方案", cellType = Excel.ColumnType.STRING)
    @ApiModelProperty(value = "解决方案")
    private String measures;

    @ApiModelProperty(value = "故障日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date faultTime;

    @Excel(name = "处理时间", cellType = Excel.ColumnType.STRING, dateFormat = "yyyy-MM-dd HH:mm")
    @ApiModelProperty(value = "修复/完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date repairTime;

    @ApiModelProperty(value = "确认时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value = "客户工程师uid/报修人uid")
    private String createBy;

    @ApiModelProperty(value = "客户工程师/报修人")
    private String createByUserName;

    @ApiModelProperty("客户工程师/报修人联系电话")
    private String createByPhone;

    @ApiModelProperty("客户工程师/报修人联系email")
    private String createByEmail;

    @ApiModelProperty(value = "接单人")
    private String handler;

    @ApiModelProperty(value = "设备编码")
    private String equipmentCode;

    @ApiModelProperty(value = "客户id")
    private String customerId;

    @ApiModelProperty(value = "维护设备类型")
    private String equipmentCategory;

    @ApiModelProperty(value = "维护设备位置")
    private String equipmentLocation;

    @ApiModelProperty(value = "紧急程度")
    private Integer priority;

    @ApiModelProperty(value = "工单id")
    private String taskId;

    @ApiModelProperty(value = "流程实例id")
    private String processInstanceId;

    @ApiModelProperty(value = "工作流id")
    private String activityId;

    @ApiModelProperty(value = "状态(0待派单1待接单2待执行3开始确认4安全确认中5已确认6执行中7执行中已挂起8待验收9正常关闭10异常关闭)")
    private Integer taskStatus;

    @ApiModelProperty(value = "工单编号")
    private String taskCode;

    @ApiModelProperty(value = "类型(0场内报修1远程诊断)")
    private Integer type;

    @ApiModelProperty(value = "附件id集合")
    private String mediaIds;

    @ApiModelProperty(value = "报修人公司")
    private String createCustomerName;

    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 是否删除(0false1true)
     */
    private Integer deleted;

    /**
     * 租户id
     */
    private String tenantId;

    private String createTimeString;

    private String repairTimeString;

    private String updateTimeString;

    private int index;

}
