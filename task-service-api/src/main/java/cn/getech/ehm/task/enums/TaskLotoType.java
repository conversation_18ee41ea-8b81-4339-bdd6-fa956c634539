package cn.getech.ehm.task.enums;

import cn.getech.ehm.common.dto.EnumListDto;
import cn.getech.ehm.task.dto.CommonConfigDto;

import java.util.ArrayList;
import java.util.List;

/**
 * 工单状态枚举
 * <AUTHOR>
 * @date 2020/3/3
 */
public enum TaskLotoType {
    DEFAULT(0,"默认/未处理", "#59A5FF"),
    YES(1,"是","#1890ffab"),
    NO(2,"否", "#364FBB"),

    ;


    TaskLotoType(int value, String name, String color) {
        this.value = value;
        this.name = name;
        this.color = color;
    }

    public boolean isSame(Integer type) {
        return null != type && this.value == type;
    }

    public static String getNameByValue(int value){
        for(TaskLotoType taskStatusType : TaskLotoType.values()){
            if(taskStatusType.getValue() == value){
                return taskStatusType.getName();
            }
        }
        return null;
    }



    private int value;

    private String name;

    private String color;

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }
}
