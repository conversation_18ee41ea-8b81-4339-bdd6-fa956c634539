package cn.getech.ehm.task.enums;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 工单类型枚举
 *
 * <AUTHOR>
 * @date 2020/3/3
 */
public enum TaskEcErrorResultType {
    DEFAULT(0, "初始状态"),
    NODEAL(1, "未处理"),
    IGNORE(2, "忽略"),
    ORDER(3, "报修");


    TaskEcErrorResultType(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public boolean isSame(Integer value) {
        return null != value && this.value == value;
    }

    public static String getNameByValue(Integer value) {
        String name = null;
        switch (value) {
            default:
                break;
        }
        return name;
    }

    private int value;

    private String name;

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    /**
     * 根据value返回枚举类型
     */
    public static TaskEcErrorResultType getByValue(int value) {
        for (TaskEcErrorResultType taskSourceType : values()) {
            if (taskSourceType.getValue() == value) {
                return taskSourceType;
            }
        }
        return null;
    }

    public static Map<Integer, String> getList() {
        Map<Integer, String> map = new LinkedHashMap<>(5);
        for (TaskEcErrorResultType taskSourceType : TaskEcErrorResultType.values()) {
            map.put(taskSourceType.getValue(), taskSourceType.getName());
        }
        return map;
    }
}
