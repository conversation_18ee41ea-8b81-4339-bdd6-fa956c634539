package cn.getech.ehm.task.enums;

import cn.getech.ehm.common.dto.EnumListDto;
import java.util.ArrayList;
import java.util.List;

/**
 * 工单推送参数监控枚举
 * <AUTHOR>
 * @date 2020/3/3
 */
public enum TaskIotPushType {
    NOT(0,"否"),
    POINT(1,"指标"),
    WAV(2,"波形");


    TaskIotPushType(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public boolean isSame(Integer type) {
        return null != type && this.value == type;
    }



    public static List<EnumListDto> getList(){
        List<EnumListDto> enumListDtos = new ArrayList<>();
        for(TaskIotPushType taskIotPushType : TaskIotPushType.values()){
            EnumListDto enumListDto = new EnumListDto();
            enumListDto.setValue(taskIotPushType.value);
            enumListDto.setName(taskIotPushType.name);
            enumListDtos.add(enumListDto);
        }
        return enumListDtos;
    }

    private int value;

    private String name;

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
