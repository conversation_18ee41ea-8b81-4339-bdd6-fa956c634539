package cn.getech.ehm.task.enums;


import cn.getech.ehm.common.dto.EnumListDto;
import java.util.ArrayList;
import java.util.List;

/**
 * CBM触发频率时间
 * <AUTHOR>
 * @date 2020/3/3
 */
public enum CbmFrequencyType {
    ZERO(0, "0H", 0),
    ONE(1, "12H", 12),
    TWO(2, "1天", 24),
    THREE(3, "3天", 3 * 24),
    FOUR(4, "5天", 5 * 24),
    FIVE(5, "7天", 7 * 24);


    CbmFrequencyType(int value, String name, long hour) {
        this.value = value;
        this.name = name;
        this.hour = hour;
    }

    public static List<EnumListDto> getList(){
        List<EnumListDto> enumListDtos = new ArrayList<>();
        for(CbmFrequencyType frequencyType : CbmFrequencyType.values()){
            EnumListDto enumListDto = new EnumListDto();
            enumListDto.setValue(frequencyType.value);
            enumListDto.setName(frequencyType.name);
            enumListDtos.add(enumListDto);
        }
        return enumListDtos;
    }

    public static long getHourByValue(int value){
        switch (value){
            case 0 : return CbmFrequencyType.ZERO.hour;
            case 1 : return CbmFrequencyType.ONE.hour;
            case 2 : return CbmFrequencyType.TWO.hour;
            case 3 : return CbmFrequencyType.THREE.hour;
            case 4 : return CbmFrequencyType.FOUR.hour;
            case 5 : return CbmFrequencyType.FIVE.hour;
        }
        return 0;
    }

    public static String getNameByValue(int value){
        switch (value){
            case 0 : return CbmFrequencyType.ZERO.name;
            case 1 : return CbmFrequencyType.ONE.name;
            case 2 : return CbmFrequencyType.TWO.name;
            case 3 : return CbmFrequencyType.THREE.name;
            case 4 : return CbmFrequencyType.FOUR.name;
            case 5 : return CbmFrequencyType.FIVE.name;
        }
        return null;
    }

    private int value;

    private String name;

    private long hour;

    public long getHour() {
        return hour;
    }

    public void setHour(long hour) {
        this.hour = hour;
    }

    public int getValue() { return value; }

    public void setValue(int value) { this.value = value; }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
