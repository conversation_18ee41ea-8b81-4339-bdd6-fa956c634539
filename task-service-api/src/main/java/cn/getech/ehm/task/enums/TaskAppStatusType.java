package cn.getech.ehm.task.enums;

/**
 * 工单App查询状态枚举
 * <AUTHOR>
 * @date 2020/3/3
 */
public enum TaskAppStatusType {

    DISPATCH(0,"待派单"),
    REPAIR_AUDIT(15,"待审批"),
    RECEIVING(1,"待接单"),
    HANDLE(2,"待执行"),
    BEGIN_CONFIRM(3,"开始确认"),
    CONFIRM(4,"安全确认中"),
    CONFIRMED(5,"已确认"),
    PROCESSING(6,"执行中"),
    //HANG_UP(7,"执行中已挂起"),
    CHECK_ACCEPT(8,"待验收"),
    CLOSED(9,"已关闭"),
    EXCEPTION_CLOSED(10,"异常关闭"),
    SUBMIT_WAIT(30,"待提交"),
    HAND_UP_AUDIT(41,"挂起审批中"),
    HAND_UP_AUDIT_PASS(42,"执行中已挂起"),
    //执行中转缺陷待审批
    PROCESS_DEFECT_AUDIT(43,"执行中转缺陷待审批"),
    //验收转缺陷待审批
    ACCEPT_DEFECT_AUDIT(44,"验收转缺陷待审批"),
    ;

    TaskAppStatusType(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public boolean isSame(Integer type) {
        return null != type && this.value == type;
    }

    public static String getNameByValue(int value){
        switch (value){
            case 0: return TaskAppStatusType.DISPATCH.getName();
            case 1: return TaskAppStatusType.RECEIVING.getName();
            case 2: return TaskAppStatusType.HANDLE.getName();
            case 3: return TaskAppStatusType.BEGIN_CONFIRM.getName();
            case 4: return TaskAppStatusType.CONFIRM.getName();
            case 5: return TaskAppStatusType.CONFIRMED.getName();
            case 6: return TaskAppStatusType.PROCESSING.getName();
            case 7: return TaskAppStatusType.CHECK_ACCEPT.getName();
            case 8: return TaskAppStatusType.CLOSED.getName();
            case 9: return TaskAppStatusType.REPAIR_AUDIT.getName();
        }

        return null;
    }

    private int value;

    private String name;

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
