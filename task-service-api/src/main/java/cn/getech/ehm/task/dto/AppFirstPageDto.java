package cn.getech.ehm.task.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * app首页
 *
 * <AUTHOR>
 * @date 2020-08-06
 */
@Data
@ApiModel(value = "AppFirstPageDto", description = "app首页")
public class AppFirstPageDto {

    @ApiModelProperty(value = "设备管理(告警单)")
    private List<WarnFirstPageDto> warns;

    @ApiModelProperty(value = "设备管理(工单)")
    private List<MaintTaskFirstPageDto> maintTasks;
}