package cn.getech.ehm.task.enums;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 工单来源枚举
 * <AUTHOR>
 * @date 2020/3/3
 */
public enum TaskSourceType {
    BREAKDOWN(1,"故障报修", "BM"),
    PLAN(2,"维保计划", "PM"),
    DEFECT(3,"设备缺陷", "DM");

    //INDUSTRY(4,"工装", "GZ");


    TaskSourceType(int value, String name, String prex) {
        this.value = value;
        this.name = name;
        this.prex = prex;
    }

    public boolean isSame(Integer value) {
        return null != value && this.value == value;
    }

    public static String getNameByValue(Integer value){
        String name = null;
        switch (value){
           case 1: name =  TaskSourceType.BREAKDOWN.name;
                break;
            case 2: name =  TaskSourceType.PLAN.name;
                break;
            case 3: name =  TaskSourceType.DEFECT.name;
                break;
            default:break;
        }
        return name;
    }

    private int value;

    private String name;

    private String prex;

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPrex() { return prex; }

    public void setPrex(String prex) { this.prex = prex; }

    /**
     * 根据value返回枚举类型
     */
    public static TaskSourceType getByValue(int value) {
        for (TaskSourceType taskSourceType : values()) {
            if (taskSourceType.getValue() == value) {
                return taskSourceType;
            }
        }
        return null;
    }

    public static String getPrexByValue(int value) {
        for (TaskSourceType taskSourceType : values()) {
            if (taskSourceType.getValue() == value) {
                return taskSourceType.prex;
            }
        }
        return "";
    }

    public static Map<Integer, String> getList(){
        Map<Integer, String> map = new LinkedHashMap<>(5);
        for(TaskSourceType taskSourceType : TaskSourceType.values()){
            map.put(taskSourceType.getValue(), taskSourceType.getName());
        }
        return map;
    }
}
