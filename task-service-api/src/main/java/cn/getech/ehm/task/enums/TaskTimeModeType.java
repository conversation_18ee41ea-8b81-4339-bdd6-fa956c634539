package cn.getech.ehm.task.enums;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 工时计算方式枚举
 *
 * <AUTHOR>
 * @since 2022-06-08
 */
public enum TaskTimeModeType {
    AUTOMATIC(1, "自动计算"),
    ARTIFICIAL(2, "人工输入"),
    NOCALCULATE(3, "不计算");


    TaskTimeModeType(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public boolean isSame(Integer value) {
        return null != value && this.value == value;
    }

    public static String getNameByValue(Integer value) {
        String name = null;
        switch (value) {
            case 1:
                name = TaskTimeModeType.AUTOMATIC.name;
                break;
            case 2:
                name = TaskTimeModeType.ARTIFICIAL.name;
                break;
            case 3:
                name = TaskTimeModeType.NOCALCULATE.name;
                break;
            default:
                break;
        }
        return name;
    }

    private int value;

    private String name;

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    /**
     * 根据value返回枚举类型
     */
    public static TaskTimeModeType getByValue(int value) {
        for (TaskTimeModeType taskSourceType : values()) {
            if (taskSourceType.getValue() == value) {
                return taskSourceType;
            }
        }
        return null;
    }

    public static Map<Integer, String> getList() {
        Map<Integer, String> map = new LinkedHashMap<>(5);
        for (TaskTimeModeType taskSourceType : TaskTimeModeType.values()) {
            map.put(taskSourceType.getValue(), taskSourceType.getName());
        }
        return map;
    }
}
