package cn.getech.ehm.task.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 统计备件dto
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "TaskPartStatisticsDto", description = "统计备件dto")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TaskPartStatisticsDto {

    @ApiModelProperty(value = "工单id")
    private String taskId;

    @ApiModelProperty(value = "设备id")
    private String equipmentId;

    @ApiModelProperty(value = "备件id")
    private String partId;

    @ApiModelProperty(value = "实际数量")
    private Integer actualQty;

}