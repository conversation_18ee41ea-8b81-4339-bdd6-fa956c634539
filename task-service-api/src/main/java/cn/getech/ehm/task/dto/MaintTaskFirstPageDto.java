package cn.getech.ehm.task.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 维护工单分页dto
 *
 * <AUTHOR>
 * @date 2021-1-21
 */
@Data
@ApiModel(value = "MaintTaskFirstPageDto", description = "维护工单分页dto")
public class MaintTaskFirstPageDto {

    @ApiModelProperty(value = "工单id")
    private String id;

    @ApiModelProperty(value = "编号")
    private String code;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "维护设备id")
    private String equipmentId;

    @ApiModelProperty(value = "维护设备名称")
    private String equipmentName;

    @ApiModelProperty(value = "设备编码")
    private String equipmentCode;

    @ApiModelProperty(value = "维护设备责任人")
    private String equipmentPrincipal;

    @ApiModelProperty(value = "维护设备状态")
    private Integer equipmentRunningStatus;

    @ApiModelProperty(value = "维护设备位置")
    private String equipmentLocation;

    @ApiModelProperty(value = "维护设备类型")
    private String equipmentCategory;

    @ApiModelProperty(value = "状态(0待派单1待接单2待执行3开始确认4安全确认中5已确认6执行中7执行中已挂起8待验收9正常关闭10异常关闭)")
    private Integer status;

    @ApiModelProperty(value = "工单类型(1故障单2维保单)")
    private Integer type;

    @ApiModelProperty(value = "紧急程度")
    private String urgency;

    @ApiModelProperty(value = "维保类型")
    private String jobType;

    @ApiModelProperty(value = "来源(1故障报修2维保计划)")
    private Integer sourceType;

    @ApiModelProperty(value = "专业/工单类别")
    private String major;

    @ApiModelProperty(value = "故障/维保内容")
    private String content;

    @ApiModelProperty(value = "派单人")
    private String[] dispatchHandler;

    @ApiModelProperty(value = "派单人")
    private String dispatchHandlerNames;

    @ApiModelProperty(value = "接单人uid")
    private String handler;

    @ApiModelProperty(value = "接单人名称")
    private String handlerName;

    @ApiModelProperty(value = "维护人员id集合")
    private String allStaffIds;

    @ApiModelProperty(value = "维护人员名称集合")
    private String staffNames;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "计划维护时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date planMaintTime;

    @ApiModelProperty(value = "流程实例id")
    private String processInstanceId;

    @ApiModelProperty(value = "首张图片id")
    private String picId;

    @ApiModelProperty(value = "图片url")
    private String picUrl;

}