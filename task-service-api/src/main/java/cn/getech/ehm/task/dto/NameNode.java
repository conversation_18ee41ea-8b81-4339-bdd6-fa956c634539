package cn.getech.ehm.task.dto;

import lombok.Data;

import java.util.Objects;

@Data
public class NameNode {
    String nameKey;
    Integer intValue;
    Integer sourceType;


    public NameNode() {
    }

    public NameNode(String nameKey) {
        this.nameKey = nameKey;
    }

    public NameNode(String nameKey, Integer intValue) {
        this.nameKey = nameKey;
        this.intValue = intValue;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null ) return true;
        NameNode that = (NameNode) o;
        return Objects.equals(nameKey, that.nameKey);
    }

    @Override
    public int hashCode() {
        return Objects.hash(nameKey, intValue);
    }
}
