package cn.getech.ehm.task.dto;

import lombok.Data;

import java.util.Objects;

@Data
public class TypeMonthNode {
    String nameKey;
    String monthKey;
    Integer intValue;

    public TypeMonthNode() {
    }

    public TypeMonthNode(String nameKey, String monthKey) {
        this.nameKey = nameKey;
        this.monthKey = monthKey;
    }

    public TypeMonthNode(String nameKey, String monthKey, Integer intValue) {
        this.nameKey = nameKey;
        this.monthKey = monthKey;
        this.intValue = intValue;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null) return true;
        TypeMonthNode that = (TypeMonthNode) o;
        return Objects.equals(nameKey, that.nameKey) &&
                Objects.equals(monthKey, that.monthKey);
    }

    @Override
    public int hashCode() {
        return Objects.hash(nameKey, monthKey, intValue);
    }
}
