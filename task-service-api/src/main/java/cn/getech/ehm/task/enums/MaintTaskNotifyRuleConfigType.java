package cn.getech.ehm.task.enums;

import java.util.Arrays;
import java.util.List;

/**
 * 工单来源枚举
 *
 * <AUTHOR>
 * @date 2020/3/3
 */
public enum MaintTaskNotifyRuleConfigType {
    TYPE_1(TaskType.BREAKDOWN.getValue(), TaskOperationType.NOTIFY_DURATION_RULE.getValue(), "0"),
    TYPE_2(TaskType.BREAKDOWN.getValue(), TaskOperationType.NOTIFY_FREQ_RULE.getValue(), "0"),
    ;

    public static List<MaintTaskNotifyRuleConfigType> getTaskTypeList() {
        return Arrays.asList(MaintTaskNotifyRuleConfigType.values());
    }

    MaintTaskNotifyRuleConfigType(Integer taskType, Integer taskOperateType, String content) {
        this.taskType = taskType;
        this.taskOperateType = taskOperateType;
        this.content = content;
    }

    private Integer taskType;

    private Integer taskOperateType;

    private String content;

    public Integer getTaskType() {
        return taskType;
    }

    public void setTaskType(Integer taskType) {
        this.taskType = taskType;
    }

    public Integer getTaskOperateType() {
        return taskOperateType;
    }

    public void setTaskOperateType(Integer taskOperateType) {
        this.taskOperateType = taskOperateType;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
