package cn.getech.ehm.task.enums;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 工单类型枚举
 * <AUTHOR>
 * @date 2020/3/3
 */
public enum TaskType {
    BREAKDOWN(1, "故障单", 1d, "故障工单", "rgba(59, 125, 221, 1)"),
    PLAN(2, "维保单", 1d, "维保工单", "rgba(241, 116, 0, 1)"),
    DEFECT(3, "缺陷单", 1d, "缺陷工单", "rgba(229, 89, 106, 1)"),
    EC(4, "EC(点检)单", 0.5, "点检工单", "rgba(26, 177, 141, 1)");

    public static Boolean isPlanOrEc(Integer type) {
        if (type == TaskType.EC.getValue()
                || type == TaskType.PLAN.getValue()) {
            return true;
        }
        return false;
    }

    public static Boolean isEc(Integer type) {
        if (type == TaskType.EC.getValue()) {
            return true;
        }
        return false;
    }

    public static Boolean isPlan(Integer type) {
        if (type == TaskType.PLAN.getValue()) {
            return true;
        }
        return false;
    }


    TaskType(int value, String name, Double integral, String label, String color) {
        this.value = value;
        this.name = name;
        this.integral = integral;
        this.label = label;
        this.color = color;
    }

    public boolean isSame(Integer value) {
        return null != value && this.value == value;
    }

    public static String getNameByValue(Integer value) {
        String name = null;
        switch (value) {
            case 1:
                name = TaskType.BREAKDOWN.name;
                break;
            case 2:
                name = TaskType.PLAN.name;
                break;
            case 3:
                name = TaskType.DEFECT.name;
                break;
            case 4:
                name = TaskType.EC.name;
                break;
            default:
                break;
        }
        return name;
    }

    public static Double getIntegralByValue(Integer value) {
        switch (value) {
            case 1:
                return TaskType.BREAKDOWN.integral;
            case 2:
                return TaskType.PLAN.integral;
            case 3:
                return TaskType.DEFECT.integral;
            case 4:
                return TaskType.EC.integral;
        }
        return 0d;
    }

    private int value;

    private String name;

    private Double integral;

    private String label;

    private String color;

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Double getIntegral() { return integral; }

    public void setIntegral(Double integral) { this.integral = integral; }

    public String getLabel() { return label; }

    public void setLabel(String label) { this.label = label; }

    public String getColor() { return color; }

    public void setColor(String color) { this.color = color; }

    /**
     * 根据value返回枚举类型
     */
    public static TaskType getByValue(int value) {
        for (TaskType taskSourceType : values()) {
            if (taskSourceType.getValue() == value) {
                return taskSourceType;
            }
        }
        return null;
    }

    public static Map<Integer, String> getList() {
        Map<Integer, String> map = new LinkedHashMap<>(5);
        for (TaskType taskSourceType : TaskType.values()) {
            map.put(taskSourceType.getValue(), taskSourceType.getName());
        }
        return map;
    }
}
