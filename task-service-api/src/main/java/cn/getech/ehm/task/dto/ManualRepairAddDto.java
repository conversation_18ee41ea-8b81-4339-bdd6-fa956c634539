package cn.getech.ehm.task.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

/**
 * 新增人工报修
 */
@Data
@EqualsAndHashCode()
public class ManualRepairAddDto {

    @ApiModelProperty(value = "设备id")
    private String equipmentId;

    @ApiModelProperty(value = "来源id")
    private String sourceId;

    @ApiModelProperty(value = "故障描述")
    private String faultDesc;

    @ApiModelProperty(value = "故障日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date faultTime;

}
