package cn.getech.ehm.task.enums;

import java.util.Arrays;
import java.util.List;

/**
 * 工单超时验收规则
 *
 * <AUTHOR>
 * @date 2020/3/3
 */
public enum MaintTaskOvertimeCheckConfigType {
    BREAKDOWN(TaskType.BREAKDOWN.getValue(), TaskOperationType.OVERTIME_CHECK_RULE.getValue(), "60"),
    PLAN(TaskType.PLAN.getValue(), TaskOperationType.OVERTIME_CHECK_RULE.getValue(), "60"),
    DEFECT(TaskType.DEFECT.getValue(), TaskOperationType.OVERTIME_CHECK_RULE.getValue(), "60"),
    EC(TaskType.EC.getValue(), TaskOperationType.OVERTIME_CHECK_RULE.getValue(), "60");

    public static List<MaintTaskOvertimeCheckConfigType> getTaskTypeList() {
        return Arrays.asList(MaintTaskOvertimeCheckConfigType.values());
    }

    MaintTaskOvertimeCheckConfigType(Integer taskType, Integer taskOperateType, String content) {
        this.taskType = taskType;
        this.taskOperateType = taskOperateType;
        this.content = content;
    }

    private Integer taskType;

    private Integer taskOperateType;

    private String content;


    public Integer getTaskType() {
        return taskType;
    }

    public void setTaskType(Integer taskType) {
        this.taskType = taskType;
    }

    public Integer getTaskOperateType() {
        return taskOperateType;
    }

    public void setTaskOperateType(Integer taskOperateType) {
        this.taskOperateType = taskOperateType;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
