package cn.getech.ehm.task.enums;

/**
 * 工单评分枚举
 *
 * <AUTHOR>
 * @date 2020/3/3
 */
public enum TaskGradeType {
    MINUS_ONE(-1, "未完成", 0d),
    ZERO(0, "0星", 0.1),
    <PERSON>(1, "1星", 0.2),
    <PERSON><PERSON><PERSON>(2, "2星", 0.4),
    THRE<PERSON>(3, "3星", 0.6),
    FOUR(4, "4星", 0.8),
    FIVE(5, "5星", 1d);


    TaskGradeType(int value, String name, Double integral) {
        this.value = value;
        this.name = name;
        this.integral = integral;
    }

    public static Double getIntegralByValue(Integer value) {
        for(TaskGradeType taskGradeType : TaskGradeType.values()){
            if(taskGradeType.getValue() == value){
                return taskGradeType.getIntegral();
            }
        }
        return 0d;
    }

    private int value;

    private String name;

    private Double integral;

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Double getIntegral() { return integral; }

    public void setIntegral(Double integral) { this.integral = integral; }
}
