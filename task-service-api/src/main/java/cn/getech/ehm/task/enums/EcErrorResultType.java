package cn.getech.ehm.task.enums;

import cn.getech.ehm.common.dto.EnumListDto;
import cn.getech.ehm.task.dto.CommonConfigDto;

import java.util.ArrayList;
import java.util.List;

/**
 * 工单状态枚举
 * <AUTHOR>
 * @date 2020/3/3
 */
public enum EcErrorResultType {
    NODEAL(1,"未处理"),
    IGNORE(2,"已忽略"),
    DONE(3,"已报修");


    EcErrorResultType(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public boolean isSame(Integer type) {
        return null != type && this.value == type;
    }

    public static String getNameByValue(int value){
        switch (value){
            case 1: return EcErrorResultType.NODEAL.getName();
            case 2: return EcErrorResultType.IGNORE.getName();
            case 3: return EcErrorResultType.DONE.getName();
        }

        return null;
    }

    private int value;

    private String name;

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
