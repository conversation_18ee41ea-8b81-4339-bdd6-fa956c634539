package cn.getech.ehm.task.enums;

/**
 * 工单类型枚举
 *
 * <AUTHOR>
 * @date 2020/3/3
 */
public enum TaskJobType {
    TBM("1", "TBM"),
    EC("2", "EC"),
    CBM("3", "CBM"),
    RH("4", "润滑"),
    XJ("5", "巡检");


    TaskJobType(String value, String name) {
        this.value = value;
        this.name = name;
    }


    private String value;

    private String name;

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    /**
     * 根据value返回枚举类型
     */
    public static TaskJobType getByValue(String value) {
        for (TaskJobType taskSourceType : values()) {
            if (taskSourceType.getValue().equals(value)) {
                return taskSourceType;
            }
        }
        return null;
    }
}
