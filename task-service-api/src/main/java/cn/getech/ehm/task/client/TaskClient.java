package cn.getech.ehm.task.client;

import cn.getech.ehm.task.dto.ParameterCategoryDto;
import cn.getech.ehm.task.dto.*;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-08-21 16:40:20
 **/
@FeignClient(name = "task-service", path = "/api/task-service")
public interface TaskClient {

    /**
     * 判断任务项是否被引用
     *
     * @param itemId
     * @return
     */
    @GetMapping(value = "/taskOpen/taskItem/reference")
    RestResponse<Boolean> taskItemRef(@RequestParam("itemId") String itemId);

    /**
     * 获取统计需要工单信息
     *
     * @param taskStatisticsReqDto
     * @return
     */
    @PostMapping(value = "/maintTask/getTaskStatistics")
    RestResponse<List<TaskStatisticsDto>> getTaskStatistics(@RequestBody TaskStatisticsReqDto taskStatisticsReqDto);

    /**
     * 根据字段查询数据库中存在的条数
     *
     * @param tableName
     * @param columnName
     * @param value
     * @return
     */
    @GetMapping(value = "/maintPlan/getCount")
    RestResponse<Integer> getCount(@RequestParam("tableName") String tableName,
                                   @RequestParam("columnName") String columnName,
                                   @RequestParam("value") String value);


    /**
     * 查询app首页的 设备管理 ，远程诊断数据
     *
     * @param size 条数
     * @return
     */
    @PostMapping(value = "/maintTaskApp/getFirstPage")
    RestResponse<AppFirstPageDto> getFirstPage(@RequestParam("size") Integer size);

    /**
     * 所有故障设备统计
     *
     * @return
     */
    @PostMapping(value = "/statistics/allEquipmentFaultStatistics")
    RestResponse<List<FaultStatisticsDto>> allEquipmentFaultStatistics(@RequestBody FaultStatisticsParam param);

    /**
     * 故障设备统计
     *
     * @return
     */
    @PostMapping(value = "/statistics/equipmentFaultStatistics")
    RestResponse<List<FaultStatisticsDto>> equipmentFaultStatistics(@RequestBody FaultStatisticsParam param);

    /**
     * 所有故障条目统计
     *
     * @return
     */
    @PostMapping(value = "/statistics/allFaultItemStatistics")
    RestResponse<List<FaultStatisticsDto>> allFaultItemStatistics(@RequestBody FaultStatisticsParam param);

    /**
     * 校验备件是否被维护计划、维护工单使用
     *
     * @param partIds
     * @return
     */
    @PostMapping("/maintTask/checkPartUsed")
    RestResponse<List<String>> checkPartUsed(@RequestBody String[] partIds);

    /**
     * 校验备件是否被维护计划、维护工单使用
     *
     * @param equipmentIds
     * @return
     */
    @AuditLog(title = "校验设备", desc = "校验设备是否被维护计划、维护工单使用", businessType = BusinessType.QUERY)
    @PostMapping("/maintTask/checkEquipmentUsed")
    RestResponse<List<String>> checkEquipmentUsed(@RequestBody String[] equipmentIds);

    /**
     * 获取工单关联的设备ids
     *
     * @return
     */
    @GetMapping("/maintTask/getUsedInfoIds")
    RestResponse<List<String>> getTaskUsedInfoIds();

    /**
     * 获取故障单关联的设备ids
     *
     * @return
     */
    @GetMapping("/manualRepair/getUsedInfoIds")
    RestResponse<List<String>> getRepairUsedInfoIds();

    /**
     * 获取计划单单关联的设备ids
     *
     * @return
     */
    @GetMapping("/maintPlan/getUsedInfoIds")
    RestResponse<List<String>> getPlanUsedInfoIds();

    /**
     * 告警新增故障报修
     */
    @PostMapping(value = "/manualRepair/saveWarnRepair")
    RestResponse<Boolean> saveWarnRepair(@RequestBody ManualRepairAddDto addDto);

    /**
     * 校验当天设备参数是否有生成故障单
     */
    @GetMapping(value = "/manualRepair/checkHaveRepair")
    RestResponse<Boolean> checkHaveRepair(@RequestParam("infoParamId") String infoParamId);

    @GetMapping("/maintTask/workbench/statistics/maintTask")
    public RestResponse<String> getWorkbenchStatisticsOfMaintTask(@RequestParam("param") String param);

    @GetMapping("/maintTask/workbench/statistics/maintTask/overtime")
    public RestResponse<String> getWorkbenchStatisticsOfMaintTaskOverTime(@RequestParam("param") String param);

    @GetMapping("/defectInfo/inner/workbench/count")
    public RestResponse<String> getCountOfStatistics(@RequestParam("param") String param);

    @GetMapping("/defectInfo/inner/workbench/count/overtime")
    public RestResponse<String> getCountOfStatisticsOverTime(@RequestParam("param") String param);

    /**
     * 生成cbm单逻辑
     */
    @PostMapping(value = "/maintPlan/createCbmOrder")
    RestResponse<Boolean> createCbmOrder(@RequestBody ParameterCategoryDto dto);

    @GetMapping(value = "/maintTask/getTaskById/foreign/{id}")
    public RestResponse<MaintTaskDtoForeign> getTaskByIdForeign(@PathVariable String id);

    /**
     * 获取设备关联故障树
     * 类型(1故障现象2故障原因3处理措施)
     */
    @GetMapping(value = "/maintTask/getUsedFaultTree")
    RestResponse<Map<String, Integer>> getUsedFaultTree(@RequestParam("equipmentId") String equipmentId, @RequestParam("faultTreeIds") List<String> faultTreeIds, @RequestParam("type") Integer type);

    @GetMapping("/maintPlan/workbench/statistics/maintPlan")
    RestResponse<String> getStatisticsOfPlanEndCount(@RequestParam("type") String type);

    @PostMapping(value = "/maintTask/getTaskById/foreign/batch")
    public RestResponse<List<MaintTaskDtoForeign>> getTaskListByIdForeign(@RequestBody List<String> ids);

    /**
     * 告警新增故障报修
     */
    @PostMapping(value = "/manualRepair/scadaSaveWarnRepair")
    RestResponse<Boolean> scadaSaveWarnRepair(@RequestBody List<ManualRepairAddDto> addDtos);
}
