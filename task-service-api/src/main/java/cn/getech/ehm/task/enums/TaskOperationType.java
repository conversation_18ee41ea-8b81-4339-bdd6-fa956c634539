package cn.getech.ehm.task.enums;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 工单来源枚举
 * <AUTHOR>
 * @date 2020/3/3
 */
public enum TaskOperationType {
    SEND_TASK(0,"派单超时"),
    REC_TASK(1,"接单超时"),
    DEAL_TASK(2,"处理超时"),
    ANY_OVERTIME_TASK(3,"任意超时异常"),
    EC_ERROR_TASK(4,"维护异常"),
    DEAL_ERROR_TASK(5,"处理异常"),
    OVERTIME_CHECK_RULE(10,"超时验收规则"),
    SEND_RULE(100,"派单规则"),
    NOTIFY_DURATION_RULE(101,"通知持续时长规则"),
    NOTIFY_FREQ_RULE(102,"通知频次规则");


    TaskOperationType(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public boolean isSame(Integer value) {
        return null != value && this.value == value;
    }


    private int value;

    private String name;


    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


}
