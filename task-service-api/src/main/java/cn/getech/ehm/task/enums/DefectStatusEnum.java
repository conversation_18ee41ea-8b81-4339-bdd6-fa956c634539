package cn.getech.ehm.task.enums;

/**
 * 工单App查询状态枚举
 * <AUTHOR>
 * @date 2020/3/3
 */
public enum DefectStatusEnum {
    NODEAL(0,"未处理"),
    DEALING(1,"处理中"),
    DONE(2,"已处理"),
    CLOSED(3,"已关闭"),
    FINISHED(4,"已完成");

    DefectStatusEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public boolean isSame(Integer type) {
        return null != type && this.value == type;
    }

    public static String getNameByValue(int value){
        switch (value){
            case 0: return DefectStatusEnum.NODEAL.getName();
            case 1: return DefectStatusEnum.DEALING.getName();
            case 2: return DefectStatusEnum.DONE.getName();
            case 3: return DefectStatusEnum.CLOSED.getName();
            case 4: return DefectStatusEnum.FINISHED.getName();
        }

        return null;
    }

    private int value;

    private String name;

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
