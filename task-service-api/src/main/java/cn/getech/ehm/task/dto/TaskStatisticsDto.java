package cn.getech.ehm.task.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

/**
 * 报表所需工单
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "TaskStatisticsDto", description = "报表所需工单")
public class TaskStatisticsDto {

    @ApiModelProperty(value = "工单id")
    private String taskId;

    @ApiModelProperty(value = "维护设备id")
    private String taskEquipmentId;

    @ApiModelProperty(value = "工单编号")
    private String taskCode;

    @ApiModelProperty(value = "工单名称")
    private String taskName;

    @ApiModelProperty(value = "状态(0待派单1待接单2待执行3开始确认4安全确认中5已确认6执行中7执行中已挂起8待验收9正常关闭10异常关闭)")
    private Integer taskStatus;

    @ApiModelProperty(value = "工单类别(0点检1预防维护2故障维修3维护改进4转产5远程诊断)")
    private Integer taskType;

    @ApiModelProperty(value = "维护人员id集合")
    private String[] allStaffIds;

    @ApiModelProperty(value = "实际停机开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date beginDowntime;

    @ApiModelProperty(value = "实际停机结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date endDowntime;

    @ApiModelProperty(value = "实际维护开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date beginMaintTime;

    @ApiModelProperty(value = "实际维护结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date endMaintTime;

    @ApiModelProperty(value = "评分")
    private Integer grade;

    @ApiModelProperty(value = "工单修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date taskUpdateTime;

    @ApiModelProperty(value = "工单创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date taskCreateTime;

    @ApiModelProperty(value = "备件id")
    private String partId;

    @ApiModelProperty(value = "备件修理数量")
    private Integer partActualQty;

    @ApiModelProperty("工单截止时间")
    private Date taskDeadlineDate;

    @ApiModelProperty(value = "验收结果0未通过1通过")
    private Integer checkAcceptResult;

    @ApiModelProperty(value = "接单人/处理人")
    private String handler;

    @ApiModelProperty(value = "派单时间")
    private Date sendTaskDate;

    @ApiModelProperty(value = "接单时间")
    private Date recTaskDate;

    private Long workingTime;
}