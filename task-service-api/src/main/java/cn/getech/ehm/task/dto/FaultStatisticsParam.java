package cn.getech.ehm.task.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 故障统计 查询参数
 *
 * <AUTHOR>
 * @date 2020-01-13
 */
@Data
@ApiModel(value = "FaultStatisticsParam", description = "故障统计查询参数")
public class FaultStatisticsParam {

    @ApiModelProperty(value = "查询数量最多的前几条")
    private Integer topCount;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @ApiModelProperty(value = "设备类型id")
    private String categoryId;

    private String locationId;

    @ApiModelProperty(value = "设备ID列表")
    private List<String> equipmentIdList;

    @ApiModelProperty("工序类型")
    private String processType;

    @ApiModelProperty("状态筛选")
    private List<String> statusFilter;

}