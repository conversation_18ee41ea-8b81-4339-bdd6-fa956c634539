package cn.getech.ehm.task.enums;

/**
 * 工单App查询状态枚举
 * <AUTHOR>
 * @date 2020/3/3
 */
public enum DefectTypeEnum {
    EMERGENCY(0,"紧急缺陷"),
    IMPORTANT(1,"重大缺陷"),
    NORMAL(2,"一般缺陷");

    DefectTypeEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public boolean isSame(Integer type) {
        return null != type && this.value == type;
    }

    public static String getNameByValue(int value){
        switch (value){
            case 0: return DefectTypeEnum.EMERGENCY.getName();
            case 1: return DefectTypeEnum.IMPORTANT.getName();
            case 2: return DefectTypeEnum.NORMAL.getName();
        }

        return null;
    }

    private int value;

    private String name;

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
