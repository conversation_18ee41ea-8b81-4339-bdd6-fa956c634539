package cn.getech.ehm.task.enums;

import cn.getech.ehm.task.dto.CommonConfigDto;
import cn.getech.ehm.common.dto.EnumListDto;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 工单状态枚举
 * <AUTHOR>
 * @date 2020/3/3
 */
public enum TaskStatusType {
    DISPATCH(0,"待派单", "#59A5FF"),
    REPAIR_AUDIT(15,"待审批","#1890ffab"),
    RECEIVING(1,"待接单", "#364FBB"),
    HANDLE(2,"待执行", "#FF9900"),
    BEGIN_CONFIRM(3,"开始确认", "#4388E6"),
    CONFIRM(4,"安全确认中", "#32A752"),
    CONFIRMED(5,"已确认", "#333333"),
    PROCESSING(6,"执行中", "#FEC319"),
    HANG_UP(7,"执行中已挂起", "#FF0000"),
    CHECK_ACCEPT(8,"待验收", "#333333"),
    CLOSED(9,"已关闭", "#4F5DFF"),
    EXCEPTION_CLOSED(10,"异常关闭", "#FF544E"),
    HAND_UP_AUDIT(41,"挂起审批中", "#FF9900"),
    HAND_UP_AUDIT_PASS(42,"执行中已挂起", "#32A752"),
    SUBMIT_WAIT(30,"待提交", "#364FBB"),
    //执行中转缺陷待审批
    PROCESS_DEFECT_AUDIT(43,"执行中转缺陷待审批", "#FF9900"),
    //验收转缺陷待审批
    ACCEPT_DEFECT_AUDIT(44,"验收转缺陷待审批", "#FF9900"),
    ;


    TaskStatusType(int value, String name, String color) {
        this.value = value;
        this.name = name;
        this.color = color;
    }

    public boolean isSame(Integer type) {
        return null != type && this.value == type;
    }

    public static String getNameByValue(int value){
        for(TaskStatusType taskStatusType : TaskStatusType.values()){
            if(taskStatusType.getValue() == value){
                return taskStatusType.getName();
            }
        }
        return null;
    }

    public static String getNameByValue(String valueStr){
        if (StringUtils.isBlank(valueStr)){
            return "";
        }
        int value = Integer.parseInt(valueStr);
        for(TaskStatusType taskStatusType : TaskStatusType.values()){
            if(taskStatusType.getValue() == value){
                return taskStatusType.getName();
            }
        }
        return null;
    }

    public static List<EnumListDto> getList(CommonConfigDto commonConfigDto){
        List<EnumListDto> enumListDtos = new ArrayList<>();
        for(TaskStatusType taskStatusType : TaskStatusType.values()){
            if((taskStatusType.getValue() == TaskStatusType.BEGIN_CONFIRM.getValue() ||
                    taskStatusType.getValue() == TaskStatusType.CONFIRM.getValue() ||
                    taskStatusType.getValue() == TaskStatusType.CONFIRMED.getValue()) && !commonConfigDto.getConfirm()){
                //去除安全确认
                continue;
            }
            if(taskStatusType.getValue() == TaskStatusType.REPAIR_AUDIT.getValue() && !commonConfigDto.getRepairAudit()){
                //去除故障单审核
                continue;
            }
            EnumListDto enumListDto = new EnumListDto();
            enumListDto.setValue(taskStatusType.value);
            enumListDto.setName(taskStatusType.name);
            enumListDto.setColor(taskStatusType.color);
            enumListDtos.add(enumListDto);
        }
        return enumListDtos;
    }

    private int value;

    private String name;

    private String color;

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }
}
