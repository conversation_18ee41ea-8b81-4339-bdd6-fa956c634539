package cn.getech.ehm.task.dto;

import lombok.Data;

import java.util.Objects;

@Data
public class MonthNode {
    String monthKey;
    Integer intValue;

    public MonthNode() {
    }

    public MonthNode(String monthKey) {
        this.monthKey = monthKey;
    }

    public MonthNode(String monthKey, Integer intValue) {
        this.monthKey = monthKey;
        this.intValue = intValue;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null ) return true;
        MonthNode that = (MonthNode) o;
        return Objects.equals(monthKey, that.monthKey);
    }

    @Override
    public int hashCode() {
        return Objects.hash(monthKey, intValue);
    }
}
