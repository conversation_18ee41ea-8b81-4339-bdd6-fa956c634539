package cn.getech.ehm.task.enums;

import java.util.Arrays;
import java.util.List;

/**
 * 工单来源枚举
 *
 * <AUTHOR>
 * @date 2020/3/3
 */
public enum MaintTaskConfigType {
    //TYPE_1(TaskType.BREAKDOWN.getValue(), TaskOperationType.SEND_TASK.getValue(), "30"),
    TYPE_2(TaskSourceType.BREAKDOWN.getValue(), TaskOperationType.REC_TASK.getValue(), "30"),
    //TYPE_3(TaskType.DEFECT.getValue(), TaskOperationType.SEND_TASK.getValue(), "30"),
    TYPE_4(TaskSourceType.DEFECT.getValue(), TaskOperationType.REC_TASK.getValue(), "30"),
    //TYPE_5(TaskType.PLAN.getValue(), TaskOperationType.SEND_TASK.getValue(), "30"),
    TYPE_6(TaskSourceType.PLAN.getValue(), TaskOperationType.REC_TASK.getValue(), "30");
    //TYPE_7(TaskType.EC.getValue(), TaskOperationType.SEND_TASK.getValue(), "30"),
    //TYPE_8(TaskType.EC.getValue(), TaskOperationType.REC_TASK.getValue(), "30");

    public static List<MaintTaskConfigType> getTaskTypeList() {
        return Arrays.asList(MaintTaskConfigType.values());
    }

    MaintTaskConfigType(Integer taskType, Integer taskOperateType, String content) {
        this.taskType = taskType;
        this.taskOperateType = taskOperateType;
        this.content = content;
    }

    private Integer taskType;

    private Integer taskOperateType;

    private String content;


    public Integer getTaskType() {
        return taskType;
    }

    public void setTaskType(Integer taskType) {
        this.taskType = taskType;
    }

    public Integer getTaskOperateType() {
        return taskOperateType;
    }

    public void setTaskOperateType(Integer taskOperateType) {
        this.taskOperateType = taskOperateType;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
