package cn.getech.ehm.task.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;


/**
 * 报表所需工单
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "TaskStatisticsReqDto", description = "报表所需工单")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskStatisticsReqDto {
    @ApiModelProperty(value = "统计开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginTime;

    @ApiModelProperty(value = "统计结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "设备id集合")
    private String[] equipmentIds;

    @ApiModelProperty(value = "设备位置id集合")
    private String[] locationIds;

    @ApiModelProperty(value = "指标选择(0无故障时间1故障时间2故障次数3停机时长4维修时长5工单数量6工时7备件耗用8人员绩效9工单准时率)")
    private Integer type;
}