package cn.getech.ehm.task.dto;

import cn.getech.ehm.common.constant.StaticValue;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TodayTaskCountDto {

    @ApiModelProperty(value = "我的工单数量")
    private Integer ourNum;

    @ApiModelProperty(value = "全部工单数量")
    private Integer allNum;

    @ApiModelProperty(value = "标签数据集合")
    private List<TodayTaskDetailDto> list;
}
