package cn.getech.ehm.task.enums;

import cn.getech.ehm.common.dto.EnumListDto;
import com.google.common.collect.Maps;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 作业项目结果类型
 */
public enum JobItemResultType {
    JUDGE(1,"判断"),
    NUMBER_VALUE(2,"数值"),
    TEXT_CONTENT(3,"文本"),
    DETECTION(4,"检测");


    JobItemResultType(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public boolean isSame(Integer type) {
        return null != type && this.value == type;
    }

    public static String getNameByValue(int value){
        switch (value){
            case 1: return JobItemResultType.JUDGE.getName();
            case 2: return JobItemResultType.NUMBER_VALUE.getName();
            case 3: return JobItemResultType.TEXT_CONTENT.getName();
            case 4: return JobItemResultType.DETECTION.getName();
        }

        return null;
    }

    public static List<EnumListDto> getList(){
        List<EnumListDto> enumListDtos = new ArrayList<>();
        for(JobItemResultType resultType : JobItemResultType.values()){
            EnumListDto enumListDto = new EnumListDto();
            enumListDto.setValue(resultType.value);
            enumListDto.setName(resultType.name);
            enumListDtos.add(enumListDto);
        }
        return enumListDtos;
    }

    public static List<String> getNameList(){
        List<String> nameList = new ArrayList<>();
        for(JobItemResultType resultType : JobItemResultType.values()){
            nameList.add(resultType.getName());
        }
        return nameList;
    }

    public static Map<String, Integer> getValueMapByName(){
        Map<String, Integer> map = Maps.newHashMap();
        for(JobItemResultType resultType : JobItemResultType.values()){
            map.put(resultType.getName(), resultType.getValue());
        }
        return map;
    }

    public static Map<Integer, String> getNameMapByValue(){
        Map<Integer, String> map = Maps.newHashMap();
        for(JobItemResultType resultType : JobItemResultType.values()){
            map.put(resultType.getValue(), resultType.getName());
        }
        return map;
    }

    private int value;

    private String name;

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
