package cn.getech.ehm.task.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <pre>
 * 设备告警 返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-11-09
 */
@Data
@ApiModel(value = "WarnFirstPageDto", description = "告警首页")
public class WarnFirstPageDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "设备id")
    private String equipmentId;

    @ApiModelProperty(value = "设备名称")
    private String equipmentName;

    @ApiModelProperty(value = "设备编码")
    private String equipmentCode;

    @ApiModelProperty(value = "设备结构位置id")
    private String equipmentParentId;

    @ApiModelProperty(value = "设备结构位置名称")
    private String equipmentParentName;

    @ApiModelProperty(value = "设备结构位置名称")
    private String equipmentLocationName;

    @ApiModelProperty(value = "参数名称")
    private String parameterName;

    @ApiModelProperty(value = "设备类型id")
    private String categoryId;

    @ApiModelProperty(value = "设备类型名称")
    private String categoryName;

    @ApiModelProperty(value = "告警等级配置id")
    private String warnConfigId;

    @ApiModelProperty(value = "告警级别值")
    private String levelName;

    @ApiModelProperty(value = "颜色")
    private String color;

    @ApiModelProperty(value = "告警信息")
    private String warnInfo;

    @ApiModelProperty(value = "告警时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date warnTime;

    @ApiModelProperty(value = "图片id")
    private String picId;

    @ApiModelProperty(value = "图片url")
    private String picUrl;

}