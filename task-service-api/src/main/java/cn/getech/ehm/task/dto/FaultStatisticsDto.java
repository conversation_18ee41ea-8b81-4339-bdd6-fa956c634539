package cn.getech.ehm.task.dto;

import cn.getech.poros.framework.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 故障统计 返回数据模型
 *
 * <AUTHOR>
 * @date 2020-01-13
 */
@Data
@ApiModel(value = "FaultStatisticsDto", description = "故障统计返回数据模型")
public class FaultStatisticsDto {

    @ApiModelProperty(value = "全局id")
    private String id;

    @ApiModelProperty(value = "名称")
    @Excel(name="名称",cellType = Excel.ColumnType.STRING )
    private String name;

    @ApiModelProperty(value = "次数")
    @Excel(name="次数",cellType = Excel.ColumnType.STRING )
    private Integer count;

    private String maintCostTime;

    private String downCostTime;




}