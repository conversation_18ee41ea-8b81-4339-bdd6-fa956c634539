<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.task.mapper.TriggerTimeMapper">
    <resultMap id="ResultMap" type="cn.getech.ehm.task.dto.plan.TriggerTimeDto">
        <result column="id" property="id" />
        <result column="plan_id" property="planId" />
        <result column="plan_equipment_time_id" property="planEquipmentTimeId" />
        <result column="trigger_time" property="triggerTime" />
        <result column="plan_maint_time" property="planMaintTime" />
        <result column="equipment_id" property="equipmentId" />
        <result column="plan_day" property="planDay" />
        <result column="plan_time" property="planTime"/>
        <result column="maintainer_ids" property="maintainerIds" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="team_ids" property="teamIds" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="person_strategy" property="personStrategy" />
        <result column="tenant_id" property="tenantId" />
        <result column="festival_type" property="festivalType" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.IntegerArrayTypeHandler"/>

    </resultMap>
    <select id="canCreateTaskDtos" resultMap="ResultMap">
        SELECT triggerTime.id, triggerTime.plan_id, triggerTime.plan_equipment_time_id,triggerTime.trigger_time,
               triggerTime.plan_maint_time,triggerTime.equipment_id,rel.plan_day,rel.plan_time,rel.person_strategy,
               rel.maintainer_ids,rel.team_ids,triggerTime.tenant_id,plan.festival_type
        FROM plan_trigger_time triggerTime
        LEFT JOIN plan_equipment_time rel ON triggerTime.plan_equipment_time_id = rel.id
        LEFT JOIN maint_plan plan ON triggerTime.plan_id = plan.id
        WHERE triggerTime.status = 0
        AND triggerTime.trigger_time &lt;= #{expireTime}
    </select>
    <select id="getNextPlanDate" resultType="cn.getech.ehm.task.dto.plan.MaintPlanCountDto">
        SELECT plan_id planId, min(plan_maint_time) nextMaintTime
        FROM plan_trigger_time
        WHERE plan_maint_time &gt;= #{date}
        AND plan_id IN
        <foreach collection="planIds" open="(" close=")" separator="," item="planId" index="index">
            #{planId}
        </foreach>
        GROUP BY plan_id
    </select>
    <update id="changeStatus">
        UPDATE plan_trigger_time SET status = #{status}
        WHERE id IN
        <foreach collection="triggerTimeIds" item="triggerTimeId" open="(" separator="," close=")">
            #{triggerTimeId}
        </foreach>
    </update>
    <select id="getNormalListByTime" resultType="cn.getech.ehm.task.entity.PlanTriggerTime">
        SELECT plan_id,plan_maint_time FROM plan_trigger_time rel
        LEFT JOIN maint_plan plan ON rel.plan_id = plan.id
        WHERE plan.enabled = 1 AND rel.plan_maint_time BETWEEN #{beginTime} AND #{endTime}
    </select>
</mapper>
