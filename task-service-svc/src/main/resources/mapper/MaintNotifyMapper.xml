<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.task.mapper.MaintNotifyMapper">
    <update id="closedTaskNotify">
        UPDATE maint_notify SET enable = 0 WHERE id IN
        <foreach collection="ids" index="index" item="id" separator="," close=")" open="(">
            #{id}
        </foreach>
    </update>
    <select id="getPlanEnableNotifyBySourceId" resultType="cn.getech.ehm.task.entity.MaintNotify">
        SELECT * FROM maint_notify WHERE enable = 1 AND
        source_id IN
        <foreach collection="sourceIds" index="index" item="sourceId" separator="," close=")" open="(">
            #{sourceId}
        </foreach>
    </select>
</mapper>
