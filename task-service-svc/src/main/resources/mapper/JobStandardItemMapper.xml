<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.task.mapper.JobStandardItemMapper">
    <select id="getCountList" resultType="cn.getech.ehm.task.dto.job.ItemCountDto">
        SELECT item.job_standard_id standardId,sum(item.standard_time) standardTimeCount, sum(item.working_time) workingTimeCount
        FROM job_standard_item item
        WHERE item.job_standard_id IN
        <foreach collection="standardIds" open="(" close=")" separator="," item="standardId" index="index">
            #{standardId}
        </foreach>
        GROUP BY item.job_standard_id
    </select>
</mapper>
