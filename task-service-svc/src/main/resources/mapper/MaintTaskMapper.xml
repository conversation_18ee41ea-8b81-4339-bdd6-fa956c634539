<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.task.mapper.MaintTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="dtoResultMap" type="cn.getech.ehm.task.dto.task.info.MaintTaskDto">
        <result column="id" property="id" />
        <result column="code" property="code" />
        <result column="name" property="name" />
        <result column="job_type" property="jobType" />
        <result column="status" property="status" />
        <result column="type" property="type" />
        <result column="source_type" property="sourceType" />
        <result column="source_id" property="sourceId" />
        <result column="source_name" property="sourceName" />
        <result column="equipment_id" property="equipmentId" />
        <result column="urgency" property="urgency" />
        <result column="major" property="major" />
        <result column="influence" property="influence" />
        <result column="content" property="content" />
        <result column="plan_maint_time" property="planMaintTime" />
        <result column="staff_ids" property="staffIds" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="team_ids" property="teamIds"  jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="all_staff_ids" property="allStaffIds" />
        <result column="reviewer" property="reviewer" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="already_reviewer" property="alreadyReviewer"  jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="confirm" property="confirm" />
        <result column="dispatch_handler" property="dispatchHandler" />
        <result column="close_reason" property="closeReason" />
        <result column="process_instance_id" property="processInstanceId" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="task_deadline_date" property="taskDeadlineDate" />
        <result column="ec_error_num" property="ecErrorNum" />
        <result column="ec_error_result" property="ecErrorResult" />
        <result column="ec_error_deal_content" property="ecErrorDealContent" />
    </resultMap>

    <resultMap id="planTaskMap" type="cn.getech.ehm.task.dto.task.info.MaintTaskPlanDto">
        <result column="id" property="id" />
        <result column="code" property="code" />
        <result column="name" property="name" />
        <result column="plan_maint_time" property="planMaintTime" />
        <result column="begin_maint_time" property="actualMaintTime" />
        <result column="staff_ids" property="staffIds" />
        <result column="team_ids" property="teamIds"  />
        <result column="all_staff_ids" property="allStaffIds"  />
    </resultMap>

    <resultMap id="statisticsMap" type="cn.getech.ehm.task.dto.TaskStatisticsDto">
        <result column="id" property="taskId" />
        <result column="equipment_id" property="taskEquipmentId" />
        <result column="code" property="taskCode" />
        <result column="name" property="taskName" />
        <result column="status" property="taskStatus" />
        <result column="type" property="taskType" />
        <result column="all_staff_ids" property="allStaffIds"/>
        <result column="begin_downtime" property="beginDowntime" />
        <result column="end_downtime" property="endDowntime" />
        <result column="begin_maint_time" property="beginMaintTime" />
        <result column="end_maint_time" property="endMaintTime" />
        <result column="grade" property="grade" />
        <result column="update_time" property="taskUpdateTime" />
        <result column="actual_qty" property="partActualQty" />
        <result column="create_time" property="taskCreateTime" />
        <result column="task_deadline_date" property="taskDeadlineDate" />
        <result column="check_accept_result" property="checkAcceptResult" />
    </resultMap>

    <resultMap id="personalPerformanceMap" type="cn.getech.ehm.task.dto.task.performance.PersonalTaskInfoDto">
<!--        <result column="uid" property="uid" />-->
        <result column="type" property="type" />
        <result column="grade" property="grade" />
        <result column="begin_maint_time" property="beginMaintTime" />
        <result column="end_maint_time" property="endMaintTime" />
        <result column="all_staff_ids" property="allStaffIds" />
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="appFirstPageResultMap" type="cn.getech.ehm.task.dto.task.info.MaintTaskDto">
        <result column="id" property="id" />
        <result column="code" property="code" />
        <result column="name" property="name" />
        <result column="job_type" property="jobType" />
        <result column="status" property="status" />
        <result column="type" property="type" />
        <result column="source_type" property="sourceType" />
        <result column="equipment_id" property="equipmentId" />
        <result column="urgency" property="urgency" />
        <result column="major" property="major" />
        <result column="content" property="content" />
        <result column="influence" property="influence" />
        <result column="plan_maint_time" property="planMaintTime" />
        <result column="staff_ids" property="staffIds" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="team_ids" property="teamIds"  jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="process_instance_id" property="processInstanceId" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>
    <resultMap id="notifyTaskMap" type="cn.getech.ehm.task.dto.task.notify.MaintTaskNotifyDto">
        <result column="id" property="id" />
        <result column="code" property="code" />
        <result column="name" property="name" />
        <result column="plan_maint_time" property="planMaintTime" />
        <result column="status" property="status" />
        <result column="all_staff_ids" property="allStaffIds" />
        <result column="tenant_id" property="tenantId" />
        <result column="notifyType" property="notifyType" />
        <result column="notify_methods" property="notifyMethods" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.IntegerArrayTypeHandler"/>
        <result column="notify_objects" property="notifyObjects" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.IntegerArrayTypeHandler"/>
        <result column="custom_uids" property="customUids" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="custom_roles" property="customRoles" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="equipment_manager" property="equipmentManager" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.IntegerArrayTypeHandler"/>
        <result column="team_ids" property="teamIds" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="maintainer_ids" property="maintainerIds" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="notifyId" property="notifyId" />
        <result column="equipment_id" property="equipmentId" />
        <result column="task_deadline_date" property="taskDeadlineDate" />
        <result column="handler" property="handler" />
    </resultMap>

    <sql id="statistics_list">
        maint.id,
        maint.code,
        maint.name,
        maint.equipment_id,
        maint.type,
        maint.status,
        maint.begin_downtime,
        maint.end_downtime,
        maint.begin_maint_time,
        maint.end_maint_time,
        ifnull(maint.grade,-1) grade,
        maint.update_time,
        maint.create_time,
        maint.task_deadline_date,
        maint.check_accept_result,
        maint.handler,
        maint.send_task_date,
        maint.rec_task_date,
        maint.end_maint_time,
        maint.working_time
    </sql>

    <sql id="personalPerformance_list">
        mt.all_staff_ids, mt.type, ifnull(mt.grade,0) grade, mt.begin_maint_time, mt.end_maint_time,mt.handler,mt.check_accept_result
    </sql>

    <select id="getMaxCode" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT max(code) FROM maint_task WHERE code LIKE concat(#{prex}, "%");
    </select>

    <select id="getDtoById" parameterType="java.lang.String" resultType="cn.getech.ehm.task.dto.task.info.MaintTaskDto">
        SELECT maint.*, di.defect_status as defectStatus
        FROM maint_task maint
        left join defect_info di on maint.defect_id = di.id
        WHERE maint.id = #{id}
    </select>
    <select id="getTodayTaskQty" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM maint_task
        WHERE status != -1
        AND plan_maint_time BETWEEN DATE_FORMAT(NOW(),'%Y-%m-%d 00:00:00')
        AND DATE_FORMAT(NOW(),'%Y-%m-%d 23:59:59')
    </select>

    <select id="getOverdueTaskQty" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM maint_task
        WHERE status IN(0, 1, 2)
        AND plan_maint_time <![CDATA[ < ]]> DATE_FORMAT(NOW(),'%Y-%m-%d 00:00:00')
    </select>

    <select id="getTodayCompletedTaskQty" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM maint_task
        WHERE status IN (8, 9)
        AND end_maint_time
        BETWEEN DATE_FORMAT(NOW(),'%Y-%m-%d 00:00:00')
        AND DATE_FORMAT(NOW(),'%Y-%m-%d 23:59:59')
    </select>

    <select id="getTaskCompleteDynamic"
            resultType="cn.getech.ehm.task.dto.task.info.TaskCompleteDynamicDto">
        SELECT id, `name`, code, `type`, handler finisher,
        CASE WHEN TIMESTAMPDIFF(MINUTE, end_maint_time,now()) <![CDATA[ < ]]> 60
        THEN CONCAT(TIMESTAMPDIFF(MINUTE, end_maint_time,now()), '分钟前')
        WHEN TIMESTAMPDIFF(HOUR, end_maint_time,now()) <![CDATA[ < ]]> 24
        THEN CONCAT(TIMESTAMPDIFF(HOUR, end_maint_time,now()), '小时前')
        ELSE CONCAT(datediff(now(), end_maint_time), '天前')
        END intervalCurrentTime
        FROM maint_task WHERE status IN (8,9)
        ORDER BY end_maint_time DESC LIMIT 5
    </select>

    <select id="getTaskOverview" resultType="cn.getech.ehm.task.dto.task.info.MaintTaskDto">
        SELECT id, `name`, code, `type` FROM maint_task
        WHERE `status` = #{status}
        AND (create_time BETWEEN DATE_FORMAT(NOW(),'%Y-%m-%d 00:00:00') AND DATE_FORMAT(NOW(),'%Y-%m-%d 23:59:59')
        OR plan_maint_time <![CDATA[ < ]]> DATE_FORMAT(NOW(),'%Y-%m-%d 23:59:59')
        OR plan_maint_time IS NULL
        )
    </select>

    <select id="getTaskEvaluated" resultType="java.util.Map">
        SELECT grade, COUNT(grade) qty FROM maint_task
        WHERE `status` = 4
        AND update_time
        BETWEEN DATE_FORMAT(NOW(),'%Y-%m-%d 00:00:00') AND DATE_FORMAT(NOW(),'%Y-%m-%d 23:59:59')
        GrOUP BY grade
    </select>

    <select id="getTaskTodoList" resultType="cn.getech.ehm.task.dto.task.info.MaintTaskDto">
        SELECT id, `name`, `status`, code, `type` FROM maint_task WHERE `status` IN(0, 1, 2, 3)
    </select>

    <select id="getStatistics" resultMap="statisticsMap">
        SELECT
            <include refid="statistics_list"/>
            <if test="reqDto.type == 7">
                ,part.part_id, part.actual_qty
            </if>
        FROM maint_task maint
        <if test="reqDto.type == 7">
            INNER JOIN maint_task_part part ON maint.id = part.task_id
        </if>
        <where>
            maint.deleted = 0
            <choose>
                <when test="reqDto.type == 0 or reqDto.type == 1">
                    AND maint.type = 1
                    AND maint.status != -1
                    AND maint.create_time BETWEEN #{reqDto.beginTime} AND #{reqDto.endTime}
                </when>
                <when test="reqDto.type == 2">
                    AND maint.type = 1
                    AND maint.status != -1
                    AND maint.create_time BETWEEN #{reqDto.beginTime} AND #{reqDto.endTime}
                </when>
                <when test="reqDto.type == 3">
                    AND maint.type = 1
                    AND maint.status != -1
                    AND maint.end_downtime &gt;= #{reqDto.beginTime}
                    AND maint.begin_downtime &lt;= #{reqDto.endTime}
                </when>
                <when test="reqDto.type == 4">
                    AND maint.status in (8,9)
                    AND maint.type = 1
                    <if test="null != reqDto.beginTime and null != reqDto.endTime">
                        AND maint.begin_maint_time BETWEEN #{reqDto.beginTime} AND #{reqDto.endTime}
                    </if>
                </when>
                <when test="reqDto.type == 5">
                    <if test="null != reqDto.beginTime and null != reqDto.endTime">
                        AND maint.create_time BETWEEN #{reqDto.beginTime} AND #{reqDto.endTime}
                    </if>
                </when>
                <when test="reqDto.type == 6">
                    AND maint.status in (8,9)
                    <if test="null != reqDto.beginTime and null != reqDto.endTime">
                        AND maint.begin_maint_time BETWEEN #{reqDto.beginTime} AND #{reqDto.endTime}
                    </if>
                </when>
                <when test="reqDto.type == 7">
                    AND maint.status in (8,9)
                    AND part.actual_qty > 0
                    <if test="null != reqDto.beginTime and null != reqDto.endTime">
                        AND maint.begin_maint_time BETWEEN #{reqDto.beginTime} AND #{reqDto.endTime}
                    </if>
                </when>
                <when test="reqDto.type == 8">
                    AND maint.status = 9
                    <if test="null != reqDto.beginTime and null != reqDto.endTime">
                        AND maint.begin_maint_time BETWEEN #{reqDto.beginTime} AND #{reqDto.endTime}
                    </if>
                </when>
                <when test="reqDto.type == 9">
                    <if test="null != reqDto.beginTime and null != reqDto.endTime">
                        AND maint.create_time BETWEEN #{reqDto.beginTime} AND #{reqDto.endTime}
                    </if>
                </when>
                <when test="reqDto.type == 15">
                    <if test="null != reqDto.beginTime and null != reqDto.endTime">
                        AND maint.create_time BETWEEN #{reqDto.beginTime} AND #{reqDto.endTime}
                    </if>
                </when>
                <when test="reqDto.type == 16">
                    <if test="null != reqDto.beginTime and null != reqDto.endTime">
                        AND maint.create_time BETWEEN #{reqDto.beginTime} AND #{reqDto.endTime}
                    </if>
                </when>
                <when test="reqDto.type == 17">
                    <if test="null != reqDto.beginTime and null != reqDto.endTime">
                        AND maint.create_time BETWEEN #{reqDto.beginTime} AND #{reqDto.endTime}
                    </if>
                </when>
                <when test="reqDto.type == 18">
                    AND maint.type = 1
                    AND maint.status != -1
                    AND maint.status in (8,9)
                    AND maint.create_time BETWEEN #{reqDto.beginTime} AND #{reqDto.endTime}
                </when>
                <otherwise></otherwise>
            </choose>
            <if test="null != reqDto.equipmentIds and reqDto.equipmentIds.length > 0">
                AND maint.equipment_id IN
                <foreach collection="reqDto.equipmentIds" index="index" item="equipmentId" open="(" close=")" separator=",">
                    #{equipmentId}
                </foreach>
            </if>
        </where>
        <if test="reqDto.type == 7">
            ORDER BY part.id ASC
        </if>
    </select>

    <select id="getPersonalPerformance" resultType="cn.getech.ehm.task.dto.task.performance.PersonalTaskInfoDto">
        select
        <include refid="personalPerformance_list"/>
        from maint_task as  mt
        where (mt.status = 9 or mt.status = 10)
          and mt.handler is not null
            <if test="beginTime != null and endTime != null">
              and mt.create_time >= #{beginTime}
              and #{endTime} >= mt.create_time
            </if>
            <if test="handlerIds !=null and handlerIds.size()>0">
                and mt.handler in
                <foreach collection="handlerIds" index="index" item="handler" open="(" close=")" separator=",">
                     #{handler}
                </foreach>

            </if>
    </select>

    <select id="typeReport" resultType="cn.getech.ehm.task.dto.NameNode">
        SELECT  (CASE t.type
        WHEN 1 THEN
        '故障单'
        WHEN 2 THEN
        '维保单'
        WHEN 3 THEN
        '缺陷单'
        WHEN 4 THEN
        '维保单'
        END ) AS nameKey,count( * ) AS intValue,
        (CASE t.type WHEN 1 THEN 1 WHEN 2 THEN 2 WHEN 3 THEN 3 WHEN 4 THEN 2 END) AS source_type
        FROM maint_task t
        WHERE 1=1 and t.deleted=0
        <if test="null != param.beginTime and null != param.endTime">
            and t.create_time BETWEEN #{param.beginTime} AND #{param.endTime}
        </if>
        <if test="param.equipmentIds !=null and param.equipmentIds.size()>0">
            and t.equipment_id in
            <foreach collection="param.equipmentIds" index="index" item="equipment" open="(" close=")" separator=",">
                #{equipment}
            </foreach>
        </if>
        GROUP BY t.type
        order by intValue desc
    </select>

    <select id="statusReport" resultType="cn.getech.ehm.task.dto.NameNode">
        SELECT (CASE t.`status`
        WHEN 0 THEN
        '待派单'
        WHEN 15 THEN
        '待审批'
        WHEN 1 THEN
        '待接单'
        WHEN 2 THEN
        '待执行'
        WHEN 3 THEN
        '开始确认'
        WHEN 4 THEN
        '安全确认中'
        WHEN 5 THEN
        '已确认'
        WHEN 6 THEN
        '执行中'
        WHEN 7 THEN
        '执行中已挂起'
        WHEN 8 THEN
        '待验收'
        WHEN 9 THEN
        '已关闭'
        WHEN 10 THEN
        '异常关闭'
        WHEN 41 THEN
        '挂起审批中'
        WHEN 42 THEN
        '执行中已挂起'
        WHEN 30 THEN
        '待提交'
        WHEN 43 THEN
        '执行中转缺陷待审批'
        WHEN 44 THEN
        '验收转缺陷待审批'
        ELSE
        '其他'
        END
        ) AS nameKey, count( * ) AS intValue
        FROM maint_task t
        WHERE 1=1 and t.deleted=0
        <if test="null != param.beginTime and null != param.endTime">
            and t.create_time BETWEEN #{param.beginTime} AND #{param.endTime}
        </if>
        <if test="param.equipmentIds !=null and param.equipmentIds.size()>0">
            and t.equipment_id in
            <foreach collection="param.equipmentIds" index="index" item="equipment" open="(" close=")" separator=",">
                #{equipment}
            </foreach>
        </if>
        GROUP BY t.status
        order by intValue desc
    </select>

    <select id="typeMonthlyReport" resultType="cn.getech.ehm.task.dto.TypeMonthNode">
        SELECT ( CASE t.type
        WHEN 1 THEN
        '故障单'
        WHEN 2 THEN
        '维保单'
        WHEN 3 THEN
        '缺陷单'
        WHEN 4 THEN
        '点检单'
        END
        ) AS nameKey,DATE_FORMAT( t.create_time, '%Y-%m' ) as monthKey,count( * ) AS intValue
        FROM maint_task t
        WHERE 1=1 and t.deleted=0
        <if test="null != param.beginTime and null != param.endTime">
         and t.create_time BETWEEN #{param.beginTime} AND #{param.endTime}
        </if>
        <if test="param.equipmentIds !=null and param.equipmentIds.size()>0">
            and t.equipment_id in
            <foreach collection="param.equipmentIds" index="index" item="equipment" open="(" close=")" separator=",">
                #{equipment}
            </foreach>
        </if>
        GROUP BY t.type,DATE_FORMAT( t.create_time, '%Y-%m' )
        ORDER BY monthKey asc
    </select>

    <select id="maintTimeMonthlyReport" resultType="cn.getech.ehm.task.dto.MonthNode">
        SELECT  DATE_FORMAT( d.begin_maint_time, '%Y-%m' ) as monthKey,sum(TIMESTAMPDIFF(HOUR,d.begin_maint_time,d.end_maint_time)) as intValue
        FROM maint_task t,maint_task_detail d
        WHERE 1=1 and t.deleted=0
        and t.id = d.task_id and t.status in(8,9) and d.begin_maint_time is not null and d.end_maint_time is not null
        <if test="null != param.beginTime and null != param.endTime">
            AND DATE_FORMAT( d.begin_maint_time, '%Y-%m' ) BETWEEN DATE_FORMAT(#{param.beginTime}, '%Y-%m' ) AND DATE_FORMAT(#{param.endTime}, '%Y-%m' )
        </if>
        GROUP BY DATE_FORMAT( d.begin_maint_time, '%Y-%m' )
        ORDER BY monthKey asc
    </select>

    <select id="faultEquipmentStatistics" resultType="cn.getech.ehm.task.dto.FaultStatisticsDto">
        SELECT equipment_id AS id, COUNT(1) AS count FROM maint_task
        WHERE type = 1 AND `status` IN (8, 9)
        AND create_time &gt;= #{param.startTime} AND create_time &lt;= #{param.endTime}
        <if test="param.equipmentIdList !=null and param.equipmentIdList.size()>0">
            AND equipment_id IN
            <foreach collection="param.equipmentIdList" index="index" item="equipmentId" open="(" close=")" separator=",">
                #{equipmentId}
            </foreach>

        </if>
        GROUP BY equipment_id
        ORDER BY count DESC
        LIMIT #{param.topCount}
    </select>

    <select id="allFaultEquipmentStatistics" resultType="cn.getech.ehm.task.dto.FaultStatisticsDto">
        SELECT equipment_id AS id, COUNT(1) AS count FROM maint_task
        WHERE type = 1 AND `status`IN (8, 9)
        AND create_time &gt;= #{param.startTime} AND create_time &lt;= #{param.endTime}
        <if test="param.equipmentIdList !=null and param.equipmentIdList.size()>0">
            AND equipment_id IN
            <foreach collection="param.equipmentIdList" index="index" item="equipmentId" open="(" close=")" separator=",">
                #{equipmentId}
            </foreach>

        </if>
        GROUP BY equipment_id
    </select>
    <select id="getLastPlanDate" resultType="cn.getech.ehm.task.dto.plan.MaintPlanCountDto">
        SELECT source_id planId, max(end_maint_time) lastMaintTime
        FROM maint_task
        WHERE deleted = 0 AND (status = 8 OR status = 9) AND end_maint_time IS NOT NULL
        AND source_id IN
        <foreach collection="planIds" open="(" close=")" separator="," item="planId" index="index">
            #{planId}
        </foreach>
        GROUP BY source_id
    </select>
    <select id="getTaskCount" resultType="cn.getech.ehm.task.dto.plan.MaintPlanCountDto">
        SELECT source_id planId, count(source_id) taskCount
        FROM maint_task
        WHERE deleted = 0 AND status != -1
        AND source_id IN
        <foreach collection="planIds" open="(" close=")" separator="," item="planId" index="index">
            #{planId}
        </foreach>
        GROUP BY source_id
    </select>
    <select id="getStatus" resultType="java.lang.Integer">
        SELECT status FROM maint_task WHERE id = #{id}
    </select>

    <select id="recTaskReport" resultType="cn.getech.ehm.task.dto.NameNode">
        select t.handler as nameKey, count(*) as intValue from maint_task t
        where
        <if test="null != param.beginTime and null != param.endTime">
            DATE_FORMAT( t.rec_task_date, '%Y-%m-%d %H:%i:%s' )
               BETWEEN DATE_FORMAT(#{param.beginTime}, '%Y-%m-%d %H:%i:%s' ) AND DATE_FORMAT(#{param.endTime}, '%Y-%m-%d %H:%i:%s' )
        </if>
        <if test="null != param.keyword and '' != param.keyword">
            AND t.handler LIKE CONCAT('%',#{param.keyword}, '%')
        </if>
        <if test="null != param.handler and '' != param.handler">
            AND t.handler = #{param.handler}
        </if>
        group by t.handler order by intValue desc;
    </select>

    <select id="recTaskCompleteReport" resultType="cn.getech.ehm.task.dto.NameNode">
        select t.handler as nameKey, count(*) as intValue from maint_task t
        where status in (8,9,10)
        <if test="null != param.beginTime and null != param.endTime">
            and DATE_FORMAT( t.rec_task_date, '%Y-%m-%d %H:%i:%s' )
            BETWEEN DATE_FORMAT(#{param.beginTime}, '%Y-%m-%d %H:%i:%s' ) AND DATE_FORMAT(#{param.endTime}, '%Y-%m-%d %H:%i:%s' )
        </if>
        <if test="null != param.keyword and '' != param.keyword">
            AND t.handler LIKE CONCAT('%',#{param.keyword}, '%')
        </if>
        <if test="null != param.handler and '' != param.handler">
            AND t.handler = #{param.handler}
        </if>
        group by t.handler order by intValue desc;
    </select>
    <select id="findTaskByEquipmentName" resultType="cn.getech.ehm.task.entity.MaintTask">
        select t.* from maint_task t
        where t.deleted = 0
        <if test="null != param.startDate and null != param.endDate">
            and DATE_FORMAT( t.create_time, '%Y-%m-%d %H:%i:%s' )
            BETWEEN DATE_FORMAT(#{param.startDate}, '%Y-%m-%d %H:%i:%s' ) AND DATE_FORMAT(#{param.endDate}, '%Y-%m-%d %H:%i:%s' )
        </if>
        <if test="param.equipmentIds !=null and param.equipmentIds.size()>0">
            AND t.equipment_id IN
            <foreach collection="param.equipmentIds" index="index" item="equipmentId" open="(" close=")" separator=",">
                #{equipmentId}
            </foreach>

        </if>
    </select>
    <select id="getListByWarnId" resultType="cn.getech.ehm.task.dto.task.info.WarnTaskPageDto">
        SELECT task.id,task.code,task.name,task.status,task.handler,repair.create_time,
               repair.source_id equipmentWarnId,task.process_instance_id
        FROM manual_repair repair
        LEFT JOIN maint_task task ON repair.id = task.source_id
        WHERE repair.deleted = 0
        AND repair.source_id = #{param.equipmentWarnId}
        ORDER BY repair.create_time DESC
    </select>

    <select id="getOverTimeTaskList" resultType="cn.getech.ehm.task.dto.task.info.MaintTaskDto">
        SELECT
        mt.*
        FROM
        maint_task mt
        LEFT JOIN act_overtime_info aoi ON mt.process_instance_id = aoi.proc_ins_id
        WHERE
        aoi.task_end_time &lt; NOW()
        AND aoi.task_real_time IS NULL
        <if test="param.equipmentIds !=null and param.equipmentIds.size()>0">
            AND mt.equipment_id IN
            <foreach collection="param.equipmentIds" index="index" item="equipmentId" open="(" close=")" separator=",">
                #{equipmentId}
            </foreach>
        </if>
        <if test="null != param.beginCreateTime and null != param.endCreateTime">
            and DATE_FORMAT( t.create_time, '%Y-%m-%d %H:%i:%s' )
            BETWEEN DATE_FORMAT(#{param.beginCreateTime}, '%Y-%m-%d %H:%i:%s' ) AND DATE_FORMAT(#{param.endCreateTime}, '%Y-%m-%d %H:%i:%s' )
        </if>
    </select>
    <select id="getNotifyTask" resultMap="notifyTaskMap">
        SELECT task.id,task.code,task.name,task.plan_maint_time,task.status,task.all_staff_ids,task.tenant_id,
               notify.type notifyType,notify.notify_methods,notify.notify_objects,notify.custom_uids,notify.custom_roles,
               notify.id notifyId,notify.maintainer_ids,notify.team_ids,notify.equipment_manager,task.equipment_id,
               task.task_deadline_date,task.handler
        FROM maint_notify notify
        LEFT JOIN maint_task task ON notify.source_id = task.id
        <where>
            notify.source_type = 2 AND notify.type = #{notifyType} AND task.deleted = 0 AND notify.enable = 1
            AND notify.notify_time &lt;= #{date}
        </where>
    </select>
    <select id="getOverdueTaskIds" resultMap="notifyTaskMap">
        SELECT id, tenant_Id FROM maint_task WHERE status NOT IN (8,9,10) AND overdue_handling_method = 2 AND task_deadline_date &lt;= now()
    </select>
</mapper>
