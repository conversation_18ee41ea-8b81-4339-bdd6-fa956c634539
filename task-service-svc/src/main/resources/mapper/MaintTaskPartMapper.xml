<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.task.mapper.MaintTaskPartMapper">
    <select id="getListByTaskId" parameterType="java.lang.String" resultType="cn.getech.ehm.task.dto.task.part.TaskPartDto">
        SELECT rel.id,rel.task_id taskId, rel.part_id partId,rel.plan_qty planQty, rel.actual_qty actualQty
        FROM maint_task_part rel
        WHERE rel.task_id = #{taskId}
        ORDER BY rel.create_time ASC
    </select>

    <select id="getTaskPartRelPageList" resultType="cn.getech.ehm.task.dto.task.info.TaskPartRelReportDto">
        SELECT e.id as equipmentId,	e.`code` as equipmentCode,e.`name` AS equipmentName,
        el.name AS equipmentLocation,
        ec.name AS equipmentCategory,
        sum( tpr.actual_qty ) AS totalActualQty, sum( tpr.actual_qty * p.price ) AS totalPrice
        FROM equipment_info e, maint_task t, maint_task_repair d, maint_task_part tpr, part_info p, equipment_location el, equipment_category ec
        WHERE
            e.deleted=0
            AND e.type = 1
            AND el.id=e.parent_id
            AND ec.id=e.category_id
            and t.equipment_id = e.id
            AND t.id = d.task_id
            AND t.id = tpr.task_id
            AND p.id = tpr.part_id
            AND ( t.`status` = 3 OR t.`status` = 4 )
            and tpr.actual_qty > 0
            <if test="null != param.keyword and '' != param.keyword">
                AND ( e.NAME LIKE concat('%', #{param.keyword} , '%') OR e.CODE LIKE concat('%', #{param.keyword} , '%') )
            </if>
            <if test="null != param.beginTime and null != param.endTime">
                AND DATE_FORMAT( t.begin_maint_time, '%Y-%m' ) BETWEEN DATE_FORMAT(#{param.beginTime}, '%Y-%m' ) AND DATE_FORMAT(#{param.endTime}, '%Y-%m' )
            </if>

            <if test="null != param.innerEquipmentCode and '' != param.innerEquipmentCode">
                AND e.`code` like CONCAT('%', #{param.innerEquipmentCode},'%')
            </if>
            <if test="null != param.innerEquipmentName and '' != param.innerEquipmentName">
                AND e.`name` like CONCAT('%', #{param.innerEquipmentName},'%')
            </if>
            <!--<if test="null != param.innerEquipmentLocation and '' != param.innerEquipmentLocation ">
                AND FIND_IN_SET(e.location_id, #{param.innerEquipmentLocation})
            </if>
            <if test="null != param.innerEquipmentCategory and '' != param.innerEquipmentCategory ">
                AND FIND_IN_SET(e.category_id, #{param.innerEquipmentCategory})
            </if>-->
            <if test="null != param.equipmentLocation and param.equipmentLocation.length != 0 ">
                AND
                <foreach collection="param.equipmentLocation" open="(" close=")" separator="or" item="locationId" index="index">
                    el.layer_code LIKE concat('%',#{locationId},'%')
                </foreach>
            </if>
            <if test="null != param.equipmentCategory and param.equipmentCategory.length != 0 ">
                AND
                <foreach collection="param.equipmentCategory" open="(" close=")" separator="or" item="categoryId" index="index">
                    el.layer_code LIKE concat('%',#{categoryId},'%')
                </foreach>
            </if>

        GROUP BY e.id
            having 1=1
            <if test="null != param.innerStartTotalActualQty and null != param.innerEndTotalActualQty">
                AND totalActualQty BETWEEN #{param.innerStartTotalActualQty} AND #{param.innerEndTotalActualQty}
            </if>
            <if test="null != param.innerStartTotalPrice and null != param.innerEndTotalPrice">
                AND totalPrice BETWEEN #{param.innerStartTotalPrice} AND #{param.innerEndTotalPrice}
            </if>
        <if test="null != param.sortValue and '' != param.sortValue">
            ${param.sortValue}
        </if>
    </select>

    <select id="getTaskPartRelDetailPageList" resultType="cn.getech.ehm.task.dto.task.info.TaskPartRelReportDto">
        select p.`code` as partCode,p.`name` as partName,
        (select name from part_category pc WHERE pc.id=part_category_id limit 1) AS partCategory,
        sum( tpr.actual_qty ) AS totalActualQty, sum( tpr.actual_qty * p.price ) AS totalPrice
        from maint_task t,maint_task_repair d,maint_task_part tpr,part_info p
        where t.equipment_id = #{param.equipmentId}
        AND t.id = d.task_id
        AND t.id = tpr.task_id
        AND p.id = tpr.part_id
        AND ( t.`status` = 3 OR t.`status` = 4 )
        and tpr.actual_qty > 0
        <if test="null != param.beginTime and null != param.endTime">
            AND DATE_FORMAT( t.begin_maint_time, '%Y-%m' ) BETWEEN DATE_FORMAT(#{param.beginTime}, '%Y-%m' ) AND DATE_FORMAT(#{param.endTime}, '%Y-%m' )
        </if>
        GROUP BY p.id
        order by p.id desc
    </select>

    <select id="checkPartUsed" parameterType="java.util.Arrays" resultType="java.lang.String">
        SELECT part_id FROM maint_task_part
        WHERE part_id IN
        <foreach collection="partIds" index="index" item="partId" open="(" close=")" separator=",">
            #{partId}
        </foreach>
    </select>
</mapper>
