<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.task.mapper.MaintTaskHistoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.getech.ehm.task.entity.MaintTaskHistory">
    <result column="id" property="id" />
    <result column="create_by" property="createBy" />
    <result column="update_by" property="updateBy" />
    <result column="create_time" property="createTime" />
    <result column="update_time" property="updateTime" />
    <result column="remark" property="remark" />
        <result column="deleted" property="deleted" />
        <result column="tenant_id" property="tenantId" />
        <result column="process_instance_id" property="processInstanceId" />
        <result column="activiti_id" property="activitiId" />
        <result column="activiti_name" property="activitiName" />
        <result column="task_id" property="taskId" />
        <result column="assignee_name" property="assigneeName" />
        <result column="comment" property="comment" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="operator" property="operator" />
        <result column="read_status" property="readStatus" />
        <result column="task_status" property="taskStatus" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        update_by,
        create_time,
        update_time,
        remark,
        deleted, tenant_id, process_instance_id, activiti_id, activiti_name, task_id, assignee_name, comment, start_time, end_time, operator, read_status, task_status
    </sql>

</mapper>
