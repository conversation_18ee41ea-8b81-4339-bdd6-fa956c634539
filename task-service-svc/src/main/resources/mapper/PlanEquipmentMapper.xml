<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.task.mapper.PlanEquipmentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.getech.ehm.task.dto.plan.PlanEquipmentDto">
        <result column="id" property="id" />
        <result column="plan_id" property="planId" />
        <result column="info_category_id" property="infoCategoryId" />
        <result column="info_location_id" property="infoLocationId"/>
        <result column="equipment_ids" property="equipmentIds" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="maintainer_ids" property="maintainerIds" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="team_ids" property="teamIds" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="standard_id" property="standardId" />
        <result column="standardName" property="standardName" />
        <result column="person_strategy" property="personStrategy" />
        <result column="plan_maint_time" property="planMaintTime" />
        <result column="fault_phenomenon_ids" property="faultPhenomenonIds" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="fault_phenomenon_remark" property="faultPhenomenonRemark" />
        <result column="remark" property="remark" />
    </resultMap>
    <resultMap id="ResultMap" type="cn.getech.ehm.task.dto.plan.SynPlanEquipmentDto">
        <result column="id" property="id" />
        <result column="plan_id" property="planId" />
        <result column="info_category_id" property="infoCategoryId" />
        <result column="info_location_id" property="infoLocationId"/>
        <result column="equipment_ids" property="equipmentIds" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="maintainer_ids" property="maintainerIds" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="team_ids" property="teamIds" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="standard_id" property="standardId" />
        <result column="standardName" property="standardName" />
        <result column="person_strategy" property="personStrategy" />
        <result column="plan_maint_time" property="planMaintTime" />
        <result column="fault_phenomenon_ids" property="faultPhenomenonIds" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="fault_phenomenon_remark" property="faultPhenomenonRemark" />
    </resultMap>
    <select id="getListByPlanIds" resultMap="ResultMap">
        SELECT rel.id,rel.plan_id, rel.info_category_id, rel.info_location_id, rel.equipment_ids,
            rel.person_strategy,rel.maintainer_ids,rel.team_ids,rel.standard_id, standard.name standardName,
            rel.plan_maint_time,rel.fault_phenomenon_ids, rel.fault_phenomenon_remark,rel.remark
        FROM plan_equipment rel
        LEFT JOIN job_standard standard ON rel.standard_id = standard.id
        WHERE rel.plan_id in
        <foreach collection="planIds" index="index" item="planId" open="(" close=")" separator=",">
            #{planId}
        </foreach>
    </select>
    <select id="getListByPlanId" resultMap="BaseResultMap">
        SELECT rel.id,rel.plan_id, rel.info_category_id, rel.info_location_id, rel.equipment_ids,
        rel.person_strategy,rel.maintainer_ids,rel.team_ids,rel.standard_id, standard.name standardName,
        rel.plan_maint_time,rel.fault_phenomenon_ids, rel.fault_phenomenon_remark,rel.remark
        FROM plan_equipment rel
        LEFT JOIN job_standard standard ON rel.standard_id = standard.id
        WHERE rel.plan_id = #{planId}
    </select>
    <select id="getCbmPlanIds" resultType="java.lang.String">
        SELECT DISTINCT plan.id FROM plan_equipment pe
        LEFT JOIN maint_plan plan ON pe.plan_id = plan.id
        LEFT JOIN maint_plan_cbm cbm ON plan.id = cbm.plan_id
        WHERE pe.info_category_id = #{categoryId} AND plan.job_type = #{jobType}
        AND cbm.category_parameter_id = #{categoryParamId}
        AND (pe.info_location_id IS NULL OR pe.info_location_id = '' OR pe.info_location_id IN
            <foreach collection="locationIds" index="index" item="locationId" open="(" close=")" separator=",">
                #{locationId}
            </foreach>
            )
        AND (pe.equipment_ids IS NULL OR pe.equipment_ids = '' OR pe.equipment_ids LIKE CONCAT("%",#{equipmentId}, "%"))
    </select>
</mapper>
