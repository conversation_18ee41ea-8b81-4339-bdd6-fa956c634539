<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.task.mapper.TaskNotifyConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.getech.ehm.task.entity.TaskNotifyConfig">
    <result column="id" property="id" />
    <result column="create_by" property="createBy" />
    <result column="update_by" property="updateBy" />
    <result column="create_time" property="createTime" />
    <result column="update_time" property="updateTime" />
    <result column="remark" property="remark" />
        <result column="deleted" property="deleted" />
        <result column="tenant_id" property="tenantId" />
        <result column="task_type" property="taskType" />
        <result column="task_status_type" property="taskStatusType" />
        <result column="notify_type" property="notifyType" />
        <result column="begin_time" property="beginTime" />
        <result column="end_time" property="endTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        update_by,
        create_time,
        update_time,
        remark,
        deleted, tenant_id, task_type, task_status_type, notify_type, begin_time, end_time
    </sql>

</mapper>
