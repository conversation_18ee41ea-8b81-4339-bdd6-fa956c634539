<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.task.mapper.MaintTaskRepairMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.getech.ehm.task.dto.task.repair.MaintTaskFaultDto">
        <result column="id" property="id" />
        <result column="taskId" property="taskId" />
        <result column="begin_downtime" property="beginDowntime" />
        <result column="end_downtime" property="endDowntime" />
        <result column="begin_maint_time" property="beginMaintTime" />
        <result column="end_maint_time" property="endMaintTime" />
        <result column="working_time" property="workingTime" />
        <result column="media_ids" property="mediaIds" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="fault_phenomenon_ids" property="faultPhenomenonIds" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="fault_phenomenon_remark" property="faultPhenomenonRemark" />
        <result column="fault_phenomenon_ids" property="faultPhenomenonIds" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="fault_phenomenon_remark" property="faultPhenomenonRemark" />
        <result column="fault_phenomenon_ids" property="faultPhenomenonIds" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="fault_phenomenon_remark" property="faultPhenomenonRemark" />
        <result column="fault_structure_ids" property="faultStructureIds" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="fault_influences" property="faultInfluences" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
    </resultMap>
    <select id="getFaultDtoById" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT repair.*,repair.id,maint.id taskId,maint.begin_downtime,maint.end_downtime,maint.begin_maint_time,maint.end_maint_time,
            maint.working_time,repair.stopped,repair.fault_phenomenon_ids,repair.fault_phenomenon_remark,
            repair.fault_reason_ids,repair.fault_reason_remark,repair.fault_measures_ids,repair.fault_measures_remark,
            repair.media_ids,repair.fault_structure_ids,maint.fault_influences,maint.damage_reason
        FROM maint_task maint
        LEFT JOIN maint_task_repair repair ON maint.id = repair.task_id
        WHERE maint.id = #{taskId}
    </select>
    <select id="getUsedFaultTree" resultType="cn.getech.ehm.task.entity.MaintTaskRepair">
        SELECT repair.fault_phenomenon_ids, repair.fault_reason_ids, repair.fault_measures_ids FROM maint_task_repair repair
            LEFT JOIN maint_task maint ON maint.id = repair.task_id
        WHERE maint.equipment_id = #{equipmentId}
        AND deleted = 0
        AND maint.type = 1
        AND maint.status IN
        <foreach collection="statusList" index="index" item="status" open="(" close=")" separator=",">
            #{status}
        </foreach>
          <if test="null != type">
              <choose>
                  <when test="type == 1">
                      AND (
                      <foreach collection="faultTreeIds" index="index" item="faultTreeId" separator=" OR ">
                          repair.fault_phenomenon_ids LIKE concat('%', #{faultTreeId} , '%')
                      </foreach>
                      )
                  </when>
                  <when test="type == 2">
                      AND (
                      <foreach collection="faultTreeIds" index="index" item="faultTreeId" separator=" OR ">
                          repair.fault_reason_ids LIKE concat('%', #{faultTreeId} , '%')
                      </foreach>
                      )
                  </when>
                  <when test="type == 3">
                      AND (
                      <foreach collection="faultTreeIds" index="index" item="faultTreeId" separator=" OR ">
                          repair.fault_measures_ids LIKE concat('%', #{faultTreeId} , '%')
                      </foreach>
                      )
                  </when>
              </choose>
          </if>
    </select>
</mapper>
