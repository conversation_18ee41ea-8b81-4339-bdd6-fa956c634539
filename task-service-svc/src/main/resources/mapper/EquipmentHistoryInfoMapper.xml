<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.task.mapper.EquipmentHistoryInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.getech.ehm.task.entity.EquipmentHistoryInfo">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="task_id" property="taskId" />
        <result column="old_equipment_id" property="oldEquipmentId" />
        <result column="new_equipment_id" property="newEquipmentId" />
        <result column="changed" property="changed" />
        <result column="tenant_id" property="tenantId" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_time,
        update_by,
        update_time,
        task_id, old_equipment_id, new_equipment_id, changed, tenant_id, deleted
    </sql>
    <select id="pageListByTaskId" resultType="cn.getech.ehm.task.dto.historyInfo.EquipmentHistoryInfoDto">
        select history.id, history.changed, history.remark, history.create_time,
         task.id taskId, task.code taskCode, task.name taskName,
         info.id oldEquipmentId, info.code oldEquipmentCode, info.name oldEquipmentName, info.`status` oldEquipmentStatus,
         history_info.id newEquipmentId, history_info.code newEquipmentCode, history_info.name newEquipmentName, history_info.status newEquipmentStatus,
        history.communicationer_id, history.communicationer_name, history.creater
        from maint_task task
        left join equipment_history_info history on task.id = history.task_id
        LEFT JOIN equipment_info info ON info.id = history.old_equipment_id
        left join equipment_info history_info on history_info.id = history.new_equipment_id
        <where>
            <if test="param.changed != null and param.changed != ''">
                and history.`changed` = #{param.changed}
            </if>
            <if test="param.taskId != null and param.taskId != ''">
                and history.task_id = #{param.taskId}
            </if>
            <if test="param.taskCode != null and param.taskCode != ''">
                and task.code like CONCAT("%",#{param.taskCode}, "%")
            </if>
            <if test="param.taskName != null and param.taskName != ''">
                and task.name like CONCAT("%",#{param.taskName}, "%")
            </if>
            <if test="param.oldEquipmentName != null and param.oldEquipmentName != ''">
                and info.name like CONCAT("%",#{param.oldEquipmentName}, "%")
            </if>
            <if test="param.oldEquipmentCode != null and param.oldEquipmentCode != ''">
                and info.code like CONCAT("%",#{param.oldEquipmentCode}, "%")
            </if>
            <if test="param.oldEquipmentStatus != null and param.oldEquipmentStatus != ''">
                and info.status = #{param.oldEquipmentStatus}
            </if>
            <if test="param.newEquipmentName != null and param.newEquipmentName != ''">
                and history_info.name like CONCAT("%",#{param.newEquipmentName}, "%")
            </if>
            <if test="param.newEquipmentCode != null and param.newEquipmentCode != ''">
                and history_info.code like CONCAT("%",#{param.newEquipmentCode}, "%")
            </if>
            <if test="param.newEquipmentStatus != null and param.newEquipmentStatus != ''">
                and history_info.status = #{param.newEquipmentStatus}
            </if>
            <if test="param.equipmentIds !=null and param.equipmentIds.size()>0">
                AND ((info.id IN
                <foreach collection="param.equipmentIds" open="(" close=")" separator="," item="equipmentId"
                         index="index">
                    #{equipmentId}
                </foreach>
                    ) or history_info.id IN
                <foreach collection="param.equipmentIds" open="(" close=")" separator="," item="equipmentId"
                         index="index">
                    #{equipmentId}
                </foreach>)
            </if>
            <if test="param.beginCreateTime != null">
                AND history.create_time &gt;= #{param.beginCreateTime}
            </if>
            <if test="param.endCreateTime != null">
                AND history.create_time &lt;= #{param.endCreateTime}
            </if>
        </where>
        order by history.create_time desc
    </select>

    <select id="pageList" resultType="cn.getech.ehm.task.dto.historyInfo.EquipmentHistoryInfoDto">
        select history.id, history.changed, history.remark, history.create_time,
        task.id taskId, task.code taskCode, task.name taskName,
        info.id oldEquipmentId, info.code oldEquipmentCode, info.name oldEquipmentName, info.`status` oldEquipmentStatus,
        history_info.id newEquipmentId, history_info.code newEquipmentCode, history_info.name newEquipmentName, history_info.status newEquipmentStatus,
        history.communicationer_id, history.communicationer_name, history.creater
        from equipment_history_info history
        left join maint_task task on task.id = history.task_id
        left join equipment_info info on info.id = task.equipment_id
        left join equipment_info history_info on history_info.id = history.new_equipment_id
        <where>
            <if test="param.changed != null and param.changed != ''">
                and history.`changed` = #{param.changed}
            </if>
            <if test="param.taskId != null and param.taskId != ''">
                and history.task_id = #{param.taskId}
            </if>
            <if test="param.taskCode != null and param.taskCode != ''">
                and task.code = #{param.taskCode}
            </if>
            <if test="param.taskName != null and param.taskName != ''">
                and task.name = #{param.taskName}
            </if>
            <if test="param.oldEquipmentName != null and param.oldEquipmentName != ''">
                and info.name = #{param.oldEquipmentName}
            </if>
            <if test="param.oldEquipmentCode != null and param.oldEquipmentCode != ''">
                and info.code = #{param.oldEquipmentCode}
            </if>
            <if test="param.oldEquipmentStatus != null and param.oldEquipmentStatus != ''">
                and info.status = #{param.oldEquipmentStatus}
            </if>
            <if test="param.newEquipmentName != null and param.newEquipmentName != ''">
                and history_info.name = #{param.newEquipmentName}
            </if>
            <if test="param.newEquipmentCode != null and param.newEquipmentCode != ''">
                and history_info.code = #{param.newEquipmentCode}
            </if>
            <if test="param.newEquipmentStatus != null and param.newEquipmentStatus != ''">
                and history_info.status = #{param.newEquipmentStatus}
            </if>
        </where>
        order by history.create_time desc
    </select>

</mapper>
