<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.task.mapper.DefectInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.getech.ehm.task.entity.DefectInfo">
    <result column="id" property="id" />
    <result column="create_by" property="createBy" />
    <result column="update_by" property="updateBy" />
    <result column="create_time" property="createTime" />
    <result column="update_time" property="updateTime" />
    <result column="remark" property="remark" />
        <result column="deleted" property="deleted" />
        <result column="tenant_id" property="tenantId" />
        <result column="equipment_id" property="equipmentId" />
        <result column="defect_name" property="defectName" />
        <result column="defect_content" property="defectContent" />
        <result column="affect_content" property="affectContent" />
        <result column="defect_type" property="defectType" />
        <result column="major_type" property="majorType" />
        <result column="live_medis_ids" property="liveMedisIds" />
        <result column="defect_status" property="defectStatus" />
        <result column="check_status" property="checkStatus" />
        <result column="end_time" property="endTime" />
        <result column="deal_person_id" property="dealPersonId" />
        <result column="suggest_deal_content" property="suggestDealContent" />
        <result column="maint_task_id" property="maintTaskId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        update_by,
        create_time,
        update_time,
        remark,
        deleted, tenant_id, equipment_id, defect_name, defect_content, affect_content, defect_type, major_type, live_medis_ids, defect_status, check_status, end_time, deal_person_id, suggest_deal_content, maint_task_id
    </sql>

</mapper>
