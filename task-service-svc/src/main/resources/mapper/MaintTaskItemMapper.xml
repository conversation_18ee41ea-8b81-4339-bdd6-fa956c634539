<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.task.mapper.MaintTaskItemMapper">
    <select id="getCanIotPushData" resultType="cn.getech.ehm.iot.dto.parameter.TaskIotPushDto">
        SELECT maint.equipment_id equipmentId, item.job_standard_item_id jobStandardItemId,
               IFNULL(maint.end_maint_time, now()) time,item.iot_push iotPush,item.result,
               item.content name,item.unit
        FROM maint_task_item item
        LEFT JOIN maint_task maint ON item.task_id = maint.id
        WHERE maint.id = #{taskId}
        AND item.iot_push IN
        <foreach collection="iotPushTypes" open="(" close=")" separator="," item="iotPushType" index="index">
            #{iotPushType}
        </foreach>
    </select>
</mapper>
