<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.task.mapper.MaintNotifyConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.getech.ehm.task.entity.MaintNotifyConfig">
    <result column="id" property="id" />
    <result column="create_by" property="createBy" />
    <result column="update_by" property="updateBy" />
    <result column="create_time" property="createTime" />
    <result column="update_time" property="updateTime" />
    <result column="remark" property="remark" />
        <result column="deleted" property="deleted" />
        <result column="tenant_id" property="tenantId" />
        <result column="type" property="type" />
        <result column="time_type" property="timeType" />
        <result column="time_gap" property="timeGap" />
        <result column="target_uids" property="targetUids" />
        <result column="deadline_time" property="deadlineTime" />
        <result column="deadline_uids" property="deadlineUids" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        update_by,
        create_time,
        update_time,
        remark,
        deleted, tenant_id, type, time_type, time_gap, target_uids, deadline_time, deadline_uids
    </sql>

</mapper>
