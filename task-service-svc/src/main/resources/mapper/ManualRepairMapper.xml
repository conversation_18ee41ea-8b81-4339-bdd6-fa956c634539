<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.task.mapper.ManualRepairMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="ListResultMap" type="cn.getech.ehm.task.dto.repair.ManualRepairListDto">
        <result column="id" property="id" />
        <result column="equipment_id" property="equipmentId" />
        <result column="major" property="major" />
        <result column="urgency" property="urgency" />
        <result column="fault_phenomenon_ids" property="faultPhenomenonIds" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="fault_phenomenon_remark" property="faultPhenomenonRemark" />
        <result column="create_time" property="createTime" />
        <result column="create_user_name" property="createUserName" />
        <result column="fault_time" property="faultTime" />
        <result column="status" property="status" />
        <result column="repair_time" property="repairTime" />
        <result column="task_id" property="taskId" />
        <result column="task_code" property="taskCode" />
        <result column="process_instance_id" property="processInstanceId" />
        <result column="activity_id" property="activityId" />
    </resultMap>
    <resultMap id="ExcelResultMap" type="cn.getech.ehm.task.dto.repair.ManualRepairExcelDto">
        <result column="equipment_id" property="equipmentId" />
        <result column="urgency" property="urgency" />
        <result column="fault_phenomenon_ids" property="faultPhenomenonIds" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="fault_phenomenon_remark" property="faultPhenomenonRemark" />
        <result column="create_time" property="createTime" />
        <result column="create_user_name" property="createUserName" />
        <result column="status" property="status" />
        <result column="repair_time" property="repairTime" />
        <result column="task_code" property="taskCode" />
    </resultMap>
    <resultMap id="DetailResultMap" type="cn.getech.ehm.task.dto.repair.ManualRepairDetailDto">
        <result column="id" property="id" />
        <result column="equipment_id" property="equipmentId" />
        <result column="major" property="major" />
        <result column="urgency" property="urgency" />
        <result column="fault_phenomenon_ids" property="faultPhenomenonIds" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="fault_phenomenon_remark" property="faultPhenomenonRemark" />
        <result column="create_user_name" property="createUserName" />
        <result column="create_time" property="createTime" />
    </resultMap>
    <select id="manualPageDto" resultMap="ListResultMap">
        SELECT repair.id,repair.equipment_id ,repair.task_id,maint.process_instance_id,
        repair.status,repair.urgency,repair.fault_phenomenon_ids, repair.fault_phenomenon_remark,repair.repair_time,
        repair.create_by,repair.create_time, repair.create_user_name,repair.task_code, repair.major
        FROM manual_repair repair
        LEFT JOIN maint_task maint ON repair.task_id = maint.id
        <where>
            <if test="null != param.beginCreateTime and null != param.endCreateTime">
                AND repair.create_time BETWEEN #{param.beginCreateTime} AND #{param.endCreateTime}
            </if>
            <if test="null != param.innerBeginCreateTime and null != param.innerEndCreateTime">
                AND repair.create_time BETWEEN #{param.innerBeginCreateTime} AND #{param.innerEndCreateTime}
            </if>
            <if test="null != param.equipmentId and ''!= param.equipmentId">
                AND repair.equipment_id = #{param.equipmentId}
            </if>
            <if test="null != param.status">
                AND repair.status = #{param.status}
            </if>
            <if test="null != param.createUserName and param.createUserName != ''">
                AND repair.create_user_name LIKE CONCAT('%',#{param.createUserName}, '%')
            </if>
            <if test="param.equipmentIds != null and param.equipmentIds.size() > 0">
                AND repair.equipment_id IN
                <foreach collection="param.equipmentIds" index="index" item="equipmentId" open="(" close=")" separator=",">
                    #{equipmentId}
                </foreach>
            </if>
            <if test="null != param.innerStatus and param.innerStatus != ''">
                AND FIND_IN_SET(repair.status, #{param.innerStatus})
            </if>
            <if test="null != param.beginRepairTime and null != param.endRepairTime">
                AND repair.repair_time BETWEEN #{param.beginRepairTime} AND #{param.endRepairTime}
            </if>
        </where>
        <if test="null != param.sortValue and '' != param.sortValue">
            ${param.sortValue}
        </if>
    </select>

    <select id="manualExcelDto" resultMap="ExcelResultMap">
        SELECT repair.status,repair.urgency,repair.fault_phenomenon_ids, repair.fault_phenomenon_remark,repair.repair_time,
        repair.create_by,repair.create_time, repair.create_user_name,repair.task_code,repair.equipment_id
        FROM manual_repair repair
        LEFT JOIN maint_task maint ON repair.task_id = maint.id
        <where>
            <if test="null != param.beginCreateTime and null != param.endCreateTime">
                AND repair.create_time BETWEEN #{param.beginCreateTime} AND #{param.endCreateTime}
            </if>
            <if test="null != param.innerBeginCreateTime and null != param.innerEndCreateTime">
                AND repair.create_time BETWEEN #{param.innerBeginCreateTime} AND #{param.innerEndCreateTime}
            </if>
            <if test="null != param.equipmentId and ''!= param.equipmentId">
                AND repair.equipment_id = #{param.equipmentId}
            </if>
            <if test="null != param.status">
                AND repair.status = #{param.status}
            </if>
            <if test="null != param.createUserName and param.createUserName != ''">
                AND repair.create_user_name LIKE CONCAT('%',#{param.createUserName}, '%')
            </if>
            <if test="param.equipmentIds != null and param.equipmentIds.size() > 0">
                AND repair.equipment_id IN
                <foreach collection="param.equipmentIds" index="index" item="equipmentId" open="(" close=")" separator=",">
                    #{equipmentId}
                </foreach>
            </if>
            <if test="null != param.innerStatus and param.innerStatus != ''">
                AND FIND_IN_SET(repair.status, #{param.innerStatus})
            </if>
            <if test="null != param.beginRepairTime and null != param.endRepairTime">
                AND repair.repair_time BETWEEN #{param.beginRepairTime} AND #{param.endRepairTime}
            </if>
        </where>
        <if test="null != param.sortValue and '' != param.sortValue">
            ${param.sortValue}
        </if>
    </select>
    <select id="detailList" resultMap="DetailResultMap">
        SELECT repair.id,repair.equipment_id ,repair.major,repair.urgency,repair.fault_phenomenon_ids, repair.fault_phenomenon_remark,repair.repair_time,
        repair.create_time, repair.create_user_name
        FROM manual_repair repair
        <where>
            <if test="null != param.beginCreateTime and null != param.endCreateTime">
                AND repair.create_time BETWEEN #{param.beginCreateTime} AND #{param.endCreateTime}
            </if>
            <if test="null != param.equipmentId and ''!= param.equipmentId">
                AND repair.equipment_id = #{param.equipmentId}
            </if>
            <if test="null != param.createUserName and param.createUserName != ''">
                AND repair.create_user_name LIKE CONCAT('%',#{param.createUserName}, '%')
            </if>
            <if test="param.equipmentIds != null and param.equipmentIds.size() > 0">
                AND repair.equipment_id IN
                <foreach collection="param.equipmentIds" index="index" item="equipmentId" open="(" close=")" separator=",">
                    #{equipmentId}
                </foreach>
            </if>
        </where>
        <if test="null != param.sortValue and '' != param.sortValue">
            ${param.sortValue}
        </if>
    </select>
    <select id="appManualPageDto" parameterType="cn.getech.ehm.task.dto.repair.RepairAppQueryParam"
            resultType="cn.getech.ehm.task.dto.repair.ManualRepairAppDto">
        SELECT repair.id,repair.equipment_id equipmentId,repair.fault_phenomenon_ids faultPhenomenonIds,
        repair.fault_phenomenon_remark faultPhenomenonRemark,repair.urgency,
        repair.task_id taskId,repair.task_code taskCode,maint.name taskName,maint.status taskStatus,repair.status,
        repair.repair_time repairTime,repair.create_time createTime,repair.create_by,
        repair.create_user_name createUserName,maint.grade
        FROM manual_repair repair
        LEFT JOIN maint_task maint ON repair.task_id = maint.id
        <where>
            <if test="null != param.equipmentId and ''!= param.equipmentId">
                AND repair.equipment_id = #{param.equipmentId}
            </if>
            <if test="param.equipmentIds != null and param.equipmentIds.size() > 0">
                AND repair.equipment_id IN
                <foreach collection="param.equipmentIds" index="index" item="equipmentId" open="(" close=")" separator=",">
                    #{equipmentId}
                </foreach>
            </if>
            <if test="null != param.createBy and ''!= param.createBy">
                AND repair.create_by = #{param.createBy}
            </if>
            <if test="null != param.status and param.status != -1">
                AND repair.status = #{param.status}
            </if>
        </where>
        ORDER BY repair.create_time DESC
    </select>
    <select id="getManualRepairCount" resultType="list">
        select count from (
        <foreach collection="list" item="itemroot" index="index" separator="union all">
            select count(1) as count from manual_repair where equipment_id in
            <foreach collection="itemroot.equipmentIds" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
            and deleted = 0
            and #{endTime} >= create_time
            and #{startTime} &lt;= create_time
            and status = 1
        </foreach>
        ) tall;
    </select>
</mapper>
