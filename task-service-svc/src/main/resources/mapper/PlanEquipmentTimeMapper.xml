<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.task.mapper.PlanEquipmentTimeMapper">
    <resultMap id="ResultMap" type="cn.getech.ehm.task.dto.plan.SynPlanEquipmentTimeDto">
        <result column="id" property="id" />
        <result column="plan_id" property="planId" />
        <result column="plan_day" property="planDay" />
        <result column="plan_time" property="planTime"/>
        <result column="maintainer_ids" property="maintainerIds" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="team_ids" property="teamIds" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler"/>
        <result column="person_strategy" property="personStrategy" />
    </resultMap>
    <select id="getListByPlanIds" resultMap="ResultMap">
        SELECT rel.id,rel.plan_id, rel.plan_day, rel.plan_time,
            rel.person_strategy,rel.maintainer_ids,rel.team_ids
        FROM plan_equipment_time rel
        WHERE rel.plan_id in
        <foreach collection="planIds" index="index" item="planId" open="(" close=")" separator=",">
            #{planId}
        </foreach>
    </select>
</mapper>
