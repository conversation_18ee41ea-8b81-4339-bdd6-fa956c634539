<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.ehm.task.mapper.JobStandardMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="ListResultMap" type="cn.getech.ehm.task.dto.job.JobStandardListDto">
        <result column="id" property="id" />
        <result column="name" property="name" />
        <result column="info_category_ids" property="infoCategoryIds" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler" />
        <result column="info_location_ids" property="infoLocationIds" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler" />
        <result column="majors" property="majors" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler" />
        <result column="job_type" property="jobType" />
        <result column="remark" property="remark" />
        <result column="equip_type" property="equipType" />
    </resultMap>

    <resultMap id="BaseResultMap" type="cn.getech.ehm.task.dto.job.JobStandardDto">
        <result column="id" property="id" />
        <result column="name" property="name" />
        <result column="info_category_ids" property="infoCategoryIds" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler" />
        <result column="info_location_ids" property="infoLocationIds" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler" />
        <result column="majors" property="majors" jdbcType="VARCHAR"
                typeHandler="cn.getech.ehm.common.mp.handler.StringArrayTypeHandler" />
        <result column="job_type" property="jobType" />
        <result column="remark" property="remark" />
        <result column="stopped" property="stopped" />
    </resultMap>
    <select id="getPageList" resultMap="ListResultMap">
        SELECT standard.id,standard.name,standard.info_category_ids,standard.info_location_ids,standard.majors,
        standard.job_type,standard.remark,standard.status,standard.equip_type
        FROM job_standard standard
        <where>
            standard.deleted = 0
            <if test="null != param.name and '' != param.name">
                AND standard.name LIKE CONCAT("%",#{param.name}, "%")
            </if>
            <if test="null != param.infoCategoryId and '' != param.infoCategoryId">
                AND standard.info_category_ids LIKE CONCAT("%",#{param.infoCategoryId}, "%")
            </if>
            <if test="null != param.infoLocationId and '' != param.infoLocationId">
                AND standard.info_location_ids LIKE CONCAT("%",#{param.infoLocationId}, "%")
            </if>
            <if test="null != param.major and '' != param.major">
                AND standard.majors LIKE CONCAT("%",#{param.major}, "%")
            </if>
            <if test="null != param.status">
                AND standard.status = #{param.status}
            </if>
            <if test="null != param.jobType and '' != param.jobType">
                AND standard.job_type = #{param.jobType}
            </if>
            <if test="param.uidsOfDept !=null and param.uidsOfDept.size()>0">
                AND create_by IN
                <foreach collection="param.uidsOfDept" index="index" item="uid" open="(" close=")" separator=",">
                    #{uid}
                </foreach>
            </if>
        <if test="null != param.equipType and '' != param.equipType">
            AND standard.equip_type = #{param.equipType}
        </if>

        </where>
        <if test="null != param.sortValue and '' != param.sortValue">
            ${param.sortValue}
        </if>
    </select>
    <select id="getDtoById" resultMap="BaseResultMap">
        SELECT standard.id,standard.name,standard.info_category_ids,standard.info_location_ids,standard.majors,
        standard.job_type,standard.remark,standard.stopped
        FROM job_standard standard
        <where>
            standard.id = #{id}
        </where>
    </select>
</mapper>
