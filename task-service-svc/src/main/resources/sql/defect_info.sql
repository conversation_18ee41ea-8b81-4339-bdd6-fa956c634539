create table defect_info
(
    id                   varchar(32)       not null
        primary key,
    create_by            varchar(255)      null,
    update_by            varchar(255)      null,
    create_time          datetime          null,
    update_time          datetime          null,
    deleted              tinyint default 0 null,
    remark               varchar(255)      null,
    tenant_id            varchar(255)      null,
    equipment_id         varchar(32)       null comment '设备id',
    defect_name          varchar(255)      null comment '缺陷名称',
    defect_content       varchar(255)      null comment '缺陷内容',
    affect_content       varchar(255)      null comment '影响描述',
    defect_type          tinyint           null comment '缺陷种类',
    major_type           tinyint           null comment '专业类别',
    live_media_ids       varchar(255)      null comment '现场图片/视频',
    defect_status        tinyint           null comment '缺陷状态',
    check_status         tinyint           null comment '验收状态',
    end_time             datetime          null comment '截止日期',
    deal_person_ids      varchar(255)      null comment '处理人ids',
    suggest_deal_content varchar(255)      null comment '建议处理方案',
    maint_task_id        varchar(32)       null comment '工单id',
    maint_task_code      varchar(32)       null comment '工单code',
    check_explain        varchar(255)      null comment '验收说明',
    real_deal_content    varchar(255)      null comment '实际处理方案',
    close_reason         varchar(255)      null comment '关闭原因',
    close_date           datetime          null comment '处理时间'
)
    comment '缺陷记录';

