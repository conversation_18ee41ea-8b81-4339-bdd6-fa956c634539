package cn.getech.ehm.task.service.impl;

import cn.getech.ehm.common.context.UserContextHolder;
import cn.getech.ehm.task.dto.activiti.ActivitiManageDto;
import cn.getech.ehm.task.dto.activiti.AuditActivitiServiceResult;
import cn.getech.ehm.task.entity.MaintTask;
import cn.getech.ehm.task.handler.ActivitiHandler;
import cn.getech.ehm.task.service.IMaintTaskService;
import cn.getech.ehm.task.service.feign.CustomService;
import cn.getech.poros.bpm.dto.task.ProcessTaskDTO;
import cn.getech.poros.bpm.param.task.ProcessTaskParam;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ManageServiceImpl {
    @Autowired
    @Lazy
    IMaintTaskService maintTaskService;
    @Autowired
    ActivitiHandler activitiHandler;
    @Autowired
    @Qualifier("activitiExecutorService")
    private ExecutorService activitiExecutorService;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    CustomService customService;

    public void deleteTask(@RequestBody ActivitiManageDto request) {
        UserContextHolder.defaultContext();
        List<MaintTask> list = maintTaskService.list(new QueryWrapper<MaintTask>().lambda()
                .select(MaintTask::getId, MaintTask::getStatus, MaintTask::getProcessInstanceId)
                .in(StringUtils.isNotBlank(request.getIncludeStatus()), MaintTask::getStatus, request.getIncludeStatus().split(","))
                .notIn(StringUtils.isNotBlank(request.getExcludeStatus()), MaintTask::getStatus, request.getExcludeStatus().split(","))
                .ge(MaintTask::getCreateTime, request.getBeginDate())
                .le(MaintTask::getCreateTime, request.getEndDate()));
        list.stream().forEach(item -> {
            CompletableFuture.runAsync(() -> {
                activitiHandler.deleteByProcessId(item.getProcessInstanceId(), UserContextHolder.getContext().getUserBaseInfo());
            }, activitiExecutorService);
        });
        List<String> idList = list.stream().map(item -> item.getId()).collect(Collectors.toList());
        maintTaskService.removeByIds(idList);
    }

    public void deleteTaskByFalg() {
        UserContextHolder.defaultContext();
        UserBaseInfo userBaseInfo = UserContextHolder.getContext().getUserBaseInfo();
        List<MaintTask> list = maintTaskService.list(new QueryWrapper<MaintTask>().lambda()
                .select(MaintTask::getId, MaintTask::getStatus, MaintTask::getProcessInstanceId)
                .eq(MaintTask::getErrorFlag, 1)
                .eq(MaintTask::getReadyDelete, 1));
        list.stream().forEach(item -> {
            CompletableFuture.runAsync(() -> {
                userBaseInfo.setUid(item.getCreateBy());
                userBaseInfo.setTenantId(item.getTenantId());
                activitiHandler.deleteByProcessIdManage(item.getProcessInstanceId());
            }, activitiExecutorService);
        });
//        UserContextHolder.defaultContext();
//        List<String> idList = list.stream().map(item -> item.getId()).collect(Collectors.toList());
//        maintTaskService.removeByIds(idList);
    }

    public void findStatusWrongList() {

    }

    public void markStatusError(ActivitiManageDto request) {
        UserContextHolder.defaultContext();
        List<MaintTask> list = maintTaskService.list(new QueryWrapper<MaintTask>().lambda()
                .select(MaintTask::getId, MaintTask::getStatus, MaintTask::getProcessInstanceId)
                .in(StringUtils.isNotBlank(request.getIncludeStatus()), MaintTask::getStatus, request.getIncludeStatus().split(","))
                .notIn(StringUtils.isNotBlank(request.getExcludeStatus()), MaintTask::getStatus, request.getExcludeStatus().split(","))
                .ge(MaintTask::getCreateTime, request.getBeginDate())
                .le(MaintTask::getCreateTime, request.getEndDate()));
        List<MaintTask> updateList = Lists.newArrayList();
        for (MaintTask item : list) {
            AuditActivitiServiceResult test = this.test(request.getToken(), item.getProcessInstanceId());
            if (test != null && StringUtils.isNotBlank(test.getTaskName()) && !test.getTaskName().equals(request.getTaskName())) {
                item.setErrorFlag(1);
                updateList.add(item);
            }
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            log.info("工单总数：{}，待修复数：{}", list.size(), updateList.size());
        }
    }

    public AuditActivitiServiceResult test(String token, String processInstanceId) {
        ProcessTaskParam processTaskParam = new ProcessTaskParam();
        processTaskParam.setProcessInstanceId(processInstanceId);
        RestResponse<List<ProcessTaskDTO>> response = customService.getUserTaskByProcessId(token, processTaskParam);
        if (response.isOk()) {
            if (CollectionUtils.isNotEmpty(response.getData())) {
                List<String> candidateUids = response.getData().get(0).getCandidateUids();
                return AuditActivitiServiceResult.builder()
                        .processUser(JSONUtil.toJsonStr(response.getData().get(0).getCandidateUids()))
                        .processUserArray(candidateUids.toArray(new String[candidateUids.size()]))
                        .taskId(response.getData().get(0).getTaskId())
                        .taskName(response.getData().get(0).getActivityName())
                        .build();
            } else {
                //没有后续任务
                return AuditActivitiServiceResult.builder()
                        .processUser("")
                        .taskId("")
                        .build();
            }
        }
        return null;
    }

    public void setInitCode(String prex, Long num) {
        redisTemplate.opsForValue().set("EHM:CODE:" + prex, num);
    }
}
