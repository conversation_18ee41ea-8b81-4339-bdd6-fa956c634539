package cn.getech.ehm.task.dto.task.assist;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

/**
 * 工单辅助人员新增体
 *
 * <AUTHOR>
 * @since 2022-06-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MaintTaskAssistAddParam", description = "工单辅助人员新增体")
public class MaintTaskAssistAddParam extends MaintTaskAssistDto {
    @ApiModelProperty(value = "工单id")
    @NotEmpty
    private String taskId;
}
