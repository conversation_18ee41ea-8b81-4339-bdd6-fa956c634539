package cn.getech.ehm.task.dto.task.info;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 工单统计查询参数对象
 *
 */
@Data
@EqualsAndHashCode()
@ApiModel(value = "工单统计查询参数对象", description = "工单统计查询参数对象")
public class MaintTaskReportQueryParam {

    @ApiModelProperty(value = "统计月份开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "统计月份不能为空")
    private Date beginTime;

    @ApiModelProperty(value = "统计月份结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "统计月份不能为空")
    private Date endTime;

    @ApiModelProperty(value = "维护设备位置ids")
    private List<String> equipmentLocationIds;

    @ApiModelProperty("设备类型")
    private List<String> equipmentCategoryIds;

    private List<String> equipmentIds;
}
