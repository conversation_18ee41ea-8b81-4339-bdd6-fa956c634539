package cn.getech.ehm.task.dto.historyInfo;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <pre>
 *  编辑参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-12-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentHistoryInfo编辑", description = "编辑参数")
public class EquipmentHistoryInfoEditParam extends ApiParam {
    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty("工单id")
    private String taskId;

    @ApiModelProperty("变更前设备id")
    private String oldEquipmentId;

    @ApiModelProperty("变更后设备id")
    private String newEquipmentId;

    @ApiModelProperty("是否变更(0false1true)")
    private Integer changed;

    @ApiModelProperty("租户ID")
    private String tenantId;

    @ApiModelProperty("是否删除(0false1true)")
    private Integer deleted;
}
