package cn.getech.ehm.task.dto.task.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 维护工单批量转派提交参数
 *
 * <AUTHOR>
 * date 2025/2/5
 */
@Data
@ApiModel(value = "TaskBatchEditParam", description = "维护工单批量操作提交参数")
public class TaskBatchTransformParam {
    @ApiModelProperty(value = "工单ID")
    private String taskId;

    @ApiModelProperty(value = "维护人员Id")
    private String staffId;
}
