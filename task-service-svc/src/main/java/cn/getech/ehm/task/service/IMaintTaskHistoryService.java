package cn.getech.ehm.task.service;

import cn.getech.ehm.task.dto.task.history.MaintTaskHistoryAddParam;
import cn.getech.ehm.task.dto.task.history.MaintTaskHistoryDto;
import cn.getech.ehm.task.dto.task.history.MaintTaskHistoryEditParam;
import cn.getech.ehm.task.dto.task.history.MaintTaskHistoryQueryParam;
import cn.getech.ehm.task.entity.MaintTask;
import cn.getech.ehm.task.entity.MaintTaskHistory;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.framework.common.service.IBaseService;

import java.util.List;

/**
 * <pre>
 * 工单历史 服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2025-02-05
 */
public interface IMaintTaskHistoryService extends IBaseService<MaintTaskHistory> {

    /**
     * 分页查询，返回Dto
     *
     * @param maintTaskHistoryQueryParam
     * @return
     */
    PageResult<MaintTaskHistoryDto> pageDto(MaintTaskHistoryQueryParam maintTaskHistoryQueryParam);

    /**
     * 保存
     *
     * @param maintTaskHistoryAddParam
     * @return
     */
    boolean saveByParam(MaintTaskHistoryAddParam maintTaskHistoryAddParam);

    /**
     * 根据id查询，转dto
     *
     * @param id
     * @return
     */
    MaintTaskHistoryDto getDtoById(Long id);

    /**
     * 批量保存
     *
     * @param rows
     */
    boolean saveDtoBatch(List<MaintTaskHistoryDto> rows);

    /**
     * 更新
     *
     * @param maintTaskHistoryEditParam
     */
    boolean updateByParam(MaintTaskHistoryEditParam maintTaskHistoryEditParam);

    public MaintTaskHistory insertStatusNode(String processInstanceId,UserBaseInfo userBaseInfo, String taskId, int oldStatus, int newStatus, List<String> uids, String operator);

    public String insertRejectStatusNode(String processInstanceId, UserBaseInfo userBaseInfo, String taskId, int oldStatus, int newStatus, String operator);

    public String insertTrashStatusNode(String processInstanceId, UserBaseInfo userBaseInfo, String taskId, int oldStatus, int newStatus, String operator);

    public String initFirstNode(UserBaseInfo userBaseInfo, String taskId);

    public String insertDoneNode(String processInstanceId, UserBaseInfo userBaseInfo, String taskId, int oldStatus, int newStatus, String operator, String comment);

    public MaintTaskHistory insertStatusNode(String processInstanceId, UserBaseInfo userBaseInfo, MaintTask maintTask, int oldStatus, int newStatus, List<String> uids, String operator);
}