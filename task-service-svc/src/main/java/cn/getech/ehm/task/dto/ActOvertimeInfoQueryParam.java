package cn.getech.ehm.task.dto;

import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <pre>
 * 节点时限记录 分页查询参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2023-03-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ActOvertimeInfo查询", description = "节点时限记录查询参数")
public class ActOvertimeInfoQueryParam extends PageParam {

    private String procInsId;
}
