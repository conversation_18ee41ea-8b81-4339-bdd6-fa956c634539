package cn.getech.ehm.task.mapper;

import cn.getech.ehm.task.dto.plan.MaintPlanDto;
import cn.getech.ehm.task.dto.plan.MaintPlanListDto;
import cn.getech.ehm.task.dto.plan.MaintPlanQueryParam;
import cn.getech.ehm.task.dto.plan.SynMaintPlanDto;
import cn.getech.ehm.task.entity.MaintPlan;
import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

/**
 * 维护计划 Mapper 接口
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
@Repository
public interface MaintPlanMapper extends BaseMapper<MaintPlan> {

    /**
     * 分页获取维护计划列表
     * @param page
     * @param maintPlanQueryParam
     * @return
     */
    IPage<MaintPlanListDto> getPageList(Page<MaintPlanQueryParam> page,
                                        @Param("param") MaintPlanQueryParam maintPlanQueryParam);

    /**
     * 获取计划详情
     * @param id
     * @return
     */
    MaintPlanDto getMaintPlanDto(String id);

    /**
     * 根据字段查询数据库中存在的条数
     * @param tableName
     * @param columnName
     * @param value
     * @return
     */
    Integer getCount(String tableName, String columnName, String value);

    /**
     * 获取需要开单的计划单详情
     * @return
     */
    @SqlParser(filter = true)
    List<SynMaintPlanDto> getSynListByIds(@Param("planIds") List<String> planIds);

    /**
     * 根据id获取开单信息
     * @param planId
     * @return
     */
    @SqlParser(filter = true)
    SynMaintPlanDto getSynById(@Param("planId") String planId);

    /**
     * 获取计划维保对象已经使用的作业标准id集合
     * @param standardIds
     * @return
     */
    List<String> getUsedStandardIds(@Param("standardIds") List<String> standardIds);

    /**
     * 获取cbm计划单id
     * @param locationIds
     * @param categoryId
     * @param equipmentId
     * @return
     */
    List<String> getCbmPlanIds(@Param("locationIds") String[] locationIds, @Param("categoryId") String categoryId, @Param("equipmentId") String equipmentId, @Param("jobType") String jobType, @Param("categoryParamId") String categoryParamId);

}
