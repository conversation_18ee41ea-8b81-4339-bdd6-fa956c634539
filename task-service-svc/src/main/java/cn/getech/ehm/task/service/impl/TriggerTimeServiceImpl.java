package cn.getech.ehm.task.service.impl;

import cn.getech.ehm.base.client.BaseServiceClient;
import cn.getech.ehm.base.dto.FestivalDateDto;
import cn.getech.ehm.base.dto.FestivalDateSearchDto;
import cn.getech.ehm.base.enums.FestivalType;
import cn.getech.ehm.common.context.UserContextHolder;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.task.dto.plan.MaintPlanCountDto;
import cn.getech.ehm.task.dto.plan.TriggerTimeDto;
import cn.getech.ehm.task.entity.PlanTriggerTime;
import cn.getech.ehm.task.mapper.TriggerTimeMapper;
import cn.getech.ehm.task.service.ITriggerTimeService;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TriggerTimeServiceImpl extends BaseServiceImpl<TriggerTimeMapper, PlanTriggerTime> implements ITriggerTimeService {

    @Autowired
    private TriggerTimeMapper triggerTimeMapper;
    @Autowired
    private BaseServiceClient baseServiceClient;
    @Autowired
    private ITriggerTimeService triggerTimeService;

    @Override
    public Boolean saveByParam(List<TriggerTimeDto> triggerTimeDtos) {
        if (CollectionUtils.isNotEmpty(triggerTimeDtos)) {
            List<PlanTriggerTime> triggerTimes = CopyDataUtil.copyList(triggerTimeDtos, PlanTriggerTime.class);
            return saveBatch(triggerTimes);
        }
        return true;
    }

    @Override
    public Boolean deleteByPlanIds(List<String> planIds) {
        LambdaQueryWrapper<PlanTriggerTime> wrapper = Wrappers.lambdaQuery();
        wrapper.in(PlanTriggerTime::getPlanId, planIds);
        return remove(wrapper);
    }

    public Boolean deleteByPlanIdsNow(List<String> planIds) {
        LambdaQueryWrapper<PlanTriggerTime> wrapper = Wrappers.lambdaQuery();
        wrapper.in(PlanTriggerTime::getPlanId, planIds).ge(PlanTriggerTime::getTriggerTime, new Date());
        return this.remove(wrapper);
    }



    @Override
    public List<TriggerTimeDto> canCreateTaskDtos() {
        List<TriggerTimeDto> canCreateTaskDtos = new ArrayList<>();

        List<String> festivalTriggerTimeIds = new ArrayList<>();

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        log.info("当前扫描截止时间：{}", DateUtil.format(calendar.getTime(), "yyyy-MM-dd HH:mm:ss"));
        List<TriggerTimeDto> triggerTimeDtos = triggerTimeMapper.canCreateTaskDtos(calendar.getTime());

        if(CollectionUtils.isNotEmpty(triggerTimeDtos)) {
            Map<String, List<TriggerTimeDto>> tenantTriggerMap = triggerTimeDtos.stream().collect(Collectors.groupingBy(TriggerTimeDto::getTenantId));
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            for(Map.Entry<String, List<TriggerTimeDto>> tenantTrigger: tenantTriggerMap.entrySet()) {
                List<TriggerTimeDto> tenantTriggerTimeDtos = tenantTrigger.getValue();
                //按租户处理 设置租户信息
                UserBaseInfo baseInfo = JSONObject.parseObject("{\"uid\":\"admin\"," +
                        "\"tenantId\":\"" + tenantTrigger.getKey() + "\"}", UserBaseInfo.class);
                UserContextHolder.getContext().setUserBaseInfo(baseInfo);
                Date startDate = new Date();
                Date endDate = null;
                for (TriggerTimeDto triggerTimeDto : tenantTriggerTimeDtos) {
                    Date date = DateUtil.beginOfDay(triggerTimeDto.getPlanMaintTime());
                    if (date.before(startDate)) {
                        startDate = date;
                    }
                    if (null == endDate) {
                        endDate = startDate;
                    } else if (date.after(endDate)) {
                        endDate = date;
                    }
                }
                endDate = DateUtil.endOfDay(endDate);

                FestivalDateSearchDto searchDto = FestivalDateSearchDto.builder().beginTime(startDate)
                        .endTime(endDate).festivalType(FestivalType.getValueList()).build();

                FestivalDateDto festivalDateDto = null;
                RestResponse<FestivalDateDto> festivalRes = baseServiceClient.getAvailableDay(searchDto);
                if (festivalRes.isOk()) {
                    festivalDateDto = festivalRes.getData();
                } else {
                    log.info("连接base-service获取节假日配置失败");
                }

                for(TriggerTimeDto triggerTimeDto : tenantTriggerTimeDtos){
                    Boolean flag = festivalDate(festivalDateDto, sdf.format(triggerTimeDto.getPlanMaintTime()), null != triggerTimeDto.getFestivalType() ? Arrays.asList(triggerTimeDto.getFestivalType()) : null);
                    if(flag){
                        festivalTriggerTimeIds.add(triggerTimeDto.getId());
                    }else {
                        canCreateTaskDtos.add(triggerTimeDto);
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(festivalTriggerTimeIds)) {
                triggerTimeService.changeStatus(festivalTriggerTimeIds, 2);
            }
        }
        return canCreateTaskDtos;
    }

    /**
     * 是否节假日
     * @return
     */
    private Boolean festivalDate(FestivalDateDto festivalDateDto, String dateStr, List<Integer> festivalType){
        if(null == festivalDateDto){
            return false;
        }
        if(CollectionUtils.isEmpty(festivalType)){
            return false;
        }
        List<String> makUpShiftDay = festivalDateDto.getMakUpShiftDay();
        List<String> jumpFestivalDay = festivalDateDto.getJumpFestivalDay();
        List<String> jumpWeekDay = festivalDateDto.getJumpWeekDay();
        if(festivalType.contains(FestivalType.MAKE_UP_SHIFT.getValue())){
            return !makUpShiftDay.contains(dateStr);
        }
        if(festivalType.contains(FestivalType.JUMP_WEEK.getValue()) && jumpWeekDay.contains(dateStr)){
            return true;
        }
        if(festivalType.contains(FestivalType.JUMP_FESTIVAL.getValue()) && jumpFestivalDay.contains(dateStr)){
            return true;
        }
        return false;
    }

    @Override
    public Boolean changeStatus(List<String> triggerTimeIds, Integer status) {
        return triggerTimeMapper.changeStatus(triggerTimeIds, status) > 0;
    }

    @Override
    public Map<String, Date> getNextPlanDateMap(List<String> planIds) {
        Map<String, Date> map = new HashMap<>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, calendar.getActualMinimum(Calendar.HOUR_OF_DAY));
        calendar.set(Calendar.MINUTE, calendar.getActualMinimum(Calendar.MINUTE));
        calendar.set(Calendar.SECOND, calendar.getActualMinimum(Calendar.SECOND));
        List<MaintPlanCountDto> dateDtos = triggerTimeMapper.getNextPlanDate(planIds, calendar.getTime());
        if (CollectionUtils.isNotEmpty(dateDtos)) {
            return dateDtos.stream().filter(dto -> StringUtils.isNotBlank(dto.getPlanId())).collect(Collectors.toMap(MaintPlanCountDto::getPlanId, MaintPlanCountDto::getNextMaintTime, (v1, v2) -> v1));
        }

        return map;
    }

    @Override
    public List<PlanTriggerTime> getNormalListByTime(Date beginTime, Date endTime) {
        return triggerTimeMapper.getNormalListByTime(beginTime, endTime);
    }

    @Override
    public Boolean stopUpdateTime(List<TriggerTimeDto> updateDtos){
        List<PlanTriggerTime> planTriggerTimes = CopyDataUtil.copyList(updateDtos, PlanTriggerTime.class);
        return updateBatchById(planTriggerTimes);
    }
}
