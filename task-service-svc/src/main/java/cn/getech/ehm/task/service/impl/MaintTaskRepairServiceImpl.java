package cn.getech.ehm.task.service.impl;

import cn.getech.ehm.base.client.BaseServiceClient;
import cn.getech.ehm.base.dto.FaultKnowledgeResDto;
import cn.getech.ehm.base.dto.FaultKnowledgeSearchDto;
import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.task.dto.task.repair.MaintTaskFaultDto;
import cn.getech.ehm.task.dto.task.repair.TaskFaultEditParam;
import cn.getech.ehm.task.entity.MaintTask;
import cn.getech.ehm.task.entity.MaintTaskRepair;
import cn.getech.ehm.task.enums.TaskStatusType;
import cn.getech.ehm.task.handler.CommonGetHandler;
import cn.getech.ehm.task.mapper.MaintTaskRepairMapper;
import cn.getech.ehm.task.service.IMaintTaskRepairService;
import cn.getech.ehm.task.service.IMaintTaskService;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import java.util.*;

/**
 * 维护工单详情表 服务实现类
 * <AUTHOR>
 * @since 2020-07-28
 */
@Slf4j
@Service
public class MaintTaskRepairServiceImpl extends BaseServiceImpl<MaintTaskRepairMapper, MaintTaskRepair> implements IMaintTaskRepairService {
    @Autowired
    private MaintTaskRepairMapper maintTaskRepairMapper;
    @Autowired
    private CommonGetHandler commonGetHandler;
    @Autowired
    private BaseServiceClient baseServiceClient;
    @Autowired
    @Lazy
    private IMaintTaskService maintTaskService;

    @Override
    public MaintTaskFaultDto getFaultDtoById(String taskId){
        return maintTaskRepairMapper.getFaultDtoById(taskId);
    }

    @Override
    public Boolean updateFault(TaskFaultEditParam editParam){
        MaintTaskRepair maintTaskRepair = CopyDataUtil.copyObject(editParam, MaintTaskRepair.class);
        boolean b = updateById(maintTaskRepair);
        maintTaskRepair = (MaintTaskRepair) this.getById(editParam.getId());
        Map<String, FaultKnowledgeResDto> faultKnowledgeMap = new HashMap<>();
        if(null != maintTaskRepair.getFaultPhenomenonIds() && maintTaskRepair.getFaultPhenomenonIds().length > 0){
            faultKnowledgeMap = this.getFaultKnowledgeMap(Arrays.asList(maintTaskRepair.getFaultPhenomenonIds()), StaticValue.ONE);
        }
        String content = this.buildPhenomenon(maintTaskRepair.getFaultPhenomenonIds(), maintTaskRepair.getFaultPhenomenonRemark(), faultKnowledgeMap);
        maintTaskService.update(new UpdateWrapper<MaintTask>().lambda().eq(MaintTask::getId, maintTaskRepair.getTaskId()).set(MaintTask::getContent,content));
        return b;
    }

    private Map<String, FaultKnowledgeResDto> getFaultKnowledgeMap(List<String> ids, Integer type){
        Map<String, FaultKnowledgeResDto> faultKnowledgeMap = new HashMap<>();
        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(ids)){
            FaultKnowledgeSearchDto searchDto = new FaultKnowledgeSearchDto();
            searchDto.setType(type);
            searchDto.setIds(ids);
            RestResponse<Map<String, FaultKnowledgeResDto>> restResponse = baseServiceClient.faultKnowledgeMap(searchDto);
            if(restResponse.isOk()){
                faultKnowledgeMap = restResponse.getData();
            }else{
                log.error("获取故障现象失败");
            }
        }
        return faultKnowledgeMap;
    }

    private String buildPhenomenon(String[] phenomenonIds, String phenomenonRemark, Map<String, FaultKnowledgeResDto> faultPhenomenonMap){
        String faultPhenomenonRemark = null;
        if(null != phenomenonIds && phenomenonIds.length > 0) {
            for (String faultPhenomenonId : phenomenonIds) {
                FaultKnowledgeResDto faultKnowledgeResDto = faultPhenomenonMap.get(faultPhenomenonId);
                if (ObjectUtils.isNull(faultKnowledgeResDto)){
                    continue;
                }
                if (StringUtils.isNotBlank(faultPhenomenonRemark)) {
                    faultPhenomenonRemark += ";" + faultKnowledgeResDto.getName();
                } else {
                    faultPhenomenonRemark = faultKnowledgeResDto.getName();
                }
            }
        }
        if(StringUtils.isNotBlank(faultPhenomenonRemark)){
            if(StringUtils.isNotBlank(phenomenonRemark)){
                faultPhenomenonRemark += ";" +  phenomenonRemark;
            }
        }else{
            faultPhenomenonRemark = phenomenonRemark;
        }
        return faultPhenomenonRemark;
    }

    @Override
    public Map<String, Integer> getUsedFaultTree(String equipmentId, List<String> faultTreeIds, Integer type){
        Map<String, Integer> map = new HashMap<>();
        Integer[] statusList = new Integer[]{TaskStatusType.CHECK_ACCEPT.getValue(),TaskStatusType.CLOSED.getValue()};
        List<MaintTaskRepair> maintTaskRepairs = maintTaskRepairMapper.getUsedFaultTree(equipmentId,faultTreeIds, type, statusList);
        if(CollectionUtils.isNotEmpty(maintTaskRepairs)){
            for(MaintTaskRepair maintTaskRepair : maintTaskRepairs) {
                if(type == 1){
                    for(String faultPhenomenonId : maintTaskRepair.getFaultPhenomenonIds()){
                        if(faultTreeIds.contains(faultPhenomenonId)){
                            Integer num = map.get(faultPhenomenonId);
                            map.put(faultPhenomenonId, null != num ? num + 1 : 1);
                        }
                    }
                }else if(type == 2){
                    for(String faultReasonId : maintTaskRepair.getFaultReasonIds()){
                        if(faultTreeIds.contains(faultReasonId)){
                            Integer num = map.get(faultReasonId);
                            map.put(faultReasonId, null != num ? num + 1 : 1);
                        }
                    }
                }else if(type == 3){
                    for(String faultMeasuringId : maintTaskRepair.getFaultMeasuresIds()){
                        if(faultTreeIds.contains(faultMeasuringId)){
                            Integer num = map.get(faultMeasuringId);
                            map.put(faultMeasuringId, null != num ? num + 1 : 1);
                        }
                    }
                }
            }
        }
        return map;
    }

    @Override
    public Map<String, MaintTaskFaultDto> getFaultDtoByTaskIds(List<String> taskIds){
        Map<String, MaintTaskFaultDto> map = Maps.newHashMap();
        if(CollectionUtils.isNotEmpty(taskIds)){
            LambdaQueryWrapper<MaintTaskRepair> wrapper = Wrappers.lambdaQuery();
            wrapper.in(MaintTaskRepair::getTaskId, taskIds);
            List<MaintTaskRepair> maintTaskRepairs = maintTaskRepairMapper.selectList(wrapper);
            List<String> faultReasonIds = new ArrayList<>();
            List<String> faultMeasureIds = new ArrayList<>();
            for(MaintTaskRepair maintTaskRepair : maintTaskRepairs){
                if(null != maintTaskRepair.getFaultReasonIds() && maintTaskRepair.getFaultReasonIds().length > 0){
                    faultReasonIds.addAll(Arrays.asList(maintTaskRepair.getFaultReasonIds()));
                }
                if(null != maintTaskRepair.getFaultMeasuresIds() && maintTaskRepair.getFaultMeasuresIds().length > 0){
                    faultMeasureIds.addAll(Arrays.asList(maintTaskRepair.getFaultMeasuresIds()));
                }
            }
            Map<String, FaultKnowledgeResDto> faultReasonMap = commonGetHandler.getFaultKnowledgeMap(faultReasonIds, StaticValue.TWO);
            Map<String, FaultKnowledgeResDto> faultMeasureMap = commonGetHandler.getFaultKnowledgeMap(faultMeasureIds, StaticValue.THREE);
            for(MaintTaskRepair maintTaskRepair : maintTaskRepairs){
                MaintTaskFaultDto maintTaskFaultDto = new MaintTaskFaultDto();
                maintTaskFaultDto.setFaultReasonRemark(this.buildContent(maintTaskRepair.getFaultReasonIds(), maintTaskRepair.getFaultReasonRemark(), faultReasonMap));
                maintTaskFaultDto.setFaultMeasuresRemark(this.buildContent(maintTaskRepair.getFaultMeasuresIds(), maintTaskRepair.getFaultMeasuresRemark(), faultMeasureMap));
                map.put(maintTaskRepair.getTaskId(), maintTaskFaultDto);
            }
        }

        return map;
    }

    private String buildContent(String[] ids, String remark, Map<String, FaultKnowledgeResDto> faultMap){
        String faultRemark = null;
        if(null != ids && ids.length > 0) {
            for (String id : ids) {
                FaultKnowledgeResDto faultKnowledgeResDto = faultMap.get(id);
                if (ObjectUtils.isNull(faultKnowledgeResDto)){
                    continue;
                }
                if (StringUtils.isNotBlank(faultRemark)) {
                    faultRemark += ";" + faultKnowledgeResDto.getName();
                } else {
                    faultRemark = faultKnowledgeResDto.getName();
                }
            }
        }
        if(StringUtils.isNotBlank(faultRemark)){
            if(StringUtils.isNotBlank(remark)){
                faultRemark += ";" +  remark;
            }
        }else{
            faultRemark = remark;
        }
        return faultRemark;
    }
}
