package cn.getech.ehm.task.util;

import com.google.common.collect.Maps;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateExceptionHandler;

import java.io.StringWriter;
import java.util.Map;

/**
 * FREEMARKER 模板工具类
 * <AUTHOR>
 */
public class FreeMarkerUtil {

    private static final String TEMPLATES = "/templates";

    private static final String UTF_8 = "UTF-8";

    private static Map<String, Configuration> configurationCache = Maps.newConcurrentMap();

    public static Configuration getConfiguration(String templateFilePath){
        if(null != configurationCache.get(templateFilePath)){
            return configurationCache.get(templateFilePath);
        }
        Configuration config = new Configuration(Configuration.VERSION_2_3_25);
        config.setDefaultEncoding(UTF_8);
        config.setTemplateExceptionHandler(TemplateExceptionHandler.RETHROW_HANDLER);
        config.setLogTemplateExceptions(false);
        config.setClassForTemplateLoading(FreeMarkerUtil.class, TEMPLATES);
        configurationCache.put(templateFilePath, config);
        return config;
    }

    /**
     * 获取模板
     * @param templateName 模板名称
     * @param data 渲染模板的数据
     * @return
     */
    public static String getContent(String templateName, Map<String, String> data) {
        try {
            Configuration configuration = getConfiguration(TEMPLATES);
            Template template = configuration.getTemplate(templateName);
            StringWriter writer = new StringWriter();
            template.process(data, writer);
            writer.flush();
            return writer.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new RuntimeException("FreeMarkerUtil process fail", ex);
        }
    }

}
