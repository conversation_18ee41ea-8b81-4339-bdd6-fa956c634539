package cn.getech.ehm.task.controller;


import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.ehm.task.dto.task.history.MaintTaskHistoryQueryParam;
import cn.getech.ehm.task.dto.task.history.MaintTaskHistoryDto;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.ehm.task.service.IMaintTaskHistoryService;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 工单历史控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-05
 */
@RestController
@RequestMapping("/maintTaskHistory")
@Api(tags = "工单历史服务接口")
public class MaintTaskHistoryController {

    @Autowired
    private IMaintTaskHistoryService maintTaskHistoryService;

    /**
     * 分页获取工单历史列表
     */
    @ApiOperation("分页获取工单历史列表")
    @PostMapping("/list")
    //@Permission("maint:task:history:list")
    public RestResponse<PageResult<MaintTaskHistoryDto>> pageList(@Valid @RequestBody MaintTaskHistoryQueryParam maintTaskHistoryQueryParam){
        return RestResponse.ok(maintTaskHistoryService.pageDto(maintTaskHistoryQueryParam));
    }

    @ApiOperation("分页获取工单历史列表")
    @GetMapping("/list")
    //@Permission("maint:task:history:list")
    public RestResponse<PageResult<MaintTaskHistoryDto>> pageListGet(@RequestParam String processInstanceId){
        MaintTaskHistoryQueryParam maintTaskHistoryQueryParam = new MaintTaskHistoryQueryParam();
        maintTaskHistoryQueryParam.setProcessInstanceId(processInstanceId);
        maintTaskHistoryQueryParam.setLimit(100);
        return RestResponse.ok(maintTaskHistoryService.pageDto(maintTaskHistoryQueryParam));
    }

//    /**
//     * 新增工单历史
//     */
//    @ApiOperation("新增工单历史")
//    @AuditLog(title = "工单历史",desc = "新增工单历史",businessType = BusinessType.INSERT)
//    @PostMapping
//    //@Permission("maint:task:history:update")
//    public RestResponse<Boolean> add(@RequestBody @Valid MaintTaskHistoryAddParam maintTaskHistoryAddParam) {
//        return RestResponse.ok(maintTaskHistoryService.saveByParam(maintTaskHistoryAddParam));
//    }
//
//    /**
//     * 修改工单历史
//     */
//    @ApiOperation(value="修改工单历史")
//    @AuditLog(title = "工单历史",desc = "修改工单历史",businessType = BusinessType.UPDATE)
//    @PutMapping
//    //@Permission("maint:task:history:update")
//    public RestResponse<Boolean> update(@RequestBody @Valid MaintTaskHistoryEditParam maintTaskHistoryEditParam) {
//        return RestResponse.ok(maintTaskHistoryService.updateByParam(maintTaskHistoryEditParam));
//    }
//
//    /**
//     * 根据id删除工单历史
//     */
//    @ApiOperation(value="根据id删除工单历史")
//    @AuditLog(title = "工单历史",desc = "工单历史",businessType = BusinessType.DELETE)
//    @DeleteMapping("/{ids}")
//    //@Permission("maint:task:history:delete")
//    public RestResponse<Boolean> delete(@PathVariable("ids") @NotEmpty Long[] ids) {
//        return RestResponse.ok(maintTaskHistoryService.removeByIds(ids));
//    }
//
//    /**
//     * 根据id获取工单历史
//     */
//    @ApiOperation(value = "根据id获取工单历史")
//    @GetMapping(value = "/{id}")
//    //@Permission("maint:task:history:list")
//    public RestResponse<MaintTaskHistoryDto> get(@PathVariable  Long id) {
//        return RestResponse.ok(maintTaskHistoryService.getDtoById(id));
//    }
//
//    /**
//     * 导出工单历史列表
//     */
//    @ApiOperation(value = "导出工单历史列表")
//    @AuditLog(title = "工单历史",desc = "导出工单历史列表",businessType = BusinessType.EXPORT)
//    @GetMapping("/export")
//   // @Permission("maint:task:history:export")
//    public void excelExport(@Valid MaintTaskHistoryQueryParam maintTaskHistoryQueryParam, HttpServletResponse response){
//        PageResult<MaintTaskHistoryDto> pageResult  = maintTaskHistoryService.pageDto(maintTaskHistoryQueryParam);
//        ExcelUtils<MaintTaskHistoryDto> util = new ExcelUtils<>(MaintTaskHistoryDto.class);
//
//        util.exportExcel(pageResult.getRecords(), "工单历史",response);
//    }
//
//    /**
//     * Excel导入工单历史
//     */
//    @ApiOperation(value = "Excel导入工单历史")
//    @AuditLog(title = "工单历史",desc = "Excel导入工单历史",businessType = BusinessType.INSERT)
//    @PostMapping("/import")
//    //@Permission("maint:task:history:import")
//    public RestResponse<Boolean> excelImport(@RequestParam("file") MultipartFile file){
//        ExcelUtils<MaintTaskHistoryDto> util = new ExcelUtils<>(MaintTaskHistoryDto.class);
//        List<MaintTaskHistoryDto> rows = util.importExcel(file);
//        if (CollectionUtils.isEmpty(rows)){
//            return RestResponse.failed();
//        }
//        return RestResponse.ok(maintTaskHistoryService.saveDtoBatch(rows));
//    }

}
