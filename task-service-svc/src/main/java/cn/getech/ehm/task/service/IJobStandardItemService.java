package cn.getech.ehm.task.service;

import cn.getech.ehm.task.dto.job.*;
import cn.getech.ehm.task.entity.JobStandardItem;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.IBaseService;
import java.util.List;
import java.util.Map;

/**
 * 作业项目service
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
public interface IJobStandardItemService extends IBaseService<JobStandardItem> {

    /**
     * 分页查询
     * @param queryParam
     * @return
     */
    PageResult<JobStandardItemDto> pageList(ItemQueryParam queryParam);

    /**
     * 根据作业标准id查询集合
     * @param standardId
     * @return
     */
    List<JobStandardItemDto> getListByStandardId(String standardId);

    /**
     * 批量保存
     * @param itemDtos
     * @return
     */
    Boolean editItems(List<JobStandardItemDto> itemDtos, String standardId, Boolean add);

    /**
     * 删除
     * @param standardId
     * @return
     */
    Boolean deleteByStandardId(String standardId);

    /**
     * 获取作业标准时间map
     * @param standardIds
     * @return
     */
    Map<String, ItemCountDto> getCountMap(List<String> standardIds);

    /**
     * 获取列表中选择项
     * @return
     */
    Map<String, List<String>> getComboMap();

    /**
     * 导入作业项目
     *
     * @param rows
     * @return
     */
    JobStandardExcelResDto excelImport(List<StandardItemExcelDto> rows, String jobType);
}