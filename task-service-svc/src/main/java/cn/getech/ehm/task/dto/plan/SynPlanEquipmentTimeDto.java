package cn.getech.ehm.task.dto.plan;

import cn.getech.ehm.common.mp.handler.StringArrayTypeHandler;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;


/**
 * 可创建工单维保对象时间dto
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "SynPlanEquipmentTimeDto", description = "维保对象时间详情dto")
public class SynPlanEquipmentTimeDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "计划id")
    private String planId;

    @ApiModelProperty(value = "计划维护日期(天)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date planDay;

    @ApiModelProperty(value = "计划维护日期(小时:分钟)")
    @JsonFormat(pattern = "HH:mm", timezone = "GMT+8")
    private Date planTime;

    @ApiModelProperty(value = "维护人员id集合")
    private String[] maintainerIds;

    @ApiModelProperty(value = "维护班组id集合")
    private String[] teamIds;

    @ApiModelProperty(value = "人员指派策略 0 根据设备 1 人工配置 2 自由扫码")
    private Integer personStrategy;

    @TableField(typeHandler = StringArrayTypeHandler.class)
    @ApiModelProperty(value = "验收维护人员集合")
    private String[] acceptMaintainerIds;

    @TableField(typeHandler = StringArrayTypeHandler.class)
    @ApiModelProperty(value = "验收维护班组集合")
    private String[] acceptTeamIds;
}