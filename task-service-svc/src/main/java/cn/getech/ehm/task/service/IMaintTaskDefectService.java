package cn.getech.ehm.task.service;

import cn.getech.ehm.task.entity.MaintTaskDefect;
import cn.getech.poros.framework.common.service.IBaseService;
import cn.getech.ehm.task.dto.MaintTaskDefectQueryParam;
import cn.getech.ehm.task.dto.MaintTaskDefectAddParam;
import cn.getech.ehm.task.dto.MaintTaskDefectEditParam;
import cn.getech.ehm.task.dto.MaintTaskDefectDto;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <pre>
 * 缺陷记录 服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
public interface IMaintTaskDefectService extends IBaseService<MaintTaskDefect> {

    /**
     * 分页查询，返回Dto
     *
     * @param maintTaskDefectQueryParam
     * @return
     */
    PageResult<MaintTaskDefectDto> pageDto(MaintTaskDefectQueryParam maintTaskDefectQueryParam);

    /**
     * 保存
     * @param maintTaskDefectAddParam
     * @return
     */
    boolean saveByParam(MaintTaskDefectAddParam maintTaskDefectAddParam);

    /**
     * 根据id查询，转dto
     * @param id
     * @return
     */
    MaintTaskDefectDto getDtoById(String id);

    /**
     * 批量保存
     * @param rows
     */
    boolean saveDtoBatch(List<MaintTaskDefectDto> rows);

    /**
     * 更新
     * @param maintTaskDefectEditParam
     */
    boolean updateByParam(MaintTaskDefectEditParam maintTaskDefectEditParam);

    public void dealTaskDefect(String taskId);
}