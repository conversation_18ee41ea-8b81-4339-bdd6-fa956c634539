package cn.getech.ehm.task.dto.task.ticket;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 作业票内容
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "TaskTicketItemDto", description = "工单作业票内容")
public class TaskTicketItemDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "工单id")
    private String taskId;

    @ApiModelProperty(value = "安全确认内容")
    private String content;

    @ApiModelProperty(value = "内容类型1风险提示2安全措施")
    private Integer contentType;

    @ApiModelProperty(value = "是否确认")
    private Boolean confirm;
}