package cn.getech.ehm.task.dto.task.repair;

import cn.getech.ehm.base.dto.FaultKnowledgeResDto;
import cn.getech.ehm.common.mp.handler.StringArrayTypeHandler;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.util.List;


/**
 * 工单故障维修
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "MaintTaskFaultDto", description = "工单单故障维修返回数据模型")
public class MaintTaskFaultDto {

    @ApiModelProperty(value = "关联表id")
    private String id;

    @ApiModelProperty(value = "工单id")
    private String taskId;

    @ApiModelProperty(value = "实际停机开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date beginDowntime;

    @ApiModelProperty(value = "实际停机结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date endDowntime;

    @ApiModelProperty("停机耗时")
    private String downTimeCost;

    @ApiModelProperty(value = "是否停机(0未1是)")
    private Integer stopped;

    @ApiModelProperty(value = "实际维护开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date beginMaintTime;

    @ApiModelProperty(value = "实际维护结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date endMaintTime;

    @ApiModelProperty(value = "实际工时")
    private Long workingTime;

    @ApiModelProperty(value = "故障影响")
    @TableField(typeHandler = StringArrayTypeHandler.class)
    private String[] faultInfluences;

    @ApiModelProperty(value = "故障现象ids")
    @TableField(typeHandler = StringArrayTypeHandler.class)
    private String[] faultPhenomenonIds;

    @ApiModelProperty(value = "故障现象Dtos")
    private List<FaultKnowledgeResDto> faultPhenomenonDtos;

    @ApiModelProperty(value = "故障现象扩展")
    private String faultPhenomenonRemark;

    @ApiModelProperty(value = "故障原因ids")
    @TableField(typeHandler = StringArrayTypeHandler.class)
    private String[] faultReasonIds;

    @ApiModelProperty(value = "故障原因Dtos")
    private List<FaultKnowledgeResDto> faultReasonDtos;

    @ApiModelProperty(value = "故障原因扩展")
    private String faultReasonRemark;

    @ApiModelProperty(value = "处理措施ids")
    @TableField(typeHandler = StringArrayTypeHandler.class)
    private String[] faultMeasuresIds;

    @ApiModelProperty(value = "处理措施Dtos")
    private List<FaultKnowledgeResDto> faultMeasuresDtos;

    @ApiModelProperty(value = "处理措施扩展")
    private String faultMeasuresRemark;

    @ApiModelProperty(value = "附件id集合")
    @TableField(typeHandler = StringArrayTypeHandler.class)
    private String[] mediaIds;

    @TableField(typeHandler = StringArrayTypeHandler.class)
    private String[] faultStructureIds;

    private List<String> faultStructureNames;

    @ApiModelProperty(value = "LOTO标志1是2否")
    private Integer lotoFlag;

    @ApiModelProperty(value = "LOTO选项")
    private String lotoContent;

    @ApiModelProperty("损坏原因")
    private Integer damageReason;

    private String[] tmpFileIds;

    private String[] lotoFileIds;
}