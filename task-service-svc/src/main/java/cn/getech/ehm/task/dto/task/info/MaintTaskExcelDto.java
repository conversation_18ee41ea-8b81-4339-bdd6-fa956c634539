package cn.getech.ehm.task.dto.task.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 维护工单主表 返回数据模型
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "MaintTaskExcelDto", description = "MaintTaskExcelDto")
public class MaintTaskExcelDto {

    @ApiModelProperty(value = "工单名称/编号")
    private String codeAndName;

    @ApiModelProperty(value = "工单状态")
    private String status;

    @ApiModelProperty(value = "紧急程度")
    private String urgencyName;

    @ApiModelProperty(value = "专业类别")
    private String majorName;

    @ApiModelProperty("退回原因")
    private String returnReason;

    @ApiModelProperty(value = "设备名称")
    private String equipmentName;

    @ApiModelProperty(value = "设备编码")
    private String equipmentCode;

    @ApiModelProperty(value = "位置")
    private String parentAllName;

    @ApiModelProperty(value = "故障现象")
    private String faultPhenomenonRemark;

    @ApiModelProperty(value = "故障影响")
    private String faultInfluenceRemark;

    @ApiModelProperty(value = "故障原因")
    private String faultReasonRemark;

    @ApiModelProperty(value = "处理措施")
    private String faultMeasuresRemark;

    @ApiModelProperty("是否超期")
    private Boolean isOverTime;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty("工单截止时间")
    private Date taskDeadlineDate;

    @ApiModelProperty("工单开始时间")
    private Date beginMaintTime;

    @ApiModelProperty("工单结束时间")
    private Date endMaintTime;

    @ApiModelProperty("实际工时(去除挂起时间)分钟")
    private Long workingTime;

    @ApiModelProperty("实际工时(去除挂起时间)小时")
    private BigDecimal workingTimeHour;

    @ApiModelProperty("接单人/处理人name")
    private String handlerName;

    @ApiModelProperty(value = "报修人/创建人")
    private String createUserName;

    @ApiModelProperty("接单时间")
    private Date recTaskDate;

    //维保相关
    @ApiModelProperty("维保类型")
    private String jobTypeName;

    @ApiModelProperty("维保等级")
    private String jobLevelName;

    @ApiModelProperty(value = "设备状态")
    private String equipmentRunningStatusName;

    @ApiModelProperty(value = "计划维保日期时间")
    private Date planMaintTime;

    @ApiModelProperty("是否异常")
    private Boolean ecError;

    @ApiModelProperty("点检异常处理方式")
    private String ecErrorResultName;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    //缺陷相关
    @ApiModelProperty(value = "缺陷单名称")
    private String defectName;

    @ApiModelProperty("关闭原因/转缺陷原因")
    private String closeReason;

    @ApiModelProperty("故障类型")
    private String damageReason;

    @ApiModelProperty("是否有挂起")
    private Boolean hasHangUp;
}