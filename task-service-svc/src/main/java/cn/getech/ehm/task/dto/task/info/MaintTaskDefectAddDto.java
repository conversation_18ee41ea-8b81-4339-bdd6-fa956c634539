package cn.getech.ehm.task.dto.task.info;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 缺陷生成工单dto
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "MaintTaskDefectAddDto", description = "缺陷生成工单dto")
public class MaintTaskDefectAddDto {

    @ApiModelProperty(value = "维护设备id")
    private String equipmentId;

    @ApiModelProperty(value = "缺陷单id")
    private String sourceId;

    @ApiModelProperty(value = "工单编号")
    private String code;

    @ApiModelProperty(value = "工单名称")
    private String name;

    @ApiModelProperty(value = "工单类别")
    private String major;

    @ApiModelProperty(value = "紧急程度")
    private String urgency;

    @ApiModelProperty(value = "缺陷名称")
    private String content;

    @ApiModelProperty(value = "接单人")
    private String[] staffIds;

    @ApiModelProperty("是否需要自动分派当班人员")
    private Boolean needAutoCalander;

    @ApiModelProperty("工单截止时间")
    private Date taskDeadlineDate;

    @ApiModelProperty(value = "缺陷原因")
    private String defectReason;

    @ApiModelProperty(value = "截止日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date endTime;

    private Boolean defectFlag;

    private String sourceTaskId;

    private String sourceTaskCode;
}
