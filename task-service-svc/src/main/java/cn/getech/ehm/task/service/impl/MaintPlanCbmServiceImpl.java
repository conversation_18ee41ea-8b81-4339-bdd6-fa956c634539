package cn.getech.ehm.task.service.impl;

import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.iot.client.ParameterClient;
import cn.getech.ehm.iot.enmu.OperationType;
import cn.getech.ehm.task.dto.plan.CbmTriggerDetailDto;
import cn.getech.ehm.task.dto.plan.CbmTriggerMainDto;
import cn.getech.ehm.task.entity.MaintPlanCbm;
import cn.getech.ehm.task.mapper.MaintPlanCbmMapper;
import cn.getech.ehm.task.service.IMaintPlanCbmService;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MaintPlanCbmServiceImpl extends BaseServiceImpl<MaintPlanCbmMapper, MaintPlanCbm> implements IMaintPlanCbmService {

    @Autowired
    private MaintPlanCbmMapper cbmMapper;
    @Autowired
    private ParameterClient parameterClient;

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean saveByParam(List<CbmTriggerMainDto> cbmTriggerMainDtos, String planId, Boolean add){
        this.deleteByPlanId(planId);
        if(CollectionUtils.isNotEmpty(cbmTriggerMainDtos)){
            List<MaintPlanCbm> maintPlanCbms = new ArrayList<>();
            int sort = 0;
            for(CbmTriggerMainDto mainDto : cbmTriggerMainDtos){
                for(CbmTriggerDetailDto detailDto : mainDto.getDetailDtos()){
                    MaintPlanCbm maintPlanCbm = CopyDataUtil.copyObject(detailDto, MaintPlanCbm.class);
                    if(add){
                        maintPlanCbm.setId(null);
                    }
                    maintPlanCbm.setPlanId(planId);
                    maintPlanCbm.setGroupNum(mainDto.getGroupNum());
                    maintPlanCbm.setSort(sort++);
                    maintPlanCbms.add(maintPlanCbm);
                }
            }
            return this.saveBatch(maintPlanCbms);
        }
        return true;
    }

    private Boolean deleteByPlanId(String planId){
        LambdaQueryWrapper<MaintPlanCbm> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(MaintPlanCbm::getPlanId, planId);
        return this.remove(wrapper);
    }

    @Override
    public List<CbmTriggerMainDto> getCbmDetail(String planId){
        Map<String, List<CbmTriggerMainDto>> map = this.getCbmDetailMap(Arrays.asList(new String[]{planId}));
        return map.get(planId);
    }

    private Map<String, List<CbmTriggerMainDto>> getCbmDetailMap(List<String> planIds){
        Map<String, List<CbmTriggerMainDto>> map = new HashMap<>();

        LambdaQueryWrapper<MaintPlanCbm> wrapper = Wrappers.lambdaQuery();
        wrapper.in(MaintPlanCbm::getPlanId, planIds);
        wrapper.orderByAsc(MaintPlanCbm::getSort);
        List<MaintPlanCbm> maintPlanCbms = cbmMapper.selectList(wrapper);
        if(CollectionUtils.isNotEmpty(maintPlanCbms)){
            String[] categoryParamIds = maintPlanCbms.stream().map(MaintPlanCbm::getCategoryParameterId).distinct().toArray(String[] :: new );
            Map<String, String> categoryParamMap = new HashMap<>();
            RestResponse<Map<String, String>> restResponse = parameterClient.getCategoryParamNameMap(categoryParamIds);
            if(restResponse.isOk()){
                categoryParamMap = restResponse.getData();
            }else{
                log.error("获取类型参数名称失败");
            }
            Map<String, List<MaintPlanCbm>> entityMap = maintPlanCbms.stream().collect(Collectors.groupingBy(MaintPlanCbm::getPlanId));
            for(Map.Entry<String, List<MaintPlanCbm>> entity : entityMap.entrySet()) {
                List<CbmTriggerMainDto> mainDtos = new ArrayList<>();
                int groupNum = -1;
                CbmTriggerMainDto mainDto = null;
                List<CbmTriggerDetailDto> detailDtos = null;
                for (MaintPlanCbm maintPlanCbm : entity.getValue()) {
                    if (groupNum != maintPlanCbm.getGroupNum()) {
                        if (groupNum != -1) {
                            //剔除首次-1，其余组编号改变，保存一下
                            mainDto.setGroupNum(groupNum);
                            mainDto.setDetailDtos(detailDtos);
                            mainDtos.add(mainDto);
                        }
                        groupNum = maintPlanCbm.getGroupNum();
                        mainDto = new CbmTriggerMainDto();
                        detailDtos = new ArrayList<>();
                    }
                    CbmTriggerDetailDto detailDto = CopyDataUtil.copyObject(maintPlanCbm, CbmTriggerDetailDto.class);
                    detailDto.setName(categoryParamMap.get(detailDto.getCategoryParameterId()));
                    detailDtos.add(detailDto);
                }
                //保存最后一次
                mainDto.setGroupNum(groupNum);
                mainDto.setDetailDtos(detailDtos);
                mainDtos.add(mainDto);

                map.put(entity.getKey(), mainDtos);
            }
        }
        return map;
    }

    @Override
    public List<String> satisfyTriggerPlanId(List<String> planIds, String categoryParamId, Map<String, Double> paramValueMap){
        Map<String, List<CbmTriggerMainDto>> map = this.getCbmDetailMap(planIds);
        List<String> createOrderPlanIds = new ArrayList<>();
        for(Map.Entry<String, List<CbmTriggerMainDto>> entity : map.entrySet()){
            for(CbmTriggerMainDto mainDto : entity.getValue()){
                List<String> categoryParamIds = mainDto.getDetailDtos().stream().map(CbmTriggerDetailDto::getCategoryParameterId).distinct().collect(Collectors.toList());
                if(CollectionUtils.isEmpty(categoryParamIds) || !categoryParamIds.contains(categoryParamId)){
                    //该触发器不包含当前推数的参数
                    continue;
                }
                if(allSatisfy(mainDto.getDetailDtos(), paramValueMap)){
                    //全部满足触发器条件
                    createOrderPlanIds.add(entity.getKey());
                    break;
                }
            }
        }
        return createOrderPlanIds;
    }

    /**
     * 是否全部满足条件
     * @param detailDtos
     * @return
     */
    private Boolean allSatisfy(List<CbmTriggerDetailDto> detailDtos, Map<String, Double> paramValueMap){
        for(CbmTriggerDetailDto detailDto : detailDtos){
            Double value = paramValueMap.get(detailDto.getCategoryParameterId());
            if(null == value){
                return false;
            }
            if(!OperationType.countByValue(value, detailDto.getThresholdValue(), detailDto.getOperationType())){
                return false;
            }
        }
        return true;
    }
}
