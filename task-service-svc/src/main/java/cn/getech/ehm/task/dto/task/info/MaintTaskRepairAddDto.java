package cn.getech.ehm.task.dto.task.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 报修生成工单dto
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "MaintTaskRepairAddDto", description = "报修生成工单dto")
public class MaintTaskRepairAddDto{

    @ApiModelProperty(value = "维护设备id")
    private String equipmentId;

    @ApiModelProperty(value = "报修单id")
    private String sourceId;

    @ApiModelProperty(value = "专业")
    private String major;

    @ApiModelProperty(value = "紧急程度")
    private String urgency;

    @ApiModelProperty(value = "影响程度")
    private String influence;

    @ApiModelProperty(value = "故障现象")
    private String content;

    @ApiModelProperty(value = "故障现象ids")
    private String[] faultPhenomenonIds;

    @ApiModelProperty(value = "故障现象扩展")
    private String faultPhenomenonRemark;

    @ApiModelProperty(value = "故障原因ids")
    private String[] faultReasonIds;

    @ApiModelProperty(value = "故障原因扩展")
    private String faultReasonRemark;

    @ApiModelProperty(value = "处理措施ids")
    private String[] faultMeasuresIds;

    @ApiModelProperty(value = "处理措施扩展")
    private String faultMeasuresRemark;

    @ApiModelProperty(value = "附件id集合")
    private String mediaIds;

    private Date taskDeadlineDate;

    @ApiModelProperty("故障部位")
    private String[] faultStructureIds;

    @ApiModelProperty(value = "LOTO标志1是2否")
    private Integer lotoFlag;

    private String sourceTaskId;

    private String sourceTaskCode;

    private Integer sourceType;
}
