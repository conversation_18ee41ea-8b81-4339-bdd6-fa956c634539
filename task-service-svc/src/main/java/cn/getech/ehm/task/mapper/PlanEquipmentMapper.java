package cn.getech.ehm.task.mapper;

import cn.getech.ehm.task.dto.plan.PlanEquipmentDto;
import cn.getech.ehm.task.dto.plan.SynPlanEquipmentDto;
import cn.getech.ehm.task.entity.PlanEquipment;
import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 维保对象 Mapper 接口
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
@Repository
public interface PlanEquipmentMapper extends BaseMapper<PlanEquipment> {

    /**
     * 根据计划ids获取维保对象集合
     * @param planIds
     * @return
     */
    @SqlParser(filter = true)
    List<SynPlanEquipmentDto> getListByPlanIds(@Param("planIds") List<String> planIds);

    /**
     * 根据计划id获取维保对象集合
     * @param planId
     * @return
     */
    List<PlanEquipmentDto> getListByPlanId(@Param("planId") String planId);

    /**
     * 获取cbm计划单id
     * @param locationIds
     * @param categoryId
     * @param equipmentId
     * @return
     */
    List<String> getCbmPlanIds(@Param("locationIds") String[] locationIds, @Param("categoryId") String categoryId, @Param("equipmentId") String equipmentId, @Param("jobType") String jobType, @Param("categoryParamId") String categoryParamId);
}
