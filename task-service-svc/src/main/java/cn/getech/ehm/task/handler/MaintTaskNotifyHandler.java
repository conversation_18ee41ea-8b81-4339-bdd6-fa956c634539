/*
package cn.getech.ehm.task.handler;

import cn.getech.ehm.base.client.BaseServiceClient;
import cn.getech.ehm.system.client.NotifyClient;
import cn.getech.ehm.system.dto.notify.*;
import cn.getech.ehm.task.dto.task.notify.MaintTaskNotifyDto;
import cn.getech.ehm.task.enmu.MaintNotifyType;
import cn.getech.ehm.task.enmu.NotifyMethodType;
import cn.getech.ehm.task.enmu.NotifyObjectType;
import cn.getech.ehm.task.util.FreeMarkerUtil;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.permission.dto.PorosSecStaffDto;
import com.alibaba.excel.util.DateUtils;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MaintTaskNotifyHandler {
    @Value("${inspection.task.notify_url:https://admin.premaint.com/web-admin/maint/order/handle/?id=}")
    private String notifyUrl;
    @Autowired
    private BaseServiceClient baseServiceClient;
    @Autowired
    private NotifyClient notifyClient;
    @Autowired
    private CommonGetHandler commonGetHandler;

    public void sendEmail(List<String> uids, MaintTaskNotifyDto taskNotifyDto, String title){
        log.info("推送消息开始");
        try {
            List<PorosSecStaffDto> porosSecStaffDtos = commonGetHandler.getStaffMap(uids);
            if(CollectionUtils.isNotEmpty(porosSecStaffDtos)){
                EnumSet<NotifyType> notifyTypes = EnumSet.noneOf(NotifyType.class);
                NotifyParam notifyParam = NotifyParam.builder().build();
                List<String> emails = porosSecStaffDtos.stream().map(PorosSecStaffDto::getEmail).distinct().collect(Collectors.toList());
                //List<String> emails = Collections.singletonList("<EMAIL>");
                Map<String, String> map = new HashMap<>();
                //map.put("subject", subject);
                map.put("name", taskNotifyDto.getName());
                map.put("code", taskNotifyDto.getCode());
                map.put("status", statusName);
                map.put("deadlineTime", DateUtils.format(taskNotifyDto.getDeadlineTime(), "yyyy-MM-dd HH:mm:ss"));
                map.put("url", notifyUrl + taskNotifyDto.getId());
                String htmlContent = FreeMarkerUtil.getContent("InspectionTaskNotice.html", map);
                EmailNotify emailNotify = EmailNotify.builder().title(title).content(htmlContent).emails(emails.toArray(new String[emails.size()])).build();
                notifyParam.setEmailNotify(emailNotify);
                notifyTypes.add(NotifyType.EMAIL);
                notifyClient.sendNotify(NotifyParam.builder().notifyTypes(EnumSet.of(NotifyType.EMAIL)).emailNotify(emailNotify).build());
            } else {
                //log.error("未获取到用户列表");
            }
        }catch (Exception e){
            log.error("巡检任务单通知失败");
            log.debug(JSONObject.toJSONString(e));
        }

    }

    private List<String> getStaffUids(String[] staffIds){
        List<String> uids = Collections.emptyList();
        if(null != staffIds && staffIds.length > 0) {
            RestResponse<List<String>> personResponse = baseServiceClient.getUidsByIds(staffIds);
            if (personResponse.isOk()) {
                uids = personResponse.getData();
            } else {
                log.error("获取人员信息失败");
            }
        }
        return uids;
    }

    public void sendMaintNotify(InspectionTaskNotifyDto taskNotifyDto){
        MaintNotifyType maintNotifyType = MaintNotifyType.getEnumByValue(taskNotifyDto.getNotifyType());
        List<Integer> notifyMethods = Arrays.asList(taskNotifyDto.getNotifyMethods());
        List<Integer> notifyObjects = Arrays.asList(taskNotifyDto.getNotifyObjects());
        if(CollectionUtils.isNotEmpty(notifyMethods) && CollectionUtils.isNotEmpty(notifyObjects)){
            List<String> uids = new ArrayList<>();
            if(notifyObjects.contains(NotifyObjectType.INSPECTION_PERSON.getValue())){
                uids.addAll(this.getStaffUids(StringUtils.isNotBlank(taskNotifyDto.getAllStaffIds()) ? taskNotifyDto.getAllStaffIds().split(StringPool.COMMA) : null));
            }
            if(notifyObjects.contains(NotifyObjectType.CUSTOM.getValue())){
                if(null != taskNotifyDto.getCustomUids() && taskNotifyDto.getCustomUids().length > 0){
                    uids.addAll(Arrays.asList(taskNotifyDto.getCustomUids()));
                }
                if(StringUtils.isNotBlank(taskNotifyDto.getCustomRole())){
                    List<String> roleUids = porosHandler.getUserByRoleId(taskNotifyDto.getCustomRole()).stream().map(PorosSecStaffDto::getUid).distinct().collect(Collectors.toList());
                    uids.addAll(roleUids);
                }
            }
            if(CollectionUtils.isNotEmpty(uids)){
                List<String> distinctUids = uids.stream().distinct().collect(Collectors.toList());
                if(notifyMethods.contains(NotifyMethodType.EMAIL.getValue())){
                    String title = "[FAC]巡检任务" + maintNotifyType.getName();
                    this.sendCommonNotify(distinctUids, taskNotifyDto, title);
                }
            }else{
                log.info(taskNotifyDto.getId() + "--------" + MaintNotifyType.getNameByValue(taskNotifyDto.getNotifyType()) + "没有通知人员");
            }
        }else{
            log.error(taskNotifyDto.getId() + "---------通知配置有误");
        }
    }

}
*/
