package cn.getech.ehm.task.dto.task.info;

import cn.getech.poros.framework.common.param.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 维护工单报警关联查询查询参数对象
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "WarnQueryParam", description = "报警关联查询参数")
public class WarnQueryParam extends PageParam {

    @ApiModelProperty(value = "报警单id")
    private String equipmentWarnId;
}
