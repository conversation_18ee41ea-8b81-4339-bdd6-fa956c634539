package cn.getech.ehm.task.dto.task.item;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 工单作业项目统计
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "ItemTotalCountDto", description = "工单作业项目统计")
public class ItemTotalCountDto {

    @ApiModelProperty(value = "大类数量")
    private Integer largeCategoryCount  = 0;

    @ApiModelProperty(value = "小类数量")
    private Integer subCategoryCount = 0;

    @ApiModelProperty(value = "作业内容数量")
    private Integer contentCount = 0;

    @ApiModelProperty(value = "异常项数量")
    private Integer exceptionCount = 0;

    @ApiModelProperty(value = "标准工时统计")
    private BigDecimal standardTimeCount = new BigDecimal(0);

    @ApiModelProperty(value = "作业时间统计")
    private BigDecimal workingTimeCount = new BigDecimal(0);
}