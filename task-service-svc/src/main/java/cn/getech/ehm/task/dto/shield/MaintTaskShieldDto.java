package cn.getech.ehm.task.dto.shield;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;


/**
 * 工单屏蔽
 */
@Data
@ApiModel(value = "MaintTaskShieldDto", description = "工单屏蔽")
public class MaintTaskShieldDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "是否开启")
    private Boolean enabled;

    @ApiModelProperty(value = "按时间范围是否开启")
    private Boolean timeEnabled;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "按工作周选择过滤")
    private Integer[] weekDates;

    @ApiModelProperty(value = "按工单范围是否开启")
    private Boolean taskEnabled;

    @ApiModelProperty(value = "工单状态")
    private Integer[] taskStatus;

    @ApiModelProperty(value = "按故障影响是否开启")
    private Boolean faultEnabled;

    @ApiModelProperty(value = "故障影响")
    private String[] faultInfluences;

}