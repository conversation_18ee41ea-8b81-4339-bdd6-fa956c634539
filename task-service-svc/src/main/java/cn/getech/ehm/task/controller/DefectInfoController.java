package cn.getech.ehm.task.controller;


import cn.getech.ehm.base.client.BaseServiceClient;
import cn.getech.ehm.task.dto.defect.*;
import cn.getech.ehm.task.dto.task.info.DefectCountDto;
import cn.getech.ehm.task.entity.DefectInfo;
import cn.getech.ehm.task.service.IDefectInfoService;
import cn.getech.poros.framework.common.annotation.CreateByAndUpdateBy;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 缺陷管理控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-21
 */
@RestController
@RequestMapping("/defectInfo")
@Api(tags = "缺陷管理服务接口")
public class DefectInfoController {

    @Autowired
    private IDefectInfoService defectInfoService;
    @Autowired
    private BaseServiceClient baseServiceClient;
    private final static Integer pageSearchTypeAll = 1;
    private final static Integer pageSearchTypeMINE = 2;
    private final static Integer pageSearchTypeTODO = 3;

    /**
     * 分页获取缺陷列表
     */
    @ApiOperation("分页获取缺陷列表")
    @PostMapping("/list")
    @CreateByAndUpdateBy(value = true)
    //@Permission("defect:info:list")
    public RestResponse<PageResult<DefectInfoDto>> pageList(@RequestBody DefectInfoQueryParam defectInfoQueryParam) {
        return RestResponse.ok(defectInfoService.pageDto(defectInfoQueryParam, pageSearchTypeAll));
    }

    /**
     * 分页获取缺陷列表
     */
    @ApiOperation("分页获取缺陷列表-我的申报")
    @PostMapping("/list/mine")
    @CreateByAndUpdateBy(value = true)
    //@Permission("defect:info:list")
    public RestResponse<PageResult<DefectInfoDto>> pageListOfMine(@RequestBody DefectInfoQueryParam defectInfoQueryParam) {
        return RestResponse.ok(defectInfoService.pageDto(defectInfoQueryParam, pageSearchTypeMINE));
    }

    /**
     * 分页获取缺陷列表
     */
    @ApiOperation("分页获取缺陷列表-缺陷处理")
    @PostMapping("/list/todo")
    @CreateByAndUpdateBy(value = true)
    //@Permission("defect:info:list")
    public RestResponse<PageResult<DefectInfoDto>> pageListOfTodo(@RequestBody DefectInfoQueryParam defectInfoQueryParam) {
        return RestResponse.ok(defectInfoService.pageDto(defectInfoQueryParam, pageSearchTypeTODO));
    }

    /**
     * 新增缺陷管理
     */
    @ApiOperation("新增缺陷管理")
    @AuditLog(title = "缺陷管理", desc = "新增缺陷管理", businessType = BusinessType.INSERT)
    @PostMapping
    //@Permission("defect:info:update")
    public RestResponse<String> add(@RequestBody @Valid DefectInfoAddParam defectInfoAddParam) {
        return RestResponse.ok(defectInfoService.saveByParam(defectInfoAddParam));
    }

    /**
     * 修改缺陷管理
     */
    @ApiOperation(value = "修改缺陷管理")
    @AuditLog(title = "缺陷管理", desc = "修改缺陷管理", businessType = BusinessType.UPDATE)
    @PutMapping
    //@Permission("defect:info:update")
    public RestResponse<Boolean> update(@RequestBody @Valid DefectInfoEditParam defectInfoEditParam) {
        return RestResponse.ok(defectInfoService.updateByParam(defectInfoEditParam));
    }

    /**
     * 根据id删除缺陷管理
     */
    @ApiOperation(value = "根据id删除缺陷管理")
    @AuditLog(title = "缺陷管理", desc = "缺陷管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    //@Permission("defect:info:delete")
    public RestResponse<Boolean> delete(@PathVariable("ids") @NotEmpty String[] ids) {
        return RestResponse.ok(defectInfoService.removeByIds(Arrays.asList(ids)));
    }

    /**
     * 根据id获取缺陷管理
     */
    @ApiOperation(value = "根据id获取缺陷管理")
    @GetMapping(value = "/{id}")
    @CreateByAndUpdateBy(value = true)
    //@Permission("defect:info:list")
    public RestResponse<DefectInfoDto> get(@PathVariable String id) {
        return RestResponse.ok(defectInfoService.getDtoById(id));
    }

//    @ApiOperation("指派处理人-直接分配")
//    @PostMapping("/submit/dealperson")
//    public RestResponse<DefectInfo> submitDealPerson(@RequestBody @Valid DefectInfoSubmitDealPersonParam param) {
//        return RestResponse.ok(defectInfoService.submitDealPerson(param));
//    }


    @ApiOperation("获取指派处理人-创建工单时的工单编号")
    @GetMapping("/get/maint/code")
    public RestResponse getMaintCode() {
        return RestResponse.ok(defectInfoService.generateMaintCode());
    }


    @ApiOperation("指派处理人-创建工单")
    @PostMapping("/submit/dealperson/maint")
    public RestResponse<Boolean> submitDealPersonAndCreateMaint(@RequestBody @Valid DefectInfoSubmitDealPersonAndMaintParam param) {
        return RestResponse.ok(defectInfoService.submitDealPersonAndMaint(param));
    }

    /**
     * 新增缺陷管理
     */
    @ApiOperation("新增缺陷管理并创建工单")
    @AuditLog(title = "缺陷管理", desc = "新增缺陷管理", businessType = BusinessType.INSERT)
    @PostMapping("/add/maint")
    //@Permission("defect:info:update")
    public RestResponse<String> addAndCreateMaint(@RequestBody @Valid DefectInfoAddMaintParam defectInfoAddParam) {
        return RestResponse.ok(defectInfoService.saveByParamAndCreateMaint(defectInfoAddParam));
    }

    @ApiOperation("提交验收情况")
    @PostMapping("/check/submit")
    public RestResponse<Boolean> submitCheck(@RequestBody DefectInfoSubmitCheckParam defectInfoSubmitCheckParam) {
        return RestResponse.ok(defectInfoService.submitCheck(defectInfoSubmitCheckParam));
    }

    @ApiOperation("更新实际处理方案")
    @PostMapping("/update/realDealContent")
    public RestResponse<Boolean> updateRealDealContent(@RequestBody DefectInfoEditParam defectInfoEditParam) {
        return RestResponse.ok(defectInfoService.updateRealDealContent(defectInfoEditParam));
    }

    @ApiOperation("关闭缺陷")
    @PostMapping("/close")
    public RestResponse closeDefect(@RequestBody DefectInfoCloseParam defectInfoCloseParam) {
        defectInfoService.closeDefect(defectInfoCloseParam.getDefectId(), defectInfoCloseParam.getReason());
        return RestResponse.ok(true);
    }

    @ApiOperation("缺陷改为已处理")
    @PostMapping("/submit/done")
    public RestResponse<Boolean> submitDone(@RequestBody @Valid DefectInfoSubmitDoneParam defectInfoSubmitDoneParam) {
        return RestResponse.ok(defectInfoService.submitDone(defectInfoSubmitDoneParam));
    }

    @GetMapping("/get/count/app")
    @ApiOperation("获取权限及计数")
    public RestResponse<List<DefectCountDto>> getCountOfApp() {
        return RestResponse.ok(defectInfoService.getCountOfApp());
    }

    @ApiOperation("获取工作台缺陷计数")
    @GetMapping("/inner/workbench/count")
    public RestResponse<String> getCountOfStatistics(String param) {
        return RestResponse.ok(defectInfoService.getCountOfStatistics(param));
    }

    @ApiOperation("获取工作台缺陷计数-超时")
    @GetMapping("/inner/workbench/count/overtime")
    public RestResponse<String> getCountOfStatisticsOverTime(String param) {
        return RestResponse.ok(defectInfoService.getCountOfStatisticsOverTime(param));
    }

//    /**
//     * 导出缺陷管理列表
//     */
//    @ApiOperation(value = "导出缺陷管理列表")
//    @AuditLog(title = "缺陷管理",desc = "导出缺陷管理列表",businessType = BusinessType.EXPORT)
//    @GetMapping("/export")
//   // @Permission("defect:info:export")
//    public void excelExport(@Valid DefectInfoQueryParam defectInfoQueryParam, HttpServletResponse response){
//        PageResult<DefectInfoDto> pageResult  = defectInfoService.pageDto(defectInfoQueryParam);
//        ExcelUtils<DefectInfoDto> util = new ExcelUtils<>(DefectInfoDto.class);
//
//        util.exportExcel(pageResult.getRecords(), "缺陷管理",response);
//    }

//    /**
//     * Excel导入缺陷管理
//     */
//    @ApiOperation(value = "Excel导入缺陷管理")
//    @AuditLog(title = "缺陷管理",desc = "Excel导入缺陷管理",businessType = BusinessType.INSERT)
//    @PostMapping("/import")
//    //@Permission("defect:info:import")
//    public RestResponse<Boolean> excelImport(@RequestParam("file") MultipartFile file){
//        ExcelUtils<DefectInfoDto> util = new ExcelUtils<>(DefectInfoDto.class);
//        List<DefectInfoDto> rows = util.importExcel(file);
//        if (CollectionUtils.isEmpty(rows)){
//            return RestResponse.failed();
//        }
//        return RestResponse.ok(defectInfoService.saveDtoBatch(rows));
//    }

}
