package cn.getech.ehm.task.dto.historyInfo;

import cn.getech.poros.framework.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * <pre>
 *  返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-12-06
 */
@Data
@ApiModel(value = "EquipmentHistoryInfoExcelDto", description = "返回数据模型")
public class EquipmentHistoryInfoExcelDto {


    @ApiModelProperty("序号")
    @Excel(name = "序号")
    private String no;

    @ApiModelProperty("工单code")
    @Excel(name = "工单编码")
    private String taskCode;

    @ApiModelProperty("工单名称")
    @Excel(name = "工单名称")
    private String taskName;

    @ApiModelProperty("变更前设备名称")
    @Excel(name = "变更前设备名称")
    private String oldEquipmentName;

    @ApiModelProperty("变更前设备code")
    @Excel(name = "变更前设备编码")
    private String oldEquipmentCode;


    @ApiModelProperty("变更后设备名称")
    @Excel(name = "变更后设备名称")
    private String newEquipmentName;

    @ApiModelProperty("变更后设备code")
    @Excel(name = "变更后设备编码")
    private String newEquipmentCode;


    @ApiModelProperty("变更原因")
    @Excel(name = "变更原因")
    private String remark;

    @ApiModelProperty("变更时间")
    @Excel(name = "变更时间")
    private String createTime;

}