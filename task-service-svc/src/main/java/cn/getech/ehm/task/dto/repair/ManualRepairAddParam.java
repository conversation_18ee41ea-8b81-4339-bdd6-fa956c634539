package cn.getech.ehm.task.dto.repair;

import cn.getech.ehm.task.enums.TaskSourceType;
import cn.getech.poros.framework.common.param.ApiParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

/**
 * 人工报修 新增参数
 *
 * <AUTHOR>
 * @date 2020-08-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ManualRepair新增", description = "人工报修新增参数")
public class ManualRepairAddParam extends ApiParam {

    @ApiModelProperty(value = "设备id")
    private String equipmentId;

    @ApiModelProperty(value = "故障类别")
    private String faultType;

    @ApiModelProperty(value = "专业")
    private String major;

    @ApiModelProperty(value = "紧急程度")
    private String urgency;

    @ApiModelProperty(value = "影响程度")
    private String influence;

    @ApiModelProperty(value = "故障现象ids")
    private String[] faultPhenomenonIds;

    @ApiModelProperty(value = "故障现象扩展")
    private String faultPhenomenonRemark;

    @ApiModelProperty(value = "故障原因ids")
    private String[] faultReasonIds;

    @ApiModelProperty(value = "故障原因扩展")
    private String faultReasonRemark;

    @ApiModelProperty(value = "处理措施ids")
    private String[] faultMeasuresIds;

    @ApiModelProperty(value = "处理措施扩展")
    private String faultMeasuresRemark;

    @ApiModelProperty(value = "附件id集合")
    private String mediaIds;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "故障日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date faultTime;

    //截止日期
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deadlineDate;

    @ApiModelProperty(value = "告警id")
    private String equipmentWarnId;

    private String sourceMaintTaskId;

    @ApiModelProperty("故障部位")
    private String[] faultStructureIds;

    private String sourceTaskId;

    private String sourceTaskCode;

    private Integer sourceType = TaskSourceType.BREAKDOWN.getValue();

    private String defectId;
}