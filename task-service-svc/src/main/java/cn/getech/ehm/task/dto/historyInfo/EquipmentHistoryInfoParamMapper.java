package cn.getech.ehm.task.dto.historyInfo;

import cn.getech.ehm.task.entity.EquipmentHistoryInfo;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.bean.BaseEntity;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <pre>
 *  参数实体映射
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-12-06
 */
@Mapper(componentModel = "spring",imports = {BaseEntity.class})
public interface  EquipmentHistoryInfoParamMapper{

    /**
     * 新增参数转换为实体
     *
     * @param equipmentHistoryInfoAddParam
     * @return
     */
    EquipmentHistoryInfo addParam2Entity(EquipmentHistoryInfoAddParam equipmentHistoryInfoAddParam);

    /**
     * 编辑参数转换为实体
     * @param equipmentHistoryInfoEditParam
     * @return
     */
    EquipmentHistoryInfo editParam2Entity(EquipmentHistoryInfoEditParam equipmentHistoryInfoEditParam);

    /**
     * 实体转换为Dto
     * @param equipmentHistoryInfo
     * @return
     */
    EquipmentHistoryInfoDto entity2Dto(EquipmentHistoryInfo equipmentHistoryInfo);

    /**
     * 分页实体转DTO
     * @param page
     * @return
     */
    PageResult<EquipmentHistoryInfoDto> pageEntity2Dto(PageResult<EquipmentHistoryInfo> page);


    /**
     * dto集合转entity集合
     * @param rows
     * @return
     */
    List<EquipmentHistoryInfo> dtoList2Entity(List<EquipmentHistoryInfoDto> rows);

}
