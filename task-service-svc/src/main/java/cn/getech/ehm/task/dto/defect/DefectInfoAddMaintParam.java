package cn.getech.ehm.task.dto.defect;

import cn.getech.poros.framework.common.param.ApiParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <pre>
 * 缺陷信息 新增参数
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-02-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DefectInfoAddMaintParam", description = "缺陷信息新增参数")
public class DefectInfoAddMaintParam extends ApiParam {

    @ApiModelProperty(value = "设备id",required = true)
    @NotBlank(message = "必须选择设备")
    private String equipmentId;
    @ApiModelProperty(value = "缺陷名称",required = true)
    //@NotBlank(message = "缺陷名称必填")
    private String defectName;
    @ApiModelProperty(value = "缺陷内容",required = true)
    private String defectContent;
    @ApiModelProperty(value = "影响描述")
    private String affectContent;
    @ApiModelProperty(value = "缺陷种类",required = true)
    private Integer defectType;
    @ApiModelProperty(value = "专业类别",required = true)
    private Integer majorType;
    @ApiModelProperty(value = "现场图片视频")
    private String[] liveMediaIds;


    //下为新增字段
    @ApiModelProperty(value = "截止日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @NotNull
    private Date endTime;

    @ApiModelProperty(value = "缺陷处理人ids")
    private String[] dealPersonIds;

    @ApiModelProperty(value = "缺陷处理人-工单缺陷处理人 ids")
    private String[] maintDealPersonIds;

    @ApiModelProperty(value = "缺陷处理人-工单缺陷处理人 ids")
    private String[] staffIds;

    @ApiModelProperty("是否需要自动分派当班人员")
    private Boolean needAutoCalander;

    @ApiModelProperty(value = "建议处理方案")
    private String suggestDealContent;

    @ApiModelProperty(value = "工单编号")
    private String maintCode;

    @ApiModelProperty(value = "工单名称")
    @NotBlank(message = "工单名称不能为空")
    private String maintName;

    @ApiModelProperty(value = "工单类别")
    @NotBlank(message = "工单类别不能为空")
    private String maintType;

    @ApiModelProperty(value = "工单紧急程度")
    @NotBlank(message = "工单紧急程度不能为空")
    private String maintUrgency;

    @ApiModelProperty(value = "工单截止日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @NotNull(message = "工单截止日期不能为空")
    private Date maintEndTIme;

    @ApiModelProperty(value = "缺陷单id")
    private String defectInfoId;

    @ApiModelProperty(value = "缺陷原因")
    private String defectReason;
}