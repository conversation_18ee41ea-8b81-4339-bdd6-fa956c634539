package cn.getech.ehm.task.dto.task.info;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;


/**
 * 维护工单主表 返回数据模型
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "MaintTaskStatisticsDto", description = "MaintTaskStatisticsDto")
public class MaintTaskStatisticsDto {

    @TableField("begin_maint_time")
    private Date beginMaintTime;

    /**
     * 实际维护结束时间
     */
    @TableField("end_maint_time")
    private Date endMaintTime;

    private String endMaintTimeTemp;

    private Date createTime;
}