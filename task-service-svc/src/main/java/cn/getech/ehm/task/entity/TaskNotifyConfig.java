package cn.getech.ehm.task.entity;

import cn.getech.ehm.common.mp.handler.StringArrayTypeHandler;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 工单通知配置
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("maint_task_notify_config")
public class TaskNotifyConfig extends BaseEntity {

    private static final long serialVersionUID=1L;

    @TableField("deleted")
    private Integer deleted;

    @TableField("tenant_id")
    private String tenantId;

    /**
     * 工单类型
     */
    @TableField(value = "task_type",typeHandler = StringArrayTypeHandler.class)
    private String[] taskType;

    /**
     * 工单状态类型
     */
    @TableField(value = "task_status_type",typeHandler = StringArrayTypeHandler.class)
    private String[] taskStatusType;

    /**
     * 提醒方式
     */
    @TableField(value = "notify_type",typeHandler = StringArrayTypeHandler.class)
    private String[] notifyType;

    /**
     * 开始时间
     */
    @TableField("begin_time")
    private Date beginTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    private Date endTime;


}
