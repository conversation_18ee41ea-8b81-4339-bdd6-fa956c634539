package cn.getech.ehm.task.service.impl;

import cn.getech.ehm.base.client.BaseServiceClient;
import cn.getech.ehm.base.dto.*;
import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.common.context.UserContextHolder;
import cn.getech.ehm.common.dto.EnumListDto;
import cn.getech.ehm.common.enums.DeletedType;
import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.common.util.ExcelUtil;
import cn.getech.ehm.common.util.SortUtil;
import cn.getech.ehm.equipment.client.EquipmentClient;
import cn.getech.ehm.equipment.client.PorosClient;
import cn.getech.ehm.equipment.dto.*;
import cn.getech.ehm.equipment.enums.RunningStatusType;
import cn.getech.ehm.system.client.NotifyClient;
import cn.getech.ehm.system.dto.notify.*;
import cn.getech.ehm.task.dto.*;
import cn.getech.ehm.task.dto.activiti.AuditActivitiServiceResult;
import cn.getech.ehm.task.dto.activiti.PartTaskAuditParam;
import cn.getech.ehm.task.dto.defect.DefectInfoAddParam;
import cn.getech.ehm.task.dto.defect.DefectInfoDto;
import cn.getech.ehm.task.dto.plan.MaintPlanCountDto;
import cn.getech.ehm.task.dto.repair.ManualRepairAddParam;
import cn.getech.ehm.task.dto.repair.ManualRepairEditParam;
import cn.getech.ehm.task.dto.screen.*;
import cn.getech.ehm.task.dto.shield.MaintTaskShieldDto;
import cn.getech.ehm.task.dto.task.assist.MaintTaskAssistListDto;
import cn.getech.ehm.task.dto.task.info.*;
import cn.getech.ehm.task.dto.task.item.MaintTaskItemDto;
import cn.getech.ehm.task.dto.task.item.TaskItemDetailDto;
import cn.getech.ehm.task.dto.task.item.TaskItemEditParam;
import cn.getech.ehm.task.dto.task.notify.MaintTaskNotifyDto;
import cn.getech.ehm.task.dto.task.part.MaintTaskPartDto;
import cn.getech.ehm.task.dto.task.part.TaskPartDto;
import cn.getech.ehm.task.dto.task.part.TaskPartEditParam;
import cn.getech.ehm.task.dto.task.performance.*;
import cn.getech.ehm.task.dto.task.process.SubmitType;
import cn.getech.ehm.task.dto.task.process.TaskSubmitParam;
import cn.getech.ehm.task.dto.task.repair.MaintTaskFaultDto;
import cn.getech.ehm.task.dto.task.repair.TaskFaultEditParam;
import cn.getech.ehm.task.dto.task.ticket.TaskTicketDto;
import cn.getech.ehm.task.dto.task.ticket.TaskTicketItemDto;
import cn.getech.ehm.task.dto.ticket.JobTicketDto;
import cn.getech.ehm.task.dto.ticket.JobTicketItemDto;
import cn.getech.ehm.task.enmu.MaintNotifySourceType;
import cn.getech.ehm.task.enmu.PlanJobLevelType;
import cn.getech.ehm.task.enmu.TaskShieldWeekType;
import cn.getech.ehm.task.enmu.TaskWorkshopType;
import cn.getech.ehm.task.entity.*;
import cn.getech.ehm.task.enums.*;
import cn.getech.ehm.task.handler.ActivitiLocalHandler;
import cn.getech.ehm.task.handler.CommonGetHandler;
import cn.getech.ehm.task.handler.FileGetHandler;
import cn.getech.ehm.task.handler.StartMaintTaskHandler;
import cn.getech.ehm.task.mapper.MaintTaskMapper;
import cn.getech.ehm.task.service.*;
import cn.getech.poros.bpm.param.process.ProcessStartParam;
import cn.getech.poros.bpm.param.task.MultiTransferParam;
import cn.getech.poros.bpm.param.task.ProcessTaskParam;
import cn.getech.poros.bpm.param.task.TaskCompleteParam;
import cn.getech.poros.bpm.param.task.TaskOptionParam;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.api.ResultCode;
import cn.getech.poros.framework.common.bean.BaseEntity;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.framework.common.context.PorosContextHolder;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.poros.framework.common.utils.DateUtils;
import cn.getech.poros.framework.common.utils.ExcelUtils;
import cn.getech.poros.permission.client.PorosSecGrantClient;
import cn.getech.poros.permission.client.PorosSecStaffClient;
import cn.getech.poros.permission.dto.PorosSecStaffDto;
import cn.getech.poros.permission.dto.SecStaffUidParam;
import cn.getech.poros.permission.dto.StaffBaseInfoDto;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.ArrayUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 维护工单主表 服务实现类
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
@Slf4j
@Service
public class MaintTaskServiceImpl extends BaseServiceImpl<MaintTaskMapper, MaintTask> implements IMaintTaskService {

    @Value("${flow.maint.code:E0004_rx}")
    private String maintFlowCode;

    @Value("${task.receive.auto:false}")
    private Boolean taskReceiveAuto;

    /**
     * 工单验收员角色
     */
    @Value("${fixed.role.evaluate.code:web-admin_evaluate}")
    private String evaluateRoleCode;
    /**
     * 派单员
     */
    @Value("${fixed.role.assing.code:web-admin_assing}")
    private String assingRoleCode;
    /**
     * 派单审核员
     */
    @Value("${fixed.role.dispatcher.code:web-admin_dispatcher}")
    private String dispatcherRoleCode;

    @Autowired
    private PorosSecGrantClient porosSecGrantClient;
    @Autowired
    private MaintTaskMapper maintTaskMapper;
    @Autowired
    private EquipmentClient equipmentClient;
    @Autowired
    @Lazy
    private IMaintTaskItemService taskItemService;
    @Autowired
    @Lazy
    private IMaintTaskPartService taskPartRelService;
    @Autowired
    @Lazy
    private IMaintTaskRepairService taskRepairService;
    @Autowired
    private BaseServiceClient baseServiceClient;
    @Autowired
    @Lazy
    private IManualRepairService manualRepairService;
    @Autowired
    private ResourceBundleMessageSource messageSource;
    @Autowired
    private NotifyClient notifyClient;
    @Autowired
    private PorosSecStaffClient porosSecStaffClient;
    @Autowired
    private PorosClient porosClient;
    @Autowired
    @Lazy
    private IPlanEquipmentTimeService planEquipmentTimeService;
    @Autowired
    @Lazy
    private IJobTicketService jobTicketService;
    @Autowired
    @Lazy
    private IMaintTaskTicketService taskTicketService;
    @Autowired
    @Lazy
    private IDefectInfoService defectInfoService;
    @Autowired
    @Lazy
    private IMaintTaskConfigService maintTaskConfigService;

    @Autowired
    @Lazy
    private IMaintTaskRepairService maintTaskRepairService;

    @Autowired
    @Lazy
    private IMaintTaskAssistPersonService assistPersonService;

    @Autowired
    @Lazy
    private ITriggerTimeService triggerTimeService;

    @Autowired
    private ActivitiLocalHandler activitiHandler;

    @Autowired
    private FileGetHandler fileGetHandler;

    @Autowired
    private CommonGetHandler commonGetHandler;
    @Autowired
    @Lazy
    private IMaintPlanService maintPlanService;

    @Autowired
    @Lazy
    private IActOvertimeInfoService actOvertimeInfoService;

    @Autowired
    @Lazy
    IEquipmentHistoryInfoService equipmentHistoryInfoService;
    @Autowired
    private IMaintTaskShieldService shieldService;
    @Autowired
    private IMaintNotifyService notifyService;

    @Value("${aliyun.sms.default-sign:Premaint}")
    private String defaultSign;

    @Value("${aliyun.sms.templates.task:SMS_215338506}")
    private String taskTemplate;

    @Autowired
    private ExecutorService executorService;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    @Lazy
    private StartMaintTaskHandler startMaintTaskHandler;
    @Autowired
    @Lazy
    private IMaintTaskDefectService maintTaskDefectService;
    @Autowired
    @Lazy
    IMaintTaskItemService maintTaskItemService;
    @Autowired
    private IMaintTaskAnalystService maintTaskAnalystService;

    public static List<Integer> finishStatusList = Arrays.asList(TaskStatusType.CHECK_ACCEPT.getValue(),
            TaskStatusType.CLOSED.getValue(),
            TaskStatusType.EXCEPTION_CLOSED.getValue());

    @Override
    public PageResult<MaintTaskPageDto> pageDto(MaintTaskQueryParam param) {
        Wrapper<MaintTask> wrapper = getPageSearchWrapper(param);
        PageResult<MaintTask> result = page(param, wrapper);
        List<MaintTaskPageDto> maintTaskPageDtos = CopyDataUtil.copyList(result.getRecords(), MaintTaskPageDto.class);
        if (CollectionUtils.isNotEmpty(maintTaskPageDtos)) {
            List<String> equipmentIds = Lists.newArrayList();
            List<String> uids = Lists.newArrayList();
            //List<String> teamIds = Lists.newArrayList();
            List<String> taskIds = Lists.newArrayList();
            for (MaintTaskPageDto dto : maintTaskPageDtos) {
                equipmentIds.add(dto.getEquipmentId());
                if (StringUtils.isNotBlank(dto.getHandler())) {
                    uids.add(dto.getHandler());
                }
                uids.add(dto.getCreateBy());
                /*if(null != dto.getTeamIds() && dto.getTeamIds().length > 0){
                    teamIds.addAll(Arrays.asList(dto.getTeamIds()));
                }*/
                taskIds.add(dto.getId());
            }

            Map<String, EquipmentListDto> equipmentMap = commonGetHandler.getEquipmentByIds(equipmentIds);
            Map<String, String> uidNameMap = commonGetHandler.getUidNameMap(uids);
            //Map<String, MaintTeamDto> teamById = commonGetHandler.getTeamById(teamIds);
            //获取辅助人员名称
            //Map<String, List<MaintTaskAssistListDto>> useNameMaps =  assistPersonService.getAssistNameList(taskIds).stream().collect(Collectors.groupingBy(MaintTaskAssistListDto::getTaskId));
            //获取故障原因/处理措施
            Map<String, MaintTaskFaultDto> taskFaultMap = (null != param.getSourceType() && param.getSourceType() == TaskSourceType.BREAKDOWN.getValue()) ? maintTaskRepairService.getFaultDtoByTaskIds(taskIds) : Maps.newHashMap();
            Map<String, String> defectNameMap = (null != param.getSourceType() && param.getSourceType() == TaskSourceType.DEFECT.getValue()) ? defectInfoService.getNameByTaskId(taskIds) : Maps.newHashMap();
            Map<String, String> faultInfluenceNameMap = commonGetHandler.getDictNameMap("fault_influence");

            Date now = new Date();
            for (MaintTaskPageDto dto : maintTaskPageDtos) {
                /*if (CollectionUtils.isNotEmpty(useNameMaps.get(dto.getId()))) {
                    dto.setUserName(joinUserName(dto.getId(), useNameMaps));
                } else {
                    dto.setUserName(null);
                }*/
                if (dto.getType() != null) {
                    if (dto.getSourceType() == TaskSourceType.BREAKDOWN.getValue()) {
                        dto.setName(dto.getName().replace("故障单", "维修单"));
                        dto.setFaultPhenomenonRemark(dto.getContent());
                        MaintTaskFaultDto maintTaskFaultDto = taskFaultMap.get(dto.getId());
                        if (null != maintTaskFaultDto) {
                            dto.setFaultReasonRemark(maintTaskFaultDto.getFaultReasonRemark());
                            dto.setFaultMeasuresRemark(maintTaskFaultDto.getFaultMeasuresRemark());
                        }
                    } else if (dto.getSourceType() == TaskSourceType.DEFECT.getValue()) {
                        dto.setDefectName(defectNameMap.get(dto.getId()));
                    }
                }
                if (null != dto.getWorkingTime()) {
                    dto.setWorkingTimeHour(new BigDecimal(dto.getWorkingTime()).divide(new BigDecimal(60), 2, BigDecimal.ROUND_HALF_UP));
                    if (dto.getWorkingTimeHour().compareTo(BigDecimal.ZERO) == 0) {
                        dto.setWorkingTimeHour(null);
                    }
                }
                dto.setStatusName(TaskStatusType.getNameByValue(dto.getStatus()));
                //dto.setMaintTeamName(commonGetHandler.getTeamName(teamById, dto.getTeamIds()));
                EquipmentListDto equipment = equipmentMap.get(dto.getEquipmentId());
                if (null != equipment) {
                    dto.setEquipmentName(equipment.getEquipmentName());
                    dto.setEquipmentCode(equipment.getEquipmentCode());
                    dto.setPicId(equipment.getPicId());
                    dto.setPicUrl(equipment.getPicUrl());
                    dto.setParentAllName(equipment.getParentAllName());
                    dto.setCategoryAllName(equipment.getCategoryAllName());
                    dto.setEquipmentRunningStatus(equipment.getRunningStatus());
                }
                dto.setCreateByUid(dto.getCreateBy());
                dto.setCreateBy(uidNameMap.get(dto.getCreateBy()));

                /*if (StringUtils.isNotBlank(dto.getAllStaffIds())) {
                    dto.setStaffNames(buildStaffNames(dto.getAllStaffIds().split(StringPool.COMMA)));
                }*/
                if (null != dto.getTaskDeadlineDate() && !finishStatusList.contains(dto.getStatus())) {
                    dto.setIsOverTime(dto.getTaskDeadlineDate().compareTo(now) < 0);
                }
                dto.setHandlerName(StringUtils.isNotBlank(dto.getHandler()) ? uidNameMap.get(dto.getHandler()) : null);
                dto.setTypeName(TaskType.getNameByValue(dto.getType()));
                dto.setFaultInfluenceRemark(this.buildFaultInfluenceRemark(faultInfluenceNameMap, dto.getFaultInfluences()));
                dto.setEcError((null != dto.getEcErrorNum() && dto.getEcErrorNum() > 0) ? true : false);
                dto.setEcErrorResultName(null != dto.getEcErrorResult() ? EcErrorResultType.getNameByValue(dto.getEcErrorResult()) : EcErrorResultType.NODEAL.getName());
                dealTimeCost(dto);
            }
        }
        return Optional.ofNullable(PageResult.<MaintTaskPageDto>builder()
                        .records(maintTaskPageDtos)
                        .total(result.getTotal())
                        .build())
                .orElse(new PageResult<>());
    }

    private String joinUserName(String id, Map<String, List<MaintTaskAssistListDto>> useNameMaps) {
        List<MaintTaskAssistListDto> list = useNameMaps.get(id);
        StringBuffer stringBuffer = new StringBuffer();
        for (int i = 0; i < list.size(); i++) {
            String useName = list.get(i).getUserName();
            if (i == 0) {
                stringBuffer.append(useName);
            } else {
                stringBuffer.append("," + useName);
            }
        }
        return stringBuffer.toString();
    }

    public void dealTimeCost(MaintTaskPageDto dto) {
        if (StringUtils.isBlank(dto.getMaintTimeCost()) && dto.getBeginMaintTime() != null && dto.getEndMaintTime() != null) {
            dto.setMaintTimeCost("" + DateUtil.between(dto.getBeginMaintTime(), dto.getEndMaintTime(), DateUnit.MINUTE));
        }
        if (StringUtils.isBlank(dto.getDownTimeCost()) && dto.getBeginDowntime() != null && dto.getEndDowntime() != null) {
            dto.setDownTimeCost("" + DateUtil.between(dto.getBeginDowntime(), dto.getEndDowntime(), DateUnit.MINUTE));
        }
    }

    public List<TaskCountDto> getCount(Boolean ourTask, String type) {
        List<TaskCountDto> taskCountDtos = new ArrayList<>();
        CommonConfigDto commonConfigDto = jobTicketService.commonConfig();
        //查询除已关闭、异常关闭的工单总数
        LambdaQueryWrapper<MaintTask> notClosedWrapper = buildWrapper(Wrappers.lambdaQuery(), null, ourTask);
        notClosedWrapper.eq(StringUtils.isNotBlank(type), MaintTask::getType, type);


        notClosedWrapper = buildTaskShieldWrapper(notClosedWrapper);
        //已关闭、异常关闭
        LambdaQueryWrapper<MaintTask> closedWrapper = buildWrapper(Wrappers.lambdaQuery(), TaskAppStatusType.CLOSED.getValue(), ourTask);
        closedWrapper.eq(StringUtils.isNotBlank(type), MaintTask::getType, type);
        closedWrapper = buildTaskShieldWrapper(closedWrapper);
        BuildInfoSearchDto buildInfoSearchDto = commonGetHandler.getCurrentUserInfoIds();
        if (null == buildInfoSearchDto || (buildInfoSearchDto.getFlag() && CollectionUtils.isEmpty(buildInfoSearchDto.getEquipmentIds()))) {
            log.error("获取设备失败");
            notClosedWrapper.eq(MaintTask::getEquipmentId, "-1");
            closedWrapper.eq(MaintTask::getEquipmentId, "-1");
        } else {
            notClosedWrapper.in(CollectionUtils.isNotEmpty(buildInfoSearchDto.getEquipmentIds()), MaintTask::getEquipmentId, buildInfoSearchDto.getEquipmentIds());
            closedWrapper.in(CollectionUtils.isNotEmpty(buildInfoSearchDto.getEquipmentIds()), MaintTask::getEquipmentId, buildInfoSearchDto.getEquipmentIds());
        }
        notClosedWrapper.select(MaintTask::getId, MaintTask::getStatus);
        //索引优化
        notClosedWrapper.ne(MaintTask::getStatus, TaskStatusType.CLOSED.getValue()).ne(MaintTask::getStatus, TaskStatusType.EXCEPTION_CLOSED.getValue());
        closedWrapper.in(MaintTask::getStatus, TaskStatusType.CLOSED.getValue(), TaskStatusType.EXCEPTION_CLOSED.getValue());
        if (StringUtils.isNotBlank(type)) {
            if (type.equals("" + TaskType.BREAKDOWN.getValue())) {

            } else {
//                List<String> uidsOfDept = commonGetHandler.getUidsOfDept();
//                notClosedWrapper.in(MaintTask::getCreateBy, uidsOfDept);
//                closedWrapper.in(MaintTask::getCreateBy, uidsOfDept);
            }
        }
        Map<Integer, Long> statusCountMap = maintTaskMapper.selectList(notClosedWrapper).stream().collect(Collectors.groupingBy(MaintTask::getStatus, Collectors.counting()));
        Integer closedCount = 0;

        for (TaskAppStatusType taskStatusType : TaskAppStatusType.values()) {
            if ((taskStatusType.getValue() == TaskAppStatusType.BEGIN_CONFIRM.getValue() ||
                    taskStatusType.getValue() == TaskAppStatusType.CONFIRM.getValue() ||
                    taskStatusType.getValue() == TaskAppStatusType.CONFIRMED.getValue()) && !commonConfigDto.getConfirm()) {
                //去除安全确认
                continue;
            }
//            if (taskStatusType.getValue() == TaskAppStatusType.REPAIR_AUDIT.getValue() && !commonConfigDto.getRepairAudit()) {
//                //去除故障单审核
//                continue;
//            }
            //如果查询我得，不反回已关闭
            if (ourTask != null && ourTask && taskStatusType.getValue() == TaskAppStatusType.CLOSED.getValue()) {
                //去除已关闭
                continue;
            }
            //如果不是查询我得，返回已关闭
            if (ourTask != null && !ourTask && taskStatusType.getValue() == TaskAppStatusType.CLOSED.getValue()) {
                closedCount = maintTaskMapper.selectCount(closedWrapper);
            }
            Long count = statusCountMap.getOrDefault(taskStatusType.getValue(), 0L);
            switch (taskStatusType) {
                //前几位app的状态码跟数据库存储一致
                case DISPATCH:
                case RECEIVING:
                case HANDLE:
                case BEGIN_CONFIRM:
                case CONFIRM:
                case CONFIRMED:
                    count = statusCountMap.get(taskStatusType.getValue());
                    break;
                case PROCESSING:
                    Long processingCount = statusCountMap.get(TaskStatusType.PROCESSING.getValue());
                    Long hangUpCount = statusCountMap.get(TaskStatusType.HANG_UP.getValue());
                    count = (null != processingCount ? processingCount : 0) + (null != hangUpCount ? hangUpCount : 0);
                    break;
                case CHECK_ACCEPT:
                    count = statusCountMap.get(TaskStatusType.CHECK_ACCEPT.getValue());
                    break;
                case CLOSED:
                    count = Long.valueOf(closedCount);
                    break;
                case REPAIR_AUDIT:
                    count = statusCountMap.get(TaskStatusType.REPAIR_AUDIT.getValue());
                    break;
            }
            TaskCountDto taskCountDto = new TaskCountDto();
            taskCountDto.setStatus(taskStatusType.getValue());
            taskCountDto.setStatusName(taskStatusType.getName());
            taskCountDto.setCount(null != count ? count.intValue() : 0);
            taskCountDtos.add(taskCountDto);
        }
        return taskCountDtos;
    }

    @Override
    public List<TaskCountDto> getWorkbenchCount() {
        List<TaskCountDto> taskCountDtos = new ArrayList<>();
        List<EnumListDto> statusList = TaskStatusType.getList(jobTicketService.commonConfig());
        List<Integer> statusValues = statusList.stream().map(EnumListDto::getValue).distinct().collect(Collectors.toList());
        LambdaQueryWrapper<MaintTask> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(MaintTask::getDeleted, DeletedType.NO.getValue());
        wrapper.in(MaintTask::getStatus, statusValues);
        // 只获取保修工单 202507017 吴伟雄
        wrapper.eq(MaintTask::getSourceType,1);
        RestResponse<BuildInfoSearchDto> authRes = equipmentClient.getCurrentUserInfoIds();
        if (!authRes.isOk()) {
            log.error("获取设备失败");
            wrapper.eq(MaintTask::getEquipmentId, "-1");
        } else {
            BuildInfoSearchDto buildInfoSearchDto = authRes.getData();
            if (buildInfoSearchDto.getFlag() && CollectionUtils.isEmpty(buildInfoSearchDto.getEquipmentIds())) {
                wrapper.eq(MaintTask::getEquipmentId, "-1");
            } else {
                wrapper.in(CollectionUtils.isNotEmpty(buildInfoSearchDto.getEquipmentIds()), MaintTask::getEquipmentId, buildInfoSearchDto.getEquipmentIds());
            }
        }
        wrapper.select(MaintTask::getId, MaintTask::getStatus);
        Map<Integer, Long> map = maintTaskMapper.selectList(wrapper).stream().collect(Collectors.groupingBy(MaintTask::getStatus, Collectors.counting()));
        for (EnumListDto status : statusList) {
            Integer count = null != map.get(status.getValue()) ? map.get(status.getValue()).intValue() : 0;
            TaskCountDto taskCountDto = new TaskCountDto();
            taskCountDto.setStatus(status.getValue());
            taskCountDto.setStatusName(status.getName());
            taskCountDto.setCount(count);
            taskCountDtos.add(taskCountDto);
        }

        return taskCountDtos;
    }

    @Override
    public List<TaskCountDto> getCount(Boolean ourTask) {
        return this.getCount(ourTask, "");
    }

    @Override
    public PageResult<MaintTaskAppPageDto> appPageDto(MaintTaskQueryAppParam queryParam) {
        Wrapper<MaintTask> wrapper = getAppPageSearchWrapper(queryParam);
        PageResult<MaintTask> pageResult = page(queryParam, wrapper);
        List<MaintTaskAppPageDto> appPageDtos = CopyDataUtil.copyList(pageResult.getRecords(), MaintTaskAppPageDto.class);
        PageResult<MaintTaskAppPageDto> result = PageResult.<MaintTaskAppPageDto>builder()
                .records(appPageDtos).total(pageResult.getTotal()).build();
        if (CollectionUtils.isNotEmpty(appPageDtos)) {
            Map<String, DictionaryItemDto> jobTypeMap = new HashMap<>();
            RestResponse<Map<String, DictionaryItemDto>> jobTypeRes = baseServiceClient.getItemMapByCode("job_type");
            if (jobTypeRes.isOk()) {
                jobTypeMap = jobTypeRes.getData();
            } else {
                log.info("连接base-service获取作业类别字典表失败");
            }
            Map<String, DictionaryItemDto> urgencyMap = new HashMap<>();
            RestResponse<Map<String, DictionaryItemDto>> urgencyRes = baseServiceClient.getItemMapByCode("urgency");
            if (urgencyRes.isOk()) {
                urgencyMap = urgencyRes.getData();
            } else {
                log.info("连接base-service获取紧急程度字典表失败");
            }
            List<String> uids = new ArrayList<>();
            List<String[]> dispatchHandlerUids = appPageDtos.stream().map(MaintTaskAppPageDto::getDispatchHandler).collect(Collectors.toList());
            for (String[] dispatchHandler : dispatchHandlerUids) {
                if (null != dispatchHandler && dispatchHandler.length > 0) {
                    uids.addAll(Arrays.asList(dispatchHandler));
                }
            }
            List<String> handlerUids = appPageDtos.stream().map(MaintTaskAppPageDto::getHandler).collect(Collectors.toList());
            uids.addAll(handlerUids);
            Map<String, String> uidMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(uids)) {
                uids = uids.stream().distinct().collect(Collectors.toList());
                String uidStrs = StringUtils.join(uids, StringPool.COMMA);
                RestResponse<Map<String, String>> uidResponse = porosSecStaffClient.getMap(uidStrs);
                if (uidResponse.isOk()) {
                    uidMap = uidResponse.getData();
                } else {
                    log.error("获取用户名称失败");
                }
            }

            String[] equipmentIds = appPageDtos.stream().map(MaintTaskAppPageDto::getEquipmentId).distinct()
                    .collect(Collectors.toList()).stream().toArray(String[]::new);
            RestResponse<Map<String, EquipmentListDto>> listRestResponse = equipmentClient.getListByIds(equipmentIds);
            if (!listRestResponse.isSuccess()) {
                log.error("远程调用equipment-service出错");
            } else {
                Map<String, EquipmentListDto> stringListMap = listRestResponse.getData();
                for (MaintTaskAppPageDto dto : appPageDtos) {
                    EquipmentListDto equipment = stringListMap.get(dto.getEquipmentId());
                    if (null != equipment) {
                        dto.setEquipmentName(equipment.getEquipmentName());
                        dto.setEquipmentCategory(equipment.getCategoryName());
                        dto.setEquipmentLocation(equipment.getParentName());
                        dto.setEquipmentCode(equipment.getEquipmentCode());
                        dto.setPicId(equipment.getPicId());
                        dto.setEquipmentPrincipal(equipment.getManagePrincipal());
                        dto.setEquipmentRunningStatus(equipment.getRunningStatus());
                        dto.setPicId(equipment.getPicId());
                        dto.setPicUrl(equipment.getPicUrl());
                    }
                    if (StringUtils.isNotBlank(dto.getAllStaffIds())) {
                        dto.setStaffNames(buildStaffNames(dto.getAllStaffIds().split(StringPool.COMMA)));
                    }
                    if (null != dto.getDispatchHandler() && dto.getDispatchHandler().length > 0) {
                        List<String> names = new ArrayList<>();
                        for (String dispatchHandler : dto.getDispatchHandler()) {
                            String name = uidMap.get(dispatchHandler);
                            if (StringUtils.isNotBlank(name)) {
                                names.add(name);
                            }
                        }
                        if (CollectionUtils.isNotEmpty(names)) {
                            dto.setDispatchHandlerNames(StringUtils.join(names, StringPool.COMMA));
                        }
                    }
                    if (StringUtils.isNotBlank(dto.getHandler())) {
                        dto.setHandlerName(uidMap.get(dto.getHandler()));
                    }
                    if (StringUtils.isNotBlank(dto.getUrgency())) {
                        DictionaryItemDto dictionaryItemDto = urgencyMap.get(dto.getUrgency());
                        dto.setUrgencyName(null != dictionaryItemDto ? dictionaryItemDto.getName() : null);
                    }
                    if (StringUtils.isNotBlank(dto.getJobType())) {
                        DictionaryItemDto dictionaryItemDto = jobTypeMap.get(dto.getJobType());
                        dto.setJobTypeName(null != dictionaryItemDto ? dictionaryItemDto.getName() : null);
                    }
                    dto.setStatusName(TaskStatusType.getNameByValue(dto.getStatus()));
                }
            }
            result.setRecords(appPageDtos);
        }
        return Optional.ofNullable(result).orElse(new PageResult<>());
    }

    /**
     * 构建维护人员名称集合
     *
     * @param staffIds
     * @return
     */
    private String buildStaffNames(String[] staffIds) {
        if (null != staffIds && staffIds.length > StaticValue.ZERO) {
            RestResponse<String> staffRestResponse = baseServiceClient.getPersonNamesByIds(staffIds);
            if (!staffRestResponse.isSuccess()) {
                log.error("获取维护人员名称集合失败");
            } else {
                String staffNames = staffRestResponse.getData();
                return staffNames;
            }
        }
        return "";
    }

    /**
     * 构建维护人员名称dto
     *
     * @return
     */
    private void buildStaffDto(MaintTaskDto dto) {
        String[] staffIds = dto.getStaffIds();
        if (null != staffIds && staffIds.length > StaticValue.ZERO) {
            Map<String, String> personNameMap = new HashMap<>();
            RestResponse<Map<String, String>> personRes = baseServiceClient.getPersonMapByIds(staffIds);
            if (personRes.isOk()) {
                personNameMap = personRes.getData();
            } else {
                log.error("获取指定维护人员失败");
            }
            List<NameDetailDto> staffDtos = new ArrayList<>();
            StringBuffer sb = new StringBuffer();
            for (String staffId : staffIds) {
                String name = personNameMap.get(staffId);
                if (StringUtils.isNotBlank(name)) {
                    NameDetailDto nameDetailDto = new NameDetailDto();
                    nameDetailDto.setId(staffId);
                    nameDetailDto.setName(name);
                    staffDtos.add(nameDetailDto);
                    sb.append(name).append(StringPool.COMMA);
                }
            }
            String names = sb.toString();
            if (StringUtils.isNotBlank(names)) {
                //去除最后一位，
                dto.setStaffNames(sb.toString().substring(0, names.length() - 1));
                dto.setStaffDtos(staffDtos);
            }
        }
    }

    /**
     * 构建维护班组名称集合
     *
     * @param teamIds
     * @return
     */
    private String buildTeamNames(String[] teamIds) {
        if (null != teamIds && teamIds.length > StaticValue.ZERO) {
            RestResponse<String> teamRestResponse = baseServiceClient.getTeamNamesByIds(teamIds);
            if (!teamRestResponse.isSuccess()) {
                log.error("获取维护班组名称集合失败");
            } else {
                String teamNames = teamRestResponse.getData();
                return teamNames;
            }
        }
        return null;
    }

    /**
     * 构建维护班组名称dto
     *
     * @return
     */
    private void buildTeamDto(MaintTaskDto dto) {
        String[] teamIds = dto.getTeamIds();
        if (null != teamIds && teamIds.length > StaticValue.ZERO) {
            Map<String, String> teamNameMap = new HashMap<>();
            RestResponse<Map<String, String>> teamRes = baseServiceClient.getTeamMapByIds(teamIds);
            if (teamRes.isOk()) {
                teamNameMap = teamRes.getData();
            } else {
                log.error("获取指定维护班组失败");
            }
            List<NameDetailDto> teamDtos = new ArrayList<>();
            StringBuffer sb = new StringBuffer();
            for (String teamId : teamIds) {
                String name = teamNameMap.get(teamId);
                if (StringUtils.isNotBlank(name)) {
                    NameDetailDto nameDetailDto = new NameDetailDto();
                    nameDetailDto.setId(teamId);
                    nameDetailDto.setName(name);
                    teamDtos.add(nameDetailDto);
                    sb.append(name).append(StringPool.COMMA);
                }
            }
            String names = sb.toString();
            if (StringUtils.isNotBlank(names)) {
                //去除最后一位，
                dto.setTeamNames(sb.toString().substring(0, names.length() - 1));
                dto.setTeamDtos(teamDtos);
            }
        }
    }

    public MaintTask maintTaskTransfer(MaintTaskPlanAddDto addDto, MaintTask maintTask) {
        if (StringUtils.isNotBlank(addDto.getJobType()) && addDto.getJobType().equals(TaskJobType.EC.getValue())) {
            maintTask.setType(TaskType.EC.getValue());
        } else {
            maintTask.setType(TaskType.PLAN.getValue());
        }
        return maintTask;
    }

    @Override
    public MaintTaskDto savePlanTask(MaintTaskPlanAddDto addDto, UserBaseInfo userBaseInfo) {
        UserContextHolder.switchContext(userBaseInfo);
        return this.savePlanTask(addDto);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public MaintTaskDto savePlanTask(MaintTaskPlanAddDto addDto) {
        if (null == addDto) {
            return null;
        }
        MaintTask maintTask = CopyDataUtil.copyObject(addDto, MaintTask.class);
        maintTask.setSourceType(TaskSourceType.PLAN.getValue());
        //单体属性转换
        maintTask = this.maintTaskTransfer(addDto, maintTask);
        //如果为按照工作日历指派人员，丰富
        /*if (addDto.getSchedulePersonStrategy() != null && addDto.getSchedulePersonStrategy()) {
            maintPlanService.getStaffByScheduleDate(maintTask);
        }*/
        //初始化状态
        maintTask.setStatus(TaskStatusType.DISPATCH.getValue());
        //自由扫码，清空维护人员、维护班组
        if (maintTask.getFreeTask().intValue() == StaticValue.ONE) {
            maintTask.setStaffIds(null);
            maintTask.setTeamIds(null);
        }
        //构建维护人员，同步状态
        buildAllStaffIds(maintTask);
        if (StringUtils.isBlank(addDto.getCode())) {
            maintTask.setCode(buildCode(maintTask.getSourceType()));
        }
        // 启动工单流程实例
        Boolean flag = startTaskProcess(maintTask);
        //启动失败，直接返回
        if (!flag) {
            return null;
        }
        //save(maintTask);
        //保存通知
        notifyService.saveMaintTaskNotify(addDto.getAdventNotify(), MaintNotifySourceType.TASK.getValue(), maintTask.getId(), maintTask.getPlanMaintTime(), maintTask.getTaskDeadlineDate(), addDto.getOverdueHandlingMethod());
        return CopyDataUtil.copyObject(maintTask, MaintTaskDto.class);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public MaintTaskDto saveRepairTask(MaintTaskRepairAddDto addDto) {
        //log.info("新增报修工单:maintTaskRepairAddDto->" + JSON.toJSONString(addDto));
        if (null == addDto) {
            return null;
        }
        MaintTask maintTask = CopyDataUtil.copyObject(addDto, MaintTask.class);
        //maintTask.setSourceType(TaskSourceType.BREAKDOWN.getValue());
        maintTask.setType(TaskType.BREAKDOWN.getValue());
        maintTask.setCode(buildCode(TaskSourceType.BREAKDOWN.getValue()));
        EquipmentListDto equipmentListDto = this.getEquipment(addDto.getEquipmentId());
        if (null != equipmentListDto) {
            String name = null != equipmentListDto ? equipmentListDto.getEquipmentName() : "";
            maintTask.setName(name + "故障单");
            //获取维护班组、人员
            /*if (maintTaskConfigService.getManualRepairSendRule().equals("1")) {
                maintPlanService.getStaffByScheduleDate(maintTask);
            } else {*/
            RestResponse<List<EquipmentMaintainerDto>> listRestResponse = equipmentClient.getMaintainerListByIds(new String[]{addDto.getEquipmentId()});
            if (listRestResponse.isOk()) {
                List<EquipmentMaintainerDto> stringListMap = listRestResponse.getData();
                EquipmentMaintainerDto equipmentMaintainerDto = stringListMap.get(0);
                maintTask.setStaffIds(equipmentMaintainerDto.getMaintainerIds());
                maintTask.setTeamIds(equipmentMaintainerDto.getTeamIds());
            } else {
                log.error("获取设备维护班组、人员出错");
            }
            //}
            //构建工作流操作人员
            buildAllStaffIds(maintTask);
        } else {
            maintTask.setName("故障维修默认名称");
        }

        Boolean flag = startTaskProcess(maintTask);
        if (!flag) {
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("insert_error", null, LocaleContextHolder.getLocale())));
        }
        //故障单标记开始时间
        LambdaUpdateWrapper<MaintTask> wrapper = Wrappers.lambdaUpdate();
        wrapper.eq(MaintTask::getId, maintTask.getId());
        wrapper.set(MaintTask::getStartTime, maintTask.getCreateTime());
        wrapper.set(MaintTask::getBeginMaintTime, maintTask.getCreateTime());
        wrapper.set(MaintTask::getHangUp, false);
        wrapper.set(MaintTask::getPlanMaintTime, maintTask.getCreateTime());
        this.update(wrapper);
        //save(maintTask);

        MaintTaskRepair maintTaskRepair = new MaintTaskRepair();
        maintTaskRepair.setTaskId(maintTask.getId());
        maintTaskRepair.setFaultPhenomenonIds(addDto.getFaultPhenomenonIds());
        maintTaskRepair.setFaultPhenomenonRemark(addDto.getFaultPhenomenonRemark());
        maintTaskRepair.setFaultMeasuresIds(addDto.getFaultMeasuresIds());
        maintTaskRepair.setFaultMeasuresRemark(addDto.getFaultMeasuresRemark());
        maintTaskRepair.setFaultReasonIds(addDto.getFaultReasonIds());
        maintTaskRepair.setFaultReasonRemark(addDto.getFaultReasonRemark());
        maintTaskRepair.setFaultStructureIds(addDto.getFaultStructureIds());
        maintTaskRepair.setLotoFlag(TaskLotoType.DEFAULT.getValue());
        if (StringUtils.isNotBlank(addDto.getMediaIds())) {
            maintTaskRepair.setMediaIds(addDto.getMediaIds().split(StringPool.COMMA));
        }
        taskRepairService.save(maintTaskRepair);
        return CopyDataUtil.copyObject(maintTask, MaintTaskDto.class);
    }

    @Override
    public String saveDefectTask(MaintTaskDefectAddDto addDto) {
        if (null == addDto) {
            return null;
        }
        MaintTask maintTask = CopyDataUtil.copyObject(addDto, MaintTask.class);
        maintTask.setSourceType(TaskSourceType.DEFECT.getValue());
        maintTask.setType(TaskType.BREAKDOWN.getValue());
        maintTask.setTaskDeadlineDate(addDto.getEndTime());
        //获取自动指派
        if (addDto.getNeedAutoCalander() != null && addDto.getNeedAutoCalander()) {
            maintPlanService.getStaffByScheduleDate(maintTask);
        }
        //构建工作流操作人员
        buildAllStaffIds(maintTask);

        Boolean flag = startTaskProcess(maintTask);
        if (!flag) {
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("insert_error", null, LocaleContextHolder.getLocale())));
        }
        //记录计划维护时间
        LambdaUpdateWrapper<MaintTask> wrapper = Wrappers.lambdaUpdate();
        wrapper.eq(MaintTask::getId, maintTask.getId());
        wrapper.set(MaintTask::getPlanMaintTime, maintTask.getCreateTime());
        this.update(wrapper);
        //save(maintTask);

        return maintTask.getId();
    }

    /**
     * 启动工单流程
     *
     * @param maintTask
     */
    private Boolean startTaskProcess(MaintTask maintTask) {
        if (StringUtils.isBlank(maintTask.getId())) {
            maintTask.setId(UUID.randomUUID().toString().replace(StringPool.DASH, StringPool.EMPTY));
        }
        List<String> pushUids = new ArrayList<>();
        Map<String, Object> variables = Maps.newHashMap();
        ProcessStartParam processStartParam = new ProcessStartParam();
        processStartParam.setProcessDefinitionKey(maintFlowCode);
        maintTask = this.setMaintTaskSendTaskDeadlineDate(maintTask);
        try {
            if (maintTask.getType() == TaskType.DEFECT.getValue()) {
                maintTask = startMaintTaskHandler.startDefectTask(maintTask, variables);
            }
            if (maintTask.getType() != TaskType.DEFECT.getValue()) {
                maintTask = startMaintTaskHandler.startBreakdownTask(maintTask, variables);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        processStartParam.setProcessInstanceName(maintTask.getName());
        UserBaseInfo currentUser = UserContextHolder.getContext().getUserBaseInfo();
        processStartParam.setUid(currentUser != null ? currentUser.getUid() : maintTask.getCreateBy());
        processStartParam.setLevel(StaticValue.ONE);
        processStartParam.setVariables(variables);
        //this.save(maintTask);
        RestResponse<String> restResponse = activitiHandler.startProcess(processStartParam, maintTask, maintTask.getStatus());
        log.info("startProcess:{}", JSON.toJSONString(restResponse));
        if (restResponse.isOk()) {
            maintTask.setProcessInstanceId(restResponse.getData());
            maintTask.setCreateTimeWeek(DateUtil.dayOfWeek(new Date()));
            this.saveOrUpdate(maintTask);
            //taskNotifyHandler.dealSendNotify(maintTask.getId(), currentUser.getUid(), currentUser.getTenantId());
            //保存当前节点处理人
            this.buildNextUids(maintTask.getId());
            return true;
        } else {
            log.error("远程调用流程引擎接口{}失败,错误信息{}", "processServiceClient.startProcess", restResponse.getMsg());
            return false;
        }
    }


    @Override
    public String buildCode(Integer sourceType) {
        StringBuffer code = new StringBuffer(TaskSourceType.getPrexByValue(sourceType));
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmm");
        String dateStr = sdf.format(new Date());
        code.append(dateStr);
        String maxCode = this.getMaxCode(code.toString());
        if (StringUtils.isBlank(maxCode)) {
            code.append(String.format("%04d", 1));
        } else {
            String suffix = maxCode;
            try {
                Integer num = Integer.valueOf(suffix);
                String newCode = String.format("%04d", num);
                code.append(newCode);
            } catch (Exception e) {
                throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("code_build", null, LocaleContextHolder.getLocale())));
            }
        }
        return code.toString();
    }

    @Override
    public String getMaxCode(String prex) {
        String key = "EHM:CODE:" + prex;
        Long increment = redisTemplate.opsForValue().increment(key);
        redisTemplate.expire(key,24, TimeUnit.HOURS);
        return "" + increment;
        //return maintTaskMapper.getMaxCode(prex);
    }

    @Override
    public MaintTaskDto getDtoById(String id) {
        MaintTaskDto maintTaskDto = maintTaskMapper.getDtoById(id);
        if (null != maintTaskDto) {
            if (null == maintTaskDto.getConfirm()) {
                JobTicketDto jobTicketDto = jobTicketService.getDto();
                maintTaskDto.setConfirm(null != jobTicketDto ? jobTicketDto.getConfirm() : false);
            }
            buildMaintTaskDto(maintTaskDto);
        }

        return maintTaskDto;
    }

    public MaintTaskDtoForeign getDtoByIdForeign(String id) {
        MaintTaskDto maintTaskDto = maintTaskMapper.getDtoById(id);
        if (null != maintTaskDto) {
            if (null == maintTaskDto.getConfirm()) {
                JobTicketDto jobTicketDto = jobTicketService.getDto();
                maintTaskDto.setConfirm(null != jobTicketDto ? jobTicketDto.getConfirm() : false);
            }

            buildMaintTaskDto(maintTaskDto);
        }
        MaintTaskDtoForeign result = CopyDataUtil.copyObject(maintTaskDto, MaintTaskDtoForeign.class);
        return result;
    }

    private void buildMaintTaskDto(MaintTaskDto maintTaskDto) {
        EquipmentListDto equipmentListDto = getEquipment(maintTaskDto.getEquipmentId());
        if (null != equipmentListDto) {
            maintTaskDto.setEquipmentName(equipmentListDto.getEquipmentName());
            maintTaskDto.setEquipmentCategoryId(equipmentListDto.getCategoryId());
            maintTaskDto.setEquipmentCategory(equipmentListDto.getCategoryName());
            maintTaskDto.setEquipmentLocation(equipmentListDto.getParentName());
            maintTaskDto.setEquipmentCode(equipmentListDto.getEquipmentCode());
            maintTaskDto.setEquipmentRunningStatus(equipmentListDto.getRunningStatus());
            maintTaskDto.setEquipmentParentAllName(equipmentListDto.getParentAllName());
            maintTaskDto.setPicId(equipmentListDto.getPicId());
            maintTaskDto.setPicUrl(equipmentListDto.getPicUrl());
        }
        List<MaintTaskAssistListDto> assistPersonList = assistPersonService.getAssistPersonList(maintTaskDto.getId());
        if (CollectionUtils.isNotEmpty(assistPersonList)) {
            maintTaskDto.setAssistPeopleList(assistPersonList);
        }
        Map<String, String> majorNameMap = commonGetHandler.getDictNameMap("major");
        Map<String, String> influenceNameMap = commonGetHandler.getDictNameMap("influence");
        Map<String, String> jobTypeNameMap = commonGetHandler.getDictNameMap("job_type");
        Map<String, String> urgencyNameMap = commonGetHandler.getDictNameMap("urgency");
        if (StringUtils.isNotBlank(maintTaskDto.getMajor())) {
            maintTaskDto.setMajorName(majorNameMap.get(maintTaskDto.getMajor()));
        }
        if (StringUtils.isNotBlank(maintTaskDto.getInfluence())) {
            maintTaskDto.setInfluenceName(influenceNameMap.get(maintTaskDto.getInfluence()));
        }
        if (StringUtils.isNotBlank(maintTaskDto.getUrgency())) {
            maintTaskDto.setUrgencyName(urgencyNameMap.get(maintTaskDto.getUrgency()));
        }
        if (StringUtils.isNotBlank(maintTaskDto.getJobType())) {
            maintTaskDto.setJobTypeName(jobTypeNameMap.get(maintTaskDto.getJobType()));
        }
        if (null != maintTaskDto.getJobLevel()) {
            maintTaskDto.setJobLevelName(PlanJobLevelType.getNameByValue(maintTaskDto.getJobLevel()));
        }
        if (null != maintTaskDto.getTaskDeadlineDate() && !finishStatusList.contains(maintTaskDto.getStatus())) {
            maintTaskDto.setIsOverTime(maintTaskDto.getTaskDeadlineDate().compareTo(new Date()) < 0);
        }
        maintTaskDto.setTypeName(TaskType.getNameByValue(maintTaskDto.getType()));
        maintTaskDto.setSourceTypeName(TaskSourceType.getNameByValue(maintTaskDto.getSourceType()));
        maintTaskDto.setStatusName(TaskStatusType.getNameByValue(maintTaskDto.getStatus()));
        if (null != maintTaskDto.getStaffIds() && maintTaskDto.getStaffIds().length > 0) {
            //maintTaskDto.setStaffNames(buildStaffNames(maintTaskDto.getStaffIds()));
            this.buildStaffDto(maintTaskDto);
        }
        if (null != maintTaskDto.getTeamIds() && maintTaskDto.getTeamIds().length > 0) {
            //maintTaskDto.setTeamNames(buildTeamNames(maintTaskDto.getTeamIds()));
            this.buildTeamDto(maintTaskDto);
        }
        maintTaskDto.setActivityId(this.getProcessTaskId(maintTaskDto.getProcessInstanceId(), false));
        //待接单可以改派
        if (maintTaskDto.getStatus() == TaskStatusType.RECEIVING.getValue()) {
            UserBaseInfo userBaseInfo = PorosContextHolder.getCurrentUser();
            if (null != userBaseInfo) {
                String currentUserId = getCurrentUserStaffId(userBaseInfo.getUid());
                if (Arrays.asList(maintTaskDto.getDispatchHandler()).contains(userBaseInfo.getUid()) || maintTaskDto.getAllStaffIds().contains(currentUserId)) {
                    //当前人员为派单角色或接单人
                    maintTaskDto.setCanTransform(true);
                }
            }
        } else if (maintTaskDto.getStatus() == TaskStatusType.CONFIRM.getValue()) {
            //安全确认中
            UserBaseInfo userBaseInfo = PorosContextHolder.getCurrentUser();
            if (null != userBaseInfo && Arrays.asList(maintTaskDto.getReviewer()).contains(userBaseInfo.getUid())) {
                if (null == maintTaskDto.getAlreadyReviewer() || !(Arrays.asList(maintTaskDto.getAlreadyReviewer()).contains(userBaseInfo.getUid()))) {
                    maintTaskDto.setCanAudit(true);
                }
            }
        }
        if (ArrayUtil.isNotEmpty(maintTaskDto.getFinishFileIds())) {
            Map<String, AttachmentClientDto> attachmentMap = fileGetHandler.getAttachmentMap(Arrays.asList(maintTaskDto.getFinishFileIds()));
            List<AttachmentClientDto> attachmentClientDtos = fileGetHandler.buildPic(maintTaskDto.getFinishFileIds(), attachmentMap);
            maintTaskDto.setFinishFileList(attachmentClientDtos);
        }
        buildCostTime(maintTaskDto);
    }

    private void buildCostTime(MaintTaskDto maintTaskDto) {
        if (StringUtils.isBlank(maintTaskDto.getMaintTimeCost()) && maintTaskDto.getBeginMaintTime() != null && maintTaskDto.getEndMaintTime() != null) {
            maintTaskDto.setMaintTimeCost("" + DateUtil.between(maintTaskDto.getBeginMaintTime(), maintTaskDto.getEndMaintTime(), DateUnit.MINUTE));
        }
        if (StringUtils.isBlank(maintTaskDto.getDownTimeCost()) && maintTaskDto.getBeginDowntime() != null && maintTaskDto.getEndDowntime() != null) {
            maintTaskDto.setDownTimeCost("" + DateUtil.between(maintTaskDto.getBeginDowntime(), maintTaskDto.getEndDowntime(), DateUnit.MINUTE));
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean updateTask(TaskEditParam editParam) {
        MaintTask maintTask = CopyDataUtil.copyObject(editParam, MaintTask.class);
        boolean result = updateById(maintTask);
        if (CollectionUtils.isNotEmpty(editParam.getAssistPeopleIds())) {
            //二次确认，传递的是删掉的辅助人员id
            assistPersonService.deleteList(editParam.getAssistPeopleIds());
        }
        RestResponse<EquipmentInfoDto> equipmentInfo = equipmentClient.getEquipmentInfo(editParam.getEquipmentId());
        String parentId = null;
        if (equipmentInfo.isOk()) {
            parentId = equipmentInfo.getData().getParentId();
        }
        if (StringUtils.isNotBlank(editParam.getEquipmentId())
                && null != editParam.getEquipmentRunningStatus() && StringUtils.isNotBlank(parentId)) {
            EquipmentInfoEditParam equipmentInfoEditParam = new EquipmentInfoEditParam();
            equipmentInfoEditParam.setId(editParam.getEquipmentId());
            equipmentInfoEditParam.setParentId(parentId);
            equipmentInfoEditParam.setRunningStatus(editParam.getEquipmentRunningStatus());
            RestResponse<Boolean> response = equipmentClient.update(equipmentInfoEditParam);
            if (response.isOk()) {
                log.info("更新设备状态成功");
            }
        }
        return result;
    }

    private void updateEquipmentRunningStatus(String equipmentId, Date date, Integer runningStatus) {
        EquipmentIotStatusDto equipmentIotStatusDto = new EquipmentIotStatusDto();
        equipmentIotStatusDto.setEquipmentId(equipmentId);
        equipmentIotStatusDto.setMarkTime(date);
        equipmentIotStatusDto.setStatus(runningStatus);
        RestResponse<Boolean> restResponse = equipmentClient.updateStatus(equipmentIotStatusDto);
        if (!restResponse.isOk()) {
            log.error("更新设备运行状态失败");
        }
    }

    @Override
    public EquipmentListDto getEquipment(String equipmentId) {
        RestResponse<Map<String, EquipmentListDto>> listRestResponse = equipmentClient.getListByIds(new String[]{equipmentId});
        if (!listRestResponse.isSuccess()) {
            log.error("远程调用equipment-service出错");
        } else {
            Map<String, EquipmentListDto> stringListMap = listRestResponse.getData();
            EquipmentListDto equipment = stringListMap.get(equipmentId);
            return equipment;
        }
        return null;
    }

    private void buildAllStaffIds(MaintTask maintTask) {
        boolean staffsFlag = null != maintTask.getStaffIds() && maintTask.getStaffIds().length > StaticValue.ZERO;
        boolean teamsFlag = null != maintTask.getTeamIds() && maintTask.getTeamIds().length > StaticValue.ZERO;
        if (staffsFlag || teamsFlag) {
            log.info("已设置维护人员或维护班组，生成权限id集合");
            List<String> personIds = new ArrayList<>();
            if (teamsFlag) {
                RestResponse<List<String>> restResponse = baseServiceClient.getPersonIdsByTeamIds(maintTask.getTeamIds());
                if (!restResponse.isSuccess()) {
                    log.error("远程调用base-service出错");
                } else {
                    personIds.addAll(restResponse.getData());
                }
            }

            if (staffsFlag) {
                personIds.addAll(Arrays.asList(maintTask.getStaffIds()));
            }
            if (CollectionUtils.isNotEmpty(personIds)) {
                personIds = personIds.stream()
                        .distinct()
                        .collect(Collectors.toList());
                maintTask.setAllStaffIds(StringUtils.join(personIds.toArray(), ","));
            }

        }
    }

    @Override
    public MaintTaskItemDto getTaskItemById(String id, Boolean onlyError) {
        LambdaQueryWrapper<MaintTask> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(MaintTask::getId, id);
        MaintTask maintTask = maintTaskMapper.selectOne(wrapper);
        if (null != maintTask) {
            MaintTaskItemDto taskItemDto = taskItemService.getByTaskId(id, maintTask.getStandardId(), onlyError);
            if (maintTask.getBeginDowntime() != null && maintTask.getEndDowntime() != null) {
                taskItemDto.setStopped(1);
                taskItemDto.setBeginDowntime(maintTask.getBeginDowntime());
                taskItemDto.setEndDowntime(maintTask.getEndDowntime());
                taskItemDto.setDownTimeCost("" + DateUtil.between(maintTask.getBeginDowntime(), maintTask.getEndDowntime(), DateUnit.MINUTE));
            } else {
                taskItemDto.setStopped(0);
            }
            return taskItemDto;
        }
        return null;
    }

    /**
     * 获取流程实例当前taskId
     *
     * @param processInstanceId
     * @param notCheck          是否校验
     * @return
     */
    @Override
    public String getProcessTaskId(String processInstanceId, Boolean notCheck) {
        if (StringUtils.isBlank(processInstanceId)) {
            //log.info("未找到流程实例id");
            return null;
        }
        UserBaseInfo user = UserContextHolder.getContext().getUserBaseInfo();
        if (null == user) {
            log.error("未找到用户");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("user_error", null, LocaleContextHolder.getLocale())));
        }
        String uid = user.getUid();
        ProcessTaskParam processTaskParam = new ProcessTaskParam();
        processTaskParam.setProcessInstanceId(processInstanceId);
        log.info("流程实例id:" + processInstanceId + ",uid:" + uid);
        AuditActivitiServiceResult processTaskUserId = activitiHandler.getProcessTaskUserId(processInstanceId);
        if (notCheck) {
            return processTaskUserId.getTaskId();
        } else {
            String[] processUserArray = processTaskUserId.getProcessUserArray();
            //如果当前工作流执行人包含当前登录用户，返回任务id
            if (ArrayUtil.isNotEmpty(processUserArray) && Arrays.asList(processUserArray).contains(uid)) {
                return processTaskUserId.getTaskId();
            }
        }
        return null;
    }

    private List<String> getProcessTaskUids(String processInstanceId) {
        if (StringUtils.isBlank(processInstanceId)) {
            return null;
        }
        UserBaseInfo user = UserContextHolder.getContext().getUserBaseInfo();
        if (null == user) {
            log.error("未找到用户");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("user_error", null, LocaleContextHolder.getLocale())));
        }
        String uid = user.getUid();
        ProcessTaskParam processTaskParam = new ProcessTaskParam();
        processTaskParam.setProcessInstanceId(processInstanceId);
        log.info("流程实例id:" + processInstanceId + ",uid:" + uid);
        AuditActivitiServiceResult processTaskUserId = activitiHandler.getProcessTaskUserId(processInstanceId);
        return Arrays.asList(processTaskUserId.getProcessUserArray());
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean updateTaskItem(TaskItemEditParam editParam) {
        log.debug("修改工单详情参数：" + JSON.toJSONString(editParam));
        updateTaskStopTime(editParam.getTaskId(), editParam.getBeginDowntime(), editParam.getEndDowntime());
        Integer status = maintTaskMapper.getStatus(editParam.getTaskId());
        if (status == TaskStatusType.HANG_UP.getValue()) {
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("status_changed", null, LocaleContextHolder.getLocale())));
        }
        this.dealMaintEcErrorNum(editParam.getItemDetailDtos(), editParam.getTaskId());
        if (CollectionUtils.isNotEmpty(editParam.getItemDetailDtos())) {
            taskItemService.saveOrUpdateList(editParam.getItemDetailDtos(), editParam.getTaskId());
        }
        return true;
    }

    private void updateTaskStopTime(String id, Date beginDowntime, Date endDowntime) {
        if (null != beginDowntime && null != endDowntime) {
            MaintTask maintTask = new MaintTask();
            maintTask.setId(id);
            maintTask.setBeginDowntime(beginDowntime);
            maintTask.setEndDowntime(endDowntime);
            maintTask.setDownTimeCost("" + DateUtil.between(maintTask.getBeginDowntime(), maintTask.getEndDowntime(), DateUnit.MINUTE));
            this.updateById(maintTask);
        }
    }

    public void dealMaintEcErrorNum(List<TaskItemDetailDto> itemDetailDtos, String taskId) {
        int errorNum = 0;
        if (CollectionUtils.isEmpty(itemDetailDtos)) {
            return;
        }
        for (TaskItemDetailDto item : itemDetailDtos) {
            if (StringUtils.isNotBlank(item.getJobItemResultType()) && StringUtils.isNotBlank(item.getResult()) &&
                    item.getJobItemResultType().equals("2") &&
                    item.getTargetMax() != null && item.getTargetMin() != null && StringUtils.isNotBlank(item.getResult()) &&
                    (item.getTargetMax().compareTo(new BigDecimal(item.getResult())) < 0
                            ||
                            item.getTargetMin().compareTo(new BigDecimal(item.getResult())) > 0)) {
                errorNum++;
            }
            if (StringUtils.isNotBlank(item.getJobItemResultType()) && item.getJobItemResultType().equals("1") && StringUtils.isNotBlank(item.getResult()) && !item.getResult().equals("ok")) {
                errorNum++;
            }
        }
        this.update(new UpdateWrapper<MaintTask>().lambda().set(MaintTask::getEcErrorNum, errorNum).eq(MaintTask::getId, taskId));
    }

    @Override
    public MaintTaskFaultDto getFaultDtoById(String id) {
        MaintTaskFaultDto maintTaskFaultDto = taskRepairService.getFaultDtoById(id);
        if (null != maintTaskFaultDto) {
            FaultKnowledgeSearchDto searchDto = new FaultKnowledgeSearchDto();
            if (null != maintTaskFaultDto.getFaultPhenomenonIds() && maintTaskFaultDto.getFaultPhenomenonIds().length > 0) {
                searchDto.setType(StaticValue.ONE);
                searchDto.setIds(Arrays.asList(maintTaskFaultDto.getFaultPhenomenonIds()));
                RestResponse<Map<String, FaultKnowledgeResDto>> restResponse = baseServiceClient.faultKnowledgeMap(searchDto);
                if (restResponse.isOk()) {
                    Map<String, FaultKnowledgeResDto> map = restResponse.getData();
                    List<FaultKnowledgeResDto> faultPhenomenonDtos = new ArrayList<>();
                    for (String faultPhenomenonId : maintTaskFaultDto.getFaultPhenomenonIds()) {
                        FaultKnowledgeResDto faultKnowledgeResDto = map.get(faultPhenomenonId);
                        if (null != faultKnowledgeResDto) {
                            faultPhenomenonDtos.add(faultKnowledgeResDto);
                        }
                    }
                    maintTaskFaultDto.setFaultPhenomenonDtos(faultPhenomenonDtos);
                } else {
                    log.error("获取故障现象失败");
                }
            }
            if (null != maintTaskFaultDto.getFaultReasonIds() && maintTaskFaultDto.getFaultReasonIds().length > 0) {
                searchDto.setType(StaticValue.TWO);
                searchDto.setIds(Arrays.asList(maintTaskFaultDto.getFaultReasonIds()));
                RestResponse<Map<String, FaultKnowledgeResDto>> restResponse = baseServiceClient.faultKnowledgeMap(searchDto);
                if (restResponse.isOk()) {
                    Map<String, FaultKnowledgeResDto> map = restResponse.getData();
                    List<FaultKnowledgeResDto> faultReasonDtos = new ArrayList<>();
                    for (String reasonId : maintTaskFaultDto.getFaultReasonIds()) {
                        FaultKnowledgeResDto faultKnowledgeResDto = map.get(reasonId);
                        if (null != faultKnowledgeResDto) {
                            faultReasonDtos.add(faultKnowledgeResDto);
                        }
                    }
                    maintTaskFaultDto.setFaultReasonDtos(faultReasonDtos);
                } else {
                    log.error("获取故障原因失败");
                }
            }
            if (null != maintTaskFaultDto.getFaultMeasuresIds() && maintTaskFaultDto.getFaultMeasuresIds().length > 0) {
                searchDto.setType(StaticValue.THREE);
                searchDto.setIds(Arrays.asList(maintTaskFaultDto.getFaultMeasuresIds()));
                RestResponse<Map<String, FaultKnowledgeResDto>> restResponse = baseServiceClient.faultKnowledgeMap(searchDto);
                if (restResponse.isOk()) {
                    Map<String, FaultKnowledgeResDto> map = restResponse.getData();
                    List<FaultKnowledgeResDto> faultMeasuresDtos = new ArrayList<>();
                    for (String measuresId : maintTaskFaultDto.getFaultMeasuresIds()) {
                        FaultKnowledgeResDto faultKnowledgeResDto = map.get(measuresId);
                        if (null != faultKnowledgeResDto) {
                            faultMeasuresDtos.add(faultKnowledgeResDto);
                        }
                    }
                    maintTaskFaultDto.setFaultMeasuresDtos(faultMeasuresDtos);
                } else {
                    log.error("获取处理措施失败");
                }
            }
            if (null != maintTaskFaultDto.getFaultStructureIds() && maintTaskFaultDto.getFaultStructureIds().length > 0) {
                RestResponse<Map<String, String>> restResponse = equipmentClient.getStructureNameMap(maintTaskFaultDto.getFaultStructureIds());
                if (restResponse.isOk()) {
                    Map<String, String> map = restResponse.getData();
                    List<String> faultStructureNames = new ArrayList<>();
                    for (String structureId : maintTaskFaultDto.getFaultStructureIds()) {
                        if (null != map.get(structureId)) {
                            faultStructureNames.add(map.get(structureId));
                        }
                    }
                    maintTaskFaultDto.setFaultStructureNames(faultStructureNames);
                }
            }
            if (maintTaskFaultDto.getBeginDowntime() != null && maintTaskFaultDto.getEndDowntime() != null) {
                maintTaskFaultDto.setStopped(1);
                maintTaskFaultDto.setDownTimeCost("" + DateUtil.between(maintTaskFaultDto.getBeginDowntime(), maintTaskFaultDto.getEndDowntime(), DateUnit.MINUTE));
            } else {
                maintTaskFaultDto.setStopped(0);
            }
        }
        return maintTaskFaultDto;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean updateTaskFault(TaskFaultEditParam editParam) {
        updateTaskStopTime(editParam.getTaskId(), editParam.getBeginDowntime(), editParam.getEndDowntime());
        Integer status = maintTaskMapper.getStatus(editParam.getTaskId());
        if (status == TaskStatusType.HANG_UP.getValue()) {
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("status_changed", null, LocaleContextHolder.getLocale())));
        }
        MaintTask maintTask = new MaintTask();
        maintTask.setId(editParam.getTaskId());
        maintTask.setFaultInfluences(editParam.getFaultInfluences());
        maintTask.setDamageReason(editParam.getDamageReason());
        this.updateById(maintTask);
        return taskRepairService.updateFault(editParam);
    }

    @Override
    public MaintTaskPartDto getPartById(String id) {
        MaintTaskPartDto maintTaskPartDto = new MaintTaskPartDto();
        maintTaskPartDto.setTaskId(id);
        MaintTask maintTask = this.getSubmitEntity(id);
        UserBaseInfo userBaseInfo = PorosContextHolder.getCurrentUser();
        String uid = userBaseInfo.getUid();
        Boolean haveAuth = false;
        if (null != maintTask.getDispatchHandler() && Arrays.asList(maintTask.getDispatchHandler()).contains(uid)) {
            haveAuth = true;
        }
        if (StringUtils.isNotBlank(maintTask.getHandler()) && maintTask.getHandler().equals(uid)) {
            haveAuth = true;
        }
        if (null != maintTask.getEvaluateHandler() && Arrays.asList(maintTask.getEvaluateHandler()).contains(uid)) {
            haveAuth = true;
        }
        maintTaskPartDto.setHaveAuth(haveAuth);
        List<TaskPartDto> parts = taskPartRelService.getByTaskId(id);
        maintTaskPartDto.setParts(parts);

        return maintTaskPartDto;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean updateTaskPart(TaskPartEditParam editParam) {
        return taskPartRelService.saveOrUpdateList(editParam.getParts(), editParam.getTaskId());
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public TaskTicketDto getTaskTicketById(String id) {
        TaskTicketDto taskTicketDto = new TaskTicketDto();
        LambdaQueryWrapper<MaintTask> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(MaintTask::getId, id);
        wrapper.select(MaintTask::getId, MaintTask::getStatus, MaintTask::getConfirm, MaintTask::getShowInformation,
                MaintTask::getTicketInformation, MaintTask::getShowTicketType, MaintTask::getTicketTypes,
                MaintTask::getTicketMediaIds, MaintTask::getReviewer, MaintTask::getAlreadyReviewer);
        MaintTask maintTask = maintTaskMapper.selectOne(wrapper);
        if (null != maintTask) {
            JobTicketDto jobTicketDto = jobTicketService.getDetialDto();
            taskTicketDto = CopyDataUtil.copyObject(maintTask, TaskTicketDto.class);
            //首次未设置安全确认，需要从配置表读取最新
            if (null == maintTask.getConfirm()) {
                taskTicketDto.setShowInformation(null != jobTicketDto ? jobTicketDto.getShowInformation() : false);
                taskTicketDto.setShowTicketType(null != jobTicketDto ? jobTicketDto.getShowTicketType() : false);
            }
            List<TaskTicketItemDto> ticketItemDtos = taskTicketService.getListByTaskId(id);
            if (CollectionUtils.isEmpty(ticketItemDtos) && null != jobTicketDto && CollectionUtils.isNotEmpty(jobTicketDto.getItemDtos())) {
                ticketItemDtos = new ArrayList<>();
                for (JobTicketItemDto jobTicketItemDto : jobTicketDto.getItemDtos()) {
                    TaskTicketItemDto taskTicketItemDto = CopyDataUtil.copyObject(jobTicketItemDto, TaskTicketItemDto.class);
                    taskTicketItemDto.setId(null);
                    ticketItemDtos.add(taskTicketItemDto);
                }
            }
            taskTicketDto.setItemDtos(ticketItemDtos);
            //安全确认中
            if (maintTask.getStatus() == TaskStatusType.CONFIRM.getValue()) {
                UserBaseInfo userBaseInfo = PorosContextHolder.getCurrentUser();
                if (Arrays.asList(maintTask.getReviewer()).contains(userBaseInfo.getUid())) {
                    if (null == maintTask.getAlreadyReviewer() || !(Arrays.asList(maintTask.getAlreadyReviewer()).contains(userBaseInfo.getUid()))) {
                        taskTicketDto.setCanAudit(true);
                    }
                }
            }

            if (null != maintTask.getReviewer() && maintTask.getReviewer().length > 0) {
                Map<String, String> uidMap = new HashMap<>();
                SecStaffUidParam secStaffUidParam = new SecStaffUidParam();
                secStaffUidParam.setUids(Arrays.asList(maintTask.getReviewer()));
                RestResponse<List<PorosSecStaffDto>> staffRes = porosSecStaffClient.findUserByUidsPost(secStaffUidParam);
                if (staffRes.isOk()) {
                    uidMap = staffRes.getData().stream().collect(Collectors.toMap(PorosSecStaffDto::getUid, PorosSecStaffDto::getName));
                } else {
                    log.error("获取用户失败");
                }
                List<String> reviewer = new ArrayList<>();
                for (String review : maintTask.getReviewer()) {
                    String name = uidMap.get(review);
                    if (StringUtils.isNotBlank(name)) {
                        reviewer.add(review + StringPool.SLASH + name);
                    }
                }
                List<String> alreadyReviewer = new ArrayList<>();
                for (String alreadyReview : maintTask.getAlreadyReviewer()) {
                    String name = uidMap.get(alreadyReview);
                    if (StringUtils.isNotBlank(name)) {
                        alreadyReviewer.add(alreadyReview + StringPool.SLASH + name);
                    }
                }
                taskTicketDto.setReviewer(reviewer.toArray(new String[reviewer.size()]));
                taskTicketDto.setAlreadyReviewer(alreadyReviewer.toArray(new String[alreadyReviewer.size()]));
            }
        }
        taskTicketDto.setTaskId(id);
        return taskTicketDto;
    }

    /**
     * 更改工作票
     *
     * @param editParam
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean updateTaskTicket(TaskTicketDto editParam) {
        Integer status = maintTaskMapper.getStatus(editParam.getTaskId());
        if (status != TaskStatusType.HANDLE.getValue() && status != TaskStatusType.BEGIN_CONFIRM.getValue() && status != TaskStatusType.CONFIRMED.getValue()) {
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("status_changed", null, LocaleContextHolder.getLocale())));
        }
        if (null != editParam.getReviewer() && editParam.getReviewer().length > 0) {
            List<String> reviewerUids = new ArrayList<>();
            for (String review : editParam.getReviewer()) {
                reviewerUids.add(review.split(StringPool.SLASH)[0]);
            }
            editParam.setReviewer(reviewerUids.toArray(new String[reviewerUids.size()]));
        }
        MaintTask maintTask = CopyDataUtil.copyObject(editParam, MaintTask.class);
        maintTask.setId(editParam.getTaskId());
        maintTask.setConfirm(true);
        Boolean flag = updateById(maintTask);
        taskTicketService.saveOrUpdateBatch(editParam.getItemDtos(), editParam.getTaskId());
        return flag;
    }

    @Override
    public MaintTaskGradeDto getGradeById(String id) {
        MaintTask maintTask = maintTaskMapper.selectById(id);
        MaintTaskGradeDto gradeDto = CopyDataUtil.copyObject(maintTask, MaintTaskGradeDto.class);
        gradeDto.setTaskId(id);
        return gradeDto;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean updateTaskGrade(MaintTaskGradeDto editParam) {
        Integer status = maintTaskMapper.getStatus(editParam.getTaskId());
        if (status != TaskStatusType.CHECK_ACCEPT.getValue()) {
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("status_changed", null, LocaleContextHolder.getLocale())));
        }
        MaintTask maintTask = CopyDataUtil.copyObject(editParam, MaintTask.class);
        maintTask.setId(editParam.getTaskId());
        return updateById(maintTask);
    }

    @Override
    public Integer getTodayTaskQty() {
        return maintTaskMapper.getTodayTaskQty();
    }

    @Override
    public Integer getOverdueTaskQty() {
        return maintTaskMapper.getOverdueTaskQty();
    }

    @Override
    public Integer getTodayCompletedTaskQty() {
        return maintTaskMapper.getTodayCompletedTaskQty();
    }

    @Override
    public List<TaskCompleteDynamicDto> getTaskCompleteDynamic() {
        return maintTaskMapper.getTaskCompleteDynamic();
    }

    @Override
    public List<MaintTaskDto> getTaskOverview(Integer status) {
        return maintTaskMapper.getTaskOverview(status);
    }

    @Override
    public List<Map<Integer, Integer>> getTaskEvaluated() {
        return maintTaskMapper.getTaskEvaluated();
    }

    @Override
    public List<MaintTaskDto> getTaskTodoList() {
        return maintTaskMapper.getTaskTodoList();
    }

    @Override
    public MaintTaskDto getByProcessInstanceId(String processInstanceId) {
        MaintTask maintTask = maintTaskMapper.selectOne(new LambdaQueryWrapper<MaintTask>().eq(MaintTask::getProcessInstanceId, processInstanceId));
        return CopyDataUtil.copyObject(maintTask, MaintTaskDto.class);
    }

    @Override
    public boolean submitProcessTask(TaskSubmitParam taskSubmitParam) {
        Boolean flag = false;
        SubmitType submitType = SubmitType.valueOf(taskSubmitParam.getSubmitType());
        switch (submitType) {
            case DISPATCH:
                flag = this.assignTask(taskSubmitParam);
                break;
            case RECEIVING:
                flag = this.receivingTask(taskSubmitParam);
                break;
            case TRANSFORM:
                flag = this.transformTask(taskSubmitParam);
                break;
            case BEGIN_CONFIRM:
                flag = this.beginConfirmTask(taskSubmitParam);
                break;
            case CONFIRM:
                flag = this.confirmTask(taskSubmitParam);
                break;
            case CONFIRMED:
                flag = this.confirmedTask(taskSubmitParam);
                break;
            case REJECT_CONFIRMED:
                flag = this.rejectConfirmedTask(taskSubmitParam);
                break;
            case PROCESSING:
                flag = this.processingTask(taskSubmitParam);
                break;
            case HANG_UP:
                flag = this.hangUpTask(taskSubmitParam);
                break;
            case COMPLETE:
                flag = this.completeTask(taskSubmitParam);
                break;
            case CHECK_ACCEPT:
                flag = this.checkAccessTask(taskSubmitParam);
                break;
            case EXCEPTION_CLOSED:
                flag = this.exceptionClosedTask(taskSubmitParam);
                break;
            case TURN_DEFECT:
                flag = this.turnDefectTask(taskSubmitParam);
                break;
            case REPAIR_AUDIT_PASS:
                flag = this.repairAuditPass(taskSubmitParam);
                break;
            case EQUIPMENT_STOP_CLOSED:
                flag = equipmentStopClosedTask(taskSubmitParam);
                break;
            case TO_SUBMIT:
                flag = this.backToSubmit(taskSubmitParam);
                break;
            case TO_WAIT_RECEIVING:
                flag = this.backToWaitReceiving(taskSubmitParam);
                break;
            case TO_DELETE:
                flag = this.backToDelete(taskSubmitParam);
                break;
            case TO_RECEIVED:
                flag = this.backtoReceived(taskSubmitParam);
                break;
            case HANG_UP_AUDIT:
                flag = this.hangupAudit(taskSubmitParam);
                break;
            case HANG_UP_AUDIT_PASS:
                flag = this.hangupAuditPass(taskSubmitParam);
                break;
            case HANG_UP_AUDIT_REJECT:
                flag = this.hangupAuditReject(taskSubmitParam);
                break;
            case PROCESS_DEFECT_AUDIT:
                flag = this.processDefectAudit(taskSubmitParam);
                break;
            case ACCEPT_DEFECT_AUDIT:
                flag = this.acceptDefectAudit(taskSubmitParam);
                break;
            case DEFECT_AUDIT_PASS:
                flag = this.defectAuditPass(taskSubmitParam);
                break;
            case DEFECT_AUDIT_REJECT:
                flag = this.defectAuditReject(taskSubmitParam);
                break;

            default:
                log.error("未知的流程任务提交类型");
                throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("check_error", null, LocaleContextHolder.getLocale())));
        }
        this.buildNextUids(taskSubmitParam.getTaskId());
        return flag;
    }


    public boolean processDefectAudit(TaskSubmitParam taskSubmitParam) {
        MaintTask maintTask = this.getSubmitEntity(taskSubmitParam.getTaskId());
        //仅执行中或待验收可以触发
        List<Integer> statusList = Lists.newArrayList(TaskStatusType.PROCESSING.getValue());
        if (!statusList.contains(maintTask.getStatus())) {
            throw new GlobalServiceException(GlobalResultMessage.of("执行中转缺陷仅允许执行中状态的工单提交"));
        }
        maintTask.setStatus(TaskStatusType.PROCESS_DEFECT_AUDIT.getValue());
        TaskCompleteParam taskCompleteParam = new TaskCompleteParam();
        taskCompleteParam.setTaskId(taskSubmitParam.getActivityId());
        Map<String, Object> variables = new HashMap<>();
        variables.put("result", true);
        variables.put("assigns", maintTask.getCreateBy());
        taskCompleteParam.setVariables(variables);
        taskCompleteParam.setComment(taskSubmitParam.getRemark());
        activitiHandler.submitActiviti(taskCompleteParam, taskSubmitParam.getTaskId(), maintTask.getStatus());
        maintTaskMapper.updateById(maintTask);
        return true;
    }

    public boolean acceptDefectAudit(TaskSubmitParam taskSubmitParam) {
        MaintTask maintTask = this.getSubmitEntity(taskSubmitParam.getTaskId());
        //仅执行中或待验收可以触发
        List<Integer> statusList = Lists.newArrayList(TaskStatusType.CHECK_ACCEPT.getValue());
        if (!statusList.contains(maintTask.getStatus())) {
            throw new GlobalServiceException(GlobalResultMessage.of("待验收转缺陷仅允许待验收状态的工单提交"));
        }
        maintTask.setStatus(TaskStatusType.ACCEPT_DEFECT_AUDIT.getValue());
        TaskCompleteParam taskCompleteParam = new TaskCompleteParam();
        taskCompleteParam.setTaskId(taskSubmitParam.getActivityId());
        Map<String, Object> variables = new HashMap<>();
        variables.put("result", true);
        variables.put("assigns", maintTask.getHandler());
        taskCompleteParam.setVariables(variables);
        taskCompleteParam.setComment(taskSubmitParam.getRemark());
        activitiHandler.submitActiviti(taskCompleteParam, taskSubmitParam.getTaskId(), maintTask.getStatus(), taskSubmitParam.getForceSubmit());
        maintTaskMapper.updateById(maintTask);
        return true;
    }

    public boolean defectAuditPass(TaskSubmitParam taskSubmitParam) {
        MaintTask maintTask = this.getSubmitEntity(taskSubmitParam.getTaskId());
        List<Integer> statusList = Lists.newArrayList(TaskStatusType.ACCEPT_DEFECT_AUDIT.getValue(),
                TaskStatusType.PROCESS_DEFECT_AUDIT.getValue());
        if (!statusList.contains(maintTask.getStatus())) {
            throw new GlobalServiceException(GlobalResultMessage.of("仅缺陷待审批状态支持通过/驳回操作"));
        }
        //如果是执行中转缺陷
        if (maintTask.getStatus() == TaskStatusType.PROCESS_DEFECT_AUDIT.getValue()) {
            maintTask.setStatus(TaskStatusType.CLOSED.getValue());
        }
        //如果是验收转缺陷
        if (maintTask.getStatus() == TaskStatusType.ACCEPT_DEFECT_AUDIT.getValue()) {
            maintTask.setStatus(TaskStatusType.CLOSED.getValue());
        }
        maintTask.setDefectFlag(true);
        maintTask.setCloseReason("转入缺陷");
        TaskCompleteParam taskCompleteParam = new TaskCompleteParam();
        taskCompleteParam.setTaskId(taskSubmitParam.getActivityId());
        Map<String, Object> variables = new HashMap<>();
        variables.put("result", true);
        //variables.put("assigns", maintTask.getHandler());
        taskCompleteParam.setVariables(variables);
        taskCompleteParam.setComment(taskSubmitParam.getRemark());
        activitiHandler.submitActiviti(taskCompleteParam, taskSubmitParam.getTaskId(), maintTask.getStatus(), taskSubmitParam.getForceSubmit());
        maintTaskMapper.updateById(maintTask);
        maintTaskDefectService.dealTaskDefect(maintTask.getId());
        return true;
    }

    public boolean defectAuditReject(TaskSubmitParam taskSubmitParam) {
        MaintTask maintTask = this.getSubmitEntity(taskSubmitParam.getTaskId());
        List<Integer> statusList = Lists.newArrayList(TaskStatusType.ACCEPT_DEFECT_AUDIT.getValue(),
                TaskStatusType.PROCESS_DEFECT_AUDIT.getValue());
        if (!statusList.contains(maintTask.getStatus())) {
            throw new GlobalServiceException(GlobalResultMessage.of("仅缺陷待审批状态支持通过/驳回操作"));
        }
        Map<String, Object> variables = new HashMap<>();
        //如果是执行中转缺陷
        if (maintTask.getStatus() == TaskStatusType.PROCESS_DEFECT_AUDIT.getValue()) {
            maintTask.setStatus(TaskStatusType.PROCESSING.getValue());
            variables.put("assigns", maintTask.getHandler());
        }
        //如果是验收转缺陷
        if (maintTask.getStatus() == TaskStatusType.ACCEPT_DEFECT_AUDIT.getValue()) {
            maintTask.setStatus(TaskStatusType.CHECK_ACCEPT.getValue());
            variables.put("assigns", maintTask.getCreateBy());
        }
        maintTask.setDefectFlag(false);
        TaskCompleteParam taskCompleteParam = new TaskCompleteParam();
        taskCompleteParam.setTaskId(taskSubmitParam.getActivityId());
        variables.put("result", true);
        taskCompleteParam.setVariables(variables);
        taskCompleteParam.setComment(taskSubmitParam.getRemark());
        activitiHandler.submitActiviti(taskCompleteParam, taskSubmitParam.getTaskId(), maintTask.getStatus(), taskSubmitParam.getForceSubmit());
        maintTaskMapper.updateById(maintTask);
        return true;
    }

    public boolean hangupAuditReject(TaskSubmitParam taskSubmitParam) {
        MaintTask maintTask = this.getSubmitEntity(taskSubmitParam.getTaskId());
        if (maintTask.getStatus() != TaskStatusType.HAND_UP_AUDIT.getValue()) {
            throw new GlobalServiceException(GlobalResultMessage.of("仅挂起审批中的工单允许 同意/驳回 挂起审批"));
        }
        maintTask.setStatus(TaskStatusType.PROCESSING.getValue());

        TaskCompleteParam taskCompleteParam = new TaskCompleteParam();
        taskCompleteParam.setTaskId(taskSubmitParam.getActivityId());
        Map<String, Object> variables = new HashMap<>();
        variables.put("result", false);
        variables.put("assigns", maintTask.getHandler());
        taskCompleteParam.setVariables(variables);
        taskCompleteParam.setComment(taskSubmitParam.getRemark());
        activitiHandler.submitActiviti(taskCompleteParam, taskSubmitParam.getTaskId(), maintTask.getStatus());
        maintTaskMapper.updateById(maintTask);
        return true;
    }

    public boolean hangupAuditPass(TaskSubmitParam taskSubmitParam) {
        MaintTask maintTask = this.getSubmitEntity(taskSubmitParam.getTaskId());
        if (maintTask.getStatus() != TaskStatusType.HAND_UP_AUDIT.getValue()) {
            throw new GlobalServiceException(GlobalResultMessage.of("仅挂起审批中的工单允许发起通过审批"));
        }
        maintTask.setStatus(TaskStatusType.HAND_UP_AUDIT_PASS.getValue());
        maintTask.setHasHangUp(true);
        TaskCompleteParam taskCompleteParam = new TaskCompleteParam();
        taskCompleteParam.setTaskId(taskSubmitParam.getActivityId());
        Map<String, Object> variables = new HashMap<>();
        variables.put("result", true);
        variables.put("assigns", maintTask.getHandler());
        taskCompleteParam.setVariables(variables);
        taskCompleteParam.setComment(taskSubmitParam.getRemark());
        activitiHandler.submitActiviti(taskCompleteParam, taskSubmitParam.getTaskId(), maintTask.getStatus());
        maintTaskMapper.updateById(maintTask);
        return true;
    }

    public boolean hangupAudit(TaskSubmitParam taskSubmitParam) {
        MaintTask maintTask = this.getSubmitEntity(taskSubmitParam.getTaskId());
        if (maintTask.getStatus() != TaskStatusType.PROCESSING.getValue()) {
            throw new GlobalServiceException(GlobalResultMessage.of("仅执行中的工单允许发起挂起审批"));
        }
        maintTask.setStatus(TaskStatusType.HAND_UP_AUDIT.getValue());

        TaskCompleteParam taskCompleteParam = new TaskCompleteParam();
        taskCompleteParam.setTaskId(taskSubmitParam.getActivityId());
        Map<String, Object> variables = new HashMap<>();
        variables.put("hangup", true);
        variables.put("assigns", maintTask.getCreateBy());
        taskCompleteParam.setVariables(variables);
        taskCompleteParam.setComment(taskSubmitParam.getRemark());
        activitiHandler.submitActiviti(taskCompleteParam, taskSubmitParam.getTaskId(), maintTask.getStatus());
        maintTaskMapper.updateById(maintTask);
        return true;
    }

    public boolean backtoReceived(TaskSubmitParam taskSubmitParam) {
        MaintTask maintTask = this.getSubmitEntity(taskSubmitParam.getTaskId());
//        if (maintTask.getStatus() == TaskStatusType.CLOSED.getValue() || maintTask.getStatus() == TaskStatusType.EXCEPTION_CLOSED.getValue()) {
//            log.error("该单已被关闭");
//            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("status_changed", null, LocaleContextHolder.getLocale())));
//        }
        maintTask.setStatus(TaskStatusType.PROCESSING.getValue());
        if (null != maintTask.getTaskDeadlineDate() && maintTask.getTaskDeadlineDate().compareTo(new Date()) < 0) {
            maintTask.setIsOverTime(true);
        } else {
            maintTask.setIsOverTime(false);
        }
        TaskCompleteParam taskCompleteParam = new TaskCompleteParam();
        taskCompleteParam.setTaskId(taskSubmitParam.getActivityId());
        Map<String, Object> variables = new HashMap<>();
        variables.put("backtosubmit", true);
        variables.put("assigns", maintTask.getHandler());
        taskCompleteParam.setVariables(variables);
        taskCompleteParam.setComment(taskSubmitParam.getRemark());
        activitiHandler.submitActiviti(taskCompleteParam, taskSubmitParam.getTaskId(), maintTask.getStatus(), taskSubmitParam.getForceSubmit());
        maintTaskMapper.updateById(maintTask);
        if (maintTask.getSourceType() == TaskSourceType.PLAN.getValue()) {
            planEquipmentTimeService.createNextTriggerTime(maintTask.getSourceId(), maintTask.getEquipmentId());
        }
        return true;
    }


    public boolean backToDelete(TaskSubmitParam taskSubmitParam) {
        MaintTask maintTask = this.getSubmitEntity(taskSubmitParam.getTaskId());
//        if (maintTask.getStatus() != TaskStatusType.SUBMIT_WAIT.getValue() || maintTask.getStatus() != TaskStatusType.REPAIR_AUDIT.getValue()) {
//            throw new GlobalServiceException(GlobalResultMessage.of("仅待提交状态时可以执行删除操作"));
//        }
        if (StringUtils.isBlank(taskSubmitParam.getActivityId())) {
            AuditActivitiServiceResult processTaskUserId = activitiHandler.getProcessTaskUserId(maintTask.getProcessInstanceId());
            if (processTaskUserId != null && StringUtils.isNotBlank(processTaskUserId.getTaskId())) {
                taskSubmitParam.setActivityId(processTaskUserId.getTaskId());
            }
        }
        TaskOptionParam taskOptionParam = new TaskOptionParam();
        taskOptionParam.setTaskId(taskSubmitParam.getActivityId());
        taskOptionParam.setComment(taskSubmitParam.getRemark());
        Boolean delete = activitiHandler.delete(maintTask.getId());
        if (delete) {
            this.removeById(taskSubmitParam.getTaskId());
            maintTaskRepairService.remove(new QueryWrapper<MaintTaskRepair>().lambda().eq(MaintTaskRepair::getTaskId, taskSubmitParam.getTaskId()));
            if (maintTask.getType() == TaskType.BREAKDOWN.getValue()) {
                manualRepairService.remove(new QueryWrapper<ManualRepair>().lambda().eq(ManualRepair::getId, maintTask.getSourceId()));
            }
            maintTaskAnalystService.remove(new QueryWrapper<MaintTaskAnalyst>().lambda().eq(MaintTaskAnalyst::getTaskId, taskSubmitParam.getTaskId()));
        }
        return true;
    }

    public boolean backToWaitReceiving(TaskSubmitParam taskSubmitParam) {
        MaintTask maintTask = this.getSubmitEntity(taskSubmitParam.getTaskId());
//        if (maintTask.getStatus() == TaskStatusType.CLOSED.getValue() || maintTask.getStatus() == TaskStatusType.EXCEPTION_CLOSED.getValue()) {
//            log.error("该单已被关闭");
//            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("status_changed", null, LocaleContextHolder.getLocale())));
//        }
        maintTask.setStatus(TaskStatusType.RECEIVING.getValue());
        if (null != maintTask.getTaskDeadlineDate() && maintTask.getTaskDeadlineDate().compareTo(new Date()) < 0) {
            maintTask.setIsOverTime(true);
        } else {
            maintTask.setIsOverTime(false);
        }
        TaskCompleteParam taskCompleteParam = new TaskCompleteParam();
        taskCompleteParam.setTaskId(taskSubmitParam.getActivityId());
        Map<String, Object> variables = new HashMap<>();
        RestResponse<List<String>> personResponse = baseServiceClient.getUidsByIds(maintTask.getAllStaffIds().split(","));
        if (personResponse.isOk()) {
            variables.put("assigns", personResponse.getData());
            maintTask.setStatus(TaskStatusType.RECEIVING.getValue());
            //派单时间，设置接单截止时间
//            this.setMaintTaskSendTaskDate(maintTask);
        }
        taskCompleteParam.setVariables(variables);
        taskCompleteParam.setComment(taskSubmitParam.getRemark());
        activitiHandler.submitActiviti(taskCompleteParam, taskSubmitParam.getTaskId(), maintTask.getStatus());
        maintTaskMapper.updateById(maintTask);
        if (maintTask.getSourceType() == TaskSourceType.PLAN.getValue()) {
            planEquipmentTimeService.createNextTriggerTime(maintTask.getSourceId(), maintTask.getEquipmentId());
        }
        return true;
    }

    public boolean backToSubmit(TaskSubmitParam taskSubmitParam) {
        MaintTask maintTask = this.getSubmitEntity(taskSubmitParam.getTaskId());
//        if (maintTask.getStatus() == TaskStatusType.CLOSED.getValue() || maintTask.getStatus() == TaskStatusType.EXCEPTION_CLOSED.getValue()) {
//            log.error("该单已被关闭");
//            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("status_changed", null, LocaleContextHolder.getLocale())));
//        }
        maintTask.setStatus(TaskStatusType.SUBMIT_WAIT.getValue());
        if (null != maintTask.getTaskDeadlineDate() && maintTask.getTaskDeadlineDate().compareTo(new Date()) < 0) {
            maintTask.setIsOverTime(true);
        } else {
            maintTask.setIsOverTime(false);
        }
        TaskCompleteParam taskCompleteParam = new TaskCompleteParam();
        taskCompleteParam.setTaskId(taskSubmitParam.getActivityId());
        Map<String, Object> variables = new HashMap<>();
        variables.put("backtosubmit", true);
        variables.put("assigns", maintTask.getCreateBy());
        taskCompleteParam.setVariables(variables);
        taskCompleteParam.setComment(taskSubmitParam.getRemark());
        activitiHandler.submitActiviti(taskCompleteParam, taskSubmitParam.getTaskId(), maintTask.getStatus());
        maintTask.setContent(null);
        maintTaskMapper.updateById(maintTask);
        if (maintTask.getSourceType() == TaskSourceType.PLAN.getValue()) {
            planEquipmentTimeService.createNextTriggerTime(maintTask.getSourceId(), maintTask.getEquipmentId());
        }
        return true;
    }

    //存储当前流程处理人
    private void buildNextUids(String taskId) {
        LambdaQueryWrapper<MaintTask> wrapper = new LambdaQueryWrapper<MaintTask>().eq(MaintTask::getId, taskId)
                .select(MaintTask::getProcessInstanceId, MaintTask::getId, MaintTask::getStatus, MaintTask::getType);
        MaintTask maintTask = maintTaskMapper.selectOne(wrapper);
        if (null != maintTask) {
            LambdaUpdateWrapper<MaintTask> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.eq(MaintTask::getId, taskId);
            if (StringUtils.isBlank(maintTask.getProcessInstanceId())
                    || (maintTask.getStatus() == TaskStatusType.EXCEPTION_CLOSED.getValue() || maintTask.getStatus() == TaskStatusType.CLOSED.getValue())) {
                updateWrapper.set(MaintTask::getUids, null);
            } else {
                List<String> uids = this.getProcessTaskUids(maintTask.getProcessInstanceId());
                if (CollectionUtils.isNotEmpty(uids)) {
                    updateWrapper.set(MaintTask::getUids, uids.toArray(new String[uids.size()]));
                } else {
                    updateWrapper.set(MaintTask::getUids, null);
                    log.error("获取流程任务处理人失败");
                }
            }
            this.update(updateWrapper);
            if (maintTask.getStatus() != null && maintTask.getType() != null && maintTask.getType() == TaskType.BREAKDOWN.getValue() &&
                    maintTask.getStatus() == TaskStatusType.CLOSED.getValue()) {
                activitiHandler.touchWorkingTimeCal(maintTask.getId());
            }
        } else {
            log.error("未找到当前工单");
        }
    }

    /**
     * 工单推送消息
     *
     * @param uids
     * @param typeValue
     * @param statusValue
     * @param name
     */
    private void push(List<String> uids, String typeValue, String statusValue, String name) {
        log.info("工单推送消息开始");
        SecStaffUidParam secStaffUidParam = new SecStaffUidParam();
        secStaffUidParam.setUids(uids);
        RestResponse<List<PorosSecStaffDto>> staffRestResponse = porosSecStaffClient.findUserByUidsPost(secStaffUidParam);
        if (staffRestResponse.isOk()) {
            List<PorosSecStaffDto> porosSecStaffDtos = staffRestResponse.getData();
            List<String> emails = new ArrayList<>();
            List<String> mobiles = new ArrayList<>();
            porosSecStaffDtos.stream().forEach(porosSecStaffDto -> {
                emails.add(porosSecStaffDto.getEmail());
                mobiles.add(porosSecStaffDto.getMobile());
            });
            String title = typeValue + StringPool.DASH + statusValue;
            List<String> _uids = uids.stream().distinct().collect(Collectors.toList());
            List<String> _emails = emails.stream().distinct().collect(Collectors.toList());
            List<String> _mobiles = mobiles.stream().distinct().collect(Collectors.toList());
            String content = "您有一条" + statusValue + "的工单:" + name + ",请及时处理";
            EmailNotify emailNotify = EmailNotify.builder().title(title).content(content).emails(_emails.toArray(new String[_emails.size()])).build();
            PushNotify pushNotify = PushNotify.builder().title(title).content(content).uids(_uids.toArray(new String[_uids.size()])).build();
            Map<String, String> smsParams = new HashMap<>(3);
            smsParams.put("name", name);
            smsParams.put("statusValue", statusValue);
            SMSNotify smsNotify = SMSNotify.builder().sign(defaultSign).template(taskTemplate).params(smsParams).mobiles(_mobiles.toArray(new String[_mobiles.size()])).build();
            notifyClient.sendNotify(NotifyParam.builder().notifyTypes(EnumSet.of(NotifyType.EMAIL, NotifyType.APP, NotifyType.SMS)).emailNotify(emailNotify).pushNotify(pushNotify).smsNotify(smsNotify).build());
        } else {
            log.error("根据UIDS获取用户列表失败", staffRestResponse.getMsg());
        }
    }

    private MaintTask getSubmitEntity(String taskId) {
        LambdaQueryWrapper<MaintTask> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(MaintTask::getId, taskId);
//        wrapper.select(MaintTask::getId, MaintTask::getStatus, MaintTask::getFreeTask, MaintTask::getReviewer, MaintTask::getCreateBy,
//                MaintTask::getSourceType, MaintTask::getName, MaintTask::getProcessInstanceId, MaintTask::getBeginMaintTime,
//                MaintTask::getEndMaintTime, MaintTask::getStartTime, MaintTask::getAlreadyReviewer, MaintTask::getReviewContent,
//                MaintTask::getHandler, MaintTask::getWorkingTime, MaintTask::getSourceId, MaintTask::getEquipmentId,
//                MaintTask::getDispatchHandler, MaintTask::getEvaluateHandler, MaintTask::getDefectId);
        MaintTask maintTask = maintTaskMapper.selectOne(wrapper);
        maintTask.setFinishFileIds(null);
        maintTask.setContent(null);
        return maintTask;
    }

    /**
     * 维护工单指派
     *
     * @return
     */
    public boolean assignTask(TaskSubmitParam taskSubmitParam) {
        List<String> pushUids;
        MaintTask maintTask = this.getSubmitEntity(taskSubmitParam.getTaskId());
        if (maintTask.getStatus() != TaskStatusType.DISPATCH.getValue()) {
            log.error("该单已被其他人指派");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("status_changed", null, LocaleContextHolder.getLocale())));
        }
        //初始化接单人
        maintTask.setStaffIds(taskSubmitParam.getStaffIds());
        maintTask.setTeamIds(taskSubmitParam.getTeamIds());
        maintTask.setHandler("");
        maintTask.setStatus(TaskStatusType.REPAIR_AUDIT.getValue());
        buildAllStaffIds(maintTask);
        UserBaseInfo userBaseInfo = PorosContextHolder.getCurrentUser();
        maintTask.setDispatchHandler(new String[]{userBaseInfo.getUid()});

        //设置派单审核员角色
        RestResponse<List<String>> dispatchRoleResponse = porosSecGrantClient.getUidsByRoleCode(dispatcherRoleCode);
        if (dispatchRoleResponse.isOk()) {
            List<String> roleUids = dispatchRoleResponse.getData();
            maintTask.setDispatchReviewer(roleUids.toArray(new String[roleUids.size()]));
            TaskCompleteParam taskCompleteParam = new TaskCompleteParam();
            taskCompleteParam.setTaskId(taskSubmitParam.getActivityId());
            Map<String, Object> variables = new HashMap<>();
            variables.put("assigns", roleUids);
            taskCompleteParam.setVariables(variables);
            taskCompleteParam.setComment(taskSubmitParam.getRemark());
            activitiHandler.submitActiviti(taskCompleteParam, taskSubmitParam.getTaskId(), maintTask.getStatus());
            maintTaskMapper.updateById(maintTask);
        } else {
            log.error(dispatchRoleResponse.getMsg());
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("check_error", null, LocaleContextHolder.getLocale())));
        }

        Boolean repairAudit = false;
        if (maintTask.getSourceType() == TaskSourceType.BREAKDOWN.getValue() || maintTask.getSourceType() == TaskSourceType.DEFECT.getValue()) {
            repairAudit = jobTicketService.getRepairTaskAudit();
        }
        //维保单或者故障单不需要审核，直接跳过审核节点/自由扫码默认跳过审核节点
        if (!repairAudit || taskSubmitParam.getFreeTask()) {
            maintTask.setStatus(TaskStatusType.RECEIVING.getValue());
            String activityId = this.getProcessTaskId(maintTask.getProcessInstanceId(), true);
            if (StringUtils.isEmpty(activityId)) {
                return false;
            }
            TaskCompleteParam taskCompleteParam = new TaskCompleteParam();
            taskCompleteParam.setTaskId(activityId);
            Map<String, Object> variables = new HashMap<>();
            if (StringUtils.isBlank(maintTask.getAllStaffIds())) {
                log.error("待接单人员为空");
                throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("check_error", null, LocaleContextHolder.getLocale())));
            }
            RestResponse<List<String>> restResponse = baseServiceClient.getUidsByIds(maintTask.getAllStaffIds().split(","));
            if (restResponse.isOk()) {
                variables.put("assigns", restResponse.getData());
                pushUids = restResponse.getData();
            } else {
                log.error(restResponse.getMsg());
                throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("feign_error", null, LocaleContextHolder.getLocale())));
            }
            taskCompleteParam.setVariables(variables);
            taskCompleteParam.setComment(taskSubmitParam.getRemark());
            activitiHandler.submitActiviti(taskCompleteParam, taskSubmitParam.getTaskId(), maintTask.getStatus());
            //派单时间，设置接单截止时间
            this.setMaintTaskSendTaskDate(maintTask);
            maintTaskMapper.updateById(maintTask);
            //推送消息
            //this.push(pushUids, TaskSourceType.getNameByValue(maintTask.getSourceType()), "待接单", maintTask.getName());
            return true;
        }
        return true;
    }

    /**
     * 维护工单接单
     *
     * @return
     */
    public boolean receivingTask(TaskSubmitParam taskSubmitParam) {
        MaintTask maintTask = this.getSubmitEntity(taskSubmitParam.getTaskId());
        UserBaseInfo userInfo = PorosContextHolder.getCurrentUser();
        if (null == userInfo) {
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("user_error", null, LocaleContextHolder.getLocale())));
        }
        if (maintTask.getType() == TaskType.BREAKDOWN.getValue()) {
            MaintTaskFaultDto faultDtoById = maintTaskRepairService.getFaultDtoById(maintTask.getId());
//            if (faultDtoById != null && faultDtoById.getLotoFlag() != null && faultDtoById.getLotoFlag() == TaskLotoType.DEFAULT.getValue()) {
//                throw new GlobalServiceException(GlobalResultMessage.of("故障单必须在app中执行LOTO作业审批"));
//            }
        }
        String staffId;
        RestResponse<String> listRestResponse = baseServiceClient.getIdByUid(userInfo.getUid());
        if (!listRestResponse.isSuccess()) {
            log.error("获取维护人员id失败");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("feign_error", null, LocaleContextHolder.getLocale())));
        } else {
            staffId = listRestResponse.getData();
        }
        if (maintTask.getFreeTask().equals(StaticValue.ONE) && maintTask.getStatus().intValue() == TaskStatusType.DISPATCH.getValue()) {
            //人工扫码，待派单工单，需要先自动指派,再跳过审核节点
            String[] staffIds = new String[]{staffId};
            taskSubmitParam.setStaffIds(staffIds);
            taskSubmitParam.setFreeTask(true);
            if (StringUtils.isBlank(taskSubmitParam.getActivityId())) {
                taskSubmitParam.setActivityId(this.getProcessTaskId(maintTask.getProcessInstanceId(), true));
            }
            this.assignTask(taskSubmitParam);
            taskSubmitParam.setActivityId(this.getProcessTaskId(maintTask.getProcessInstanceId(), true));
            maintTask = this.getSubmitEntity(taskSubmitParam.getTaskId());
        } else if (maintTask.getStatus().intValue() == TaskStatusType.DISPATCH.getValue() && maintTask.getAllStaffIds().contains(staffId)) {
            //不是自由扫码得工单，但是处于待派单状态，并且当前人在此工单的参与人当中，允许其提前接单
            String[] staffIds = new String[]{staffId};
            taskSubmitParam.setStaffIds(staffIds);
            taskSubmitParam.setFreeTask(true);
            if (StringUtils.isBlank(taskSubmitParam.getActivityId())) {
                taskSubmitParam.setActivityId(this.getProcessTaskId(maintTask.getProcessInstanceId(), true));
            }
            this.assignTask(taskSubmitParam);
            taskSubmitParam.setActivityId(this.getProcessTaskId(maintTask.getProcessInstanceId(), true));
            maintTask = this.getSubmitEntity(taskSubmitParam.getTaskId());
        } else if (maintTask.getStatus().intValue() == TaskStatusType.RECEIVING.getValue() && maintTask.getAllStaffIds().contains(staffId)) {
            //已处于待接单的，不跳过
        } else {
            //正常接单需要校验是否被其他人处理
            if (maintTask.getStatus() != TaskStatusType.RECEIVING.getValue()) {
                log.error("该单已被其他人接受");
                throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("status_changed", null, LocaleContextHolder.getLocale())));
            }
        }
        maintTask.setRecTaskDate(new Date());
        if (maintTask.getSourceType() != TaskSourceType.BREAKDOWN.getValue()) {
            //记录工单开始日期(报修创建时候已标记)
            maintTask.setBeginMaintTime(new Date());
            maintTask.setStartTime(new Date());
            maintTask.setHangUp(false);
        }
        maintTask.setStatus(TaskStatusType.HANDLE.getValue());
        maintTask.setHandler(userInfo.getUid());

        TaskCompleteParam taskCompleteParam = new TaskCompleteParam();
        taskCompleteParam.setTaskId(taskSubmitParam.getActivityId());
        Map<String, Object> variables = new HashMap<>();
        variables.put("assigns", Lists.newArrayList(userInfo.getUid()));
        variables.put("backtosubmit", false);
        taskCompleteParam.setVariables(variables);
        taskCompleteParam.setComment(taskSubmitParam.getRemark());
        activitiHandler.submitActiviti(taskCompleteParam, taskSubmitParam.getTaskId(), maintTask.getStatus());
        maintTaskMapper.updateById(maintTask);
        if (maintTask.getSourceType() == TaskSourceType.BREAKDOWN.getValue()) {
            //更新关联保修单
            ManualRepairEditParam manualRepairEditParam = new ManualRepairEditParam();
            manualRepairEditParam.setId(maintTask.getSourceId());
            manualRepairEditParam.setRepairTime(maintTask.getEndMaintTime());
            manualRepairEditParam.setStatus(StaticValue.ONE);
            manualRepairService.updateStatus(manualRepairEditParam);
        }
        //this.push(Lists.newArrayList(userInfo.getUid()), TaskSourceType.getNameByValue(maintTask.getSourceType()), "待处理", maintTask.getName());
        return true;
    }

    /**
     * 维护工单开始安全确认
     *
     * @return
     */
    public boolean beginConfirmTask(TaskSubmitParam taskSubmitParam) {
        MaintTask maintTask = this.getSubmitEntity(taskSubmitParam.getTaskId());
        if (maintTask.getStatus() != TaskStatusType.HANDLE.getValue()) {
            log.error("该单已被执行");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("status_changed", null, LocaleContextHolder.getLocale())));
        }

        maintTask.setReviewContent("");
        maintTask.setAlreadyReviewer(new String[]{});
        maintTask.setStatus(TaskStatusType.BEGIN_CONFIRM.getValue());
        //保存作业票信息
        TaskTicketDto taskTicketDto = this.getTaskTicketById(maintTask.getId());
        this.updateTaskTicket(taskTicketDto);

        TaskCompleteParam taskCompleteParam = new TaskCompleteParam();
        taskCompleteParam.setTaskId(taskSubmitParam.getActivityId());
        Map<String, Object> variables = new HashMap<>();
        variables.put("confirm", true);
        variables.put("transfer", false);
        RestResponse<List<String>> roleResponse = porosSecGrantClient.getUidsByRoleCode(assingRoleCode);
        if (roleResponse.isOk() && CollectionUtils.isNotEmpty(roleResponse.getData())) {
            List<String> roleUids = roleResponse.getData();
            variables.put("assigns", roleUids);
        }
        taskCompleteParam.setVariables(variables);
        taskCompleteParam.setComment(taskSubmitParam.getRemark());
        activitiHandler.submitActiviti(taskCompleteParam, taskSubmitParam.getTaskId(), maintTask.getStatus());
        maintTaskMapper.updateById(maintTask);
        return true;
    }

    /**
     * 维护工单安全确认提交审核
     *
     * @return
     */
    public boolean confirmTask(TaskSubmitParam taskSubmitParam) {
        MaintTask maintTask = this.getSubmitEntity(taskSubmitParam.getTaskId());
        if (maintTask.getStatus() != TaskStatusType.BEGIN_CONFIRM.getValue()) {
            log.error("该单已开始确认审核");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("status_changed", null, LocaleContextHolder.getLocale())));
        }

        maintTask.setStatus(TaskStatusType.CONFIRM.getValue());

        TaskCompleteParam taskCompleteParam = new TaskCompleteParam();
        taskCompleteParam.setTaskId(taskSubmitParam.getActivityId());
        Map<String, Object> variables = new HashMap<>();
        variables.put("assigns", Arrays.asList(maintTask.getReviewer()));
        taskCompleteParam.setVariables(variables);
        taskCompleteParam.setComment(taskSubmitParam.getRemark());
        activitiHandler.submitActiviti(taskCompleteParam, taskSubmitParam.getTaskId(), maintTask.getStatus());
        maintTaskMapper.updateById(maintTask);
        return true;
    }

    /**
     * 维护工单已确认
     *
     * @return
     */
    public boolean confirmedTask(TaskSubmitParam taskSubmitParam) {
        MaintTask maintTask = this.getSubmitEntity(taskSubmitParam.getTaskId());
        if (maintTask.getStatus() != TaskStatusType.CONFIRM.getValue()) {
            log.error("该单已被确认");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("status_changed", null, LocaleContextHolder.getLocale())));
        }
        UserBaseInfo userBaseInfo = PorosContextHolder.getCurrentUser();
        List<String> alreadyReviewer = new ArrayList<>();
        if (null != maintTask.getAlreadyReviewer() && maintTask.getAlreadyReviewer().length > 0) {
            alreadyReviewer.addAll(Arrays.asList(maintTask.getAlreadyReviewer()));
        }
        alreadyReviewer.add(userBaseInfo.getUid());
        //将当前登录人员放入已审核人员中
        maintTask.setAlreadyReviewer(alreadyReviewer.toArray(new String[alreadyReviewer.size()]));
        String content = userBaseInfo.getName();
        if (StringUtils.isNotBlank(taskSubmitParam.getRemark())) {
            content = content + ":" + taskSubmitParam.getRemark() + ";";
        } else {
            content += ":通过;";
        }
        String reviewContent = StringUtils.isNotBlank(maintTask.getReviewContent()) ?
                maintTask.getReviewContent() + content : content;
        maintTask.setReviewContent(reviewContent);

        //审核人员全部通过
        if (maintTask.getReviewer().length == alreadyReviewer.size()) {
            maintTask.setStatus(TaskStatusType.CONFIRMED.getValue());
            maintTask.setSecureConfirmed(1);
            TaskCompleteParam taskCompleteParam = new TaskCompleteParam();
            taskCompleteParam.setTaskId(taskSubmitParam.getActivityId());
            Map<String, Object> variables = new HashMap<>();
            variables.put("assigns", Lists.newArrayList(maintTask.getHandler()));
            taskCompleteParam.setVariables(variables);
            taskCompleteParam.setComment(maintTask.getReviewContent());
            activitiHandler.submitActiviti(taskCompleteParam, taskSubmitParam.getTaskId(), maintTask.getStatus());
            maintTaskMapper.updateById(maintTask);
            return true;
        } else {
            return maintTaskMapper.updateById(maintTask) > 0;
        }
    }

    /**
     * 维护工单确认驳回
     *
     * @return
     */
    public boolean rejectConfirmedTask(TaskSubmitParam taskSubmitParam) {
        MaintTask maintTask = this.getSubmitEntity(taskSubmitParam.getTaskId());
        if (maintTask.getStatus() != TaskStatusType.CONFIRM.getValue()) {
            log.error("该单已被确认");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("status_changed", null, LocaleContextHolder.getLocale())));
        }
        UserBaseInfo userBaseInfo = PorosContextHolder.getCurrentUser();

        String content = userBaseInfo.getName() + ":" + taskSubmitParam.getRemark() + ";";
        String reviewContent = StringUtils.isNotBlank(maintTask.getReviewContent()) ?
                maintTask.getReviewContent() + content : content;
        maintTask.setReviewContent(reviewContent);
        maintTask.setStatus(TaskStatusType.BEGIN_CONFIRM.getValue());
        maintTask.setAlreadyReviewer(new String[]{});
        PartTaskAuditParam taskAuditParam = new PartTaskAuditParam();
        taskAuditParam.setActivityId(taskSubmitParam.getActivityId());
        taskAuditParam.setComment(reviewContent);
        taskAuditParam.setProcessInstanceId(maintTask.getProcessInstanceId());
        activitiHandler.rejectActiviti(taskAuditParam, taskSubmitParam.getTaskId(), maintTask.getStatus());
        maintTaskMapper.updateById(maintTask);
        return true;
    }

    /**
     * 开始工作
     *
     * @return
     */
    public boolean processingTask(TaskSubmitParam taskSubmitParam) {
        MaintTask maintTask = this.getSubmitEntity(taskSubmitParam.getTaskId());
        List<Integer> statusList = Lists.newArrayList(TaskStatusType.HANDLE.getValue(), TaskStatusType.CONFIRMED.getValue(),
                TaskStatusType.HAND_UP_AUDIT_PASS.getValue(), TaskStatusType.HANG_UP.getValue());
        if (!statusList.contains(maintTask.getStatus())) {
            log.error("该单已被执行");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("status_changed", null, LocaleContextHolder.getLocale())));
        }
        List<MaintTaskAssistPerson> assistPeoples =
                assistPersonService.getAssistPersonListByTaskId(taskSubmitParam.getTaskId(), TaskTimeModeType.AUTOMATIC.getValue());
        TaskCompleteParam taskCompleteParam = new TaskCompleteParam();
        taskCompleteParam.setTaskId(taskSubmitParam.getActivityId());
        Map<String, Object> variables = new HashMap<>();

        if (null == maintTask.getWorkingTime()) {
            if (null == maintTask.getBeginMaintTime()) {
                //兼容旧数据
                maintTask.setBeginMaintTime(new Date());
            }
            //首次记录从接单到开始工作的时间
            /*Long workingTime = (new Date().getTime() - maintTask.getBeginMaintTime().getTime()) / 1000 / 60;
            maintTask.setWorkingTime(workingTime);*/
        }

        if (maintTask.getStatus() != TaskStatusType.HANG_UP.getValue()) {
            if (maintTask.getStatus() == TaskStatusType.HANDLE.getValue()) {
                //从待执行跳过安全确认
                variables.put("confirm", false);
                variables.put("transfer", false);
                maintTask.setConfirm(false);
            }
            variables.put("assigns", Lists.newArrayList(maintTask.getHandler()));
            taskCompleteParam.setVariables(variables);
            taskCompleteParam.setComment(taskSubmitParam.getRemark());
            maintTask.setStatus(TaskStatusType.PROCESSING.getValue());
            activitiHandler.submitActiviti(taskCompleteParam, taskSubmitParam.getTaskId(), maintTask.getStatus());
            maintTaskMapper.updateById(maintTask);
            updateAssistStartTime(assistPeoples);
            return true;
        } else {
            //重置标记日期(挂起后重新计时)
            maintTask.setStartTime(new Date());
            maintTask.setHangUp(false);
            //挂起状态启动不修改工作流状态
            maintTask.setStatus(TaskStatusType.PROCESSING.getValue());
            int result = maintTaskMapper.updateById(maintTask);
            updateAssistStartTime(assistPeoples);
            return result > 0;
        }
    }

    private void updateAssistStartTime(List<MaintTaskAssistPerson> assistPeoples) {
        if (CollectionUtils.isNotEmpty(assistPeoples)) {
            assistPeoples.forEach(ap -> ap.setStartTime(new Date()));
            assistPersonService.updateBatchById(assistPeoples);
        }
    }

    /**
     * 挂起
     *
     * @return
     */
    public boolean hangUpTask(TaskSubmitParam taskSubmitParam) {
        MaintTask maintTask = this.getSubmitEntity(taskSubmitParam.getTaskId());
        if (maintTask.getStatus() != TaskStatusType.PROCESSING.getValue()) {
            log.error("该单已被执行");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("status_changed", null, LocaleContextHolder.getLocale())));
        }

        maintTask.setHangUp(true);
        maintTask.setStatus(TaskStatusType.HANG_UP.getValue());
        maintTask.setHasHangUp(true);
        //挂起后计算本次工作时间
        Long workingTime = (new Date().getTime() - maintTask.getStartTime().getTime()) / 1000 / 60;
        maintTask.setWorkingTime(null != maintTask.getWorkingTime() ? maintTask.getWorkingTime() + workingTime : workingTime);
        return maintTaskMapper.updateById(maintTask) > 0;
    }

    /**
     * 完成工作
     *
     * @return
     */
    public boolean completeTask(TaskSubmitParam taskSubmitParam) {
        MaintTask maintTask = this.getSubmitEntity(taskSubmitParam.getTaskId());
        if (maintTask.getStatus() != TaskStatusType.PROCESSING.getValue()) {
            log.error("该单已被完成");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("status_changed", null, LocaleContextHolder.getLocale())));
        }
        //如果最后提交时针对ec结果未处理，则置为未处理
        if (maintTask.getType() != null && maintTask.getType() == TaskType.EC.getValue()
                && (null == maintTask.getEcErrorResult() || TaskEcErrorResultType.DEFAULT.getValue() == maintTask.getEcErrorResult())) {
            maintTask.setEcErrorResult(TaskEcErrorResultType.NODEAL.getValue());
        }
        List<MaintTaskAssistPerson> assistPeoples =
                assistPersonService.getAssistPersonListByTaskId(taskSubmitParam.getTaskId(), TaskTimeModeType.AUTOMATIC.getValue());
        //this.dealMaintTime(maintTask);
        maintTask.setHangUp(false);
        //计算结束日期
        if (maintTask.getType() != null && maintTask.getType() != TaskType.DEFECT.getValue() && maintTask.getType() != TaskType.BREAKDOWN.getValue()) {
            maintTask.setEndMaintTime(new Date());
            //完成工作计算工时(去除掉挂起时间；中间如果挂起过，starTime已经重置过，之前的区间已经计算，只计算最后一次区间)
            Long workingTime = (maintTask.getEndMaintTime().getTime() - maintTask.getStartTime().getTime()) / 1000 / 60;
            maintTask.setWorkingTime(null != maintTask.getWorkingTime() ? maintTask.getWorkingTime() + workingTime : workingTime);
            if (maintTask.getWorkingTime() <= 0) {
                //时间太短，不够一分钟，直接截取为一分钟
                maintTask.setWorkingTime(1L);
            }
            maintTask.setMaintTimeCost("" + maintTask.getWorkingTime());
        }

        if (StringUtils.isBlank(maintTask.getDownTimeCost()) && maintTask.getBeginDowntime() != null && maintTask.getEndDowntime() != null) {
            maintTask.setDownTimeCost("" + DateUtil.between(maintTask.getBeginDowntime(), maintTask.getEndDowntime(), DateUnit.MINUTE));
        }

        maintTask.setStatus(TaskStatusType.CHECK_ACCEPT.getValue());

        TaskCompleteParam taskCompleteParam = new TaskCompleteParam();
        taskCompleteParam.setTaskId(taskSubmitParam.getActivityId());
        Map<String, Object> variables = new HashMap<>();
        List<String> uids = new ArrayList<>();
        Boolean jumpCheck = false;
        if (maintTask.getSourceType() == TaskSourceType.BREAKDOWN.getValue()) {
            //故障维修只能创建人评价
            uids.add(maintTask.getCreateBy());
        } else if (maintTask.getSourceType() == TaskSourceType.DEFECT.getValue()) {
            //缺陷，工单评价员角色评价
            RestResponse<List<String>> restResponse = porosSecGrantClient.getUidsByRoleCode(evaluateRoleCode);
            if (restResponse.isOk()) {
                uids = restResponse.getData();
            } else {
                log.error("获取中台工单评价角色人员失败->" + JSONObject.toJSON(restResponse));
                throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("feign_error", null, LocaleContextHolder.getLocale())));
            }
        } else if (maintTask.getType() == TaskType.PLAN.getValue() && maintTask.getJobLevel() == PlanJobLevelType.ONE.getValue()) {
            maintTask.setStatus(TaskStatusType.CLOSED.getValue());
            jumpCheck = true;
        } else {
            String sourcePlanEquipmentId = maintTask.getSourcePlanEquipmentId();
            if (StringUtils.isNotBlank(sourcePlanEquipmentId)) {
                PlanEquipmentTime byId = (PlanEquipmentTime) planEquipmentTimeService.getById(sourcePlanEquipmentId);
                if (ArrayUtil.isNotEmpty(byId.getAcceptMaintainerIds())) {
                    List<String> uidOfMaintainerIds = commonGetHandler.getUidOfMaintainerIds(byId.getAcceptMaintainerIds());
                    uids.addAll(uidOfMaintainerIds);
                }
                if (ArrayUtil.isNotEmpty(byId.getAcceptTeamIds())) {
                    uids.addAll(commonGetHandler.getUidOfMaintTeamIds(byId.getAcceptTeamIds()));
                }
            }
            if (ArrayUtils.isNotEmpty(maintTask.getAcceptMaintainerIds())) {
                uids.addAll(commonGetHandler.getUidOfMaintainerIds(maintTask.getAcceptMaintainerIds()));
            }
            if (ArrayUtils.isNotEmpty(maintTask.getAcceptTeamIds())) {
                uids.addAll(commonGetHandler.getUidOfMaintTeamIds(maintTask.getAcceptTeamIds()));
            }
            if (CollectionUtils.isEmpty(uids)) {
                //维保计划中没有设置单独的维保人员的，从角色中填充
                RestResponse<List<String>> restResponse = porosSecGrantClient.getUidsByRoleCode(evaluateRoleCode);
                if (restResponse.isOk()) {
                    uids = restResponse.getData();
                } else {
                    log.error("获取中台工单评价角色人员失败->" + JSONObject.toJSON(restResponse));
                    throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("feign_error", null, LocaleContextHolder.getLocale())));
                }
            }
        }
        if (!jumpCheck) {
            if (CollectionUtils.isEmpty(uids)) {
                log.error("未配置工单评价员，不能转到评价节点");
                throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("check_error", null, LocaleContextHolder.getLocale())));
            } else {
                maintTask.setEvaluateHandler(uids.toArray(new String[uids.size()]));
            }
        }
        variables.put("assigns", uids);
        variables.put("transfer", false);
        variables.put("hangup", false);
        taskCompleteParam.setVariables(variables);
        taskCompleteParam.setComment(taskSubmitParam.getRemark());
        activitiHandler.submitActiviti(taskCompleteParam, taskSubmitParam.getTaskId(), maintTask.getStatus());
        if (null != maintTask.getTaskDeadlineDate() && maintTask.getTaskDeadlineDate().compareTo(new Date()) < 0) {
            maintTask.setIsOverTime(true);
        } else {
            maintTask.setIsOverTime(false);
        }
        maintTaskMapper.updateById(maintTask);
        updateAssistWorkTime(assistPeoples);
        if (maintTask.getSourceType() == TaskSourceType.BREAKDOWN.getValue()) {
            //更新关联保修单
            ManualRepairEditParam manualRepairEditParam = new ManualRepairEditParam();
            manualRepairEditParam.setId(maintTask.getSourceId());
            manualRepairEditParam.setRepairTime(maintTask.getEndMaintTime());
            manualRepairEditParam.setStatus(StaticValue.TWO);
            manualRepairService.updateStatus(manualRepairEditParam);

            //推送故障树引用
            MaintTaskFaultDto faultDto = maintTaskRepairService.getFaultDtoById(maintTask.getId());
            if (null != faultDto) {
                FaultTreeCountParam param = CopyDataUtil.copyObject(faultDto, FaultTreeCountParam.class);
                baseServiceClient.updateQuoteCount(param);
            }
        } else if (maintTask.getSourceType() == TaskSourceType.PLAN.getValue()) {
            planEquipmentTimeService.createNextTriggerTime(maintTask.getSourceId(), maintTask.getEquipmentId());
            //推送参数监控
            taskItemService.pushToIot(maintTask.getId());
        }
        //this.push(uids, TaskSourceType.getNameByValue(maintTask.getSourceType()), "待验收", maintTask.getName());
        return true;
    }


    @Async
    public void dealKnowledgePush(MaintTaskRepair taskSubmitParam) {
        MaintTaskFaultDto maintTaskFaultDto = taskRepairService.getFaultDtoById(taskSubmitParam.getTaskId());
        if (ObjectUtils.isNull(maintTaskFaultDto)) {
            return;
        }
        MaintTask byId = (MaintTask) this.getById(taskSubmitParam.getTaskId());
        if (ObjectUtils.isNull(byId)) {
            return;
        }
        RestResponse<EquipmentInfoDto> equipmentInfo = equipmentClient.getEquipmentInfo(byId.getEquipmentId());
        if (!equipmentInfo.isOk()) {
            return;
        }
        EquipmentInfoDto data = equipmentInfo.getData();
        if (ObjectUtils.isNull(data) || StringUtils.isEmpty(data.getCategoryId())) {
            return;
        }
        PushFaultTreeParam pushFaultTreeParam = new PushFaultTreeParam();
        pushFaultTreeParam.setPhenomenonDesc(StringUtils.isNotBlank(maintTaskFaultDto.getFaultPhenomenonRemark()) ? maintTaskFaultDto.getFaultPhenomenonRemark() : "");
        pushFaultTreeParam.setReasonDesc(StringUtils.isNotBlank(maintTaskFaultDto.getFaultReasonRemark()) ? maintTaskFaultDto.getFaultReasonRemark() : "");
        pushFaultTreeParam.setMeasuresTitle(StringUtils.isNotBlank(maintTaskFaultDto.getFaultMeasuresRemark()) ? maintTaskFaultDto.getFaultMeasuresRemark() : "");
        pushFaultTreeParam.setEquipmentCategoryId(data.getCategoryId());
        log.info("推送故障树：{},{}", JSON.toJSONString(taskSubmitParam), pushFaultTreeParam);
        baseServiceClient.pushFaultTree(pushFaultTreeParam);
    }

    public void dealMaintTime(MaintTask maintTask) {
        maintTask.setEndMaintTime(new Date());
        if (maintTask.getBeginMaintTime() != null && maintTask.getEndMaintTime() != null) {
            maintTask.setMaintTimeCost("" + DateUtil.between(maintTask.getBeginMaintTime(), maintTask.getEndMaintTime(), DateUnit.MINUTE));
        }
        if (StringUtils.isBlank(maintTask.getDownTimeCost()) && maintTask.getBeginDowntime() != null && maintTask.getEndDowntime() != null) {
            maintTask.setDownTimeCost("" + DateUtil.between(maintTask.getBeginDowntime(), maintTask.getEndDowntime(), DateUnit.MINUTE));
        }
    }

    private void updateAssistWorkTime(List<MaintTaskAssistPerson> assistPeoples) {
        if (CollectionUtils.isNotEmpty(assistPeoples)) {
            assistPeoples.forEach(ap -> {
                Date endTime = new Date();
                ap.setEndTime(endTime);
                if (ap.getStartTime() != null) {
                    double workTime = cn.getech.ehm.task.util.DateUtils.getDateMistake(ap.getStartTime(), endTime);
                    ap.setWorkHours(BigDecimal.valueOf(workTime));
                }
            });
            assistPersonService.updateBatchById(assistPeoples);
        }
    }

    /**
     * 维护工单验收
     *
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean checkAccessTask(TaskSubmitParam taskSubmitParam) {
        MaintTask maintTask = this.getSubmitEntity(taskSubmitParam.getTaskId());
        List<Integer> statusList = Lists.newArrayList(TaskStatusType.CHECK_ACCEPT.getValue(), TaskStatusType.PROCESSING.getValue());
        if (!statusList.contains(maintTask.getStatus())) {
            log.error("该单暂不可被验收");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("status_changed", null, LocaleContextHolder.getLocale())));
        }
        maintTask.setStatus(TaskStatusType.CLOSED.getValue());
        if (maintTask.getType() != null && (maintTask.getType() == TaskType.DEFECT.getValue() || maintTask.getType() == TaskType.BREAKDOWN.getValue())) {
            maintTask.setEndMaintTime(new Date());
            //完成工作计算工时(去除掉挂起时间；中间如果挂起过，starTime已经重置过，之前的区间已经计算，只计算最后一次区间)
            Long workingTime = (maintTask.getEndMaintTime().getTime() - maintTask.getStartTime().getTime()) / 1000 / 60;
            maintTask.setWorkingTime(null != maintTask.getWorkingTime() ? maintTask.getWorkingTime() + workingTime : workingTime);
            if (maintTask.getWorkingTime() <= 0) {
                //时间太短，不够一分钟，直接截取为一分钟
                maintTask.setWorkingTime(1L);
            }
            maintTask.setMaintTimeCost("" + maintTask.getWorkingTime());
        }
        TaskCompleteParam taskCompleteParam = new TaskCompleteParam();
        taskCompleteParam.setTaskId(taskSubmitParam.getActivityId());
        taskCompleteParam.setComment(taskSubmitParam.getRemark());

        Map<String, Object> variables = new HashMap<>();
        variables.put("backtosubmit", false);
        taskCompleteParam.setVariables(variables);
        activitiHandler.submitActiviti(taskCompleteParam, taskSubmitParam.getTaskId(), maintTask.getStatus(), taskSubmitParam.getForceSubmit());
        maintTaskMapper.updateById(maintTask);
        if (maintTask.getSourceType() == TaskSourceType.DEFECT.getValue()) {
            //缺陷生成的工单，需要同步状态
            defectInfoService.updateDefectStatusById(maintTask.getSourceId(), DefectStatusEnum.DONE.getValue());
        }
//        if (maintTask != null && maintTask.getType() != null && maintTask.getType() == TaskType.BREAKDOWN.getValue()) {
//            activitiHandler.touchWorkingTimeCal(maintTask.getId());
//        }
        return true;
    }

    /**
     * 异常关闭
     *
     * @return
     */
    public boolean exceptionClosedTask(TaskSubmitParam taskSubmitParam) {
        MaintTask maintTask = this.getSubmitEntity(taskSubmitParam.getTaskId());
        if (maintTask.getStatus() == TaskStatusType.CLOSED.getValue() || maintTask.getStatus() == TaskStatusType.EXCEPTION_CLOSED.getValue()) {
            log.error("该单已被关闭");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("status_changed", null, LocaleContextHolder.getLocale())));
        }
        maintTask.setStatus(TaskStatusType.EXCEPTION_CLOSED.getValue());
        maintTask.setCloseReason(taskSubmitParam.getRemark());
        List<MaintTaskAssistPerson> assistPeoples =
                assistPersonService.getAssistPersonListByTaskId(taskSubmitParam.getTaskId(), TaskTimeModeType.AUTOMATIC.getValue());
        TaskOptionParam taskOptionParam = new TaskOptionParam();
        taskOptionParam.setTaskId(taskSubmitParam.getActivityId());
        taskOptionParam.setComment(taskSubmitParam.getRemark());
        activitiHandler.trashTask(taskOptionParam, taskSubmitParam.getTaskId(), maintTask.getStatus());
        if (null != maintTask.getTaskDeadlineDate() && maintTask.getTaskDeadlineDate().compareTo(new Date()) < 0) {
            maintTask.setIsOverTime(true);
        } else {
            maintTask.setIsOverTime(false);
        }
        maintTaskMapper.updateById(maintTask);
        // 更新辅助人员参与结束时间和工作时长
        updateAssistWorkTime(assistPeoples);
        if (maintTask.getSourceType() == TaskSourceType.BREAKDOWN.getValue()) {
            //关闭关联报修单
            ManualRepairEditParam manualRepairEditParam = new ManualRepairEditParam();
            manualRepairEditParam.setId(maintTask.getSourceId());
            manualRepairEditParam.setStatus(StaticValue.ZERO);
            manualRepairEditParam.setRepairTime(new Date());
            manualRepairService.updateStatus(manualRepairEditParam);
        } else if (maintTask.getSourceType() == TaskSourceType.PLAN.getValue()) {
            planEquipmentTimeService.createNextTriggerTime(maintTask.getSourceId(), maintTask.getEquipmentId());
        } else if (maintTask.getSourceType() == TaskSourceType.DEFECT.getValue()) {
            //缺陷生成的工单，需要同步状态
            defectInfoService.updateDefectStatusById(maintTask.getSourceId(), DefectStatusEnum.CLOSED.getValue());
        }
        return true;
    }

    /**
     * 转缺陷
     *
     * @return
     */
    public boolean turnDefectTask(TaskSubmitParam taskSubmitParam) {
        MaintTask maintTask = this.getSubmitEntity(taskSubmitParam.getTaskId());
//        maintTask.setStatus(TaskStatusType.EXCEPTION_CLOSED.getValue());
//        maintTask.setDefect(true);
//        maintTask.setCloseReason(taskSubmitParam.getRemark());
        List<MaintTaskAssistPerson> assistPeoples =
                assistPersonService.getAssistPersonListByTaskId(taskSubmitParam.getTaskId(), TaskTimeModeType.AUTOMATIC.getValue());
//        TaskOptionParam taskOptionParam = new TaskOptionParam();
//        taskOptionParam.setTaskId(taskSubmitParam.getActivityId());
//        taskOptionParam.setComment(taskSubmitParam.getRemark());
//        activitiHandler.trashTask(taskOptionParam, taskSubmitParam.getTaskId(), maintTask.getStatus());
        maintTaskMapper.updateById(maintTask);
        updateAssistWorkTime(assistPeoples);
//        if (maintTask.getSourceType() == TaskSourceType.BREAKDOWN.getValue()) {
//            //关闭关联报修单
//            ManualRepairEditParam manualRepairEditParam = new ManualRepairEditParam();
//            manualRepairEditParam.setId(maintTask.getSourceId());
//            manualRepairEditParam.setStatus(StaticValue.ZERO);
//            manualRepairEditParam.setRepairTime(new Date());
//            manualRepairService.updateStatus(manualRepairEditParam);
//        }
        return true;
    }

    /**
     * 维护工单故障单审批通过
     *
     * @return
     */
    public boolean repairAuditPass(TaskSubmitParam taskSubmitParam) {
        MaintTask maintTask = this.getSubmitEntity(taskSubmitParam.getTaskId());
        List<String> pushUids = new ArrayList<>();
        if (maintTask.getStatus() != TaskStatusType.REPAIR_AUDIT.getValue()) {
            log.error("该单已被其他人审批");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("status_changed", null, LocaleContextHolder.getLocale())));
        }

        maintTask.setStaffIds(taskSubmitParam.getStaffIds());
        maintTask.setTeamIds(taskSubmitParam.getTeamIds());
        maintTask.setHandler("");
        maintTask.setStatus(TaskStatusType.RECEIVING.getValue());
        buildAllStaffIds(maintTask);
        TaskCompleteParam taskCompleteParam = new TaskCompleteParam();
        taskCompleteParam.setTaskId(taskSubmitParam.getActivityId());
        Map<String, Object> variables = new HashMap<>();
        if (StringUtils.isBlank(maintTask.getAllStaffIds())) {
            log.error("待接单人员为空");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("check_error", null, LocaleContextHolder.getLocale())));
        }
        RestResponse<List<String>> restResponse = baseServiceClient.getUidsByIds(maintTask.getAllStaffIds().split(","));
        if (restResponse.isOk()) {
            variables.put("assigns", restResponse.getData());
            pushUids = restResponse.getData();
        } else {
            log.error(restResponse.getMsg());
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("feign_error", null, LocaleContextHolder.getLocale())));
        }
        taskCompleteParam.setVariables(variables);
        taskCompleteParam.setComment(taskSubmitParam.getRemark());
        activitiHandler.submitActiviti(taskCompleteParam, taskSubmitParam.getTaskId(), maintTask.getStatus());
        //派单审批通过时间为派单时间，设置接单截止时间
        this.setMaintTaskSendTaskDate(maintTask);
        maintTaskMapper.updateById(maintTask);
        //推送消息
        //this.push(pushUids, TaskSourceType.getNameByValue(maintTask.getSourceType()), "待接单", maintTask.getName());
        if (maintTask.getType() == TaskType.DEFECT.getValue() && StringUtils.isNotBlank(maintTask.getSourceId())) {
            DefectInfoDto dtoById = defectInfoService.getDtoById(maintTask.getSourceId());
            if (StringUtils.isNotBlank(dtoById.getSourceTaskId())) {
                MaintTaskDto dtoById1 = this.getDtoById(dtoById.getSourceTaskId());
                if (dtoById1 != null) {
                    TaskSubmitParam taskSubmit = new TaskSubmitParam();
                    taskSubmit.setActivityId(dtoById1.getActivityId());
                    taskSubmit.setTaskId(dtoById1.getId());
                    taskSubmit.setSubmitType(SubmitType.CHECK_ACCEPT.getCode());
                    taskSubmit.setRemark("工单转缺陷: 同意 旧工单号：" + dtoById1.getCode());
                    this.submitProcessTask(taskSubmit);
                    if (dtoById1.getSourceType() == TaskSourceType.BREAKDOWN.getValue()) {
                        ManualRepairEditParam manualRepairEditParam = new ManualRepairEditParam();
                        manualRepairEditParam.setId(dtoById1.getSourceId());
                        manualRepairEditParam.setStatus(StaticValue.ZERO);
                        manualRepairEditParam.setRepairTime(new Date());
                        manualRepairService.updateStatus(manualRepairEditParam);
                    }
                }
            }
        }
        return true;
    }

    /**
     * 维护工单转派
     *
     * @return
     */
    public boolean transformTask(TaskSubmitParam taskSubmitParam) {
        MaintTask maintTask = this.getSubmitEntity(taskSubmitParam.getTaskId());
//        if (maintTask.getStatus() != TaskStatusType.RECEIVING.getValue()) {
//            log.error("该单不是待接单状态");
//            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("status_changed", null, LocaleContextHolder.getLocale())));
//        }
        //初始化接单人
        maintTask.setStaffIds(taskSubmitParam.getStaffIds());
        maintTask.setTeamIds(taskSubmitParam.getTeamIds());
        maintTask.setHandler("");

        buildAllStaffIds(maintTask);
        if (maintTask.getAllStaffIds().isEmpty()) {
            log.error("待接单人员为空");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("check_error", null, LocaleContextHolder.getLocale())));
        }
        RestResponse<List<String>> restResponse = baseServiceClient.getUidsByIds(maintTask.getAllStaffIds().split(","));
        List<String> assignees;
        if (restResponse.isOk() && CollectionUtils.isNotEmpty(restResponse.getData())) {
            assignees = restResponse.getData();
        } else {
            log.error(restResponse.getMsg());
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("feign_error", null, LocaleContextHolder.getLocale())));
        }
        //改派直接获取工作流id
        String activityId = getProcessTaskId(maintTask.getProcessInstanceId(), true);
        //如果是执行中/待执行转派，走流程提交，其他状态走转派方式，保持当前状态
        if (maintTask.getStatus() == TaskStatusType.PROCESSING.getValue() || maintTask.getStatus() == TaskStatusType.HANDLE.getValue()) {
            maintTask.setStatus(TaskStatusType.RECEIVING.getValue());
            TaskCompleteParam taskCompleteParam = new TaskCompleteParam();
            taskCompleteParam.setTaskId(taskSubmitParam.getActivityId());
            Map<String, Object> variables = new HashMap<>();
            variables.put("transfer", true);
            variables.put("confirm", false);
            variables.put("assigns", assignees);
            taskCompleteParam.setVariables(variables);
            taskCompleteParam.setComment(taskSubmitParam.getRemark());
            activitiHandler.submitActiviti(taskCompleteParam, taskSubmitParam.getTaskId(), maintTask.getStatus());
            return maintTaskMapper.updateById(maintTask) > 0;
        } else {
            MultiTransferParam transferParam = new MultiTransferParam();
            transferParam.setTaskId(activityId);
            transferParam.setAssigneeIds(assignees);
            transferParam.setComment(taskSubmitParam.getRemark());
            activitiHandler.multiTransferTask(transferParam, maintTask.getId(), maintTask.getStatus());
            return maintTaskMapper.updateById(maintTask) > 0;
        }
    }

    /**
     * 一级保养设备停机关闭工单
     */
    public boolean equipmentStopClosedTask(TaskSubmitParam taskSubmitParam) {
        MaintTask maintTask = this.getSubmitEntity(taskSubmitParam.getTaskId());
        if (maintTask.getStatus() == TaskStatusType.CLOSED.getValue() || maintTask.getStatus() == TaskStatusType.EXCEPTION_CLOSED.getValue()) {
            log.error("该单已被关闭");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("status_changed", null, LocaleContextHolder.getLocale())));
        }
        maintTask.setStatus(TaskStatusType.CLOSED.getValue());
        maintTask.setEquipmentStopClosed(true);
        maintTask.setCloseReason("设备停机");
        if (null != maintTask.getTaskDeadlineDate() && maintTask.getTaskDeadlineDate().compareTo(new Date()) < 0) {
            maintTask.setIsOverTime(true);
        } else {
            maintTask.setIsOverTime(false);
        }
        TaskOptionParam taskOptionParam = new TaskOptionParam();
        taskOptionParam.setTaskId(taskSubmitParam.getActivityId());
        taskOptionParam.setComment("设备停机");
        activitiHandler.trashTask(taskOptionParam, taskSubmitParam.getTaskId(), maintTask.getStatus());
        maintTaskMapper.updateById(maintTask);
        if (maintTask.getSourceType() == TaskSourceType.PLAN.getValue()) {
            planEquipmentTimeService.createNextTriggerTime(maintTask.getSourceId(), maintTask.getEquipmentId());
        }
        return true;
    }

    /**
     * 获取维护人员对应id
     *
     * @param userName
     * @return
     */
    private String getStaffId(String userName) {
        List<String> personNames = new ArrayList<>();
        personNames.add(userName);
        RestResponse<Map<String, MaintPersonDto>> personRes = baseServiceClient.getMaintainerListByNames(personNames);
        if (personRes.isOk()) {
            MaintPersonDto maintPersonDto = personRes.getData().get(userName);
            if (null != maintPersonDto) {
                return maintPersonDto.getId();
            }

        } else {
            log.error("获取维护人员失败");
            //throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("feign_error", null, LocaleContextHolder.getLocale())));
        }
        return "-1";
    }

    /**
     * 获取用户对应uid
     *
     * @param userName
     * @return
     */
    private List<String> getUidsByName(String userName) {
        PorosStaffSearchDto staffSearchDto = new PorosStaffSearchDto();
        staffSearchDto.setName(userName);
        RestResponse<List<PorosSecStaffDto>> restResponse = porosClient.getUserList(staffSearchDto);
        if (restResponse.isOk()) {
            return restResponse.getData().stream().map(PorosSecStaffDto::getUid).distinct().collect(Collectors.toList());
        } else {
            log.error("获取租户下用户失败");
        }
        return null;
    }


    /**
     * 过滤设备
     *
     * @return
     */
    private LambdaQueryWrapper<MaintTask> buildEquipmentSearchWrapper(MaintTaskQueryParam queryParam, LambdaQueryWrapper<MaintTask> wrapper) {
        List<String> equipmentIds = queryParam.getEquipmentIds();
        EquipmentInfoSearchDto param = new EquipmentInfoSearchDto();
        param.setKeyword(queryParam.getEquipmentCodeOrName());
        param.setEquipmentCode(queryParam.getEquipmentCode());
        param.setEquipmentName(queryParam.getEquipmentName());
        param.setCategoryId(queryParam.getCategoryId());
        param.setCategoryIds(queryParam.getCategoryIds());
        param.setLocationId(queryParam.getLocationId());
        param.setLocationIds(queryParam.getLocationIds());
        param.setRunningStatus(queryParam.getEquipmentRunningStatus());
        if (queryParam != null && queryParam.getSourceType() != null && queryParam.getSourceType() == TaskSourceType.PLAN.getValue()) {
            param.setSceneCode("maintPlan");
        }
        RestResponse<List<String>> listRestResponse = equipmentClient.getEquipmentIdsByParam(param);
        if (!listRestResponse.isOk()) {
            log.error("远程调用equipment-service查询设备失败");
            equipmentIds = null;
        } else {
            List<String> searchEquipmentIds = listRestResponse.getData();
            if (CollectionUtils.isEmpty(searchEquipmentIds)) {
                //远程未查询到设备
                equipmentIds = null;
            } else {
                if (CollectionUtils.isNotEmpty(equipmentIds)) {
                    equipmentIds.retainAll(searchEquipmentIds);
                } else {
                    equipmentIds = searchEquipmentIds;
                }
            }
        }
        if (!CollectionUtils.isNotEmpty(equipmentIds)) {
            wrapper.eq(MaintTask::getEquipmentId, StaticValue.MINUS_ONE);
        } else {
            wrapper.in(MaintTask::getEquipmentId, equipmentIds);
        }
        return wrapper;
    }

    private LambdaQueryWrapper<MaintTask> buildTaskShieldWrapper(LambdaQueryWrapper<MaintTask> wrapper) {
        //工单屏蔽
        MaintTaskShieldDto taskShieldDto = shieldService.getDto();
        if (null != taskShieldDto && taskShieldDto.getEnabled()) {
            if (taskShieldDto.getTimeEnabled() && null != taskShieldDto.getBeginTime() && null != taskShieldDto.getEndTime()) {
                if (null == taskShieldDto.getWeekDates() || taskShieldDto.getWeekDates().length == 0 || Arrays.asList(taskShieldDto.getWeekDates()).contains(TaskShieldWeekType.ALL.getValue())) {
                    wrapper.notBetween(MaintTask::getCreateTime, taskShieldDto.getBeginTime(), taskShieldDto.getEndTime());
                } else {
                    wrapper.nested(w -> w.notBetween(MaintTask::getCreateTime, taskShieldDto.getBeginTime(), taskShieldDto.getEndTime())
                            .or(w1 -> w1.between(MaintTask::getCreateTime, taskShieldDto.getBeginTime(), taskShieldDto.getEndTime())
                                    .notIn(MaintTask::getCreateTimeWeek, taskShieldDto.getWeekDates())));
                }
            }
            if (taskShieldDto.getTaskEnabled()) {
                if (null != taskShieldDto.getTaskStatus() && taskShieldDto.getTaskStatus().length > 0) {
                    wrapper.notIn(MaintTask::getStatus, taskShieldDto.getTaskStatus());
                }
            }
            if (taskShieldDto.getFaultEnabled() && null != taskShieldDto.getFaultInfluences() && taskShieldDto.getFaultInfluences().length > 0) {
                wrapper.nested(w -> {
                    for (String faultInfluence : taskShieldDto.getFaultInfluences()) {
                        w.and(w1 -> w1.notLike(MaintTask::getFaultInfluences, faultInfluence));
                    }
                });
            }
        }
        return wrapper;
    }

    private Wrapper<MaintTask> getPageSearchWrapper(MaintTaskQueryParam queryParam) {
        LambdaQueryWrapper<MaintTask> wrapper = Wrappers.lambdaQuery();

        wrapper.eq(StringUtils.isNotBlank(queryParam.getEquipmentId()), MaintTask::getEquipmentId, queryParam.getEquipmentId());
        //工单屏蔽
        wrapper = buildTaskShieldWrapper(wrapper);

        wrapper = buildEquipmentSearchWrapper(queryParam, wrapper);
        wrapper.eq(StringUtils.isNotBlank(queryParam.getSourceId()), MaintTask::getSourceId, queryParam.getSourceId());
        if (StringUtils.isNotBlank(queryParam.getCreateBy())) {
            List<String> uids = this.getUidsByName(queryParam.getCreateBy());
            if (CollectionUtils.isNotEmpty(uids)) {
                wrapper.in(MaintTask::getCreateBy, uids);
            } else {
                //名称未查到对应人员
                wrapper.eq(MaintTask::getCreateBy, "-1");
            }
        }
        wrapper.eq(StringUtils.isNotBlank(queryParam.getHandler()), MaintTask::getHandler, queryParam.getHandler());
        wrapper.like(StringUtils.isNotBlank(queryParam.getStaffName()), MaintTask::getAllStaffIds, this.getStaffId(queryParam.getStaffName()));
        wrapper.and(StringUtils.isNotBlank(queryParam.getCodeOrName()), w -> w.like(MaintTask::getCode, queryParam.getCodeOrName())
                .or()
                .like(MaintTask::getName, queryParam.getCodeOrName()));
        wrapper.eq(null != queryParam.getStatus(), MaintTask::getStatus, queryParam.getStatus());
        wrapper.in(ArrayUtil.isNotEmpty(queryParam.getStatusArr()), MaintTask::getStatus, queryParam.getStatusArr());
        wrapper.eq(null != queryParam.getSourceType(), MaintTask::getSourceType, queryParam.getSourceType());
        wrapper.in(ArrayUtil.isNotEmpty(queryParam.getSourceTypes()), MaintTask::getSourceType, queryParam.getSourceTypes());
        wrapper.eq(StringUtils.isNotBlank(queryParam.getUrgency()), MaintTask::getUrgency, queryParam.getUrgency());
        wrapper.eq(StringUtils.isNotBlank(queryParam.getMajor()), MaintTask::getMajor, queryParam.getMajor());
        wrapper.like(StringUtils.isNotBlank(queryParam.getContent()), MaintTask::getContent, queryParam.getContent());
        wrapper.between(null != queryParam.getBeginRecTakeTime() && null != queryParam.getEndRecTakeTime(), MaintTask::getRecTaskDate, queryParam.getBeginRecTakeTime(), queryParam.getEndRecTakeTime());
        wrapper.between(null != queryParam.getBeginCreateTime() && null != queryParam.getEndCreateTime(), MaintTask::getCreateTime, queryParam.getBeginCreateTime(), queryParam.getEndCreateTime());
        wrapper.between(null != queryParam.getBeginUpdateTime() && null != queryParam.getEndUpdateTime(), MaintTask::getUpdateTime, queryParam.getBeginUpdateTime(), queryParam.getEndUpdateTime());
        wrapper.eq(MaintTask::getDeleted, DeletedType.NO.getValue());
        wrapper.eq(ObjectUtils.isNotEmpty(queryParam.getType()), MaintTask::getType, queryParam.getType());
        wrapper.in(ArrayUtil.isNotEmpty(queryParam.getTypes()), MaintTask::getType, queryParam.getTypes());
        wrapper.gt(queryParam.getIsEcError() != null && queryParam.getIsEcError(), MaintTask::getEcErrorNum, new Integer(0));
        wrapper.in(ArrayUtil.isNotEmpty(queryParam.getEcErrorResults()), MaintTask::getEcErrorResult, queryParam.getEcErrorResults());
        wrapper.eq(null != queryParam.getOurTask() && queryParam.getOurTask(), MaintTask::getHandler, wrapper.eq(null != queryParam.getOurTask() && queryParam.getOurTask(), MaintTask::getHandler, UserContextHolder.getContext().getUserBaseInfo().getUid()));
        wrapper.eq(null != queryParam.getJobLevel(), MaintTask::getJobLevel, queryParam.getJobLevel());
        wrapper.eq(StringUtils.isNotBlank(queryParam.getJobType()), MaintTask::getJobType, queryParam.getJobType());
        wrapper.like(StringUtils.isNotBlank(queryParam.getCloseReason()), MaintTask::getCloseReason, queryParam.getCloseReason());
        wrapper.eq(StringUtils.isNotBlank(queryParam.getDamageReason()), MaintTask::getDamageReason, queryParam.getDamageReason());
        wrapper.eq(null != queryParam.getHasHangUp(), MaintTask::getHasHangUp, queryParam.getHasHangUp());
        if (null != queryParam.getTodayTaskStatus()) {
            if (queryParam.getTodayTaskStatus() == 1) {
                wrapper.in(MaintTask::getStatus, TaskStatusType.CLOSED.getValue(), TaskStatusType.EXCEPTION_CLOSED.getValue(), TaskStatusType.CHECK_ACCEPT.getValue());
            } else if (queryParam.getTodayTaskStatus() == 2) {
                wrapper.notIn(MaintTask::getStatus, TaskStatusType.CLOSED.getValue(), TaskStatusType.EXCEPTION_CLOSED.getValue(), TaskStatusType.CHECK_ACCEPT.getValue());
            }
        }

        if (ArrayUtil.isNotEmpty(queryParam.getStaffId())) {
            int length = queryParam.getStaffId().length;
            wrapper.nested(item -> {
                int i = 0;
                for (String staff : queryParam.getStaffId()) {
                    item.like(MaintTask::getAllStaffIds, staff).or(i < length);
                    i++;
                }
            });
        }
//        wrapper.select(MaintTask::getId, MaintTask::getCode, MaintTask::getName, MaintTask::getUrgency, MaintTask::getStatus,
//                MaintTask::getSourceType, MaintTask::getMajor, MaintTask::getEquipmentId, MaintTask::getContent,
//                MaintTask::getCreateBy, MaintTask::getCreateTime, MaintTask::getUpdateBy, MaintTask::getUpdateTime,
//                MaintTask::getAllStaffIds, MaintTask::getProcessInstanceId);


        if (CollectionUtils.isNotEmpty(queryParam.getOverTimeType())) {
            List<String> overTimeType = queryParam.getOverTimeType();
            Date now = new Date();
            if (overTimeType.contains("" + TaskOperationType.SEND_TASK.getValue())) {
                wrapper.le(MaintTask::getSendTaskDeadlineDate, now).eq(MaintTask::getStatus, TaskStatusType.DISPATCH.getValue());
            }
            if (overTimeType.contains("" + TaskOperationType.REC_TASK.getValue())) {
                wrapper.le(MaintTask::getRecTaskDeadlineDate, now).eq(MaintTask::getStatus, TaskStatusType.RECEIVING.getValue());
            }
            if (overTimeType.contains("" + TaskOperationType.DEAL_TASK.getValue())) {
                wrapper.le(MaintTask::getTaskDeadlineDate, now).notIn(MaintTask::getStatus, TaskStatusType.CHECK_ACCEPT.getValue(), TaskStatusType.CLOSED.getValue(), TaskStatusType.EXCEPTION_CLOSED.getValue());
            }
            if (overTimeType.contains("" + TaskOperationType.ANY_OVERTIME_TASK.getValue())) {
                wrapper.apply("process_instance_id in (select distinct(proc_ins_id) from act_overtime_info where (task_end_time < NOW() and task_real_time IS NULL ) or (task_real_time > task_end_time))");
                wrapper.notIn(MaintTask::getStatus, TaskStatusType.CHECK_ACCEPT.getValue(), TaskStatusType.CLOSED.getValue(), TaskStatusType.EXCEPTION_CLOSED.getValue());
//                wrapper.nested(item -> {
//                    item.le(MaintTask::getSendTaskDeadlineDate, now).eq(MaintTask::getStatus, TaskStatusType.DISPATCH.getValue())
//                            .or()
//                            .le(MaintTask::getRecTaskDeadlineDate, now).eq(MaintTask::getStatus, TaskStatusType.RECEIVING.getValue())
//                            .or()
//                            .le(MaintTask::getTaskDeadlineDate, now).notIn(MaintTask::getStatus, TaskStatusType.CHECK_ACCEPT.getValue(), TaskStatusType.CLOSED.getValue(), TaskStatusType.EXCEPTION_CLOSED.getValue());
//                });
            }
            if (overTimeType.contains("" + TaskOperationType.EC_ERROR_TASK.getValue())) {
                wrapper.gt(MaintTask::getEcErrorNum, 0);
            }
            if (overTimeType.contains("" + TaskOperationType.DEAL_ERROR_TASK.getValue())) {
                wrapper.eq(MaintTask::getStatus, TaskStatusType.EXCEPTION_CLOSED.getValue());
            }
        }
        if (null != queryParam.getIsOverTime()) {
            if (queryParam.getIsOverTime()) {
                wrapper.nested(w -> w.eq(MaintTask::getIsOverTime, true)
                        .or(w1 -> w1.notIn(MaintTask::getStatus, finishStatusList)
                                .lt(MaintTask::getTaskDeadlineDate, new Date())));
            } else {
                wrapper.nested(w -> w.in(MaintTask::getStatus, finishStatusList)
                        .eq(MaintTask::getIsOverTime, false)
                        .or(w1 -> w1.notIn(MaintTask::getStatus, finishStatusList)
                                .gt(MaintTask::getTaskDeadlineDate, new Date())));
            }
        }
        if (queryParam.getIsComplete() != null && queryParam.getIsComplete()) {
            wrapper.in(MaintTask::getStatus, Arrays.asList(8, 9, 10));
        }
        if (queryParam.getIsComplete() != null && !queryParam.getIsComplete()) {
            wrapper.in(MaintTask::getStatus, Arrays.asList(0, 1, 2, 3, 4, 5, 6, 7, 15));
        }
        if (CollectionUtils.isNotEmpty(queryParam.getAreaTypes())) {
            wrapper.in(MaintTask::getAreaType, queryParam.getAreaTypes());
        }
        if (CollectionUtils.isNotEmpty(queryParam.getProcessTypes())) {
            wrapper.in(MaintTask::getProcessType, queryParam.getProcessTypes());
        }
        if (queryParam.getType() != null) {
            if (queryParam.getType() == TaskType.BREAKDOWN.getValue()) {

            } else {
//                List<String> uidsOfDept = commonGetHandler.getUidsOfDept();
//                wrapper.in(MaintTask::getCreateBy, uidsOfDept);
            }
        }
        if (queryParam.getSourceType() != null) {
            if (queryParam.getSourceType() == TaskSourceType.BREAKDOWN.getValue()) {

            } else {
//                List<String> uidsOfDept = commonGetHandler.getUidsOfDept();
//                wrapper.in(MaintTask::getCreateBy, uidsOfDept);
            }
        }
        //排序
        if (null != queryParam.getSortPros() && queryParam.getSortPros().size() > 0) {
            String sortValue = SortUtil.buildSortValue(queryParam.getSortPros(), MaintTask.class, null);
            wrapper.last(sortValue);
        } else {
            wrapper.orderByDesc(MaintTask::getCreateTime, MaintTask::getCode);
        }
        return wrapper;
    }

    /**
     * 获取当前登录人对应维护人员id
     *
     * @return
     */
    private String getCurrentUserStaffId(String uid) {
        RestResponse<String> listRestResponse = baseServiceClient.getIdByUid(uid);
        if (!listRestResponse.isSuccess()) {
            log.error("获取维护人员id失败");
            //throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("feign_error", null, LocaleContextHolder.getLocale())));
            return StaticValue.MINUS_ONE + "";
        } else {
            String staffId = listRestResponse.getData();
            if (StringUtils.isNotBlank(staffId)) {
                return staffId;
            } else {
                return StaticValue.MINUS_ONE + "";
            }

        }
    }

    private Wrapper<MaintTask> getAppPageSearchWrapper(MaintTaskQueryAppParam queryParam) {
        LambdaQueryWrapper<MaintTask> wrapper = Wrappers.lambdaQuery();
        //工单屏蔽
        wrapper = buildTaskShieldWrapper(wrapper);
        wrapper.and(StringUtils.isNotBlank(queryParam.getKeyword()), w -> w.like(MaintTask::getCode, queryParam.getKeyword())
                .or()
                .like(MaintTask::getName, queryParam.getKeyword()));

        wrapper.eq(StringUtils.isNotBlank(queryParam.getEquipmentId()), MaintTask::getEquipmentId, queryParam.getEquipmentId());
        if (StringUtils.isNotBlank(queryParam.getHandler())) {
            String staffId = this.getStaffId(queryParam.getHandler());
            List<String> uids = this.getUidsByName(queryParam.getHandler());
            if (CollectionUtils.isNotEmpty(uids)) {
                //wrapper.and(w -> w.in(MaintTask::getHandler, uids).or().like(MaintTask::getAllStaffIds, staffId).or().like(MaintTask::getDispatchHandler, uids));
                //移动端用这个字段搜索派单人，所以改为搜索派单人字段
                wrapper.and(w -> w.like(MaintTask::getDispatchHandler, uids.get(0)));
            } else {
                //名称未查到对应人员
                wrapper.eq(MaintTask::getHandler, "-1");
            }
        }
        wrapper.eq(StringUtils.isNotBlank(queryParam.getMajor()), MaintTask::getMajor, queryParam.getMajor());
        wrapper.eq(StringUtils.isNotBlank(queryParam.getUrgency()), MaintTask::getUrgency, queryParam.getUrgency());
        wrapper.eq(StringUtils.isNotBlank(queryParam.getSourceId()), MaintTask::getSourceId, queryParam.getSourceId());
        wrapper.eq(queryParam.getType() != null, MaintTask::getType, queryParam.getType());
        wrapper.eq(null != queryParam.getJobLevel(), MaintTask::getJobLevel, queryParam.getJobLevel());
        wrapper.eq(StringUtils.isNotBlank(queryParam.getJobType()), MaintTask::getJobType, queryParam.getJobType());

        MaintTaskQueryParam maintTaskQueryParam = new MaintTaskQueryParam();
        if (StringUtils.isNotBlank(queryParam.getParentId())) {
            //app使用的是parentId
            if (queryParam.getParentType() != null && queryParam.getParentType() == 1) {
                maintTaskQueryParam.setLocationId(queryParam.getParentId());
            } else {
                maintTaskQueryParam.setEquipmentId(queryParam.getEquipmentId());
            }
        }
        maintTaskQueryParam.setCategoryId(queryParam.getCategoryId());
        maintTaskQueryParam.setEquipmentCodeOrName(queryParam.getEquipmentKeyword());
        wrapper = buildEquipmentSearchWrapper(maintTaskQueryParam, wrapper);
        if (queryParam.getFreeTask()) {
            UserBaseInfo userInfo = PorosContextHolder.getCurrentUser();
            String currentUserId = getCurrentUserStaffId(userInfo.getUid());
            if (queryParam.getOurTask()) {
                //不允许出现3级维保单
                wrapper.ne(MaintTask::getJobLevel, 3);
                //待派单自由接单标识、待接单，自己是处理人
                wrapper.nested(item -> item.nested(item2 -> item2.eq(MaintTask::getStatus, TaskStatusType.DISPATCH.getValue()).eq(MaintTask::getFreeTask, true)
                ).or().nested(item2 -> {
                    item2.eq(MaintTask::getStatus, TaskStatusType.RECEIVING.getValue()).like(MaintTask::getUids, currentUserId).eq(MaintTask::getFreeTask, false);
                }));
            } else {

            }
        } else {
            wrapper = buildWrapper(wrapper, queryParam.getSearchStatus(), queryParam.getOurTask());
        }
        wrapper.eq(null != queryParam.getSourceType(), MaintTask::getSourceType, queryParam.getSourceType());
        if (queryParam.getType() != null) {
            if (queryParam.getType() == TaskType.BREAKDOWN.getValue()) {

            } else {
//                List<String> uidsOfDept = commonGetHandler.getUidsOfDept();
//                wrapper.in(MaintTask::getCreateBy, uidsOfDept);
            }
        }

        wrapper.select(MaintTask::getId, MaintTask::getCode, MaintTask::getName, MaintTask::getSourceType, MaintTask::getProcessInstanceId,
                MaintTask::getEquipmentId, MaintTask::getCreateTime, MaintTask::getStatus, MaintTask::getPlanMaintTime,
                MaintTask::getAllStaffIds, MaintTask::getType, MaintTask::getContent, MaintTask::getDispatchHandler,
                MaintTask::getHandler, MaintTask::getMajor, MaintTask::getUrgency, MaintTask::getJobType, MaintTask::getConfirm);
        wrapper.orderByDesc(MaintTask::getUpdateTime, MaintTask::getCode);
        return wrapper;
    }

    private LambdaQueryWrapper<MaintTask> buildWrapper(LambdaQueryWrapper<MaintTask> wrapper, Integer searchStatus, Boolean ourTask) {
        UserBaseInfo userInfo = PorosContextHolder.getCurrentUser();
        String currentUserId = getCurrentUserStaffId(userInfo.getUid());
        String uid = userInfo.getUid();
        if (null != searchStatus) {
            if (ourTask) {
                wrapper.like(MaintTask::getUids, uid);
            }
            switch (searchStatus.intValue()) {
                case 0:
                    wrapper.eq(MaintTask::getStatus, TaskStatusType.DISPATCH.getValue());
                    break;
                case 1:
                    wrapper.eq(MaintTask::getStatus, TaskStatusType.RECEIVING.getValue());
                    break;
                case 2:
                    wrapper.eq(MaintTask::getStatus, TaskStatusType.HANDLE.getValue());
                    break;
                case 3:
                    wrapper.eq(MaintTask::getStatus, TaskStatusType.BEGIN_CONFIRM.getValue());
                    break;
                case 4:
                    wrapper.eq(MaintTask::getStatus, TaskStatusType.CONFIRM.getValue());
                    break;
                case 5:
                    wrapper.eq(MaintTask::getStatus, TaskStatusType.CONFIRMED.getValue());
                    break;
                case 6:
                    wrapper.and(w -> w.eq(MaintTask::getStatus, TaskStatusType.PROCESSING.getValue())
                            .or().eq(MaintTask::getStatus, TaskStatusType.HANG_UP.getValue()));
                    break;
                case 8:
                    wrapper.eq(MaintTask::getStatus, TaskStatusType.CHECK_ACCEPT.getValue());
                    break;
                case 9:
                    wrapper.and(w -> w.eq(MaintTask::getStatus, TaskStatusType.CLOSED.getValue())
                            .or().eq(MaintTask::getStatus, TaskStatusType.EXCEPTION_CLOSED.getValue()));
                    break;
                default:
                    wrapper.eq(MaintTask::getStatus, searchStatus);
            }
        } else {
            if (ourTask) {
                wrapper.like(MaintTask::getUids, uid);
            } /*else {
                wrapper.and(w -> w.like(MaintTask::getDispatchHandler, userInfo.getUid())
                        .or()
                        .like(MaintTask::getAllStaffIds, currentUserId)
                        .or()
                        .eq(MaintTask::getHandler, userInfo.getUid())
                        .or()
                        .like(MaintTask::getReviewer, userInfo.getUid())
                        .or()
                        .like(MaintTask::getEvaluateHandler, userInfo.getUid())
                        .or()
                        .like(MaintTask::getDispatchReviewer, userInfo.getUid()));
            }*/
        }
        return wrapper;
    }

    @Override
    public List<TaskStatisticsDto> getTaskStatistics(TaskStatisticsReqDto reqDto) {
        return maintTaskMapper.getStatistics(reqDto);
    }

    @Override
    public List<FaultStatisticsDto> faultEquipmentStatistics(FaultStatisticsParam param) {
        addEndTimeOneMonths(param);
        return maintTaskMapper.faultEquipmentStatistics(param);
    }

    @Override
    public List<FaultStatisticsDto> allFaultEquipmentStatistics(FaultStatisticsParam param) {
        addEndTimeOneMonths(param);
        return maintTaskMapper.allFaultEquipmentStatistics(param);
    }

    private void addEndTimeOneMonths(FaultStatisticsParam param) {
        param.setEndTime(DateUtils.addMonths(param.getEndTime(), 1));
    }

    @Override
    public List<NameNode> typeReport(MaintTaskReportQueryParam maintTaskReportQueryParam) {
        if (CollectionUtils.isNotEmpty(maintTaskReportQueryParam.getEquipmentLocationIds())
                || CollectionUtils.isNotEmpty(maintTaskReportQueryParam.getEquipmentCategoryIds())) {
            List<String> equipmentIds = this.dealLocationToEquipmentId(maintTaskReportQueryParam);
            maintTaskReportQueryParam.setEquipmentIds(equipmentIds);
        }
        return maintTaskMapper.typeReport(maintTaskReportQueryParam);
    }

    @Override
    public List<TypeMonthNode> typeMonthlyReport(MaintTaskReportQueryParam maintTaskReportQueryParam) {
        if (CollectionUtils.isNotEmpty(maintTaskReportQueryParam.getEquipmentLocationIds())
                || CollectionUtils.isNotEmpty(maintTaskReportQueryParam.getEquipmentCategoryIds())) {
            List<String> equipmentIds = this.dealLocationToEquipmentId(maintTaskReportQueryParam);
            maintTaskReportQueryParam.setEquipmentIds(equipmentIds);
        }
        String[] types = new String[]{"故障单", "维保单", "缺陷单", "点检单"};
        List<TypeMonthNode> list = maintTaskMapper.typeMonthlyReport(maintTaskReportQueryParam);
        List<TypeMonthNode> allList = new ArrayList<>();

        Calendar c1 = Calendar.getInstance();
        Calendar c2 = Calendar.getInstance();

        for (String type : types) {
            c1.setTime(maintTaskReportQueryParam.getBeginTime());
            c2.setTime(maintTaskReportQueryParam.getEndTime());
            while (c1.compareTo(c2) <= 0) {
                if ((c1.get(Calendar.MONTH) + 1) < 10) {
                    if (!allList.contains(new TypeMonthNode(type, c1.get(Calendar.YEAR) + "-0" + (c1.get(Calendar.MONTH) + 1)))) {
                        allList.add(new TypeMonthNode(type, c1.get(Calendar.YEAR) + "-0" + (c1.get(Calendar.MONTH) + 1), 0));
                    }
                } else {
                    if (!allList.contains(new TypeMonthNode(type, c1.get(Calendar.YEAR) + "-" + (c1.get(Calendar.MONTH) + 1)))) {
                        allList.add(new TypeMonthNode(type, c1.get(Calendar.YEAR) + "-" + (c1.get(Calendar.MONTH) + 1), 0));
                    }
                }
                c1.add(Calendar.MONTH, 1);
            }
        }

        for (TypeMonthNode monthType : list) {
            TypeMonthNode monthTypeNode = new TypeMonthNode(monthType.getNameKey(), monthType.getMonthKey(), 0);
            if (allList.contains(monthType)) {
                Collections.replaceAll(allList, monthTypeNode, monthType);
            }
        }
        return allList;

    }

    @Override
    public List<NameNode> statusReport(MaintTaskReportQueryParam maintTaskReportQueryParam) {
        if (CollectionUtils.isNotEmpty(maintTaskReportQueryParam.getEquipmentLocationIds())
                || CollectionUtils.isNotEmpty(maintTaskReportQueryParam.getEquipmentCategoryIds())) {
            List<String> equipmentIds = this.dealLocationToEquipmentId(maintTaskReportQueryParam);
            maintTaskReportQueryParam.setEquipmentIds(equipmentIds);
        }
        return maintTaskMapper.statusReport(maintTaskReportQueryParam);
    }

    @Override
    public List<MonthNode> maintTimeMonthlyReport(MaintTaskReportQueryParam maintTaskReportQueryParam) {

        List<MonthNode> list = maintTaskMapper.maintTimeMonthlyReport(maintTaskReportQueryParam);

        Calendar c1 = Calendar.getInstance();
        Calendar c2 = Calendar.getInstance();
        c1.setTime(maintTaskReportQueryParam.getBeginTime());
        c2.setTime(maintTaskReportQueryParam.getEndTime());

        while (c1.compareTo(c2) <= 0) {
            if ((c1.get(Calendar.MONTH) + 1) < 10) {
                if (!list.contains(new MonthNode(c1.get(Calendar.YEAR) + "-0" + (c1.get(Calendar.MONTH) + 1)))) {
                    list.add(new MonthNode(c1.get(Calendar.YEAR) + "-0" + (c1.get(Calendar.MONTH) + 1), 0));
                }
            } else {
                if (!list.contains(new MonthNode(c1.get(Calendar.YEAR) + "-" + (c1.get(Calendar.MONTH) + 1)))) {
                    list.add(new MonthNode(c1.get(Calendar.YEAR) + "-" + (c1.get(Calendar.MONTH) + 1), 0));
                }
            }
            c1.add(Calendar.MONTH, 1);
        }

        list.sort(new Comparator<MonthNode>() {
            @Override
            public int compare(MonthNode o1, MonthNode o2) {
                return o1.getMonthKey().compareTo(o2.getMonthKey());
            }
        });

        return list;

    }

    @Override
    public PageResult<PersonalPerformanceDto> personalPerformancePage(PersonalPerformanceQueryParam personPerformanceQueryParam) {

        // 查询到所有的维护人员Id、班组Id信息
        List<PersonalTaskInfoDto> personalPerformances = maintTaskMapper.getPersonalPerformance(null, null, personPerformanceQueryParam.getBeginTime(), personPerformanceQueryParam.getEndTime(), null);

        if (personalPerformances == null || personalPerformances.size() == 0) {
            return new PageResult<>();
        }

        Map<String, List<PersonalTaskInfoDto>> personalTaskInfoMap = generatePersonalTaskMapByUid(personalPerformances);

        List<PersonalPerformanceDto> performanceSort = performanceSort(personalTaskInfoMap);

        List<String> uids = new ArrayList<>();

        int startIndex = (personPerformanceQueryParam.getPageNo() - 1) * personPerformanceQueryParam.getLimit();

        int endIndex = personPerformanceQueryParam.getPageNo() * personPerformanceQueryParam.getLimit();

        // 如果指定 取消分页指定数量方式
        if (personPerformanceQueryParam.getUids() != null && personPerformanceQueryParam.getUids().size() != 0) {
            for (int i = startIndex; i < endIndex && i < personPerformanceQueryParam.getUids().size(); i++) {
                uids.add(personPerformanceQueryParam.getUids().get(i));
            }
        } else {
            for (int i = startIndex; i < endIndex && i < performanceSort.size(); i++) {
                uids.add(performanceSort.get(i).getUid());
            }
        }

        List<PersonalPerformanceDto> resultList = new ArrayList<>();

        if (uids != null && uids.size() != 0) {

            List<MaintPersonDto> resultData = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(personPerformanceQueryParam.getTeamIds())) {
                RestResponse<List<String>> personIdsByTeamIds = baseServiceClient.getPersonIdsByTeamIds(personPerformanceQueryParam.getTeamIds().toArray(new String[personPerformanceQueryParam.getTeamIds().size()]));
                if (!personIdsByTeamIds.isOk()) {
                    log.error("查询班组失败");
                } else {
                    RestResponse<List<MaintPersonDto>> maintPersonPageResult = baseServiceClient.getMaintainerListByIds(personIdsByTeamIds.getData().toArray(new String[personIdsByTeamIds.getData().size()]));
                    if (!maintPersonPageResult.isOk()) {
                        log.error("查询班组人员失败");
                    } else {
                        resultData.addAll(maintPersonPageResult.getData());
                    }
                }
            } else {
                RestResponse<List<MaintPersonDto>> maintPersonPageResult = baseServiceClient.getMaintPersonDtoByUids(uids.toArray(new String[]{}));
                if (!maintPersonPageResult.isOk()) {
                    log.error("查询班组人员失败");
                } else {
                    resultData.addAll(maintPersonPageResult.getData());
                }
            }

            if (resultData != null && resultData.size() != 0) {
                resultData.forEach(dto -> {
                    PersonalPerformanceDto personalPerformanceDto = new PersonalPerformanceDto(new TaskInfoStatisticsDto(),
                            new TaskInfoStatisticsDto(),
                            new TaskInfoStatisticsDto(),
                            new TaskInfoStatisticsDto(),
                            new TaskInfoStatisticsDto());
                    if (CollectionUtils.isEmpty(personPerformanceQueryParam.getUids()) ||
                            (CollectionUtils.isNotEmpty(personPerformanceQueryParam.getUids()) && personPerformanceQueryParam.getUids().contains(dto.getUid()))) {
                        List<PersonalTaskInfoDto> personalTaskInfoDtos = personalTaskInfoMap.get(dto.getUid());
                        personalPerformanceDto.setName(dto.getName());
                        personalPerformanceDto.setUid(dto.getUid());
                        // 没有数据 初始化 所有为零的数据
                        if (personalTaskInfoDtos == null || personalTaskInfoDtos.size() == 0) {
                            resultList.add(personalPerformanceDto);
                        } else {
                            Double taskIntegral = 0d;
                            Double evaluationIntegral = 0d;
                            BigDecimal repairTimes = new BigDecimal("0");
                            BigDecimal repairCostTime = new BigDecimal("0");
                            for (PersonalTaskInfoDto taskInfoDto : personalTaskInfoDtos) {
                                evaluationIntegral = evaluationIntegral + TaskGradeType.getIntegralByValue(taskInfoDto.getGrade()) * taskInfoDto.getCheckAcceptResult();

                                if (taskInfoDto.getType() == TaskType.EC.getValue()) {
                                    taskIntegral = taskIntegral + TaskType.EC.getIntegral();
                                    TaskInfoStatisticsDto pointCheckTask = personalPerformanceDto.getPointCheckTask();
                                    calculationCount(pointCheckTask, taskInfoDto);
                                } else if (taskInfoDto.getType() == TaskType.PLAN.getValue()) {
                                    taskIntegral = taskIntegral + TaskType.PLAN.getIntegral();
                                    TaskInfoStatisticsDto preventionMaintainTask = personalPerformanceDto.getPreventionMaintainTask();
                                    calculationCount(preventionMaintainTask, taskInfoDto);
                                } else if (taskInfoDto.getType() == TaskType.DEFECT.getValue()) {
                                    taskIntegral = taskIntegral + TaskType.DEFECT.getIntegral();
                                    TaskInfoStatisticsDto faultMaintainTask = personalPerformanceDto.getFaultMaintainTask();
                                    calculationCount(faultMaintainTask, taskInfoDto);
                                } else if (taskInfoDto.getType() == TaskType.BREAKDOWN.getValue()) {
                                    taskIntegral = taskIntegral + TaskType.BREAKDOWN.getIntegral();
                                    TaskInfoStatisticsDto maintainImproveTask = personalPerformanceDto.getMaintainImproveTask();
                                    calculationCount(maintainImproveTask, taskInfoDto);
                                }
                                repairTimes = repairTimes.add(new BigDecimal("1"));
                                if (taskInfoDto.getBeginMaintTime() != null && taskInfoDto.getEndMaintTime() != null) {
                                    Long between = DateUtil.between(taskInfoDto.getBeginMaintTime(), taskInfoDto.getEndMaintTime(), DateUnit.MINUTE);
                                    repairCostTime = repairCostTime.add(new BigDecimal(between));
                                }
                            }

                            personalPerformanceDto.setTaskIntegral(new BigDecimal(taskIntegral).setScale(StaticValue.ONE, BigDecimal.ROUND_HALF_UP).doubleValue());
                            personalPerformanceDto.setEvaluationIntegral(new BigDecimal(evaluationIntegral).setScale(StaticValue.ONE, BigDecimal.ROUND_HALF_UP).doubleValue());
                            personalPerformanceDto.setTotalIntegral(new BigDecimal(taskIntegral + evaluationIntegral).setScale(StaticValue.ONE, BigDecimal.ROUND_HALF_UP).doubleValue());
                            personalPerformanceDto.setRepairCostTime(repairCostTime.toString());
                            personalPerformanceDto.setRepairTimes(repairTimes.toString());
                            resultList.add(personalPerformanceDto);
                        }
                    }
                });
            }


            resultList.sort((a, b) -> {
                return b.getTotalIntegral() < a.getTotalIntegral() ? -1 : 1;
            });


            return Optional.ofNullable(PageResult.<PersonalPerformanceDto>builder()
                            .records(resultList)
                            .total(
                                    personPerformanceQueryParam.getUids() != null && personPerformanceQueryParam.getUids().size() != 0 ?
                                            personPerformanceQueryParam.getUids().size() : performanceSort.size())
                            .build())
                    .orElse(new PageResult<>());
        }
        return new PageResult<>();
    }

    public List<String> dealParamTeamIds(List<String> maintPersonUidList, PersonalPerformanceTopQueryParam param) {
        if (CollectionUtils.isNotEmpty(param.getTeamIds())) {
            RestResponse<List<MaintTeamDto>> maintTeamListByIds = baseServiceClient.getMaintTeamListByIds(param.getTeamIds().toArray(new String[param.getTeamIds().size()]));
            if (!maintTeamListByIds.isOk()) {
                throw new GlobalServiceException(GlobalResultMessage.of("查询班组失败"));
            }
            List<MaintTeamDto> teamListByIdsData = maintTeamListByIds.getData();
            List<String> maintPersonIdList = Lists.newArrayList();
            for (MaintTeamDto team : teamListByIdsData) {
                String[] memberIds = team.getMemberIds();
                if (ArrayUtil.isNotEmpty(memberIds)) {
                    maintPersonIdList.addAll(Arrays.asList(memberIds));
                }
            }
            RestResponse<List<MaintPersonDto>> maintainerListByIds = baseServiceClient.getMaintainerListByIds(maintPersonIdList.toArray(new String[maintPersonIdList.size()]));
            if (!maintainerListByIds.isOk()) {
                throw new GlobalServiceException(GlobalResultMessage.of("查询班组人员失败"));
            }
            List<MaintPersonDto> maintainerListByIdsData = maintainerListByIds.getData();

            for (MaintPersonDto person : maintainerListByIdsData) {
                maintPersonUidList.add(person.getUid());
            }
        }
        return maintPersonUidList;
    }

    @Override
    public List<PersonalPerformanceDto> personPerformanceTop(PersonalPerformanceTopQueryParam param) {
//        List<String> maintPersonUidList = Lists.newArrayList();
//        maintPersonUidList = this.dealParamTeamIds(maintPersonUidList, param);

        List<PersonalTaskInfoDto> personalPerformances = maintTaskMapper.getPersonalPerformance(null, null, param.getBeginTime(), param.getEndTime(), null);

        if (personalPerformances == null || personalPerformances.size() == 0) {
            return new ArrayList<>();
        }
        Map<String, List<PersonalTaskInfoDto>> personalTaskInfoMap = generatePersonalTaskMapByUid(personalPerformances);
        //log.info("personalTaskInfoMap:{}", JSON.toJSONString(personalTaskInfoMap));
        List<PersonalPerformanceDto> listPersonalPerformance = performanceSort(personalTaskInfoMap);
        //log.info("listPersonalPerformance:{}", JSON.toJSONString(listPersonalPerformance));
        List<String> uids = new ArrayList<>();

        for (int i = 0; i < param.getTopN() && i < listPersonalPerformance.size(); i++) {
            uids.add(listPersonalPerformance.get(i).getUid());
        }

        List<PersonalPerformanceDto> resultList = new ArrayList<>();

        if (uids != null && uids.size() != 0) {
            List<MaintPersonDto> resultData = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(param.getTeamIds())) {
                RestResponse<List<String>> personIdsByTeamIds = baseServiceClient.getPersonIdsByTeamIds(param.getTeamIds().toArray(new String[param.getTeamIds().size()]));
                if (!personIdsByTeamIds.isOk()) {
                    log.error("查询班组失败");
                } else {
                    RestResponse<List<MaintPersonDto>> maintPersonPageResult = baseServiceClient.getMaintainerListByIds(personIdsByTeamIds.getData().toArray(new String[personIdsByTeamIds.getData().size()]));
                    if (!maintPersonPageResult.isOk()) {
                        log.error("查询班组人员失败");
                    } else {
                        resultData.addAll(maintPersonPageResult.getData());
                    }
                }
            } else {
                RestResponse<List<MaintPersonDto>> maintPersonPageResult = baseServiceClient.getMaintPersonDtoByUids(uids.toArray(new String[]{}));
                if (!maintPersonPageResult.isOk()) {
                    log.error("查询班组人员失败");
                } else {
                    resultData.addAll(maintPersonPageResult.getData());
                }
            }
            //log.info("resultData:{}", JSON.toJSONString(resultData));

            if (resultData != null && resultData.size() != 0) {
                for (int i = 0; i < listPersonalPerformance.size(); i++) {
                    PersonalPerformanceDto dto = listPersonalPerformance.get(i);
                    for (MaintPersonDto maintPersonDto : resultData) {
                        if (dto.getUid().equals(maintPersonDto.getUid())) {
                            dto.setName(maintPersonDto.getName());
                            resultList.add(dto);
                        }
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(resultList)) {
            if (resultList.size() > param.getTopN()) {
                resultList = resultList.subList(0, 9);
            }
        }
        final BigDecimal[] repairTimes = {new BigDecimal("0")};
        final BigDecimal[] repairCostTime = {new BigDecimal("0")};
        resultList.stream().forEach(item -> {
            repairTimes[0] = repairTimes[0].add(new BigDecimal(item.getRepairTimes()));
            repairCostTime[0] = repairCostTime[0].add(new BigDecimal(item.getRepairCostTime()));
        });
        BigDecimal repairTimesAvg = new BigDecimal("0");
        BigDecimal repairCostTimeAvg = new BigDecimal("0");
        if (CollectionUtils.isNotEmpty(resultList)) {
            repairTimesAvg = repairTimes[0].divide(new BigDecimal(resultList.size()), RoundingMode.HALF_UP);
            repairCostTimeAvg = repairCostTime[0].divide(new BigDecimal(resultList.size()), RoundingMode.HALF_UP);
            BigDecimal finalRepairTimesAvg = repairTimesAvg;
            BigDecimal finalRepairCostTimeAvg = repairCostTimeAvg;
            resultList.forEach(item -> {
                item.setRepairTimesAvg(finalRepairTimesAvg.toString());
                item.setRepairCostTimeAvg(finalRepairCostTimeAvg.toString());
            });
        }

        return resultList;
    }

    @Override
    public PageResult<MaintTaskAppPageDto> maintTaskFirstPage(MaintTaskQueryAppParam queryParam) {
        return this.appPageDto(queryParam);
    }

    @Override
    public List<String> checkPartUsed(String[] partIds) {
        if (null != partIds) {
            //备件-维护计划 以及备件-维护工单
            List<String> relPartIds = taskPartRelService.checkPartUsed(partIds);
            if (CollectionUtils.isNotEmpty(relPartIds)) {
                return relPartIds;
            }
        }
        return null;
    }

    @Override
    public List<String> checkEquipmentUsed(String[] equipmentIds) {
        // 校验工单是否在使用
        String[] maintTaskUsed = checkEquipmentMaintTaskUsed(equipmentIds);
        return Arrays.asList(maintTaskUsed);
    }

    @Override
    public List<String> getUsedInfoIds() {
        LambdaQueryWrapper<MaintTask> wrapper = Wrappers.lambdaQuery();
        wrapper.select(MaintTask::getEquipmentId);
        return maintTaskMapper.selectList(wrapper).stream().map(MaintTask::getEquipmentId).distinct().collect(Collectors.toList());
    }

    private String[] checkEquipmentMaintTaskUsed(String[] equipmentIds) {
        LambdaQueryWrapper<MaintTask> queryWrapper = Wrappers.lambdaQuery();

        queryWrapper.in(MaintTask::getEquipmentId, equipmentIds);
        queryWrapper.select(MaintTask::getEquipmentId);

        List<MaintTask> list = list(queryWrapper);

        return list.stream().map(e -> e.getEquipmentId()).distinct().toArray(String[]::new);
    }

    private List<PersonalPerformanceDto> performanceSort(Map<String, List<PersonalTaskInfoDto>> personalTaskInfoMap) {

        List<PersonalPerformanceDto> listPersonalPerformance = new ArrayList<>();

        for (Map.Entry<String, List<PersonalTaskInfoDto>> personalTaskInfoEntry : personalTaskInfoMap.entrySet()) {
            List<PersonalTaskInfoDto> personalTaskInfoDtos = personalTaskInfoEntry.getValue();
            PersonalPerformanceDto personalPerformanceDto = new PersonalPerformanceDto();
            personalPerformanceDto.setUid(personalTaskInfoEntry.getKey());
            if (personalTaskInfoDtos != null && personalTaskInfoDtos.size() != 0) {

                Double taskIntegral = 0d;
                BigDecimal repairTimes = new BigDecimal("0");
                BigDecimal repairCostTime = new BigDecimal("0");
                Double evaluationIntegral = 0d;

                for (PersonalTaskInfoDto taskInfoDto : personalTaskInfoDtos) {
                    taskIntegral = taskIntegral + TaskType.getIntegralByValue(taskInfoDto.getType());
                    repairTimes = repairTimes.add(new BigDecimal("1"));
                    evaluationIntegral = evaluationIntegral + TaskGradeType.getIntegralByValue(taskInfoDto.getGrade()) * taskInfoDto.getCheckAcceptResult();
                    if (taskInfoDto.getBeginMaintTime() != null && taskInfoDto.getEndMaintTime() != null) {
                        Long between = DateUtil.between(taskInfoDto.getBeginMaintTime(), taskInfoDto.getEndMaintTime(), DateUnit.MINUTE);
                        repairCostTime = repairCostTime.add(new BigDecimal(between));
                    }
                }

                personalPerformanceDto.setTaskIntegral(new BigDecimal(taskIntegral).setScale(StaticValue.ONE, BigDecimal.ROUND_HALF_UP).doubleValue());
                personalPerformanceDto.setEvaluationIntegral(new BigDecimal(evaluationIntegral).setScale(StaticValue.ONE, BigDecimal.ROUND_HALF_UP).doubleValue());
                personalPerformanceDto.setTotalIntegral(new BigDecimal(taskIntegral + evaluationIntegral).setScale(StaticValue.ONE, BigDecimal.ROUND_HALF_UP).doubleValue());
                personalPerformanceDto.setRepairTimes(repairTimes.toString());
                personalPerformanceDto.setRepairCostTime(repairCostTime.toString());
                listPersonalPerformance.add(personalPerformanceDto);
            }

        }

        listPersonalPerformance.sort((a, b) -> {
            if (b.getTotalIntegral() - a.getTotalIntegral() > 0) {
                return 1;
            } else {
                return -1;
            }
        });

        return listPersonalPerformance;
    }

    private Map<String, List<PersonalTaskInfoDto>> generatePersonalTaskMapByUid(List<PersonalTaskInfoDto> personalPerformances) {

        RestResponse<List<MaintPersonIdDto>> maintPersonIdDto = baseServiceClient.getMaintPersonIdDto();

        if (!maintPersonIdDto.isOk()) {
            log.error("获取维护人员失败, " + maintPersonIdDto.getMsg());
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("feign_error", null, LocaleContextHolder.getLocale())));
        }

        List<MaintPersonIdDto> personIdDtoDatas = maintPersonIdDto.getData();

        Map<String, List<PersonalTaskInfoDto>> personalTaskInfoMap = new HashMap<>();

        if (personIdDtoDatas == null || personIdDtoDatas.size() == 0) {
            return personalTaskInfoMap;
        }
        personalPerformances.forEach(dto -> {

            if (StringUtils.isNotEmpty(dto.getAllStaffIds())) {
                String[] staffIds = dto.getHandler();
                // 去重
                staffIds = Stream.of(staffIds).distinct().toArray(String[]::new);
                for (String staffId : staffIds) {
                    personalTaskInfoMapPut(personalTaskInfoMap, staffId, dto);
//                    {
//                        for (MaintPersonIdDto personIdDto : personIdDtoDatas) {
//                            if (staffId.equals(personIdDto.getStaffId())) {
//                                personalTaskInfoMapPut(personalTaskInfoMap, personIdDto.getUid(), dto);
//                            }
//                        }
//                    }
                }
            }
/*

            if (StringUtils.isNotEmpty(dto.getTeamIds())){
                String[] teamIds = dto.getTeamIds().split(",");
                for (String teamId : teamIds){
                    for (MaintPersonIdDto personIdDto : personIdDtoDatas){
                        for (String personTeamId : personIdDto.getTeamIds()){
                            if (personTeamId.equals(teamId)){
                                personalTaskInfoMapPut(personalTaskInfoMap, personIdDto.getUid(), dto);
                            }
                        }
                    }
                }
            }
*/

        });
        return personalTaskInfoMap;
    }

    private void personalTaskInfoMapPut(Map<String, List<PersonalTaskInfoDto>> personalTaskInfoMap,
                                        String uid,
                                        PersonalTaskInfoDto dto) {
        List<PersonalTaskInfoDto> list = personalTaskInfoMap.get(uid);
        if (list == null) {
            list = new ArrayList<>();
        }
        list.add(dto);
        personalTaskInfoMap.put(uid, list);
    }


    private Double calculationType(Integer type) {

        if (type == 0) {
            // 点检 0.5分
            return 1.0;
        } else if (type == 2) {
            // 故障维修 2.0分
            return 1.0;
        } else if (type == 1 || type == 3 || type == 4) {
            // 1预防维护3维护改进4转产
            return 1.0;
        }
        return 0.0;
    }

    private Double calculationGrade(Integer grade) {
        switch (grade) {
            case 5:
                return 0.5;
            case 4:
                return 0.3;
            case 3:
                return 0.2;
            case 2:
                return 0.0;
            case 1:
                return 0.0;
            default:
                return 0.0;
        }
    }

    private void calculationCount(TaskInfoStatisticsDto task, PersonalTaskInfoDto taskInfoDto) {
        Integer taskCount = task.getTaskCount();
        Long timeCount = task.getTimeCount();
        taskCount += 1;
        task.setTaskCount(taskCount);
        long betweenCount = 0L;
        if (taskInfoDto.getBeginMaintTime() != null && taskInfoDto.getEndMaintTime() != null) {
            betweenCount = DateUtil.between(taskInfoDto.getBeginMaintTime(), taskInfoDto.getEndMaintTime(), DateUnit.HOUR);
        }
        timeCount += betweenCount;
        task.setTimeCount(timeCount);
    }

    @Override
    public Map<String, Date> getLastPlanDateMap(List<String> planIds) {
        Map<String, Date> map = new HashMap<>();
        List<MaintPlanCountDto> dateDtos = maintTaskMapper.getLastPlanDate(planIds);
        if (CollectionUtils.isNotEmpty(dateDtos)) {
            map = dateDtos.stream().filter(dto -> null != dto.getLastMaintTime()).collect(Collectors.toMap(MaintPlanCountDto::getPlanId, MaintPlanCountDto::getLastMaintTime, (v1, v2) -> v1));
        }

        return map;
    }

    @Override
    public Map<String, Integer> getTaskCountMap(List<String> planIds) {
        Map<String, Integer> map = new HashMap<>();
        List<MaintPlanCountDto> dateDtos = maintTaskMapper.getTaskCount(planIds);
        if (CollectionUtils.isNotEmpty(dateDtos)) {
            map = dateDtos.stream().filter(dto -> StringUtils.isNotBlank(dto.getPlanId())).collect(Collectors.toMap(MaintPlanCountDto::getPlanId, MaintPlanCountDto::getTaskCount));
        }

        return map;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean submitDefect(TaskDefectAddParam defectAddParam) {
        MaintTask maintTask = this.getSubmitEntity(defectAddParam.getTaskId());
        //maintTask.setStatus(TaskStatusType.EXCEPTION_CLOSED.getValue());
        maintTask.setDefect(true);
        maintTask.setCloseReason(defectAddParam.getRemark());
        List<MaintTaskAssistPerson> assistPeoples =
                assistPersonService.getAssistPersonListByTaskId(defectAddParam.getTaskId(), TaskTimeModeType.AUTOMATIC.getValue());
//        TaskOptionParam taskOptionParam = new TaskOptionParam();
//        taskOptionParam.setTaskId(defectAddParam.getActivityId());
//        taskOptionParam.setComment(defectAddParam.getRemark());
//        activitiHandler.trashTask(taskOptionParam, maintTask.getId(), maintTask.getStatus());
        DefectInfoAddParam defectInfoAddParam = new DefectInfoAddParam();
        defectInfoAddParam.setEquipmentId(defectAddParam.getEquipmentId());
        defectInfoAddParam.setDefectName(defectAddParam.getDefectName());
        defectInfoAddParam.setDefectType(Integer.valueOf(StringUtils.isNotBlank(defectAddParam.getDefectType()) ? defectAddParam.getDefectType() : "0"));
        defectInfoAddParam.setDefectContent(defectAddParam.getDefectContent());
        defectInfoAddParam.setAffectContent(defectAddParam.getAffectContent());
        defectInfoAddParam.setMajorType(Integer.valueOf(StringUtils.isNotBlank(defectAddParam.getMajor()) ? defectAddParam.getMajor() : "0"));
        defectInfoAddParam.setSourceTaskId(defectAddParam.getSourceTaskId());
        defectInfoAddParam.setDefectReason(defectAddParam.getDefectReason());
        defectInfoAddParam.setEndTime(defectAddParam.getEndTime());
        if (null != defectAddParam.getLiveMediaIds()) {
            defectInfoAddParam.setLiveMediaIds(defectAddParam.getLiveMediaIds());
        }
        String defectId = defectInfoService.saveByParam(defectInfoAddParam);
        maintTask.setDefectId(defectId);

        maintTaskMapper.updateById(maintTask);
        updateAssistWorkTime(assistPeoples);
        if (maintTask.getSourceType() == TaskSourceType.BREAKDOWN.getValue()) {
            //关闭关联报修单
//            ManualRepairEditParam manualRepairEditParam = new ManualRepairEditParam();
//            manualRepairEditParam.setId(maintTask.getSourceId());
//            manualRepairEditParam.setStatus(StaticValue.ZERO);
//            manualRepairEditParam.setRepairTime(new Date());
//            manualRepairService.updateStatus(manualRepairEditParam);
        } else if (maintTask.getSourceType() == TaskSourceType.DEFECT.getValue()) {
            //缺陷生成的工单，需要同步状态
            defectInfoService.updateDefectStatusById(maintTask.getSourceId(), DefectStatusEnum.CLOSED.getValue());
        }

        return true;
    }

    public String getWorkbenchStatisticsOfMaintTask(String type, String status, String isTody) {
        Date now = new Date();
        Integer countFinished = 0;
        Integer countNoFinished = 0;
        Integer total = 0;
        RestResponse<BuildInfoSearchDto> authRes = equipmentClient.getCurrentUserInfoIds();
        if (!authRes.isOk()) {
            log.error("获取设备权限失败");
        } else {
            BuildInfoSearchDto buildInfoSearchDto = authRes.getData();
            if (!buildInfoSearchDto.getFlag() || CollectionUtils.isNotEmpty(buildInfoSearchDto.getEquipmentIds())) {
                countFinished = this.count(new QueryWrapper<MaintTask>().lambda()
                        .eq(StringUtils.isNotBlank(type), MaintTask::getType, type)
                        .eq(StringUtils.isNotBlank(status), MaintTask::getStatus, status)
                        .in(CollectionUtils.isNotEmpty(buildInfoSearchDto.getEquipmentIds()), MaintTask::getEquipmentId, buildInfoSearchDto.getEquipmentIds())
                        .in(StringUtils.isBlank(status), MaintTask::getStatus, TaskStatusType.CLOSED.getValue(), TaskStatusType.EXCEPTION_CLOSED.getValue(), TaskStatusType.CHECK_ACCEPT.getValue())
                        .ge(StringUtils.isNotBlank(isTody) && isTody.equals("1"), MaintTask::getCreateTime, DateUtil.beginOfDay(now))
                        .le(StringUtils.isNotBlank(isTody) && isTody.equals("1"), MaintTask::getCreateTime, DateUtil.endOfDay(now)));
                countNoFinished = this.count(new QueryWrapper<MaintTask>().lambda()
                        .eq(StringUtils.isNotBlank(type), MaintTask::getType, type)
                        .eq(StringUtils.isNotBlank(status), MaintTask::getStatus, status)
                        .in(CollectionUtils.isNotEmpty(buildInfoSearchDto.getEquipmentIds()), MaintTask::getEquipmentId, buildInfoSearchDto.getEquipmentIds())
                        .notIn(StringUtils.isBlank(status), MaintTask::getStatus, TaskStatusType.CLOSED.getValue(), TaskStatusType.EXCEPTION_CLOSED.getValue(), TaskStatusType.CHECK_ACCEPT.getValue())
                        .ge(StringUtils.isNotBlank(isTody) && isTody.equals("1"), MaintTask::getCreateTime, DateUtil.beginOfDay(now))
                        .le(StringUtils.isNotBlank(isTody) && isTody.equals("1"), MaintTask::getCreateTime, DateUtil.endOfDay(now)));

                total = this.count(new QueryWrapper<MaintTask>().lambda()
                        .eq(StringUtils.isNotBlank(type), MaintTask::getType, type)
                        .in(CollectionUtils.isNotEmpty(buildInfoSearchDto.getEquipmentIds()), MaintTask::getEquipmentId, buildInfoSearchDto.getEquipmentIds())
                        .ge(StringUtils.isNotBlank(isTody) && isTody.equals("1"), MaintTask::getCreateTime, DateUtil.beginOfDay(now))
                        .le(StringUtils.isNotBlank(isTody) && isTody.equals("1"), MaintTask::getCreateTime, DateUtil.endOfDay(now)));

            }
        }
        if (isTody.equals("1")) {
            return "" + countFinished + "/" + countNoFinished + "/" + total;
        } else {
            return "" + countFinished;
        }

    }

    public String getWorkbenchStatisticsOfMaintTaskOfOverTime(String overTimeType) {
        Date now = new Date();
        LambdaQueryWrapper<MaintTask> wrapper = new QueryWrapper<MaintTask>().lambda();
        RestResponse<BuildInfoSearchDto> authRes = equipmentClient.getCurrentUserInfoIds();
        if (!authRes.isOk()) {
            log.error("获取设备权限失败");
            return "0";
        } else {
            BuildInfoSearchDto buildInfoSearchDto = authRes.getData();
            if (!buildInfoSearchDto.getFlag() || CollectionUtils.isNotEmpty(buildInfoSearchDto.getEquipmentIds())) {
                wrapper.in(CollectionUtils.isNotEmpty(buildInfoSearchDto.getEquipmentIds()), MaintTask::getEquipmentId, buildInfoSearchDto.getEquipmentIds());
            } else {
                return "0";
            }
        }
        if (StringUtils.isNotBlank(overTimeType)) {
            if (overTimeType.equals("" + TaskOperationType.SEND_TASK.getValue())) {
                wrapper.le(MaintTask::getSendTaskDeadlineDate, now).eq(MaintTask::getStatus, TaskStatusType.DISPATCH.getValue());
            }
            if (overTimeType.equals("" + TaskOperationType.REC_TASK.getValue())) {
                wrapper.le(MaintTask::getRecTaskDeadlineDate, now).eq(MaintTask::getStatus, TaskStatusType.RECEIVING.getValue());
            }
            if (overTimeType.equals("" + TaskOperationType.DEAL_TASK.getValue())) {
                wrapper.le(MaintTask::getTaskDeadlineDate, now).notIn(MaintTask::getStatus, TaskStatusType.CHECK_ACCEPT.getValue(), TaskStatusType.CLOSED.getValue(), TaskStatusType.EXCEPTION_CLOSED.getValue());
            }
            if (overTimeType.equals("" + TaskOperationType.ANY_OVERTIME_TASK.getValue())) {
                wrapper.apply("process_instance_id in (select distinct(proc_ins_id) from act_overtime_info where (task_end_time < NOW() and task_real_time IS NULL ) or (task_real_time > task_end_time))");
                wrapper.le(MaintTask::getTaskDeadlineDate, now).notIn(MaintTask::getStatus, TaskStatusType.CHECK_ACCEPT.getValue(), TaskStatusType.CLOSED.getValue(), TaskStatusType.EXCEPTION_CLOSED.getValue());
            }
        }
        return "" + this.count(wrapper);
    }

    public MaintTask setMaintTaskSendTaskDate(MaintTask maintTask) {
        maintTask.setSendTaskDate(new Date());
        Integer offsetDays = maintTaskConfigService.getByTaskTypeAndConfigType(maintTask.getSourceType(), TaskOperationType.REC_TASK.getValue());
        maintTask.setRecTaskDeadlineDate(DateUtil.offset(new Date(), DateField.MINUTE, offsetDays));
        if (maintTask.getStatus() == TaskStatusType.RECEIVING.getValue()) {
            notifyService.saveRepairTaskReceiveNotify(maintTask.getId(), maintTask.getRecTaskDeadlineDate());
        }
        return maintTask;
    }

    public MaintTask setMaintTaskSendTaskDeadlineDate(MaintTask maintTask) {
        Integer offsetDays = maintTaskConfigService.getByTaskTypeAndConfigType(maintTask.getSourceType(), TaskOperationType.SEND_TASK.getValue());
        maintTask.setSendTaskDeadlineDate(DateUtil.offset(new Date(), DateField.MINUTE, offsetDays));
        return maintTask;
    }

    public List<WorkCalendarAppResult> getWorkCalendarOfApp(WorkCalendarQueryParam param) {
        LinkedHashMap<String, WorkCalendarResult> workCalendar = this.getWorkCalendar(param);
        List<WorkCalendarAppResult> workCalendarAppResultList = Lists.newArrayList();
        for (String key : workCalendar.keySet()) {
            WorkCalendarResult workCalendarResult = workCalendar.get(key);
            WorkCalendarAppResult app = new WorkCalendarAppResult();
            app.setDate(key);
            app.setResult(workCalendarResult);
            workCalendarAppResultList.add(app);
        }
        return workCalendarAppResultList;
    }

    public LinkedHashMap<String, WorkCalendarResult> getWorkCalendar(WorkCalendarQueryParam param) {
        EquipmentInfoSearchDto equipmentInfoSearchDto = new EquipmentInfoSearchDto();
        equipmentInfoSearchDto.setLocationIds(param.getEquipmentLocationIds());
        RestResponse<List<String>> response = equipmentClient.getEquipmentIdsByParam(equipmentInfoSearchDto);

        if (!response.isOk() || CollectionUtils.isEmpty(response.getData())) {
            throw new GlobalServiceException(GlobalResultMessage.of("查找不到设备"));
        }
        List<String> equipmentIds = response.getData();
        if (CollectionUtils.isEmpty(equipmentIds)) {
            return Maps.newLinkedHashMap();
        }
        Date startDate = DateUtil.beginOfMonth(param.getSearchDate());
        Date endDate = DateUtil.endOfMonth(param.getSearchDate());

        LambdaQueryWrapper<MaintTask> wrapper = new QueryWrapper<MaintTask>()
                .lambda()
                .select(MaintTask::getId, MaintTask::getCreateTime, MaintTask::getStatus, MaintTask::getType)
                .ge(MaintTask::getCreateTime, startDate)
                .le(MaintTask::getCreateTime, endDate)
                .eq(StringUtils.isNotBlank(param.getAreaType()), MaintTask::getAreaType, param.getAreaType())
                .in(CollectionUtils.isNotEmpty(param.getProcessType()), MaintTask::getProcessType, param.getProcessType())
                .in(CollectionUtils.isNotEmpty(equipmentIds), MaintTask::getEquipmentId, equipmentIds);
        //工单屏蔽
        wrapper = buildTaskShieldWrapper(wrapper);
        List<MaintTask> maintTaskList = this.list(wrapper);
        List<PlanTriggerTime> planTriggerTimeList = triggerTimeService.getNormalListByTime(startDate, endDate);
        maintTaskList.stream().forEach(item -> {
            Date dateTime = DateUtil.beginOfDay(item.getCreateTime());
            item.setCreateTime(dateTime);
        });
        List<MaintTask> taskCompleteList =
                maintTaskList.stream()
                        .filter(maintTask -> (maintTask.getType() != TaskType.BREAKDOWN.getValue() && (maintTask.getStatus() == 8 || maintTask.getStatus() == 9 || maintTask.getStatus() == 10))
                                ||
                                (maintTask.getType() == TaskType.BREAKDOWN.getValue() && maintTask.getStatus() == TaskStatusType.CLOSED.getValue()))
                        .collect(Collectors.toList());
        planTriggerTimeList.stream().forEach(item -> {
            Date dateTime = DateUtil.beginOfDay(item.getPlanMaintTime());
            item.setPlanMaintTime(dateTime);
        });

        Map<Date, Map<Integer, Long>> orderCountMap = maintTaskList.parallelStream().collect(Collectors.groupingBy(BaseEntity::getCreateTime, Collectors.groupingBy(MaintTask::getType, Collectors.counting())));
        Map<Date, Map<Integer, Long>> orderCompleteMap = taskCompleteList.parallelStream().collect(Collectors.groupingBy(BaseEntity::getCreateTime, Collectors.groupingBy(MaintTask::getType, Collectors.counting())));
        Map<Date, List<PlanTriggerTime>> planTaskMap = planTriggerTimeList.parallelStream().collect(Collectors.groupingBy(PlanTriggerTime::getPlanMaintTime));

        long days = DateUtil.between(startDate, endDate, DateUnit.DAY);
        List<WorkCalendarResult> workCalendarResults = Lists.newArrayList();
        for (int i = 0; i <= days; i++) {
            Date date = DateUtils.addDays(startDate, i);
            Map<Integer, Long> tempOrder = orderCountMap.get(date);
            Map<Integer, Long> completeMap = orderCompleteMap.get(date);
            List<PlanTriggerTime> todayPlanTriggerTimeList = planTaskMap.get(date);
            Integer tempPlanTaskCount = 0;
            if (CollectionUtils.isNotEmpty(todayPlanTriggerTimeList)) {
                tempPlanTaskCount = todayPlanTriggerTimeList.stream().filter(item -> StringUtils.isNotBlank(item.getPlanId())).map(PlanTriggerTime::getPlanId).collect(Collectors.toSet()).size();
            }
            WorkCalendarResult build = WorkCalendarResult.builder().date(date).build();
            if (tempOrder == null) {
                build.setBreanDownCompleteCount(0);
                build.setBreanDownCount(0);
                build.setEcCompleteCount(0);
                build.setEcCount(0);
                build.setDefectCompleteCount(0);
                build.setDefectCount(0);
                build.setPlanCompleteCount(0);
                build.setPlanCount(0);
            } else {
                build.setBreanDownCount(Integer.valueOf(tempOrder.getOrDefault(TaskType.BREAKDOWN.getValue(), 0L).toString()));
                build.setEcCount(Integer.valueOf(tempOrder.getOrDefault(TaskType.EC.getValue(), 0L).toString()));
                build.setDefectCount(Integer.valueOf(tempOrder.getOrDefault(TaskType.DEFECT.getValue(), 0L).toString()));
                build.setPlanCount(Integer.valueOf(tempOrder.getOrDefault(TaskType.PLAN.getValue(), 0L).toString()));
                if (completeMap != null) {
                    build.setBreanDownCompleteCount(Integer.valueOf(completeMap.getOrDefault(TaskType.BREAKDOWN.getValue(), 0L).toString()));
                    build.setEcCompleteCount(Integer.valueOf(completeMap.getOrDefault(TaskType.EC.getValue(), 0L).toString()));
                    build.setDefectCompleteCount(Integer.valueOf(completeMap.getOrDefault(TaskType.DEFECT.getValue(), 0L).toString()));
                    build.setPlanCompleteCount(Integer.valueOf(completeMap.getOrDefault(TaskType.PLAN.getValue(), 0L).toString()));
                } else {
                    build.setBreanDownCompleteCount(0);
                    build.setEcCompleteCount(0);
                    build.setDefectCompleteCount(0);
                    build.setPlanCompleteCount(0);
                }
            }
            if (tempPlanTaskCount == null) {
                build.setPlanTaskCount(0);
            } else {
                build.setPlanTaskCount(Integer.valueOf(tempPlanTaskCount.toString()));
            }
            workCalendarResults.add(build);
        }
        LinkedHashMap<String, WorkCalendarResult> collect = workCalendarResults.stream().collect(Collectors.toMap(item -> DateUtil.format(item.getDate(), "yyyy-MM-dd"), item -> item, (k1, k2) -> k1, LinkedHashMap::new));
        return collect;
    }


    public PageResult<MaintTaskDto> getTodoOfTask(MaintTaskQueryParam param) {
        PageResult pageResult = new PageResult();
        LambdaQueryWrapper<MaintTask> wrapper = Wrappers.lambdaQuery();
        UserBaseInfo userBaseInfo = UserContextHolder.getContext().getUserBaseInfo();
        if (null == userBaseInfo) {
            log.error("用户信息为空");
        } else {
            wrapper.like(MaintTask::getUids, userBaseInfo.getUid());
            wrapper.notIn(MaintTask::getStatus, Arrays.asList(TaskStatusType.CLOSED.getValue(), TaskStatusType.EXCEPTION_CLOSED.getValue()));
            wrapper = buildTaskShieldWrapper(wrapper);
            wrapper.orderByDesc(MaintTask::getUpdateTime);
            PageResult<MaintTask> result = page(param, wrapper);
            List<MaintTaskDto> maintTaskDtos = CopyDataUtil.copyList(result.getRecords(), MaintTaskDto.class);
            if (CollectionUtils.isNotEmpty(maintTaskDtos)) {
                for (MaintTaskDto maintTaskDto : maintTaskDtos) {
                    if (maintTaskDto != null && maintTaskDto.getType() != null) {
                        maintTaskDto.setTypeName(TaskType.getNameByValue(maintTaskDto.getType()));
                        maintTaskDto.setStatusName(TaskStatusType.getNameByValue(maintTaskDto.getStatus()));
                        maintTaskDto.setSourceTypeName(TaskSourceType.getNameByValue(maintTaskDto.getSourceType()));
                    }
                }
            }
            pageResult.setTotal(result.getTotal());
            pageResult.setRecords(maintTaskDtos);
        }
        return pageResult;
    }

    @Override
    public List<MaintTaskExcelDto> exportList(MaintTaskQueryParam param) {
        List<MaintTaskExcelDto> maintTaskExcelDtos = new ArrayList<>();
        Wrapper<MaintTask> wrapper = getPageSearchWrapper(param);
        List<MaintTask> maintTaskPageDtos = this.list(wrapper);
        Date now = new Date();
        if (CollectionUtils.isNotEmpty(maintTaskPageDtos)) {
            List<String> equipmentIds = Lists.newArrayList();
            List<String> uids = Lists.newArrayList();
            List<String> teamIds = Lists.newArrayList();
            List<String> taskIds = Lists.newArrayList();
            for (MaintTask dto : maintTaskPageDtos) {
                equipmentIds.add(dto.getEquipmentId());
                if (StringUtils.isNotBlank(dto.getHandler())) {
                    uids.add(dto.getHandler());
                }
                uids.add(dto.getCreateBy());
                if (null != dto.getTeamIds() && dto.getTeamIds().length > 0) {
                    teamIds.addAll(Arrays.asList(dto.getTeamIds()));
                }
                taskIds.add(dto.getId());
            }

            Map<String, EquipmentListDto> equipmentMap = commonGetHandler.getEquipmentByIds(equipmentIds);
            //Map<String, String> uidNameMap = commonGetHandler.getUidNameMap(uids.stream().distinct().collect(Collectors.toList()));
            Map<String, String> uidNameMap = commonGetHandler.getStaffMap(uids.stream().distinct().collect(Collectors.toList())).stream()
                    .collect(Collectors.toMap(PorosSecStaffDto::getUid, PorosSecStaffDto::getName));

            //获取故障原因/处理措施
            Map<String, MaintTaskFaultDto> taskFaultMap = (null != param.getSourceType() && param.getSourceType() == TaskSourceType.BREAKDOWN.getValue()) ? maintTaskRepairService.getFaultDtoByTaskIds(taskIds) : Maps.newHashMap();
            Map<String, String> defectNameMap = (null != param.getSourceType() && param.getSourceType() == TaskSourceType.DEFECT.getValue()) ? defectInfoService.getNameByTaskId(taskIds) : Maps.newHashMap();

            Map<String, String> majorNameMap = commonGetHandler.getDictNameMap("major");
            Map<String, String> jobTypeNameMap = commonGetHandler.getDictNameMap("job_type");
            Map<String, String> urgencyNameMap = commonGetHandler.getDictNameMap("urgency");
            Map<String, String> faultInfluenceNameMap = commonGetHandler.getDictNameMap("fault_influence");
            Map<String, String> damageReasonNameMap = commonGetHandler.getDictNameMap("damage_reason");

            for (MaintTask dto : maintTaskPageDtos) {
                MaintTaskExcelDto excelDto = new MaintTaskExcelDto();
                excelDto.setCodeAndName(dto.getCode() + "/" + dto.getName());
                excelDto.setStatus(TaskStatusType.getNameByValue(dto.getStatus()));
                excelDto.setMajorName(StringUtils.isBlank(majorNameMap.get(dto.getMajor())) ? "" : majorNameMap.get(dto.getMajor()));
                excelDto.setUrgencyName(StringUtils.isBlank(urgencyNameMap.get(dto.getUrgency())) ? "" : urgencyNameMap.get(dto.getUrgency()));
                excelDto.setFaultInfluenceRemark(this.buildFaultInfluenceRemark(faultInfluenceNameMap, dto.getFaultInfluences()));
                excelDto.setJobTypeName(StringUtils.isBlank(jobTypeNameMap.get(dto.getJobType())) ? "" : jobTypeNameMap.get(dto.getJobType()));
                excelDto.setJobLevelName(null != dto.getJobLevel() ? PlanJobLevelType.getNameByValue(dto.getJobLevel()) : "");
                excelDto.setCloseReason(dto.getCloseReason());
                excelDto.setRecTaskDate(dto.getRecTaskDate());
                excelDto.setDamageReason(StringUtils.isBlank(damageReasonNameMap.get(dto.getDamageReason())) ? "" : damageReasonNameMap.get(dto.getDamageReason()));
                excelDto.setHasHangUp(null != dto.getHasHangUp() ? dto.getHasHangUp() : false);
                EquipmentListDto equipment = equipmentMap.get(dto.getEquipmentId());
                if (null != equipment) {
                    excelDto.setEquipmentName(equipment.getEquipmentName());
                    excelDto.setEquipmentCode(equipment.getEquipmentCode());
                    excelDto.setParentAllName(equipment.getParentAllName());
                    if (null != equipment.getRunningStatus()) {
                        excelDto.setEquipmentRunningStatusName(RunningStatusType.getNameByValue(equipment.getRunningStatus()));
                    } else {
                        excelDto.setEquipmentRunningStatusName(RunningStatusType.NORMAL.getName());
                    }
                }
                if (dto.getType() != null) {
                    if (dto.getSourceType() == TaskSourceType.BREAKDOWN.getValue()) {
                        excelDto.setFaultPhenomenonRemark(dto.getContent());
                        MaintTaskFaultDto maintTaskFaultDto = taskFaultMap.get(dto.getId());
                        if (null != maintTaskFaultDto) {
                            excelDto.setFaultReasonRemark(maintTaskFaultDto.getFaultReasonRemark());
                            excelDto.setFaultMeasuresRemark(maintTaskFaultDto.getFaultMeasuresRemark());
                        }
                    } else if (dto.getSourceType() == TaskSourceType.DEFECT.getValue()) {
                        excelDto.setDefectName(defectNameMap.get(dto.getId()));
                    }
                }
                if (null != dto.getWorkingTime()) {
                    excelDto.setWorkingTime(dto.getWorkingTime());
                    excelDto.setWorkingTimeHour(new BigDecimal(dto.getWorkingTime()).divide(new BigDecimal(60), 2, BigDecimal.ROUND_HALF_UP));
                    if (excelDto.getWorkingTimeHour().compareTo(BigDecimal.ZERO) == 0) {
                        excelDto.setWorkingTimeHour(null);
                    }
                }
                if (null != dto.getTaskDeadlineDate() && !finishStatusList.contains(dto.getStatus())) {
                    excelDto.setIsOverTime(dto.getTaskDeadlineDate().compareTo(now) < 0);
                } else {
                    excelDto.setIsOverTime(dto.getIsOverTime());
                }
                excelDto.setCreateTime(dto.getCreateTime());
                excelDto.setBeginMaintTime(dto.getBeginMaintTime());
                excelDto.setEndMaintTime(dto.getEndMaintTime());
                excelDto.setTaskDeadlineDate(dto.getTaskDeadlineDate());
                excelDto.setCreateUserName(uidNameMap.get(dto.getCreateBy()));
                excelDto.setHandlerName(StringUtils.isNotBlank(dto.getHandler()) ? uidNameMap.get(dto.getHandler()) : null);
                excelDto.setPlanMaintTime(dto.getPlanMaintTime());
                excelDto.setEcError((null != dto.getEcErrorNum() && dto.getEcErrorNum() > 0) ? true : false);
                excelDto.setEcErrorResultName(null != dto.getEcErrorResult() ? EcErrorResultType.getNameByValue(dto.getEcErrorResult()) : EcErrorResultType.NODEAL.getName());
                excelDto.setUpdateTime(dto.getUpdateTime());
                excelDto.setReturnReason(dto.getReturnReason());

                maintTaskExcelDtos.add(excelDto);
            }
        }

        return maintTaskExcelDtos;
    }

    private String buildFaultInfluenceRemark(Map<String, String> faultInfluenceNameMap, String[] faultInfluences) {
        String faultInfluenceRemark = null;
        if (null != faultInfluences && faultInfluences.length > 0) {
            for (String faultInfluence : faultInfluences) {
                String faultInfluenceName = faultInfluenceNameMap.get(faultInfluence);
                if (StringUtils.isNotBlank(faultInfluenceRemark)) {
                    faultInfluenceRemark += ";" + faultInfluenceName;
                } else {
                    faultInfluenceRemark = faultInfluenceName;
                }
            }
        }
        return faultInfluenceRemark;
    }

    public String generateTaskContent(MaintTask task,
                                      Map<String, FaultKnowledgeResDto> faultPhemones,
                                      Map<String, FaultKnowledgeResDto> faultReason,
                                      Map<String, FaultKnowledgeResDto> faultMeasure,
                                      Map<String, MaintTaskRepair> repairByTaskId,
                                      Map<String, DefectInfo> defectListById,
                                      Map<String, DictionaryItemDto> major,
                                      Map<String, DictionaryItemDto> defect_type,
                                      Map<String, List<MaintTaskItem>> maintTaskItemMap) {
        StringBuilder sb = new StringBuilder();
        if (task.getType() == TaskType.BREAKDOWN.getValue()) {
            MaintTaskRepair maintTaskRepair = commonGetHandler.getRepairByTaskId(repairByTaskId, task.getId());
            String faultMeasure1 = commonGetHandler.getFaultMeasure(faultMeasure, maintTaskRepair.getFaultMeasuresIds(), maintTaskRepair.getFaultMeasuresRemark());
            String faultPhemones1 = commonGetHandler.getFaultPhemones(faultPhemones, maintTaskRepair.getFaultPhenomenonIds(), maintTaskRepair.getFaultPhenomenonRemark());
            String faultReason1 = commonGetHandler.getFaultReason(faultReason, maintTaskRepair.getFaultReasonIds(), maintTaskRepair.getFaultReasonRemark());
            sb.append("故障现象：" + faultPhemones1 +
                    "；故障原因：" + faultReason1 +
                    "；处理措施：" + faultMeasure1);
        }
        if (task.getType() == TaskType.PLAN.getValue() || task.getType() == TaskType.EC.getValue()) {
            List<MaintTaskItem> maintTaskItemList = maintTaskItemMap.get(task.getId());
            if (CollectionUtils.isNotEmpty(maintTaskItemList)) {
                List<MaintTaskItem> abnormalList = maintTaskItemList.stream().filter(item -> item.getAbnormal()).collect(Collectors.toList());
                List<MaintTaskItem> normalList = maintTaskItemList.stream().filter(item -> !item.getAbnormal()).collect(Collectors.toList());
                Map<String, List<MaintTaskItem>> largeCollect = maintTaskItemList.stream().collect(Collectors.groupingBy(item -> item.getLargeCategory()));
                Map<String, List<MaintTaskItem>> subCollect = maintTaskItemList.stream().collect(Collectors.groupingBy(item -> item.getSubCategory()));
                BigDecimal standardCount = maintTaskItemList.stream().filter(item -> item.getStandardTime() != null).map(item -> item.getStandardTime()).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal workingTimeCount = maintTaskItemList.stream().filter(item -> item.getWorkingTime() != null).map(item -> item.getWorkingTime()).reduce(BigDecimal.ZERO, BigDecimal::add);
                sb.append((CollectionUtils.isNotEmpty(normalList) ? normalList.size() : 0) + "项正常，");
                sb.append((CollectionUtils.isNotEmpty(abnormalList) ? normalList.size() : 0) + "项异常/共" + maintTaskItemList.size() + "项");
                sb.append("【大类 " + largeCollect.keySet().size());
                sb.append("小类 " + subCollect.keySet().size());
                sb.append("作业内容 " + maintTaskItemList.size());
                sb.append("标准工时 " + standardCount.toString());
                sb.append("作业时间 " + workingTimeCount.toString() + "】");
            }
        }
        if (task.getType() == TaskType.DEFECT.getValue()) {
            DefectInfo defectInfo = defectListById.get(task.getSourceId());
            if (ObjectUtils.isNull(defectInfo)) {
                defectInfo = defectListById.get(task.getDefectId());
            }
            if (ObjectUtils.isNotEmpty(defectInfo)) {
                DictionaryItemDto dictionaryItemDto = defect_type.getOrDefault("" + defectInfo.getDefectType(), new DictionaryItemDto());
                sb.append("缺陷种类：" + dictionaryItemDto.getName());
                DictionaryItemDto dictionaryItemDto1 = major.getOrDefault("" + defectInfo.getMajorType(), new DictionaryItemDto());
                sb.append("；缺陷专业类别：" + dictionaryItemDto1.getName());
                sb.append("；缺陷名称：" + defectInfo.getDefectName());
                sb.append("；缺陷内容：" + defectInfo.getDefectContent());
                sb.append("；影响描述：" + defectInfo.getAffectContent());
                sb.append("；建议处理方案：" + defectInfo.getSuggestDealContent());
                sb.append("；实际处理方案：" + defectInfo.getRealDealContent() + ";");
            }
        }
        return sb.toString();
    }

    @Override
    public Integer exportListCount(MaintTaskQueryParam maintTaskQueryParam) {
        List<MaintTaskExcelDto> maintTaskExcelDtos = new ArrayList<>();
        Wrapper<MaintTask> wrapper = getPageSearchWrapper(maintTaskQueryParam);
        int count = this.count(wrapper);

        return count;
    }

    public Boolean updateEcError(MaintTaskEcErrorEditParam param) {
        MaintTask maintTask = CopyDataUtil.copyObject(param, MaintTask.class);
        return this.update(maintTask, new QueryWrapper<MaintTask>().lambda().eq(MaintTask::getId, param.getId()));
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public MaintTaskDto saveManualRepair(ManualRepairAddParam manualRepairAddParam) {
        ManualRepairAddParam manualRepair = manualRepairAddParam;
        UserBaseInfo user = PorosContextHolder.getCurrentUser();
        if (null == manualRepairAddParam.getFaultTime()) {
            manualRepair.setFaultTime(new Date());
        }

        Assert.notNull(ResultCode.PARAM_VALID_ERROR, manualRepair);


        Map<String, FaultKnowledgeResDto> faultKnowledgeMap = new HashMap<>();
        if (null != manualRepair.getFaultPhenomenonIds() && manualRepair.getFaultPhenomenonIds().length > 0) {
            faultKnowledgeMap = getFaultKnowledgeMap(Arrays.asList(manualRepair.getFaultPhenomenonIds()), StaticValue.ONE);
        }
        String content = buildPhenomenon(manualRepair.getFaultPhenomenonIds(), manualRepair.getFaultPhenomenonRemark(), faultKnowledgeMap);
        MaintTaskRepairAddDto addDto = CopyDataUtil.copyObject(manualRepair, MaintTaskRepairAddDto.class);
        addDto.setSourceId(manualRepairAddParam.getSourceMaintTaskId());
        addDto.setContent(content);
        addDto.setTaskDeadlineDate(manualRepair.getDeadlineDate());
        MaintTaskDto maintTaskDto = this.saveRepairTask(addDto);
        return maintTaskDto;

    }

    @Override
    public LinkedHashMap<String, List<UserCalendarResult>> getUserCalcuelate(UserCalendarQueryParam param) {
        LinkedHashMap<String, List<UserCalendarResult>> result = new LinkedHashMap<>();
        Date startDate = DateUtil.beginOfMonth(param.getSearchDate());
        Date endDate = DateUtil.endOfMonth(param.getSearchDate());
        List<MaintTask> maintTaskList = findRecTaskListByParam(param, startDate, endDate);
        // 获取用户名称集合
        Map<String, String> userInfoMap = getUserInfoMap(maintTaskList);
        maintTaskList.forEach(item -> {
            Date dateTime = DateUtil.beginOfDay(item.getRecTaskDate());
            item.setRecTaskDate(dateTime);
        });
        List<MaintTask> recTaskCompleteList =
                maintTaskList.stream().filter(maintTask -> maintTask.getStatus() == 8 || maintTask.getStatus() == 9 || maintTask.getStatus() == 10).collect(Collectors.toList());
        Map<Date, Map<String, Long>> recTaskMap = maintTaskList.stream().collect(Collectors.groupingBy(MaintTask::getRecTaskDate, Collectors.groupingBy(MaintTask::getHandler, Collectors.counting())));
        Map<Date, Map<String, Long>> recTaskCompleteMap = recTaskCompleteList.stream().collect(Collectors.groupingBy(MaintTask::getRecTaskDate, Collectors.groupingBy(MaintTask::getHandler, Collectors.counting())));
        long days = DateUtil.between(startDate, endDate, DateUnit.DAY);
        for (int i = 0; i <= days; i++) {
            List<UserCalendarResult> userCalendarResults = new ArrayList<>();
            Date date = DateUtils.addDays(startDate, i);
            String key = DateUtil.format(date, "yyyy-MM-dd");
            Map<String, Long> totalMap = recTaskMap.get(date);
            Map<String, Long> completeMap = recTaskCompleteMap.get(date);
            if (totalMap != null && completeMap != null) {
                for (Map.Entry<String, Long> entry : totalMap.entrySet()) {
                    UserCalendarResult userCalendarResult = new UserCalendarResult();
                    if (StringUtils.isNotBlank(userInfoMap.get(entry.getKey()))) {
                        userCalendarResult.setHandlerName(userInfoMap.get(entry.getKey()));
                    }
                    Long complete = completeMap.get(entry.getKey());
                    userCalendarResult.setHandler(entry.getKey());
                    userCalendarResult.setTakeCount(entry.getValue());
                    if (complete != null) {
                        userCalendarResult.setTakeCompleteCount(complete);
                    } else {
                        userCalendarResult.setTakeCompleteCount(0L);
                    }
                    userCalendarResults.add(userCalendarResult);
                }
            } else if (totalMap != null) {
                for (Map.Entry<String, Long> entry : totalMap.entrySet()) {
                    UserCalendarResult userCalendarResult = new UserCalendarResult();
                    if (StringUtils.isNotBlank(userInfoMap.get(entry.getKey()))) {
                        userCalendarResult.setHandlerName(userInfoMap.get(entry.getKey()));
                    }
                    userCalendarResult.setHandler(entry.getKey());
                    userCalendarResult.setTakeCount(entry.getValue());
                    userCalendarResult.setTakeCompleteCount(0L);
                    userCalendarResults.add(userCalendarResult);
                }
            }
            result.put(key, userCalendarResults);
        }
        return result;
    }

    @Override
    public LinkedHashMap<String, List<EquipmentCalendarResult>> getEquipmentCalcuelate(EquipmentCalendarQueryParam param) {
        LinkedHashMap<String, List<EquipmentCalendarResult>> result = new LinkedHashMap<>();
        Date startDate = DateUtil.beginOfMonth(param.getSearchDate());
        Date endDate = DateUtil.endOfMonth(param.getSearchDate());
        List<MaintTask> maintTaskList = getMaintTaskList(param, startDate, endDate);
        // 获取设备名称集合
        Map<String, String> equipmentMap = getEquipmentMap(maintTaskList);
        maintTaskList.forEach(item -> {
            Date dateTime = DateUtil.beginOfDay(item.getCreateTime());
            item.setCreateTime(dateTime);
        });
        List<MaintTask> taskCompleteList =
                maintTaskList.stream().filter(maintTask -> maintTask.getStatus() == 8 || maintTask.getStatus() == 9 || maintTask.getStatus() == 10).collect(Collectors.toList());
        Map<Date, Map<String, Long>> taskMap = maintTaskList.stream().collect(Collectors.groupingBy(MaintTask::getCreateTime, Collectors.groupingBy(MaintTask::getEquipmentId, Collectors.counting())));
        Map<Date, Map<String, Long>> taskCompleteMap = taskCompleteList.stream().collect(Collectors.groupingBy(MaintTask::getCreateTime, Collectors.groupingBy(MaintTask::getEquipmentId, Collectors.counting())));
        long days = DateUtil.between(startDate, endDate, DateUnit.DAY);
        for (int i = 0; i <= days; i++) {
            List<EquipmentCalendarResult> equipmentCalendarResults = new ArrayList<>();
            Date date = DateUtils.addDays(startDate, i);
            String key = DateUtil.format(date, "yyyy-MM-dd");
            Map<String, Long> totalMap = taskMap.get(date);
            Map<String, Long> completeMap = taskCompleteMap.get(date);
            if (totalMap != null && completeMap != null) {
                for (Map.Entry<String, Long> entry : totalMap.entrySet()) {
                    EquipmentCalendarResult calendarResult = new EquipmentCalendarResult();
                    if (StringUtils.isNotBlank(equipmentMap.get(entry.getKey()))) {
                        calendarResult.setEquipmentName(equipmentMap.get(entry.getKey()));
                    }
                    Long complete = completeMap.get(entry.getKey());
                    calendarResult.setEquipmentId(entry.getKey());
                    calendarResult.setTakeCount(entry.getValue());
                    if (complete != null) {
                        calendarResult.setTakeCompleteCount(complete);
                    } else {
                        calendarResult.setTakeCompleteCount(0L);
                    }
                    equipmentCalendarResults.add(calendarResult);
                }
            } else if (totalMap != null) {
                for (Map.Entry<String, Long> entry : totalMap.entrySet()) {
                    EquipmentCalendarResult calendarResult = new EquipmentCalendarResult();
                    if (StringUtils.isNotBlank(equipmentMap.get(entry.getKey()))) {
                        calendarResult.setEquipmentName(equipmentMap.get(entry.getKey()));
                    }
                    calendarResult.setEquipmentId(entry.getKey());
                    calendarResult.setTakeCount(entry.getValue());
                    calendarResult.setTakeCompleteCount(0L);
                    equipmentCalendarResults.add(calendarResult);
                }
            }
            result.put(key, equipmentCalendarResults);
        }
        return result;
    }

    private List<MaintTask> getMaintTaskList(EquipmentCalendarQueryParam param, Date startDate, Date endDate) {
        EquipmentTaskQueryParam queryParam = new EquipmentTaskQueryParam();
        queryParam.setStartDate(startDate);
        queryParam.setEndDate(endDate);
        LambdaQueryWrapper<MaintTask> wrapper = Wrappers.lambdaQuery();
        EquipmentInfoSearchDto equipmentInfoSearchDto = new EquipmentInfoSearchDto();
        equipmentInfoSearchDto.setKeyword(param.getEquipmentName());
        equipmentInfoSearchDto.setLocationIds(param.getEquipmentLocationIds());
        RestResponse<List<String>> response = equipmentClient.getEquipmentIdsByParam(equipmentInfoSearchDto);

        if (!response.isOk() || CollectionUtils.isEmpty(response.getData())) {
            return new ArrayList<>();
        } else {
            wrapper.in(MaintTask::getEquipmentId, response.getData());
        }

        wrapper.between(MaintTask::getCreateTime, startDate, endDate);
        wrapper = buildTaskShieldWrapper(wrapper);
        return (List<MaintTask>) this.list(wrapper);
    }

    private Map<String, String> getEquipmentMap(List<MaintTask> maintTaskList) {
        Map<String, String> equipmentMap = new HashMap<>();
        String[] equipmentIds = maintTaskList.stream().map(MaintTask::getEquipmentId).distinct().toArray(String[]::new);
        RestResponse<Map<String, EquipmentListDto>> response = equipmentClient.getListByIds(equipmentIds);
        if (response.isOk() && response.getData() != null) {
            Map<String, EquipmentListDto> equipmentListDtoMap = response.getData();
            equipmentMap = equipmentListDtoMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().getEquipmentName()));
        } else {
            log.error("==========>从设备服务获取设备信息失败.");
        }
        return equipmentMap;
    }

    private List<MaintTask> findRecTaskListByParam(UserCalendarQueryParam param, Date startDate, Date endDate) {
        LambdaQueryWrapper<MaintTask> wrapper = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(param.getKeyword())) {
            wrapper.like(MaintTask::getHandler, param.getKeyword());
        }
        if (StringUtils.isNotBlank(param.getUserName())) {
            List<String> uids = getUidsByName(param.getUserName());
            if (CollectionUtils.isNotEmpty(uids)) {
                wrapper.in(MaintTask::getHandler, uids);
            } else {
                return new ArrayList<>();
            }
        }
        EquipmentInfoSearchDto equipmentInfoSearchDto = new EquipmentInfoSearchDto();
        equipmentInfoSearchDto.setLocationIds(param.getEquipmentLocationIds());
        RestResponse<List<String>> response = equipmentClient.getEquipmentIdsByParam(equipmentInfoSearchDto);

        if (!response.isOk() || CollectionUtils.isEmpty(response.getData())) {
            return new ArrayList<>();
        } else {
            wrapper.in(MaintTask::getEquipmentId, response.getData());
        }
        if (param.getIsMyHandler()) {
            wrapper.eq(MaintTask::getHandler, PorosContextHolder.getCurrentUser().getUid());
        }
        wrapper.ge(MaintTask::getRecTaskDate, startDate).le(MaintTask::getRecTaskDate, endDate);
        wrapper = buildTaskShieldWrapper(wrapper);
        return (List<MaintTask>) this.list(wrapper);
    }

    private Map<String, String> getUserInfoMap(List<MaintTask> maintTaskList) {
        List<String> uids = maintTaskList.stream().map(MaintTask::getHandler).distinct().collect(Collectors.toList());
        String uidStr = StringUtils.join(uids, StringPool.COMMA);
        RestResponse<Map<String, String>> response = porosSecStaffClient.getMap(uidStr);
        Map<String, String> userInfoMap = new HashMap<>();
        if (response.isOk() && response.getData() != null) {
            userInfoMap = response.getData();
        } else {
            log.error("==========>从中台获取人员信息失败.");
        }
        return userInfoMap;
    }

    private Map<String, FaultKnowledgeResDto> getFaultKnowledgeMap(List<String> ids, Integer type) {
        Map<String, FaultKnowledgeResDto> faultKnowledgeMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(ids)) {
            FaultKnowledgeSearchDto searchDto = new FaultKnowledgeSearchDto();
            searchDto.setType(type);
            searchDto.setIds(ids);
            RestResponse<Map<String, FaultKnowledgeResDto>> restResponse = baseServiceClient.faultKnowledgeMap(searchDto);
            if (restResponse.isOk()) {
                faultKnowledgeMap = restResponse.getData();
            } else {
                log.error("获取故障现象失败");
            }
        }
        return faultKnowledgeMap;
    }

    private String buildPhenomenon(String[] phenomenonIds, String phenomenonRemark, Map<String, FaultKnowledgeResDto> faultPhenomenonMap) {
        String faultPhenomenonRemark = null;
        if (null != phenomenonIds && phenomenonIds.length > 0) {
            for (String faultPhenomenonId : phenomenonIds) {
                FaultKnowledgeResDto faultKnowledgeResDto = faultPhenomenonMap.get(faultPhenomenonId);
                if (StringUtils.isNotBlank(faultPhenomenonRemark)) {
                    faultPhenomenonRemark += ";" + faultKnowledgeResDto.getName();
                } else {
                    faultPhenomenonRemark = faultKnowledgeResDto.getName();
                }
            }
        }
        if (StringUtils.isNotBlank(faultPhenomenonRemark)) {
            if (StringUtils.isNotBlank(phenomenonRemark)) {
                faultPhenomenonRemark += ";" + phenomenonRemark;
            }
        } else {
            faultPhenomenonRemark = phenomenonRemark;
        }
        return faultPhenomenonRemark;
    }

    public Boolean updateMaintTaskOfFile(MaintTaskFileParam param) {
        MaintTask maintTask = new MaintTask();
        maintTask.setId(param.getId());
        maintTask.setFinishFileIds(param.getFinishFileIds());
        return this.update(new UpdateWrapper<MaintTask>().lambda().eq(MaintTask::getId, param.getId()).set(MaintTask::getFinishFileIds, param.getFinishFileIds()));
    }

    @Override
    public PageResult<WarnTaskPageDto> getListByWarnId(WarnQueryParam queryParam) {
        Page<WarnTaskPageDto> page = new Page<>(queryParam.getPageNo(), queryParam.getLimit());
        Page<WarnTaskPageDto> pageDto = maintTaskMapper.getListByWarnId(page, queryParam);
        List<WarnTaskPageDto> result = pageDto.getRecords();
        if (CollectionUtils.isNotEmpty(result)) {
            List<String> handlerUids = result.stream().filter(dto -> StringUtils.isNotBlank(dto.getHandler())).map(WarnTaskPageDto::getHandler).collect(Collectors.toList());
            Map<String, String> uidMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(handlerUids)) {
                String uidStrs = StringUtils.join(handlerUids, StringPool.COMMA);
                RestResponse<Map<String, String>> uidResponse = porosSecStaffClient.getMap(uidStrs);
                if (uidResponse.isOk()) {
                    uidMap = uidResponse.getData();
                } else {
                    log.error("获取用户名称失败");
                }
            }
            for (WarnTaskPageDto dto : result) {
                dto.setStatusName(null != dto.getStatus() ? TaskStatusType.getNameByValue(dto.getStatus()) : null);
                dto.setHandlerName(StringUtils.isNotBlank(dto.getHandler()) ? uidMap.get(dto.getHandler()) : null);
                dto.setActivityId(getProcessTaskId(dto.getProcessInstanceId(), false));
            }
        }
        return Optional.ofNullable(PageResult.<WarnTaskPageDto>builder()
                        .records(result)
                        .total(pageDto.getTotal())
                        .build())
                .orElse(new PageResult<>());
    }

    public void markDeletedBatch(String id) {
        MaintTask byId = (MaintTask) this.getById(id);
        if (byId != null) {
            this.removeById(byId.getId());
            activitiHandler.deleteByProcessId(byId.getProcessInstanceId());
        } else {
            throw new GlobalServiceException(GlobalResultMessage.of("该计划不存在"));
        }
    }

    @Override
    public Boolean closeTaskBatch(List<TaskSubmitParam> taskSubmitParams) {
        List<String> idList = taskSubmitParams.stream().map(TaskSubmitParam::getTaskId).distinct().collect(Collectors.toList());
        List<MaintTask> taskList = this.listByIds(idList);
        if (CollectionUtils.isEmpty(taskList)) {
            return false;
        }
        Map<String, String> processTaskMap = new HashMap<>();
        taskList.forEach(task -> {
            String activityId = this.getProcessTaskId(task.getProcessInstanceId(), true);
            processTaskMap.put(task.getId(), activityId);
        });
        taskSubmitParams.forEach(param -> {
            param.setActivityId(processTaskMap.get(param.getTaskId()));
            this.submitProcessTask(param);
        });
        return true;
    }

    @Override
    public String batchEdit(TaskBatchEditParam param) {
        Integer num = 0;
        if (CollectionUtils.isNotEmpty(param.getTaskIds())) {
            LambdaQueryWrapper<MaintTask> wrapper = Wrappers.lambdaQuery();
            wrapper.in(MaintTask::getId, param.getTaskIds());
            wrapper.eq(MaintTask::getDeleted, DeletedType.NO.getValue());
            wrapper.select(MaintTask::getId, MaintTask::getProcessInstanceId, MaintTask::getStatus);
            List<MaintTask> maintTaskList = this.list(wrapper);
            if (CollectionUtils.isNotEmpty(maintTaskList)) {
                List<MaintTask> editTasks = Lists.newArrayList();
                for (MaintTask maintTask : maintTaskList) {
                    if (param.getSubmitType() == SubmitType.CHECK_ACCEPT.getCode() && maintTask.getStatus() == TaskStatusType.CHECK_ACCEPT.getValue()) {
                        String activityId = this.getProcessTaskId(maintTask.getProcessInstanceId(), true);
                        if (StringUtils.isNotBlank(activityId)) {
                            TaskSubmitParam taskSubmitParam = new TaskSubmitParam();
                            taskSubmitParam.setTaskId(maintTask.getId());
                            taskSubmitParam.setActivityId(activityId);
                            taskSubmitParam.setSubmitType(param.getSubmitType());
                            Boolean flag = this.submitProcessTask(taskSubmitParam);
                            if (flag) {
                                num++;
                                maintTask.setStatus(TaskStatusType.CLOSED.getValue());
                                maintTask.setGrade(param.getGrade());
                                maintTask.setCheckAcceptResult(param.getCheckAcceptResult());
                                maintTask.setComment(param.getComment());
                                maintTask.setHandleInformation(param.getHandleInformation());
                                maintTask.setHandleResult(param.getHandleResult());
                                editTasks.add(maintTask);
                            }
                        }
                    } else if (param.getSubmitType() == SubmitType.EXCEPTION_CLOSED.getCode()) {
                        String activityId = this.getProcessTaskId(maintTask.getProcessInstanceId(), true);
                        TaskSubmitParam taskSubmitParam = new TaskSubmitParam();
                        taskSubmitParam.setTaskId(maintTask.getId());
                        taskSubmitParam.setActivityId(activityId);
                        taskSubmitParam.setSubmitType(param.getSubmitType());
                        taskSubmitParam.setRemark(param.getRemark());
                        Boolean flag = this.submitProcessTask(taskSubmitParam);
                        if (flag) {
                            num++;
                        }
                    } else if (param.getSubmitType() == SubmitType.TO_DELETE.getCode()) {
                        String activityId = this.getProcessTaskId(maintTask.getProcessInstanceId(), true);
                        TaskSubmitParam taskSubmitParam = new TaskSubmitParam();
                        taskSubmitParam.setTaskId(maintTask.getId());
                        taskSubmitParam.setActivityId(activityId);
                        taskSubmitParam.setSubmitType(param.getSubmitType());
                        taskSubmitParam.setRemark(param.getRemark());
                        Boolean flag = this.submitProcessTask(taskSubmitParam);
                        if (flag) {
                            num++;
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(editTasks)) {
                    this.updateBatchById(editTasks);
                }
            }
        }
        return num > 0 ? "本次批量操作拥有权限的工单" + num + "条" : "本次未选择有权限操作的工单";
    }

    @Override
    public TodayTaskCountDto todayTask(Boolean ourTask) {
        Date now = new Date();
        DateTime begin = DateUtil.beginOfMonth(now);
        DateTime end = DateUtil.endOfMonth(now);
        TodayTaskCountDto todayTaskCountDto = new TodayTaskCountDto();
        //判断当前是早班还是晚班 0-7:59昨天晚班 2 8-19:59 今天白班 3 20:00-24:00 今天晚班
        Integer hour = DateUtil.hour(new Date(), true);
        Integer ourNum = StaticValue.ZERO;
        Integer allNum = StaticValue.ZERO;
        Map<Integer, TodayTaskDetailDto> typeCountMap = new HashMap<>();
        RestResponse<BuildInfoSearchDto> authRes = equipmentClient.getCurrentUserInfoIds();
        if (!authRes.isOk()) {
            log.error("获取设备权限失败");
        } else {
            BuildInfoSearchDto buildInfoSearchDto = authRes.getData();
            if (!buildInfoSearchDto.getFlag() || CollectionUtils.isNotEmpty(buildInfoSearchDto.getEquipmentIds())) {
                Date nowBeginDate = DateUtil.beginOfDay(new Date());
                List<Integer> repairCompleteStatus = Arrays.asList(new Integer[]{TaskStatusType.CLOSED.getValue()});
                List<Integer> otherCompleteStatus = Arrays.asList(new Integer[]{TaskStatusType.CLOSED.getValue(), TaskStatusType.CHECK_ACCEPT.getValue()});

                //保养要拆单  一级保养统计当天 根据时间划分早班、晚班；08:00～20:00为早班，20:00～08:00为晚班
                LambdaQueryWrapper<MaintTask> wrapper = Wrappers.lambdaQuery();
                if (hour >= 0 && hour < 8) {
                    wrapper.between(MaintTask::getCreateTime, DateUtil.offset(nowBeginDate, DateField.HOUR, -4), new Date());
                } else if (hour >= 8 && hour < 20) {
                    wrapper.between(MaintTask::getCreateTime, DateUtil.offset(nowBeginDate, DateField.HOUR, 8), new Date());
                } else {
                    wrapper.between(MaintTask::getCreateTime, DateUtil.offset(nowBeginDate, DateField.HOUR, 20), new Date());
                }
                wrapper.eq(MaintTask::getJobLevel, PlanJobLevelType.ONE.getValue());
                wrapper.eq(MaintTask::getSourceType, TaskSourceType.PLAN.getValue());
                wrapper.in(CollectionUtils.isNotEmpty(buildInfoSearchDto.getEquipmentIds()), MaintTask::getEquipmentId, buildInfoSearchDto.getEquipmentIds());
                wrapper.select(MaintTask::getId, MaintTask::getType, MaintTask::getStatus, MaintTask::getHandler, MaintTask::getJobLevel, MaintTask::getSourceType, MaintTask::getCreateTime);
                List<MaintTask> oneLevelPlanMaintTasks = maintTaskMapper.selectList(wrapper);
                if (CollectionUtils.isNotEmpty(oneLevelPlanMaintTasks)) {
                    allNum += oneLevelPlanMaintTasks.size();
                    //早班晚班都放在TaskWorkshopType.PLAN_ONE_LEVEL_MORNING里
                    this.buildTaskSourceTypeCountMap(oneLevelPlanMaintTasks, typeCountMap, repairCompleteStatus, otherCompleteStatus, TaskWorkshopType.PLAN_ONE_LEVEL_MORNING.getValue());
                }

                //其他按月统计
                wrapper = Wrappers.lambdaQuery();
                wrapper.notIn(MaintTask::getType, TaskType.DEFECT.getValue(), TaskType.BREAKDOWN.getValue());
                wrapper.in(CollectionUtils.isNotEmpty(buildInfoSearchDto.getEquipmentIds()), MaintTask::getEquipmentId, buildInfoSearchDto.getEquipmentIds());
                wrapper.select(MaintTask::getId, MaintTask::getType, MaintTask::getStatus, MaintTask::getHandler, MaintTask::getJobLevel, MaintTask::getSourceType, MaintTask::getCreateTime);

                //二级保养、三级保养、报修、故障月份记录为上月26-本月25号
                //20250709,二级保养、三级保养、报修也改为了自然月
                wrapper.between(MaintTask::getCreateTime, begin,end);
                List<MaintTask> maintTasks = maintTaskMapper.selectList(wrapper);

                //20250228,故障和缺陷依然走自然月模式
                wrapper = Wrappers.lambdaQuery();
                wrapper.in(MaintTask::getType, TaskType.DEFECT.getValue(), TaskType.BREAKDOWN.getValue());
                wrapper.in(CollectionUtils.isNotEmpty(buildInfoSearchDto.getEquipmentIds()), MaintTask::getEquipmentId, buildInfoSearchDto.getEquipmentIds());
                wrapper.select(MaintTask::getId, MaintTask::getType, MaintTask::getStatus, MaintTask::getHandler, MaintTask::getJobLevel, MaintTask::getSourceType, MaintTask::getCreateTime);
                wrapper.between(MaintTask::getCreateTime, begin, end);
                List<MaintTask> maintTasks2 = maintTaskMapper.selectList(wrapper);
                if (CollectionUtils.isNotEmpty(maintTasks2)) {
                    maintTasks.addAll(maintTasks2);
                }
                //20250228,故障和缺陷依然走自然月模式
                if (CollectionUtils.isNotEmpty(maintTasks)) {
                    Map<Integer, List<MaintTask>> taskSourceTypeMap = maintTasks.stream().collect(Collectors.groupingBy(MaintTask::getSourceType));
                    for (Map.Entry<Integer, List<MaintTask>> entity : taskSourceTypeMap.entrySet()) {
                        List<MaintTask> sourceTypeTasks = entity.getValue();
                        if (entity.getKey() == TaskSourceType.PLAN.getValue()) {
                            //二级保养/三级保养统计当月
                            Map<Integer, List<MaintTask>> jobLevelMap = sourceTypeTasks.stream().collect(Collectors.groupingBy(MaintTask::getJobLevel));
                            for (Map.Entry<Integer, List<MaintTask>> jobLevelEntity : jobLevelMap.entrySet()) {
                                List<MaintTask> jobLevelTasks = jobLevelEntity.getValue();
                                if (jobLevelEntity.getKey() == PlanJobLevelType.ONE.getValue()) {
                                    //一级单独统计
                                } else if (jobLevelEntity.getKey() == PlanJobLevelType.TWO.getValue()) {
                                    allNum += jobLevelTasks.size();
                                    this.buildTaskSourceTypeCountMap(jobLevelTasks, typeCountMap, repairCompleteStatus, otherCompleteStatus, TaskWorkshopType.PLAN_TWO_LEVEL.getValue());
                                } else if (jobLevelEntity.getKey() == PlanJobLevelType.THREE.getValue()) {
                                    allNum += jobLevelTasks.size();
                                    this.buildTaskSourceTypeCountMap(jobLevelTasks, typeCountMap, repairCompleteStatus, otherCompleteStatus, TaskWorkshopType.PLAN_THREE_LEVEL.getValue());
                                } else {
                                    //暂不统计其他
                                }
                            }
                        } else if (entity.getKey() == TaskSourceType.BREAKDOWN.getValue()) {
                            allNum += sourceTypeTasks.size();
                            this.buildTaskSourceTypeCountMap(sourceTypeTasks, typeCountMap, repairCompleteStatus, otherCompleteStatus, TaskWorkshopType.BREAKDOWN.getValue());
                        } else if (entity.getKey() == TaskSourceType.DEFECT.getValue()) {
                            allNum += sourceTypeTasks.size();
                            this.buildTaskSourceTypeCountMap(sourceTypeTasks, typeCountMap, repairCompleteStatus, otherCompleteStatus, TaskWorkshopType.DEFECT.getValue());
                        }
                    }
                }
            }
        }

        todayTaskCountDto.setAllNum(allNum);
        todayTaskCountDto.setOurNum(ourNum);
        List<TodayTaskDetailDto> detailDtos = new ArrayList<>();
        for (TaskWorkshopType workshopType : TaskWorkshopType.values()) {
            //当前时间判断是早班晚班
            if ((workshopType == TaskWorkshopType.PLAN_ONE_LEVEL_MORNING && (hour < 8 || hour >= 20)) ||
                    (workshopType == TaskWorkshopType.PLAN_ONE_LEVEL_NIGHT && hour >= 8 && hour < 20)) {
                continue;
            }

            //一级维保早班晚班都存储在TaskWorkshopType.PLAN_ONE_LEVEL_MORNING里
            Integer workshopTypeValue = workshopType == TaskWorkshopType.PLAN_ONE_LEVEL_NIGHT ? TaskWorkshopType.PLAN_ONE_LEVEL_MORNING.getValue()
                    : workshopType.getValue();
            TodayTaskDetailDto detailDto = typeCountMap.get(workshopTypeValue);
            if (null == detailDto) {
                detailDto = TodayTaskDetailDto.builder().allCount(StaticValue.ZERO).finishedCount(StaticValue.ZERO).notFinishedCount(StaticValue.ZERO).taskType(workshopType.getValue()).build();
            }
            //重置一级维保早班晚班
            detailDto.setTaskType(workshopType.getValue());
            detailDto.setLabel(workshopType.getLabel());
            detailDto.setColor(workshopType.getColor());
            detailDtos.add(detailDto);
        }
        todayTaskCountDto.setList(detailDtos);

        return todayTaskCountDto;
    }

    private void buildTaskSourceTypeCountMap(List<MaintTask> maintTasks, Map<Integer, TodayTaskDetailDto> typeCountMap,
                                             List<Integer> repairCompleteStatus, List<Integer> otherCompleteStatus, Integer taskWorkshopType) {
        Integer allCount = StaticValue.ZERO;
        Integer finishedCount = StaticValue.ZERO;
        Integer notFinishedCount = StaticValue.ZERO;
        for (MaintTask maintTask : maintTasks) {
            allCount++;
            if (
                    (maintTask.getSourceType() == TaskSourceType.BREAKDOWN.getValue() && repairCompleteStatus.contains(maintTask.getStatus())) ||
                            (maintTask.getSourceType() == TaskSourceType.DEFECT.getValue() && repairCompleteStatus.contains(maintTask.getStatus())) ||
                            (maintTask.getSourceType() != TaskSourceType.BREAKDOWN.getValue() && maintTask.getSourceType() != TaskSourceType.DEFECT.getValue() && otherCompleteStatus.contains(maintTask.getStatus()))
            ) {
                finishedCount++;
            } else {
                notFinishedCount++;
            }
        }
        TodayTaskDetailDto detailDto = TodayTaskDetailDto.builder().taskType(taskWorkshopType).allCount(allCount).finishedCount(finishedCount).notFinishedCount(notFinishedCount).build();
        typeCountMap.put(taskWorkshopType, detailDto);
    }

    public List<String> dealLocationToEquipmentId(MaintTaskReportQueryParam param) {
        List<String> equipmentIds = Lists.newArrayList();
        EquipmentInfoSearchDto equipmentInfoSearchDto = new EquipmentInfoSearchDto();
        equipmentInfoSearchDto.setLocationIds(param.getEquipmentLocationIds());
        equipmentInfoSearchDto.setCategoryIds(param.getEquipmentCategoryIds());
        RestResponse<List<String>> equipmentIdsByEquipmentInfo = equipmentClient.getEquipmentIdsByParam(equipmentInfoSearchDto);
        if (!equipmentIdsByEquipmentInfo.isOk() || CollectionUtils.isEmpty(equipmentIdsByEquipmentInfo.getData())) {
            throw new GlobalServiceException(GlobalResultMessage.of("查找不到相关设备"));
        }
        equipmentIds = equipmentIdsByEquipmentInfo.getData();
        return equipmentIds;
    }

    @Override
    public List<MaintTaskPageDto> listDto(MaintTaskQueryParam maintTaskQueryParam) {
        Wrapper<MaintTask> wrapper = getPageSearchWrapper(maintTaskQueryParam);
        List<MaintTask> result = this.list(wrapper);
        List<MaintTaskPageDto> maintTaskPageDtos = CopyDataUtil.copyList(result, MaintTaskPageDto.class);
        if (CollectionUtils.isNotEmpty(maintTaskPageDtos)) {
            String[] equipmentIds = maintTaskPageDtos.stream().map(MaintTaskPageDto::getEquipmentId).distinct()
                    .collect(Collectors.toList()).stream().toArray(String[]::new);
            Set<String> handlerIdSet = maintTaskPageDtos.stream().filter(item -> StringUtils.isNotBlank(item.getHandler())).map(item -> item.getHandler()).collect(Collectors.toSet());
            Map<String, StaffBaseInfoDto> names = commonGetHandler.getNames(Lists.newArrayList(handlerIdSet.iterator()));
            RestResponse<Map<String, EquipmentListDto>> listRestResponse = equipmentClient.getListByIds(equipmentIds);
            Map<String, EquipmentListDto> equipmentMap = new HashMap<>();
            if (!listRestResponse.isSuccess()) {
                log.error("远程调用equipment-service出错");
            } else {
                equipmentMap = listRestResponse.getData();

            }
            String[] uids = maintTaskPageDtos.stream().map(MaintTaskPageDto::getCreateBy).distinct().toArray(String[]::new);
            Map<String, String> uidMap = new HashMap<>();
            if (null != uids && uids.length > StaticValue.ZERO) {
                String uidStrs = StringUtils.join(uids, StringPool.COMMA);
                RestResponse<Map<String, String>> uidResponse = porosSecStaffClient.getMap(uidStrs);
                if (uidResponse.isOk()) {
                    uidMap = uidResponse.getData();
                } else {
                    log.error("获取用户名称失败");
                }
            }
            Set<String> teamIdSet = maintTaskPageDtos.stream().map(item -> item.getTeamIds()).map(item -> ArrayUtil.isNotEmpty(item) ? item[0] : null).filter(item -> item != null).collect(Collectors.toSet());
            Map<String, MaintTeamDto> teamById = commonGetHandler.getTeamById(Lists.newArrayList(teamIdSet.iterator()));
            //获取辅助人员名称
            List<String> idList = maintTaskPageDtos.stream().map(MaintTaskPageDto::getId).distinct().collect(Collectors.toList());
            List<MaintTaskAssistListDto> nameList = assistPersonService.getAssistNameList(idList);
            Map<String, List<MaintTaskAssistListDto>> useNameMaps = new HashMap<>();
            if (CollectionUtils.isNotEmpty(nameList)) {
                useNameMaps = nameList.stream().collect(Collectors.groupingBy(MaintTaskAssistListDto::getTaskId));

            }
            Date now = new Date();
            int i = 1;
            for (MaintTaskPageDto dto : maintTaskPageDtos) {
                if (CollectionUtils.isNotEmpty(useNameMaps.get(dto.getId()))) {
                    dto.setUserName(joinUserName(dto.getId(), useNameMaps));
                } else {
                    dto.setUserName(null);
                }
                if (dto.getType() != null && dto.getType() == StaticValue.ONE) {
                    dto.setName(dto.getName().replace("故障单", "维修单"));
                }
                dto.setStatusName(TaskStatusType.getNameByValue(dto.getStatus()));
                dto.setMaintTeamName(commonGetHandler.getTeamName(teamById, dto.getTeamIds()));
                EquipmentListDto equipment = equipmentMap.get(dto.getEquipmentId());
                if (null != equipment) {
                    dto.setEquipmentName(equipment.getEquipmentName());
                    dto.setEquipmentCode(equipment.getEquipmentCode());
                    dto.setEquipmentCategoryName(equipment.getCategoryName());
                    dto.setPicId(equipment.getPicId());
                    dto.setPicUrl(equipment.getPicUrl());
                }
                dto.setCreateBy(uidMap.get(dto.getCreateBy()));
                if (StringUtils.isNotBlank(dto.getAllStaffIds())) {
                    dto.setStaffNames(buildStaffNames(dto.getAllStaffIds().split(StringPool.COMMA)));
                }
                if (null != dto.getTaskDeadlineDate() && !finishStatusList.contains(dto.getStatus())) {
                    dto.setIsOverTime(dto.getTaskDeadlineDate().compareTo(now) < 0);
                }
                if (dto.getHandler() != null) {
                    dto.setHandlerName(commonGetHandler.getNames(names, dto.getHandler()));
                }
                dto.setTypeName(TaskType.getNameByValue(dto.getType()));
                dto.setTaskNo("" + (i++));
                dto.setNameStr(dto.getName() + "/" + dto.getCode());
                dto.setUpdateTimeStr(DateUtil.format(dto.getUpdateTime(), "yyyy-MM-dd HH:mm:ss"));
                dealTimeCost(dto);
            }
        }
        return maintTaskPageDtos;
    }

    //    public PageResult<MaintTaskPageDto> getOverTimeTaskList(MaintTaskQueryParam queryParam) {
//        Page<MaintTaskDto> page = new Page<>(queryParam.getPageNo(), queryParam.getLimit());
//        Page<MaintTaskDto> overTimeTaskList = maintTaskMapper.getOverTimeTaskList(page, queryParam);
//        List<MaintTaskPageDto> maintTaskPageDtos = CopyDataUtil.copyList(overTimeTaskList.getRecords(), MaintTaskPageDto.class);
//        if (CollectionUtils.isNotEmpty(maintTaskPageDtos)) {
//            //Map<String, String> categoryNameMap = commonGetHandler.getCategoryNameMap();
//            String[] equipmentIds = maintTaskPageDtos.stream().map(MaintTaskPageDto::getEquipmentId).distinct()
//                    .collect(Collectors.toList()).stream().toArray(String[]::new);
//            Set<String> handlerIdSet = maintTaskPageDtos.stream().filter(item -> StringUtils.isNotBlank(item.getHandler())).map(item -> item.getHandler()).collect(Collectors.toSet());
//            Map<String, StaffBaseInfoDto> names = commonGetHandler.getNames(Lists.newArrayList(handlerIdSet.iterator()));
//            RestResponse<Map<String, EquipmentListDto>> listRestResponse = equipmentClient.getListByIds(equipmentIds);
//            Map<String, EquipmentListDto> equipmentMap = new HashMap<>();
//            if (!listRestResponse.isSuccess()) {
//                log.error("远程调用equipment-service出错");
//            } else {
//                equipmentMap = listRestResponse.getData();
//
//            }
//            String[] uids = maintTaskPageDtos.stream().map(MaintTaskPageDto::getCreateBy).distinct().toArray(String[]::new);
//            Map<String, String> uidMap = new HashMap<>();
//            if (null != uids && uids.length > StaticValue.ZERO) {
//                String uidStrs = StringUtils.join(uids, StringPool.COMMA);
//                RestResponse<Map<String, String>> uidResponse = porosSecStaffClient.getMap(uidStrs);
//                if (uidResponse.isOk()) {
//                    uidMap = uidResponse.getData();
//                } else {
//                    log.error("获取用户名称失败");
//                }
//            }
//            Set<String> teamIdSet = maintTaskPageDtos.stream().map(item -> item.getTeamIds()).map(item -> ArrayUtil.isNotEmpty(item) ? item[0] : null).filter(item -> item != null).collect(Collectors.toSet());
//            Map<String, MaintTeamDto> teamById = commonGetHandler.getTeamById(Lists.newArrayList(teamIdSet.iterator()));
//            //获取辅助人员名称
//            List<String> idList = maintTaskPageDtos.stream().map(MaintTaskPageDto::getId).distinct().collect(Collectors.toList());
//            List<MaintTaskAssistListDto> nameList = assistPersonService.getAssistNameList(idList);
//            Map<String, List<MaintTaskAssistListDto>> useNameMaps = new HashMap<>();
//            if (CollectionUtils.isNotEmpty(nameList)) {
//                useNameMaps = nameList.stream().collect(Collectors.groupingBy(MaintTaskAssistListDto::getTaskId));
//
//            }
//            Date now = new Date();
//            for (MaintTaskPageDto dto : maintTaskPageDtos) {
//                if (CollectionUtils.isNotEmpty(useNameMaps.get(dto.getId()))) {
//                    dto.setUserName(joinUserName(dto.getId(), useNameMaps));
//                } else {
//                    dto.setUserName(null);
//                }
//                if (dto.getType() != null && dto.getType() == StaticValue.ONE) {
//                    dto.setName(dto.getName().replace("故障单", "维修单"));
//                }
//                dto.setStatusName(TaskStatusType.getNameByValue(dto.getStatus()));
//                dto.setMaintTeamName(commonGetHandler.getTeamName(teamById, dto.getTeamIds()));
//                EquipmentListDto equipment = equipmentMap.get(dto.getEquipmentId());
//                if (null != equipment) {
//                    dto.setEquipmentName(equipment.getEquipmentName());
//                    dto.setEquipmentCode(equipment.getEquipmentCode());
//                    dto.setPicId(equipment.getPicId());
//                    dto.setPicUrl(equipment.getPicUrl());
//                    dto.setParentAllName(equipment.getParentAllName());
//                    dto.setCategoryAllName(equipment.getCategoryAllName());
//                }
//                dto.setCreateBy(uidMap.get(dto.getCreateBy()));
//                if (dto.getHandler() != null) {
//                    dto.setHandlerName(commonGetHandler.getNames(names, dto.getHandler()));
//                }
//                dto.setTypeName(TaskType.getNameByValue(dto.getType()));
//            }
//        }
//        return Optional.ofNullable(PageResult.<MaintTaskPageDto>builder()
//                        .records(maintTaskPageDtos)
//                        .total(overTimeTaskList.getTotal())
//                        .build())
//                .orElse(new PageResult<>());
//    }
    public void exportOvertimeTask(MaintTaskQueryParam maintTaskQueryParam, HttpServletResponse response) {
        ExcelUtils<MaintTaskOvertimeExcelDto> util = new ExcelUtil<>(MaintTaskOvertimeExcelDto.class);
        List<MaintTaskPageDto> maintTaskExcelDtos = this.listDto(maintTaskQueryParam);
        Set<String> collect = maintTaskExcelDtos.stream().filter(item -> StringUtils.isNotBlank(item.getProcessInstanceId())).map(item -> item.getProcessInstanceId()).collect(Collectors.toSet());
        List<ActOvertimeInfo> list = actOvertimeInfoService.list(new QueryWrapper<ActOvertimeInfo>().lambda().in(ActOvertimeInfo::getProcInsId, collect));
        if (CollectionUtils.isNotEmpty(list)) {
            Map<String, List<ActOvertimeInfo>> collect1 = list.stream().collect(Collectors.groupingBy(item -> item.getProcInsId()));
            for (MaintTaskPageDto task : maintTaskExcelDtos) {
                List<ActOvertimeInfo> orDefault = collect1.getOrDefault(task.getProcessInstanceId(), null);
                if (orDefault != null) {
                    StringBuilder sb = new StringBuilder();
                    for (ActOvertimeInfo temp : orDefault) {
                        sb.append(temp.getTaskName() + ",截止时间：" + DateUtil.format(temp.getTaskEndTime(), "yyyy-MM-dd HH:mm:ss") + ",完成时间：" + DateUtil.format(temp.getTaskRealTime(), "yyyy-MM-dd HH:mm:ss") + ";\n");
                    }
                    task.setOverTimeContent(sb.toString());
                }
            }
        }
        List<MaintTaskOvertimeExcelDto> maintTaskOvertimeExcelDtos = CopyDataUtil.copyList(maintTaskExcelDtos, MaintTaskOvertimeExcelDto.class);

        util.exportExcel(maintTaskOvertimeExcelDtos, "超时异常工单列表", response);
    }

    public void exportEcErrorTask(MaintTaskQueryParam maintTaskQueryParam, HttpServletResponse response) {
        ExcelUtils<MaintTaskEcErrorExcelDto> util = new ExcelUtil<>(MaintTaskEcErrorExcelDto.class);
        List<MaintTaskPageDto> maintTaskExcelDtos = this.listDto(maintTaskQueryParam);
        Set<String> collect = maintTaskExcelDtos.stream().filter(item -> StringUtils.isNotBlank(item.getId())).map(item -> item.getId()).collect(Collectors.toSet());
        List<MaintTaskEcErrorExcelDto> maintTaskEcErrorExcelDtos = Lists.newArrayList();
        List<TaskItemDetailDto> byTaskIds = taskItemService.getByTaskIds(Lists.newArrayList(collect), true);
        Map<String, List<TaskItemDetailDto>> collect1 = byTaskIds.stream().collect(Collectors.groupingBy(item -> item.getTaskId()));
        for (MaintTaskPageDto task : maintTaskExcelDtos) {
            List<TaskItemDetailDto> orDefault = collect1.getOrDefault(task.getId(), null);
            task.setEcErrorResultName(EcErrorResultType.getNameByValue(task.getEcErrorResult()));
            if (CollectionUtils.isNotEmpty(orDefault)) {
                for (TaskItemDetailDto taskItem : orDefault) {
                    task.setMaintTaskItemCategoryStr(taskItem.getLargeCategory() + "/" + taskItem.getSubCategory());
                    task.setMaintTaskItemContent(taskItem.getContent());
                    if (taskItem.getJobItemResultType().equals("2")) {
                        task.setTargetStr(taskItem.getTargetMin().toString() + "-" + taskItem.getTargetMax().toString());
                    } else {
                        task.setTargetStr(taskItem.getTargetValue() != null ? taskItem.getTargetValue().toString() : null);
                    }
                    MaintTaskEcErrorExcelDto temp = CopyDataUtil.copyObject(task, MaintTaskEcErrorExcelDto.class);
                    temp.setBenchmark(taskItem.getBenchmark());
                    //判断
                    if (taskItem.getJobItemResultType().equals("1")) {
                        temp.setResult(taskItem.getResult().equals("1") ? "正常" : "异常");
                        //数值
                    } else if (taskItem.getJobItemResultType().equals("2")) {
                        temp.setResult(taskItem.getResult());
                        //内容
                    } else {
                        temp.setResult(taskItem.getResult());
                    }

                    temp.setDescription(taskItem.getDescription());
                    maintTaskEcErrorExcelDtos.add(temp);
                }
            } else {
                MaintTaskEcErrorExcelDto temp = CopyDataUtil.copyObject(task, MaintTaskEcErrorExcelDto.class);
                maintTaskEcErrorExcelDtos.add(temp);
            }
        }

        util.exportExcel(maintTaskEcErrorExcelDtos, "维护异常工单列表", response);
    }

    public void exportErrorTask(MaintTaskQueryParam maintTaskQueryParam, HttpServletResponse response) {
        ExcelUtils<MaintTaskErrorExcelDto> util = new ExcelUtil<>(MaintTaskErrorExcelDto.class);
        List<MaintTaskPageDto> maintTaskExcelDtos = this.listDto(maintTaskQueryParam);
        List<MaintTaskErrorExcelDto> maintTaskErrorExcelDtos = CopyDataUtil.copyList(maintTaskExcelDtos, MaintTaskErrorExcelDto.class);
        util.exportExcel(maintTaskErrorExcelDtos, "处理异常工单列表", response);
    }

    @Override
    public Boolean editEquipmentInfo(TaskEquipmentInfoEditParam param) {
        EquipmentHistoryInfo equipmentHistoryInfo = (EquipmentHistoryInfo) equipmentHistoryInfoService.getOne(new QueryWrapper<EquipmentHistoryInfo>().lambda().eq(EquipmentHistoryInfo::getDeleted, 0).eq(EquipmentHistoryInfo::getTaskId, param.getId()).eq(EquipmentHistoryInfo::getChanged, 0));
        if (ObjectUtils.isNull(equipmentHistoryInfo)) {
            equipmentHistoryInfo = new EquipmentHistoryInfo();
            equipmentHistoryInfo.setNewEquipmentId(param.getNewEquipmentId());
            equipmentHistoryInfo.setOldEquipmentId(param.getEquipmentId());
            equipmentHistoryInfo.setTaskId(param.getId());
            equipmentHistoryInfo.setChanged(0);
            equipmentHistoryInfo.setRemark(param.getRemark());
            equipmentHistoryInfo.setCreater(PorosContextHolder.getCurrentUser().getName());
            equipmentHistoryInfo.setCommunicationerId(param.getCommunicationerId());
            equipmentHistoryInfo.setCommunicationerName(param.getCommunicationerName());
            equipmentHistoryInfoService.save(equipmentHistoryInfo);
        } else {
            equipmentHistoryInfo.setChanged(1);
            equipmentHistoryInfoService.updateById(equipmentHistoryInfo);

            equipmentHistoryInfo = new EquipmentHistoryInfo();
            equipmentHistoryInfo.setNewEquipmentId(param.getNewEquipmentId());
            equipmentHistoryInfo.setOldEquipmentId(param.getEquipmentId());
            equipmentHistoryInfo.setTaskId(param.getId());
            equipmentHistoryInfo.setChanged(0);
            equipmentHistoryInfo.setRemark(param.getRemark());
            equipmentHistoryInfo.setCreater(PorosContextHolder.getCurrentUser().getName());
            equipmentHistoryInfo.setCommunicationerId(param.getCommunicationerId());
            equipmentHistoryInfo.setCommunicationerName(param.getCommunicationerName());
            equipmentHistoryInfoService.save(equipmentHistoryInfo);
        }

        this.updateTask(param);
        /*RestResponse<List<String>> equipmentRoleResponse = porosSecGrantClient.getUidsByRoleCode(equipmentRoleCode);
        if (equipmentRoleResponse.isOk()) {
            List<String> roleUids = equipmentRoleResponse.getData();
            if (CollectionUtils.isNotEmpty(roleUids)) {
                this.push(roleUids, TaskSourceType.getNameByValue(param.getSourceType()), "待确认变更", param.getName(), param.getId());
            }
        } else {
            log.error("获取中台工单变更设备审核员角色人员失败->" + JSONObject.toJSON(equipmentRoleResponse));
        }*/
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateTask(TaskEquipmentInfoEditParam param) {
        EquipmentHistoryInfo equipmentHistoryInfo = (EquipmentHistoryInfo) equipmentHistoryInfoService.getOne(new QueryWrapper<EquipmentHistoryInfo>().lambda().eq(EquipmentHistoryInfo::getDeleted, 0).eq(EquipmentHistoryInfo::getTaskId, param.getId()).eq(EquipmentHistoryInfo::getChanged, 0));
        if (ObjectUtils.isNotNull(equipmentHistoryInfo)) {
            MaintTaskDto dtoById = this.getDtoById(param.getId());
            EquipmentListDto equipmentInfoById = commonGetHandler.getEquipmentInfoById(param.getNewEquipmentId());
            MaintTask maintTask = new MaintTask();
            maintTask.setId(param.getId());
            maintTask.setEquipmentId(param.getNewEquipmentId());
            maintTask.setName(dtoById.getName().replace(dtoById.getEquipmentName(), "" + equipmentInfoById.getEquipmentName()));
            this.updateById(maintTask);


            equipmentHistoryInfo.setNewEquipmentId(param.getNewEquipmentId());
            equipmentHistoryInfo.setOldEquipmentId(param.getEquipmentId());
            equipmentHistoryInfo.setTaskId(param.getId());
            equipmentHistoryInfo.setChanged(1);
            equipmentHistoryInfoService.updateById(equipmentHistoryInfo);
        } else {
            throw new GlobalServiceException(GlobalResultMessage.of("请先变更设备"));
        }
        return true;
    }

    @Override
    public Boolean updateTaskUids() {
        UserBaseInfo userBaseInfo = UserContextHolder.getContext().getUserBaseInfo();
        executorService.execute(() -> {
            UserContextHolder.getContext().setUserBaseInfo(userBaseInfo);
            LambdaQueryWrapper<MaintTask> wrapper = Wrappers.lambdaQuery();
            wrapper.isNotNull(MaintTask::getProcessInstanceId);
            wrapper.eq(MaintTask::getDeleted, DeletedType.NO.getValue());
            wrapper.notIn(MaintTask::getStatus, Arrays.asList(TaskStatusType.EXCEPTION_CLOSED.getValue(), TaskStatusType.CLOSED.getValue()));
            wrapper.select(MaintTask::getProcessInstanceId, MaintTask::getId, MaintTask::getStatus);
            List<MaintTask> maintTasks = maintTaskMapper.selectList(wrapper);
            if (CollectionUtils.isNotEmpty(maintTasks)) {
                List<MaintTask> updateTasks = new ArrayList<>();
                for (MaintTask maintTask : maintTasks) {
                    ProcessTaskParam processTaskParam = new ProcessTaskParam();
                    processTaskParam.setProcessInstanceId(maintTask.getProcessInstanceId());
                    AuditActivitiServiceResult processTaskUserId = activitiHandler.getProcessTaskUserId(processTaskParam.getProcessInstanceId());
                    if (ArrayUtil.isNotEmpty(processTaskUserId.getProcessUserArray())) {
                        List<String> uids = Arrays.asList(processTaskUserId.getProcessUserArray());
                        if (CollectionUtils.isNotEmpty(uids)) {
                            maintTask.setUids(uids.toArray(new String[uids.size()]));
                            updateTasks.add(maintTask);
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(updateTasks)) {
                    this.updateBatchById(updateTasks);
                }
            }
        });
        return true;
    }

    @Override
    public List<MaintTaskNotifyDto> getNotifyTask(Integer notifyType, Date date) {
        return maintTaskMapper.getNotifyTask(notifyType, date);
    }

    @Override
    public Boolean synExceptionClose(List<String> ids) {
        LambdaQueryWrapper<MaintTask> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(MaintTask::getDeleted, DeletedType.NO.getValue());
        wrapper.in(MaintTask::getId, ids);
        wrapper.select(MaintTask::getProcessInstanceId, MaintTask::getStatus, MaintTask::getId, MaintTask::getSourceType);
        List<MaintTask> maintTasks = maintTaskMapper.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(maintTasks)) {
            List<MaintTask> closeTasks = new ArrayList<>();
            for (MaintTask maintTask : maintTasks) {
                if (maintTask.getStatus() == TaskStatusType.CLOSED.getValue() || maintTask.getStatus() == TaskStatusType.EXCEPTION_CLOSED.getValue()) {
                    log.error("该单已被关闭");
                } else {
                    maintTask.setStatus(TaskStatusType.EXCEPTION_CLOSED.getValue());
                    maintTask.setIsOverTime(true);
                    String activityId = this.getProcessTaskId(maintTask.getProcessInstanceId(), true);
                    if (StringUtils.isBlank(activityId)) {
                        log.error("获取流程任务ID失败,直接关闭工单不处理流程");
                    }else {
                        TaskOptionParam taskOptionParam = new TaskOptionParam();
                        taskOptionParam.setTaskId(activityId);
                        taskOptionParam.setComment("超期自动关闭");
                        activitiHandler.trashTask(taskOptionParam, maintTask.getId(), maintTask.getStatus(),UserContextHolder.getContext().getUserBaseInfo());
                    }
                    maintTask.setCloseReason("超期自动关闭");
                    maintTask.setUids(new String[]{""});
                    closeTasks.add(maintTask);
                    if (maintTask.getSourceType() == TaskSourceType.PLAN.getValue()) {
                        planEquipmentTimeService.createNextTriggerTime(maintTask.getSourceId(), maintTask.getEquipmentId(),UserContextHolder.getContext().getUserBaseInfo());
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(closeTasks)) {
                this.updateBatchById(closeTasks);
                Set<String> collect = closeTasks.stream().map(MaintTask::getId).collect(Collectors.toSet());
                this.update(new UpdateWrapper<MaintTask>().lambda().set(MaintTask::getUids, null).in(MaintTask::getId, collect));
            }
        }

        return true;
    }

    @Override
    public List<MaintTaskNotifyDto> getOverdueTaskIds() {
        return maintTaskMapper.getOverdueTaskIds();
    }

    @Override
    public void transformBatch(List<TaskBatchTransformParam> paramList) {
        LambdaQueryWrapper<MaintTask> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(MaintTask::getDeleted, DeletedType.NO.getValue());
        wrapper.in(MaintTask::getId, paramList.stream().map(TaskBatchTransformParam::getTaskId).collect(Collectors.toList()));
        wrapper.select(MaintTask::getProcessInstanceId, MaintTask::getStatus, MaintTask::getId, MaintTask::getSourceType);
        List<MaintTask> maintTasks = maintTaskMapper.selectList(wrapper);

        // 获取改派人Uid
        RestResponse<List<String>> restResponse = baseServiceClient.getUidsByIds(new String[]{paramList.get(0).getStaffId()});
        String assigneeId;
        if (restResponse.isOk() && CollectionUtils.isNotEmpty(restResponse.getData())) {
            assigneeId = restResponse.getData().get(0);
        } else {
            log.error(restResponse.getMsg());
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("feign_error", null, LocaleContextHolder.getLocale())));
        }

        for (MaintTask maintTask : maintTasks) {
            if (maintTask.getStatus() != TaskStatusType.PROCESSING.getValue()) {
                log.error("工单状态不是执行中");
            } else {
                String activityId = this.getProcessTaskId(maintTask.getProcessInstanceId(), true);
                MultiTransferParam param = new MultiTransferParam();
                param.setAssigneeIds(Lists.newArrayList(assigneeId));
                param.setTaskId(activityId);
                activitiHandler.multiTransferTask(param, maintTask.getId(), maintTask.getStatus());
            }
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public MaintTaskCacheDto offlineCache(String id) {
        MaintTaskCacheDto maintTaskDto = CopyDataUtil.copyObject(this.getDtoById(id), MaintTaskCacheDto.class);
        if (null != maintTaskDto) {
            if (maintTaskDto.getStatus() != TaskStatusType.HANDLE.getValue()) {
                log.error("该单已被执行");
                throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("status_changed", null, LocaleContextHolder.getLocale())));
            }
            LambdaUpdateWrapper<MaintTask> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(MaintTask::getId, id);
            wrapper.set(MaintTask::getOfflineCache, true);
            this.update(wrapper);

            if (StringUtils.isNotBlank(maintTaskDto.getAllStaffIds())) {
                maintTaskDto.setAllStaffNames(buildStaffNames(maintTaskDto.getAllStaffIds().split(StringPool.COMMA)));
            }
            if (null != maintTaskDto.getDispatchHandler() && maintTaskDto.getDispatchHandler().length > 0) {
                String uidStrs = StringUtils.join(maintTaskDto.getDispatchHandler(), StringPool.COMMA);
                Map<String, String> uidMap = commonGetHandler.getUidNameMap(uidStrs);
                List<String> names = new ArrayList<>();
                for (String dispatchHandler : maintTaskDto.getDispatchHandler()) {
                    String name = uidMap.get(dispatchHandler);
                    if (StringUtils.isNotBlank(name)) {
                        names.add(name);
                    }
                }
                if (CollectionUtils.isNotEmpty(names)) {
                    maintTaskDto.setDispatchHandlerNames(StringUtils.join(names, StringPool.COMMA));
                }
            }
            maintTaskDto.setItemDetailDtos(taskItemService.offlineCache(maintTaskDto.getId(), maintTaskDto.getStandardId()));
        }
        return maintTaskDto;
    }

    private void buildOfflineTask(MaintTaskCacheEditDto dto, MaintTask maintTask) {
        maintTask.setHangUp(false);
        maintTask.setEcErrorResult(dto.getEcErrorResult());
        maintTask.setEcErrorDealContent(dto.getEcErrorDealContent());
        if (maintTask.getType() != null && TaskType.isEc(maintTask.getType())
                && (null == maintTask.getEcErrorResult() || TaskEcErrorResultType.DEFAULT.getValue() == maintTask.getEcErrorResult())) {
            maintTask.setEcErrorResult(TaskEcErrorResultType.NODEAL.getValue());
        }
        maintTask.setUrgency(dto.getUrgency());
        maintTask.setBeginMaintTime(dto.getBeginMaintTime());
        maintTask.setEndMaintTime(dto.getEndMaintTime());
        maintTask.setBeginDowntime(dto.getBeginDowntime());
        maintTask.setEndDowntime(dto.getEndDowntime());
        maintTask.setFinishFileIds(dto.getFinishFileIds());
        maintTask.setConfirm(false);
        if (maintTask.getBeginMaintTime() != null && maintTask.getEndMaintTime() != null) {
            maintTask.setMaintTimeCost("" + DateUtil.between(maintTask.getBeginMaintTime(), maintTask.getEndMaintTime(), DateUnit.MINUTE));
            maintTask.setStartTime(dto.getBeginMaintTime());
            Long workingTime = (maintTask.getEndMaintTime().getTime() - maintTask.getBeginMaintTime().getTime()) / 1000 / 60;
            maintTask.setWorkingTime(workingTime);
        }
        if (StringUtils.isBlank(maintTask.getDownTimeCost()) && maintTask.getBeginDowntime() != null && maintTask.getEndDowntime() != null) {
            maintTask.setDownTimeCost("" + DateUtil.between(maintTask.getBeginDowntime(), maintTask.getEndDowntime(), DateUnit.MINUTE));
        }
    }

    private List<String> buildEvaluateHandler(MaintTask maintTask) {
        List<String> uids = new ArrayList<>();
        RestResponse<List<String>> restResponse = porosSecGrantClient.getUidsByRoleCode(evaluateRoleCode);
        if (restResponse.isOk()) {
            uids = restResponse.getData();
        } else {
            log.error("获取中台工单评价角色人员失败->" + JSONObject.toJSON(restResponse));
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("feign_error", null, LocaleContextHolder.getLocale())));
        }
        if (CollectionUtils.isEmpty(uids)) {
            log.error("未配置工单评价员，不能转到评价节点");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("check_error", null, LocaleContextHolder.getLocale())));
        } else {
            maintTask.setEvaluateHandler(uids.toArray(new String[uids.size()]));
        }
        return uids;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean offlineCacheSubmit(MaintTaskCacheEditDto dto) {
        MaintTask maintTask = (MaintTask) this.getById(dto.getId());
        if (null != maintTask && (maintTask.getStatus() == TaskStatusType.PROCESSING.getValue() || maintTask.getStatus() == TaskStatusType.HANDLE.getValue())) {
            String activityId = this.getProcessTaskId(maintTask.getProcessInstanceId(), true);
            if (StringUtils.isBlank(activityId)) {
                throw new GlobalServiceException(GlobalResultMessage.of("对应工作流未找到"));
            }
            if (dto.getStatus() == TaskStatusType.CHECK_ACCEPT.getValue()) {
                if (maintTask.getStatus() == TaskStatusType.HANDLE.getValue()) {
                    //先把工作流改为执行中
                    TaskCompleteParam taskCompleteParam = new TaskCompleteParam();
                    taskCompleteParam.setTaskId(activityId);
                    Map<String, Object> variables = new HashMap<>();
                    //从待执行跳过安全确认
                    variables.put("confirm", false);
                    variables.put("transfer", false);
                    maintTask.setConfirm(false);
                    variables.put("assigns", Lists.newArrayList(maintTask.getHandler()));
                    taskCompleteParam.setVariables(variables);
                    maintTask.setStatus(TaskStatusType.PROCESSING.getValue());
                    activitiHandler.submitActiviti(taskCompleteParam, maintTask.getId(), maintTask.getStatus());

                    LambdaUpdateWrapper<MaintTask> wrapper = new LambdaUpdateWrapper<>();
                    wrapper.eq(MaintTask::getId, maintTask.getId());
                    wrapper.set(MaintTask::getStatus, TaskStatusType.PROCESSING.getValue());
                    wrapper.set(MaintTask::getConfirm, false);
                    this.update(wrapper);
                }
                if (maintTask.getStatus() == TaskStatusType.PROCESSING.getValue()) {
                    activityId = this.getProcessTaskId(maintTask.getProcessInstanceId(), true);
                    if (StringUtils.isBlank(activityId)) {
                        throw new GlobalServiceException(GlobalResultMessage.of("对应工作流未找到"));
                    }
                    if (CollectionUtils.isNotEmpty(dto.getItemDetailDtos())) {
                        //更新作业项,辅助人员
                        Boolean ecErrorNum = taskItemService.saveOrUpdateList(dto.getItemDetailDtos(), maintTask.getId());
                        //maintTask.setEcErrorNum(ecErrorNum);
                    }
                    assistPersonService.offlineCacheSubmit(dto.getAssistPeopleList(), maintTask.getId(), dto.getBeginMaintTime(), dto.getEndMaintTime());
                    this.buildOfflineTask(dto, maintTask);
                    TaskCompleteParam taskCompleteParam = new TaskCompleteParam();
                    taskCompleteParam.setTaskId(activityId);
                    Map<String, Object> variables = new HashMap<>();
                    List<String> uids = this.buildEvaluateHandler(maintTask);
                    variables.put("assigns", uids);
                    variables.put("transfer", false);
                    taskCompleteParam.setVariables(variables);
                    maintTask.setStatus(TaskStatusType.CHECK_ACCEPT.getValue());
                    activitiHandler.submitActiviti(taskCompleteParam, maintTask.getId(), maintTask.getStatus());

                    maintTaskMapper.updateById(maintTask);
                    //更新设备状态
                    if (StringUtils.isNotBlank(dto.getEquipmentId())
                            && null != dto.getEquipmentRunningStatus()) {
                        this.updateEquipmentRunningStatus(dto.getEquipmentId(), dto.getEndMaintTime(), dto.getEquipmentRunningStatus());
                    }
                    planEquipmentTimeService.createNextTriggerTime(maintTask.getSourceId(), maintTask.getEquipmentId());
                }
            } else if (dto.getStatus() == TaskStatusType.EXCEPTION_CLOSED.getValue()) {
                TaskOptionParam taskOptionParam = new TaskOptionParam();
                taskOptionParam.setTaskId(activityId);
                taskOptionParam.setComment(dto.getCloseReason());
                maintTask.setStatus(TaskStatusType.EXCEPTION_CLOSED.getValue());
                activitiHandler.trashTask(taskOptionParam, maintTask.getId(), maintTask.getStatus());
                LambdaUpdateWrapper<MaintTask> wrapper = new LambdaUpdateWrapper<>();
                wrapper.eq(MaintTask::getId, maintTask.getId());
                wrapper.set(MaintTask::getStatus, TaskStatusType.EXCEPTION_CLOSED.getValue());
                wrapper.set(MaintTask::getCloseReason, dto.getCloseReason());
                this.update(wrapper);
                planEquipmentTimeService.createNextTriggerTime(maintTask.getSourceId(), maintTask.getEquipmentId());
            }
        }

        return true;
    }

    @Override
    public Boolean offlineCacheSubmitCheck(String id) {
        MaintTask task = (MaintTask) this.getById(id);
        if (null != task && (task.getStatus() == TaskStatusType.PROCESSING.getValue() || task.getStatus() == TaskStatusType.HANDLE.getValue())) {
            return true;
        }
        return false;
    }

    public void reopenTask(MaintTaskReopenDto request) {
        activitiHandler.reopenTask(request);
    }
}
