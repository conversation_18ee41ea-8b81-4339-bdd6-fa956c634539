package cn.getech.ehm.task.enmu;

import cn.getech.ehm.common.dto.EnumListDto;

import java.util.ArrayList;
import java.util.List;

/**
 * 超期处理方式枚举
 * <AUTHOR>
 * @date 2020/3/3
 */
public enum OverdueHandlingType {
    NON(1, "不处理"),
    EXCEPTION_CLOSE(2, "异常关闭");


    OverdueHandlingType(int value, String name) {
        this.value = value;
        this.name = name;
    }

    private int value;

    private String name;

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static List<EnumListDto> getList(){
        List<EnumListDto> enumListDtos = new ArrayList<>();
        for(OverdueHandlingType overdueHandlingType : OverdueHandlingType.values()){
            EnumListDto enumListDto = new EnumListDto();
            enumListDto.setValue(overdueHandlingType.value);
            enumListDto.setName(overdueHandlingType.name);
            enumListDtos.add(enumListDto);
        }
        return enumListDtos;
    }
}

