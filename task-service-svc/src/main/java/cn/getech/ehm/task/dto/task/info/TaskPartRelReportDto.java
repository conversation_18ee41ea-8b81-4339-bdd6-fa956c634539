package cn.getech.ehm.task.dto.task.info;

import cn.getech.poros.framework.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 维护工单主表 返回数据模型
 *
 * <AUTHOR>
 * @date 2021-1-14
 */
@Data
@ApiModel(value = "TaskPartRelReportDto", description = "备件耗用成本统计返回数据模型")
public class TaskPartRelReportDto {

    @ApiModelProperty(value = "维护设备id")
    private String equipmentId;

    @ApiModelProperty(value = "维护设备code")
    @Excel(name = "设备编号", cellType = Excel.ColumnType.STRING)
    private String equipmentCode;

    @ApiModelProperty(value = "维护设备名称")
    @Excel(name = "设备名称", cellType = Excel.ColumnType.STRING)
    private String equipmentName;

    @ApiModelProperty(value = "维护设备类型")
    @Excel(name = "设备类型", cellType = Excel.ColumnType.STRING)
    private String equipmentCategory;

    @ApiModelProperty(value = "维护设备位置")
    @Excel(name = "设备位置", cellType = Excel.ColumnType.STRING)
    private String equipmentLocation;

    @ApiModelProperty(value = "备件耗用数量")
    @Excel(name = "备件耗用数量", cellType = Excel.ColumnType.STRING)
    private int totalActualQty;

    @ApiModelProperty(value = "备件耗用成本（元）")
    @Excel(name = "备件耗用成本（元）", cellType = Excel.ColumnType.STRING)
    private Double totalPrice;

    @ApiModelProperty(value = "备件code")
    private String partCode;

    @ApiModelProperty(value = "备件名称")
    private String partName;

    @ApiModelProperty(value = "备件类型")
    private String partCategory;

}