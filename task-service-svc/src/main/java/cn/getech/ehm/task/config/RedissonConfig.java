package cn.getech.ehm.task.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.ClusterServersConfig;
import org.redisson.config.Config;
import org.redisson.config.SentinelServersConfig;
import org.redisson.config.SingleServerConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;

@Configuration
@EnableConfigurationProperties({RedisProperties.class})
public class RedissonConfig {

    @Autowired
    private RedisProperties redisProperties;

//    @Bean(value = "redissonClient", destroyMethod = "shutdown")
//    public RedissonClient redissonClient() throws Exception{
//
//        Config config = new Config();
//        config.useSingleServer().setAddress(String.format("redis://%s:%s", this.host, this.port));
//        if (!this.password.isEmpty()) {
//            config.useSingleServer().setPassword(this.password);
//        }
//        config.useSingleServer().setDatabase(this.database);
//
//        StringCodec codec = new StringCodec();
//        config.setCodec(codec);
//        return Redisson.create(config);
//    }

    @Bean(value = "redissonClient", destroyMethod = "shutdown")
    public RedissonClient redissonClient() throws Exception {
        Config config = null;
        Method clusterMethod = ReflectionUtils.findMethod(RedisProperties.class, "getCluster");
        Method timeoutMethod = ReflectionUtils.findMethod(RedisProperties.class, "getTimeout");
        Object timeoutValue = ReflectionUtils.invokeMethod(timeoutMethod, this.redisProperties);
        int timeout;
        Method nodesMethod;
        if (null == timeoutValue) {
            timeout = 10000;
        } else if (!(timeoutValue instanceof Integer)) {
            nodesMethod = ReflectionUtils.findMethod(timeoutValue.getClass(), "toMillis");
            timeout = ((Long) ReflectionUtils.invokeMethod(nodesMethod, timeoutValue)).intValue();
        } else {
            timeout = (Integer) timeoutValue;
        }
        Method method;
        if (this.redisProperties.getSentinel() != null) {
            nodesMethod = ReflectionUtils.findMethod(RedisProperties.Sentinel.class, "getNodes");
            Object nodesValue = ReflectionUtils.invokeMethod(nodesMethod, this.redisProperties.getSentinel());
            String[] nodes;
            if (nodesValue instanceof String) {
                nodes = this.convert(Arrays.asList(((String) nodesValue).split(",")));
            } else {
                nodes = this.convert((List) nodesValue);
            }

            config = new Config();
            ((SentinelServersConfig) config.useSentinelServers().setMasterName(this.redisProperties.getSentinel().getMaster()).addSentinelAddress(nodes).setDatabase(this.redisProperties.getDatabase()).setConnectTimeout(timeout)).setPassword(this.redisProperties.getPassword());
        } else if (clusterMethod != null && ReflectionUtils.invokeMethod(clusterMethod, this.redisProperties) != null) {
            Object clusterObject = ReflectionUtils.invokeMethod(clusterMethod, this.redisProperties);
            method = ReflectionUtils.findMethod(clusterObject.getClass(), "getNodes");
            List<String> nodesObject = (List) ReflectionUtils.invokeMethod(method, clusterObject);
            String[] nodes = this.convert(nodesObject);
            config = new Config();
            ((ClusterServersConfig) config.useClusterServers().addNodeAddress(nodes).setConnectTimeout(timeout)).setPassword(this.redisProperties.getPassword());
        } else {
            config = new Config();
            String prefix = "redis://";
            method = ReflectionUtils.findMethod(RedisProperties.class, "isSsl");
            if (method != null && (Boolean) ReflectionUtils.invokeMethod(method, this.redisProperties)) {
                prefix = "rediss://";
            }

            ((SingleServerConfig) config.useSingleServer().setAddress(prefix + this.redisProperties.getHost() + ":" + this.redisProperties.getPort()).setConnectTimeout(timeout)).setDatabase(this.redisProperties.getDatabase()).setPassword(this.redisProperties.getPassword());
        }
        return Redisson.create(config);
    }

    private String[] convert(List<String> nodesObject) {
        List<String> nodes = new ArrayList(nodesObject.size());
        Iterator var3 = nodesObject.iterator();

        while (true) {
            while (var3.hasNext()) {
                String node = (String) var3.next();
                if (!node.startsWith("redis://") && !node.startsWith("rediss://")) {
                    nodes.add("redis://" + node);
                } else {
                    nodes.add(node);
                }
            }

            return (String[]) nodes.toArray(new String[nodes.size()]);
        }
    }


}
