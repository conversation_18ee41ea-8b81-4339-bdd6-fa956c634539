package cn.getech.ehm.task.dto.defect;

import cn.getech.poros.framework.common.param.PageParam;
import cn.hutool.core.date.DateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <pre>
 * 缺陷信息 分页查询参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-02-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DefectInfo查询", description = "缺陷信息查询参数")
public class DefectInfoQueryParam extends PageParam {

    @ApiModelProperty(value = "缺陷内容")
    private String defectContent;

    @ApiModelProperty(value = "缺陷名")
    private String defectName;

    @ApiModelProperty(value = "搜索开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date searchStartDate;

    @ApiModelProperty(value = "搜索截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date searchEndDate;

//    @ApiModelProperty(value = "设备号/名称")
//    private String equipmentName;

    @ApiModelProperty("设备位置Ids")
    private List<String> locationIds;

    @ApiModelProperty(value = "缺陷状态")
    private String defectStatus;

    private Integer[] defectStatusArray;

    @ApiModelProperty(value = "缺陷种类")
    private String defectType;

    @ApiModelProperty(value = "验收状态")
    private String checkStatus;

    @ApiModelProperty(value = "是否超时")
    private String hasOverTime;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "处理人")
    private String dealPerson;

    @ApiModelProperty(value = "搜索开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date dealTimeStartDate;

    @ApiModelProperty(value = "搜索截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date dealTimeEndDate;

}
