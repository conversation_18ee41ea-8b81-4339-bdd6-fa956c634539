package cn.getech.ehm.task.dto.task.info;

import cn.getech.ehm.base.dto.AttachmentClientDto;
import cn.getech.ehm.task.dto.task.assist.MaintTaskAssistListDto;
import cn.getech.ehm.task.dto.task.item.TaskItemDetailDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * 维护工单离线返回数据模型
 */
@Data
@ApiModel(value = "MaintTaskCacheDto", description = "维护工单离线返回数据模型")
public class MaintTaskCacheDto {

    @ApiModelProperty(value = "工单id")
    private String id;

    @ApiModelProperty(value = "工单编号")
    private String code;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "维保类型")
    private String jobType;

    @ApiModelProperty(value = "维保类型名称")
    private String jobTypeName;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "影响程度")
    private String influence;

    @ApiModelProperty(value = "影响程度名称")
    private String influenceName;

    @ApiModelProperty(value = "状态名称")
    private String statusName;

    @ApiModelProperty(value = "工单类型(1故障2维保3缺陷单)")
    private Integer type;

    @ApiModelProperty(value = "工单类型名称")
    private String typeName;

    @ApiModelProperty(value = "工单来源")
    private Integer sourceType;

    @ApiModelProperty(value = "计划单/故障单/缺陷单id")
    private String sourceId;

    @ApiModelProperty(value = "计划单名称")
    private String sourceName;

    @ApiModelProperty(value = "工单来源名称")
    private String sourceTypeName;

    @ApiModelProperty(value = "紧急程度/优先程度")
    private String urgency;

    @ApiModelProperty(value = "紧急程度名称")
    private String urgencyName;

    @ApiModelProperty(value = "专业/工单类别")
    private String major;

    @ApiModelProperty(value = "专业/工单类别")
    private String majorName;

    @ApiModelProperty(value = "设备编码")
    private String equipmentCode;

    @ApiModelProperty(value = "维护设备id")
    private String equipmentId;

    @ApiModelProperty(value = "维护设备名称")
    private String equipmentName;

    @ApiModelProperty(value = "维护设备位置")
    private String equipmentLocation;

    @ApiModelProperty(value = "维护设备类型Id")
    private String equipmentCategoryId;

    @ApiModelProperty(value = "维护设备类型")
    private String equipmentCategory;

    @ApiModelProperty(value = "维护设备状态")
    private Integer equipmentRunningStatus;

    @ApiModelProperty(value = "设备路径")
    private String equipmentParentAllName;

    @ApiModelProperty(value = "故障现象/作业标准")
    private String content;

    @ApiModelProperty(value = "计划维护时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date planMaintTime;

    @ApiModelProperty(value = "维护人员id集合")
    private String[] staffIds;

    @ApiModelProperty(value = "维护人员名称集合")
    private String staffNames;

    @ApiModelProperty(value = "维护人员集合")
    private List<NameDetailDto> staffDtos;

    @ApiModelProperty(value = "维护班组id集合")
    private String[] teamIds;

    @ApiModelProperty(value = "维护班组名称集合")
    private String teamNames;

    @ApiModelProperty(value = "维护人员集合")
    private List<NameDetailDto> teamDtos;

    @ApiModelProperty(value = "人员和班组组合人员ids")
    private String allStaffIds;

    @ApiModelProperty(value = "人员和班组组合人员名称")
    private String allStaffNames;

    @ApiModelProperty(value = "派单人")
    private String[] dispatchHandler;

    @ApiModelProperty(value = "派单人")
    private String dispatchHandlerNames;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "首张图片id")
    private String picId;

    @ApiModelProperty(value = "图片url")
    private String picUrl;

    @ApiModelProperty("工单截止时间")
    private Date taskDeadlineDate;

    @ApiModelProperty("作业标准id")
    private String standardId;

    @ApiModelProperty(value = "工单作业项详情")
    List<TaskItemDetailDto> itemDetailDtos;

    @ApiModelProperty("完工附件")
    private String[] finishFileIds;

    @ApiModelProperty("完工附件")
    private List<AttachmentClientDto> finishFileList;

    @ApiModelProperty("辅助人员信息")
    private List<MaintTaskAssistListDto>  assistPeopleList;

}