package cn.getech.ehm.task.dto.plan;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 维保对象发单日期
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "TriggerTimeDto", description = "维保对象发单日期")
public class TriggerTimeDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "计划id")
    private String planId;

    @ApiModelProperty(value = "维保对象时间id")
    private String planEquipmentTimeId;

    @ApiModelProperty(value = "设备id")
    private String equipmentId;

    @ApiModelProperty(value = "发单日期")
    private Date triggerTime;

    @ApiModelProperty(value = "计划维护日期")
    private Date planMaintTime;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "计划维护日期(天)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date planDay;

    @ApiModelProperty(value = "计划维护日期(小时:分钟)")
    @JsonFormat(pattern = "HH:mm", timezone = "GMT+8")
    private Date planTime;

    @ApiModelProperty(value = "维护人员id集合")
    private String[] maintainerIds;

    @ApiModelProperty(value = "维护班组id集合")
    private String[] teamIds;

    @ApiModelProperty(value = "人员指派策略 0 根据设备 1 人工配置 2 自由扫码")
    private Integer personStrategy;

    @ApiModelProperty(value = "租户id")
    private String tenantId;

    @ApiModelProperty("节假日")
    private Integer[] festivalType;
}