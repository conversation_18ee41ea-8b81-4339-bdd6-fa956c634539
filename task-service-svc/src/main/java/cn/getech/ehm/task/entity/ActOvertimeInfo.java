package cn.getech.ehm.task.entity;

import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <p>
 * 节点时限记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("act_overtime_info")
public class ActOvertimeInfo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @TableField("deleted")
    private Integer deleted;

    @TableField("tenant_id")
    private String tenantId;

    /**
     * 流程id
     */
    @TableField("proc_ins_id")
    private String procInsId;

    /**
     * 任务id
     */
    @TableField("task_id")
    private String taskId;

    /**
     * 任务名
     */
    @TableField("task_name")
    private String taskName;

    /**
     * 任务开始时间
     */
    @TableField("task_begin_time")
    private Date taskBeginTime;

    /**
     * 任务逾期时间
     */
    @TableField("task_end_time")
    private Date taskEndTime;

    private Date taskRealTime;

    /**
     * 流程key
     */
    @TableField("proc_def_key")
    private String procDefKey;

    /**
     * 流程开始时间
     */
    @TableField("proc_begin_time")
    private Date procBeginTime;

    /**
     * 流程逾期时间
     */
    @TableField("proc_end_time")
    private Date procEndTime;


}
