package cn.getech.ehm.task.entity;

import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 工单辅助人员表
 *
 * <AUTHOR>
 * @since 2022-06-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("maint_task_assist_person")
public class MaintTaskAssistPerson extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "工单id")
    @TableField("task_id")
    private String taskId;

    @ApiModelProperty(value = "辅助人员id")
    @TableField("user_id")
    private String userId;

    @ApiModelProperty(value = "辅助人员名称")
    @TableField("user_name")
    private String userName;

    @ApiModelProperty(value = "参与说明")
    @TableField("description")
    private String description;

    @ApiModelProperty(value = "计时方式")
    @TableField("time_mode")
    private Integer timeMode;

    @ApiModelProperty(value = "参与开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "start_time", updateStrategy = FieldStrategy.IGNORED)
    private Date startTime;

    @ApiModelProperty(value = "参与结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "end_time", updateStrategy = FieldStrategy.IGNORED)
    private Date endTime;

    @ApiModelProperty(value = "工时/h")
    @TableField(value = "work_hours", updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal workHours;

    @TableField("tenant_id")
    private String tenantId;
}
