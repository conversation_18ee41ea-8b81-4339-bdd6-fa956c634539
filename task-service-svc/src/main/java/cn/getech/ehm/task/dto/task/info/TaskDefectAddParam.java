package cn.getech.ehm.task.dto.task.info;

import cn.getech.poros.framework.common.param.ApiParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

/**
 * 工单转缺陷新增参数
 *
 * <AUTHOR>
 * @date 2022-02-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "TaskDefectAddParam", description = "工单转缺陷新增参数")
public class TaskDefectAddParam extends ApiParam {

    @ApiModelProperty(value = "设备id",required = true)
    @NotEmpty(message = "必须选择设备")
    private String equipmentId;
    @ApiModelProperty(value = "缺陷名称",required = true)
    @NotEmpty(message = "缺陷名称必填")
    private String defectName;
    @ApiModelProperty(value = "缺陷内容",required = true)
    @NotEmpty(message = "缺陷内容必填")
    private String defectContent;
    @ApiModelProperty(value = "影响描述")
    private String affectContent;
    @ApiModelProperty(value = "缺陷种类",required = true)
    private String defectType;
    @ApiModelProperty(value = "专业类别",required = true)
    private String major;
    @ApiModelProperty(value = "现场图片视频")
    private String[] liveMediaIds;

    @ApiModelProperty(value = "工作流任务ID")
    private String activityId;

    @ApiModelProperty(value = "工单ID")
    private String taskId;

    @ApiModelProperty(value = "意见")
    private String remark;

    @ApiModelProperty(value = "来源工单id")
    private String sourceTaskId;

    @ApiModelProperty(value = "截止日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date endTime;

    private String defectReason;
}