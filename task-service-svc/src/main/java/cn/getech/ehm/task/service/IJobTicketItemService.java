package cn.getech.ehm.task.service;

import cn.getech.ehm.task.dto.ticket.JobTicketItemDto;
import cn.getech.ehm.task.entity.JobTicketItem;
import cn.getech.poros.framework.common.service.IBaseService;

import java.util.List;

/**
 * 作业票内容service
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
public interface IJobTicketItemService extends IBaseService<JobTicketItem> {

    /**
     * 新增作业票内容
     * @param addDto
     * @return
     */
    String saveByParam(JobTicketItemDto addDto);

    /**
     * 修改作业票内容
     * @param editDto
     * @return
     */
    Boolean updateByParam(JobTicketItemDto editDto);

    /**
     * 批量新增
     * @param itemDtos
     * @return
     */
    Boolean saveBatchByParams(List<JobTicketItemDto> itemDtos, String ticketId);

    /**
     * 删除作业票内容
     * @param id
     * @return
     */
    Boolean deleteById(String id);

    /**
     * 根据ticketId获取内容集合
     * @param ticketId
     * @return
     */
    List<JobTicketItemDto> getListByTicketId(String ticketId);
}