package cn.getech.ehm.task.dto.task.info;

import cn.getech.poros.framework.common.param.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;
import java.util.Map;

/**
 * 备件耗用成本统计 分页查询参数对象
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "备件耗用成本统计查询", description = "备件耗用成本统计查询参数")
public class TaskPartRelQueryParam extends PageParam {

    @ApiModelProperty(value = "查询关键字(设备编号/设备名称)")
    private String keyword;

    @ApiModelProperty(value = "统计月份开始")
    @JsonFormat(pattern = "yyyy-MM", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM")
    private Date beginTime;

    @ApiModelProperty(value = "统计月份结束")
    @JsonFormat(pattern = "yyyy-MM", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM")
    private Date endTime;


    @ApiModelProperty(value = "")
    private String innerEquipmentCode;

    @ApiModelProperty(value = "")
    private String innerEquipmentName;

    @ApiModelProperty(value = "维护设备类型(逗号分开)")
    private String innerEquipmentCategory;

    @ApiModelProperty(hidden = true)
    private String[] equipmentCategory;

    @ApiModelProperty(value = "维护设备位置(逗号分开)")
    private String innerEquipmentLocation;

    @ApiModelProperty(hidden = true)
    private String[] equipmentLocation;

    @ApiModelProperty(value = "开始备件耗用数量")
    private Integer innerStartTotalActualQty;

    @ApiModelProperty(value = "结束备件耗用数量")
    private Integer innerEndTotalActualQty;

    @ApiModelProperty(value = "开始备件耗用成本（元）")
    private Double innerStartTotalPrice;

    @ApiModelProperty(value = "结束备件耗用成本（元）")
    private Double innerEndTotalPrice;

    @ApiModelProperty(value = "排序[属性:排序(ASC升序DESC降序)]", example = "createTime:ASC")
    private Map<String, String> sortPros;

    @ApiModelProperty(value = "排序sql",hidden = true)
    private String sortValue;
}
