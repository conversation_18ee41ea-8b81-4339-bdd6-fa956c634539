package cn.getech.ehm.task.listener;

import cn.getech.ehm.task.config.RabbitMQConfig;
import cn.getech.ehm.task.dto.task.info.MaintTaskPlanAddDto;
import cn.getech.ehm.task.service.IMaintTaskService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class MaintPlanListener {
    @Autowired
    IMaintTaskService maintTaskService;


    @RabbitListener(queues = RabbitMQConfig.DIRECT_QUEUE)
    public void handleTask(String message) {
        log.info("MQ:维保计划释放工单消费请求：{}", message);
        MaintTaskPlanAddDto newDto = JSON.parseObject(message, MaintTaskPlanAddDto.class);
        maintTaskService.savePlanTask(newDto, newDto.getUserBaseInfo());
        log.info("MQ:维保计划释放工单完毕");
    }
}
