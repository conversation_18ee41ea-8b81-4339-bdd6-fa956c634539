package cn.getech.ehm.task.dto.job;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * 作业标准导入res
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "JobStandardExcelResDto", description = "作业标准导入res")
public class JobStandardExcelResDto {

    @ApiModelProperty(value = "作业项目集合")
    private List<JobStandardItemDto> itemDtos;

    @ApiModelProperty(value = "作业项目统计")
    private ItemCountDto countDto;
}