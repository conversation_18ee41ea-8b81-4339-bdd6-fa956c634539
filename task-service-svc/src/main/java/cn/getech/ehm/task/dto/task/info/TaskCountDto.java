package cn.getech.ehm.task.dto.task.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 工单统计
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "TaskCountDto", description = "工单统计")
public class TaskCountDto {

    @ApiModelProperty(value = "状态值")
    private Integer status;

    @ApiModelProperty(value = "状态名称")
    private String statusName;

    @ApiModelProperty(value = "状态数量")
    private Integer count;
}