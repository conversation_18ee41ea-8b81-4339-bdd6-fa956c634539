package cn.getech.ehm.task.service.impl;

import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.equipment.client.EquipmentClient;
import cn.getech.ehm.equipment.dto.EquipmentInfoSearchDto;
import cn.getech.ehm.task.dto.historyInfo.*;
import cn.getech.ehm.task.entity.EquipmentHistoryInfo;
import cn.getech.ehm.task.mapper.EquipmentHistoryInfoMapper;
import cn.getech.ehm.task.service.IEquipmentHistoryInfoService;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.api.ResultCode;
import cn.getech.poros.framework.common.bean.BaseEntity;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;


/**
 * <pre>
 *  服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2022-12-06
 */
@Slf4j
@Service
public class EquipmentHistoryInfoServiceImpl extends BaseServiceImpl<EquipmentHistoryInfoMapper, EquipmentHistoryInfo> implements IEquipmentHistoryInfoService {

    @Autowired
    private EquipmentHistoryInfoParamMapper equipmentHistoryInfoParamMapper;
    @Autowired
    private EquipmentHistoryInfoMapper equipmentHistoryInfoMapper;
    @Autowired
    private EquipmentClient equipmentClient;

    @Override
    public PageResult<EquipmentHistoryInfoDto> pageDto(EquipmentHistoryInfoQueryParam equipmentHistoryInfoQueryParam) {
        Wrapper<EquipmentHistoryInfo> wrapper = getPageSearchWrapper(equipmentHistoryInfoQueryParam);
        PageResult<EquipmentHistoryInfoDto> result = equipmentHistoryInfoParamMapper.pageEntity2Dto(page(equipmentHistoryInfoQueryParam, wrapper));

        return Optional.ofNullable(result).orElse(new PageResult<>());
    }

    @Override
    public PageResult<EquipmentHistoryInfoDto> listRecord(EquipmentHistoryInfoQueryParam equipmentHistoryInfoQueryParam) {
        Page<EquipmentHistoryInfoDto> page = new Page<>(equipmentHistoryInfoQueryParam.getPageNo(), equipmentHistoryInfoQueryParam.getLimit());

        if (ObjectUtils.isNull(equipmentHistoryInfoQueryParam.getChanged())) {
            equipmentHistoryInfoQueryParam.setChanged(0);
        }
        IPage<EquipmentHistoryInfoDto> dtoPage = equipmentHistoryInfoMapper.pageList(page, equipmentHistoryInfoQueryParam);
        List<EquipmentHistoryInfoDto> records = dtoPage.getRecords();

        return Optional.ofNullable(PageResult.<EquipmentHistoryInfoDto>builder()
                .records(records)
                .total(dtoPage.getTotal())
                .build())
                .orElse(new PageResult<>());
    }

    @Override
    public PageResult<EquipmentHistoryInfoDto> list(EquipmentHistoryInfoQueryParam equipmentHistoryInfoQueryParam) {
        List<String> equipmentIds = Lists.newArrayList();
        EquipmentInfoSearchDto equipmentInfoSearchDto = new EquipmentInfoSearchDto();
        equipmentInfoSearchDto.setCategoryIds(equipmentHistoryInfoQueryParam.getEquipmentCategoryIds());
        equipmentInfoSearchDto.setKeyword(equipmentHistoryInfoQueryParam.getEquipmentName());
        equipmentInfoSearchDto.setEquipmentCode(equipmentHistoryInfoQueryParam.getEquipmentCode());
        RestResponse<List<String>> equipmentIdsByEquipmentInfo = equipmentClient.getEquipmentIdsByParam(equipmentInfoSearchDto);
        if (!equipmentIdsByEquipmentInfo.isOk() || CollectionUtils.isEmpty(equipmentIdsByEquipmentInfo.getData())) {
            throw new GlobalServiceException(GlobalResultMessage.of("查找不到相关设备"));
        }
        equipmentIds = equipmentIdsByEquipmentInfo.getData();
        if (CollectionUtils.isNotEmpty(equipmentIds)){
            equipmentHistoryInfoQueryParam.setEquipmentIds(equipmentIds);
            Page<EquipmentHistoryInfoDto> page = new Page<>(equipmentHistoryInfoQueryParam.getPageNo(), equipmentHistoryInfoQueryParam.getLimit());

            IPage<EquipmentHistoryInfoDto> dtoPage = equipmentHistoryInfoMapper.pageListByTaskId(page, equipmentHistoryInfoQueryParam);
            List<EquipmentHistoryInfoDto> records = dtoPage.getRecords();
            return Optional.ofNullable(PageResult.<EquipmentHistoryInfoDto>builder()
                            .records(records)
                            .total(dtoPage.getTotal())
                            .build())
                    .orElse(new PageResult<>());
        }else {
            return Optional.ofNullable(PageResult.<EquipmentHistoryInfoDto>builder()
                            .total(0)
                            .build())
                    .orElse(new PageResult<>());
        }



    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveByParam(EquipmentHistoryInfoAddParam equipmentHistoryInfoAddParam) {
        EquipmentHistoryInfo equipmentHistoryInfo = equipmentHistoryInfoParamMapper.addParam2Entity(equipmentHistoryInfoAddParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,equipmentHistoryInfo);
        return save(equipmentHistoryInfo);
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean updateByParam(EquipmentHistoryInfoEditParam equipmentHistoryInfoEditParam) {
        EquipmentHistoryInfo equipmentHistoryInfo = equipmentHistoryInfoParamMapper.editParam2Entity(equipmentHistoryInfoEditParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,equipmentHistoryInfo);
        return updateById(equipmentHistoryInfo);
    }


    @Override
    public EquipmentHistoryInfoDto getDtoById(Long id) {
        return equipmentHistoryInfoParamMapper.entity2Dto((EquipmentHistoryInfo) this.getById(id));
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveDtoBatch(List<EquipmentHistoryInfoDto> rows) {
        return saveBatch(equipmentHistoryInfoParamMapper.dtoList2Entity(rows));
    }

    private Wrapper<EquipmentHistoryInfo> getPageSearchWrapper(EquipmentHistoryInfoQueryParam equipmentHistoryInfoQueryParam) {
        LambdaQueryWrapper<EquipmentHistoryInfo> wrapper = Wrappers.<EquipmentHistoryInfo>lambdaQuery();
        if(BaseEntity.class.isAssignableFrom(EquipmentHistoryInfo.class)){
            wrapper.orderByDesc(EquipmentHistoryInfo::getUpdateTime,EquipmentHistoryInfo::getCreateTime);
        }
        return wrapper;
    }
}
