package cn.getech.ehm.task.dto.taskConfig;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;

import java.util.Date;

/**
 * <pre>
 * 工单配置信息 新增参数
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-04-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MaintTaskConfig新增", description = "工单配置信息新增参数")
public class MaintTaskConfigAddParam extends ApiParam {

    @ApiModelProperty(value = "")
    private String createBy;
    @ApiModelProperty(value = "")
    private String updateBy;
    @ApiModelProperty(value = "")
    private Date createTime;
    @ApiModelProperty(value = "")
    private Date updateTime;
    @ApiModelProperty(value = "")
    private String remark;

    @ApiModelProperty("工单类型")
    private Integer taskType;;

    @ApiModelProperty("配置类型")
    private Integer configType;

    @ApiModelProperty("配置内容")
    private String configContent;
}