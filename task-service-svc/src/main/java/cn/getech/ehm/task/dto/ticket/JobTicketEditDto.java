package cn.getech.ehm.task.dto.ticket;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 作业票修改dto
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "JobTicketEditDto", description = "作业票修改dto")
public class JobTicketEditDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "是否安全确认")
    private Boolean confirm;

    @ApiModelProperty(value = "是否显示工况/风险信息")
    private Boolean showInformation;

    @ApiModelProperty(value = "是否显示作业票类型")
    private Boolean showTicketType;

    @ApiModelProperty(value = "是否开启故障单/缺陷单派单人工审批")
    private Boolean repairTaskAudit;
}