package cn.getech.ehm.task.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class PermissionMenu {
    @JsonProperty("buttonMaks")
    private List<String> buttonMaks;
    @JsonProperty("menuIdSet")
    private List<String> menuIdSet;
    @JsonProperty("menus")
    private List<MenusDTO> menus;
    @JsonProperty("systemId")
    private String systemId;
    @JsonProperty("systemName")
    private String systemName;

    @NoArgsConstructor
    @Data
    public static class MenusDTO {
        @JsonProperty("checked")
        private Boolean checked;
        @JsonProperty("children")
        private List<?> children;
        @JsonProperty("componentPath")
        private String componentPath;
        @JsonProperty("createTime")
        private String createTime;
        @JsonProperty("customIcon")
        private String customIcon;
        @JsonProperty("englishName")
        private String englishName;
        @JsonProperty("hidden")
        private Boolean hidden;
        @JsonProperty("icon")
        private String icon;
        @JsonProperty("id")
        private String id;
        @JsonProperty("keepAlive")
        private Integer keepAlive;
        @JsonProperty("location")
        private String location;
        @JsonProperty("mask")
        private String mask;
        @JsonProperty("name")
        private String name;
        @JsonProperty("newTabOpen")
        private Integer newTabOpen;
        @JsonProperty("orderNum")
        private Integer orderNum;
        @JsonProperty("parentId")
        private String parentId;
        @JsonProperty("perMark")
        private String perMark;
        @JsonProperty("refApiUrlList")
        private List<?> refApiUrlList;
        @JsonProperty("resourceType")
        private String resourceType;
        @JsonProperty("systemId")
        private String systemId;
        @JsonProperty("target")
        private String target;
        @JsonProperty("url")
        private String url;
    }
}
