package cn.getech.ehm.task.dto.historyInfo;

import cn.getech.poros.framework.common.param.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <pre>
 *  分页查询参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-12-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentHistoryInfo查询", description = "查询参数")
public class EquipmentHistoryInfoQueryParam extends PageParam {
    @ApiModelProperty("工单id")
    private String taskId;

    @ApiModelProperty("工单code")
    private String taskCode;

    @ApiModelProperty("工单名称")
    private String taskName;

    @ApiModelProperty("变更前设备id")
    private String oldEquipmentId;

    @ApiModelProperty("变更前设备名称")
    private String oldEquipmentName;

    @ApiModelProperty("变更前设备code")
    private String oldEquipmentCode;

    @ApiModelProperty("变更前设备状态")
    private String oldEquipmentStatus;

    @ApiModelProperty("变更后设备id")
    private String newEquipmentId;

    @ApiModelProperty("变更后设备名称")
    private String newEquipmentName;

    @ApiModelProperty("变更后设备code")
    private String newEquipmentCode;

    @ApiModelProperty("变更后设备状态")
    private String newEquipmentStatus;

    @ApiModelProperty("是否变更(0false1true)")
    private Integer changed;

    private List<String> equipmentIds;

    private List<String> equipmentCategoryIds;

    private String equipmentCode;

    private String equipmentName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginCreateTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endCreateTime;
}
