package cn.getech.ehm.task.dto.plan;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * cbm触发器dto
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "CbmTriggerDetailDto", description = "cbm触发器dto")
public class CbmTriggerDetailDto {

    @ApiModelProperty(value = "触发器详情id")
    private String id;

    @ApiModelProperty(value = "计划id")
    private String planId;

    @ApiModelProperty(value = "设备类型测点下参数id")
    private String categoryParameterId;

    @ApiModelProperty(value = "设备类型测点下参数名称")
    private String name;

    @ApiModelProperty(value = "操作类型")
    private Integer operationType;

    @ApiModelProperty(value = "门限值")
    private Double thresholdValue;

    @ApiModelProperty(value = "触发器分组num")
    private Integer groupNum;

}