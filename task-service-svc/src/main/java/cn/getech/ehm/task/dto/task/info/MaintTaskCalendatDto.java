package cn.getech.ehm.task.dto.task.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;


/**
 * 维护工单主表 返回数据模型
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "MaintTaskCalendatDto", description = "维护工单主表返回数据模型")
public class MaintTaskCalendatDto {

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "工单类型(1故障2维保3缺陷单)")
    private Integer type;

    private Date createTime;

    private Integer taskCount;
}