package cn.getech.ehm.task.dto.task.performance;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 个人绩效返回数据模型
 *
 * <AUTHOR>
 * @date 2021-1-18
 */
@Data
@ApiModel(value = "PersonPerformanceDto", description = "个人绩效返回数据模型")
public class PersonalPerformanceDto {

    public PersonalPerformanceDto() {
    }

    public PersonalPerformanceDto(TaskInfoStatisticsDto pointCheckTask, TaskInfoStatisticsDto preventionMaintainTask, TaskInfoStatisticsDto faultMaintainTask, TaskInfoStatisticsDto maintainImproveTask, TaskInfoStatisticsDto conversionTask) {
        this.pointCheckTask = pointCheckTask;
        this.preventionMaintainTask = preventionMaintainTask;
        this.faultMaintainTask = faultMaintainTask;
        this.maintainImproveTask = maintainImproveTask;
        this.conversionTask = conversionTask;
    }

    @ApiModelProperty(value = "用户名称")
    private String name;

    @ApiModelProperty(value = "用户Id")
    private String uid;

    @ApiModelProperty(value = "总任务")
    private TaskInfoStatisticsDto totalTask;

    @ApiModelProperty(value = "点检单")
    private TaskInfoStatisticsDto pointCheckTask;

    @ApiModelProperty(value = "维保单")
    private TaskInfoStatisticsDto preventionMaintainTask;

    @ApiModelProperty(value = "缺陷单")
    private TaskInfoStatisticsDto faultMaintainTask;

    @ApiModelProperty(value = "故障单")
    private TaskInfoStatisticsDto maintainImproveTask;

    @ApiModelProperty(value = "转产")
    private TaskInfoStatisticsDto conversionTask;

    @ApiModelProperty(value = "任务积分")
    private Double taskIntegral = 0.0;

    @ApiModelProperty(value = "评价积分")
    private Double evaluationIntegral = 0.0;

    @ApiModelProperty(value = "总积分")
    private Double totalIntegral = 0.0;

    @ApiModelProperty(value = "维修次数")
    private String repairTimes;

    @ApiModelProperty(value = "维修时常")
    private String repairCostTime;

    @ApiModelProperty(value = "平均维修次数")
    private String repairTimesAvg;

    @ApiModelProperty(value = "平均维修时常")
    private String repairCostTimeAvg;
}
