package cn.getech.ehm.task.controller;

import cn.getech.ehm.task.dto.NameNode;
import cn.getech.ehm.task.dto.task.statistics.TaskStatisticsSearchDto;
import cn.getech.ehm.task.service.IMaintTaskStatisticsService;
import cn.getech.poros.framework.common.api.RestResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/statistics")
@Api(tags = "web接口：统计服务接口")
public class StatisticsController {

    @Autowired
    private IMaintTaskStatisticsService maintTaskStatisticsService;

    @ApiOperation("机台别趋势图")
    @PostMapping("/by/locationId")
    public RestResponse bylocationId(@RequestBody TaskStatisticsSearchDto param) {
        return RestResponse.ok(maintTaskStatisticsService.byLocationId(param));
    }

    @ApiOperation("每日别趋势图")
    @PostMapping("/by/time")
    public RestResponse byTime(@RequestBody TaskStatisticsSearchDto param) {
        return RestResponse.ok(maintTaskStatisticsService.byTime(param));
    }

    @ApiOperation("班组别趋势图")
    @PostMapping("/by/staff")
    public RestResponse byStaff(@RequestBody TaskStatisticsSearchDto param) {
        return RestResponse.ok(maintTaskStatisticsService.byStaff(param));
    }

    @ApiOperation("部位故障别趋势图")
    @PostMapping("/by/structure")
    public RestResponse byStructure(@RequestBody TaskStatisticsSearchDto param) {
        return RestResponse.ok(maintTaskStatisticsService.byStructure(param));
    }

    @ApiOperation("设备故障柏拉图")
    @PostMapping("/by/structure/plato")
    public RestResponse byStructurePlato(@RequestBody TaskStatisticsSearchDto param) {
        return RestResponse.ok(maintTaskStatisticsService.byStructurePlato(param));
    }

    @ApiOperation("设备完好率图")
    @PostMapping("/by/repair/time")
    public RestResponse byRepairTime(@RequestBody TaskStatisticsSearchDto param) {
        return RestResponse.ok(maintTaskStatisticsService.byRepairTime(param));
    }

    @ApiOperation("工单统计")
    @PostMapping("/analysis")
    public RestResponse analysis(@Valid @RequestBody TaskStatisticsSearchDto request){
        return RestResponse.ok(maintTaskStatisticsService.analysis(request));
    }
}
