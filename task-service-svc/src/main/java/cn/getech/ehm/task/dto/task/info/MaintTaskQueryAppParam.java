package cn.getech.ehm.task.dto.task.info;

import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;

/**
 * 维护工单主App查询参数对象
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MaintTaskApp查询", description = "维护工单App查询参数")
public class MaintTaskQueryAppParam extends PageParam {

    @ApiModelProperty(value = "维护设备id")
    private String equipmentId;

    @ApiModelProperty(value = "维护设备名称/编号")
    private String equipmentKeyword;

    @ApiModelProperty(value = "维护设备位置id")
    private String locationId;

    @ApiModelProperty(value = "维护设备类型id")
    private String categoryId;

    @ApiModelProperty(value = "查询工单状态(0待派单1待接单2待执行3开始确认4安全确认中5已确认6执行中7待验收8已关闭9待审批)")
    private Integer searchStatus;

    @ApiModelProperty(value = "紧急程度")
    private String urgency;

    @ApiModelProperty(value = "来源(1故障报修2维保计划)")
    private Integer sourceType;

    @ApiModelProperty(value = "专业/工单类别")
    private String major;

    @ApiModelProperty(value = "来源id集合(计划id/报修id)")
    private String sourceId;

    @ApiModelProperty(value = "流程实力Ids")
    private List<String> processInstanceIds;

    @ApiModelProperty(value = "派单人/处理人")
    private String handler;

    @ApiModelProperty(value = "true我的工单false所有工单")
    private Boolean ourTask = true;

    @ApiModelProperty(value = "是否扫码接单")
    private Boolean freeTask = false;

    @ApiModelProperty(value = "父id")
    private String parentId;

    @ApiModelProperty(value = "父节点类型(1位置11主设备12副设备)")
    private Integer parentType;

    @ApiModelProperty(value = "工单类型(1故障2维保3缺陷单)")
    private Integer type;

    @ApiModelProperty("维保等级")
    private Integer jobLevel;

    @ApiModelProperty("维保类型")
    private String jobType;

}
