package cn.getech.ehm.task.controller;

import cn.getech.ehm.task.dto.task.performance.PersonalPerformanceDto;
import cn.getech.ehm.task.dto.task.performance.PersonalPerformanceQueryParam;
import cn.getech.ehm.task.dto.task.performance.PersonalPerformanceTopQueryParam;
import cn.getech.ehm.task.service.IMaintTaskService;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;

/**
 * 绩效统计
 *
 * <AUTHOR>
 * @since 2021-01-19
 */
@RestController
@RequestMapping("/performanceStatistics")
@Api(tags = "web接口：绩效统计服务接口")
public class PerformanceStatisticsController {

    @Autowired
    private IMaintTaskService maintTaskService;

    /**
     * 分页查询个人绩效分数&任务数&工时
     */
    @ApiOperation("分页查询个人绩效分数&任务数&工时")
    @AuditLog(title = "分页查询个人绩效分数&任务数&工时",desc = "人员绩效明细",businessType = BusinessType.QUERY)
    @PostMapping("/personPerformancePage")
    public RestResponse<PageResult<PersonalPerformanceDto>> personPerformancePage(@RequestBody PersonalPerformanceQueryParam param) {
        return RestResponse.ok(maintTaskService.personalPerformancePage(param));
    }


    /**
     * 查找topN人员绩效的数据
     */
    @ApiOperation("查找topN人员绩效的数据")
    @AuditLog(title = "查找topN人员绩效的数据",desc = "人员绩效排名",businessType = BusinessType.QUERY)
    @PostMapping("/personPerformanceTop")
    public RestResponse<List<PersonalPerformanceDto>> personPerformanceTop(@RequestBody PersonalPerformanceTopQueryParam param) {
        return RestResponse.ok(maintTaskService.personPerformanceTop(param));
    }
}
