package cn.getech.ehm.task.mapper;

import cn.getech.ehm.task.dto.task.info.TaskPartRelDetailQueryParam;
import cn.getech.ehm.task.dto.task.info.TaskPartRelQueryParam;
import cn.getech.ehm.task.dto.task.info.TaskPartRelReportDto;
import cn.getech.ehm.task.dto.task.part.TaskPartDto;
import cn.getech.ehm.task.entity.MaintTaskPart;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

/**
 * 维护工单、备件关联表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
@Repository
public interface MaintTaskPartMapper extends BaseMapper<MaintTaskPart> {
    /**
     * 根据工单id获取集合
     * @param taskId
     * @return
     */
    List<TaskPartDto> getListByTaskId(@Param("taskId") String taskId);
    /**
     * 分页获取备件耗用成本列表
     * @param taskPartRelQueryParam
     * @return
     */
    Page<TaskPartRelReportDto> getTaskPartRelPageList(@Param("page") Page<TaskPartRelReportDto> page,
                                                      @Param("param") TaskPartRelQueryParam taskPartRelQueryParam);

    /**
     * 分页获取备件耗用详情列表
     * @param page
     * @param taskPartRelDetailQueryParam
     * @return
     */
    Page<TaskPartRelReportDto> getTaskPartRelDetailPageList(@Param("page")Page<TaskPartRelReportDto> page,
                                                            @Param("param") TaskPartRelDetailQueryParam taskPartRelDetailQueryParam);

    /**
     * 校验备件是否被维护工单相关使用
     *
     * @param partIds
     * @return
     */
    List<String> checkPartUsed(@Param("partIds") String[] partIds);
}
