package cn.getech.ehm.task.dto.plan;

import cn.getech.ehm.common.mp.handler.StringArrayTypeHandler;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

/**
 * 维护计划 返回数据模型
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "SynMaintPlanDto", description = "出工单计划返回数据模型")
public class SynMaintPlanDto {

    @ApiModelProperty(value = "计划id")
    private String id;

    @ApiModelProperty(value = "计划名称")
    private String name;

    @ApiModelProperty(value = "维保类别")
    private String jobType;

    @ApiModelProperty(value = "维保等级")
    private Integer jobLevel;

    @ApiModelProperty(value = "报修单id")
    private String repairId;

    @ApiModelProperty(value = "紧急程度/优先程度")
    private String urgency;

    @ApiModelProperty(value = "专业")
    private String major;

    @ApiModelProperty(value = "触发逻辑1以上次工单完成日期开始2定时触发")
    private Integer triggerType;

    @ApiModelProperty(value = "间隔值(触发周期)")
    private Integer interVal;

    @ApiModelProperty(value = "提前释放天数")
    private Integer advanceDay;

    @ApiModelProperty(value = "周期 0 单次 1 天 2 周 3 月")
    private String period;

    @ApiModelProperty(value = "周期为周/月时，数组(周为1-7月为1-31，last)")
    @TableField(typeHandler = StringArrayTypeHandler.class)
    private String[] execVal;

    @ApiModelProperty(value = "周期为月时，数组(1-12月份)")
    @TableField(typeHandler = StringArrayTypeHandler.class)
    private String[] monthExecVal;

    @ApiModelProperty(value = "截止日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expiryTime;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(hidden = true)
    private String tenantId;

    @ApiModelProperty(value = "是否故障单自动转换")
    private Boolean repairAuto;

    @ApiModelProperty(value = "截止天数/小时")
    private Integer deadlineDays;

    @ApiModelProperty(value = "cbm触发频率")
    private Integer cbmFrequency;

    @ApiModelProperty("开关")
    private Integer enabled;

    @ApiModelProperty(value = "设备类型id")
    private String infoCategoryId;

    @ApiModelProperty(value = "设备位置id")
    private String infoLocationId;

    @ApiModelProperty(value = "设备id集合")
    private String[] equipmentIds;

    @ApiModelProperty(value = "作业标准id")
    private String standardId;

    @ApiModelProperty(value = "作业标准名称")
    private String standardName;

    @ApiModelProperty("节假日")
    private Integer[] festivalType;

    @ApiModelProperty(value = "超期后处理方式")
    private Integer overdueHandlingMethod;

    @ApiModelProperty(value = "设备停机后处理方式")
    private Integer stopHandlingMethod;

}