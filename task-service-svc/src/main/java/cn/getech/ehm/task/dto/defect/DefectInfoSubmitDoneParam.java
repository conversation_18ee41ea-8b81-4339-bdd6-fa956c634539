package cn.getech.ehm.task.dto.defect;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * <pre>
 * 缺陷信息 编辑参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-02-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DefectInfoSubmitDoneParam", description = "缺陷信息编辑参数")
public class DefectInfoSubmitDoneParam extends ApiParam {


    @ApiModelProperty(value = "实际处理方案")
    @NotBlank
    private String realDealContent;

    @ApiModelProperty(value = "")
    private String id;

}
