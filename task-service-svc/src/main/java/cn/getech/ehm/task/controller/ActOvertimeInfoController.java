package cn.getech.ehm.task.controller;


import cn.getech.ehm.task.dto.ActOvertimeInfoDto;
import cn.getech.ehm.task.dto.ActOvertimeInfoQueryParam;
import cn.getech.ehm.task.service.IActOvertimeInfoService;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import cn.getech.poros.framework.common.utils.ExcelUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <p>
 * 任务超时记录控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-15
 */
@RestController
@RequestMapping("/actOvertimeInfo")
@Api(tags = "任务超时记录服务接口")
public class ActOvertimeInfoController {

    @Autowired
    private IActOvertimeInfoService actOvertimeInfoService;

    /**
     * 分页获取任务超时记录列表
     */
    @ApiOperation("分页获取任务超时记录列表")
    @PostMapping("/list")
    //@Permission("act:overtime:info:list")
    public RestResponse<PageResult<ActOvertimeInfoDto>> pageList(@RequestBody @Valid ActOvertimeInfoQueryParam actOvertimeInfoQueryParam) {
        return RestResponse.ok(actOvertimeInfoService.pageDto(actOvertimeInfoQueryParam));
    }


}
