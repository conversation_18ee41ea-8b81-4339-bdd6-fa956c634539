package cn.getech.ehm.task.handler;

import cn.getech.ehm.base.client.BaseServiceClient;
import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.common.context.UserContext;
import cn.getech.ehm.common.context.UserContextHolder;
import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.task.dto.activiti.AuditActivitiServiceResult;
import cn.getech.ehm.task.dto.activiti.PartTaskAuditParam;
import cn.getech.ehm.task.dto.task.info.MaintTaskReopenDto;
import cn.getech.ehm.task.entity.MaintTask;
import cn.getech.ehm.task.entity.MaintTaskHistory;
import cn.getech.ehm.task.entity.MaintTaskItem;
import cn.getech.ehm.task.enums.TaskStatusType;
import cn.getech.ehm.task.service.IMaintTaskHistoryService;
import cn.getech.ehm.task.service.IMaintTaskItemService;
import cn.getech.ehm.task.service.IMaintTaskService;
import cn.getech.poros.bpm.client.TaskServiceClient;
import cn.getech.poros.bpm.param.process.ProcessStartParam;
import cn.getech.poros.bpm.param.task.MultiTransferParam;
import cn.getech.poros.bpm.param.task.TaskCompleteParam;
import cn.getech.poros.bpm.param.task.TaskOptionParam;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.stream.Collectors;

@Service
@Slf4j
@RefreshScope
public class ActivitiLocalHandler {

    // Constants for better maintainability
    private static final String OPERATION_SUBMIT = "操作";
    private static final String OPERATION_REJECT = "驳回";
    private static final String OPERATION_CLOSE = "关闭";
    private static final String OPERATION_TRANSFER = "转派";
    private static final String OPERATION_DELETE = "删除任务";
    private static final String CANDIDATE_TASK = "候选任务";
    private static final String EXCEPTION_CLOSE = "异常关闭";
    private static final String PROCESS_CHANGED_MESSAGE = "流程已变动，可能已被其他人处理，请刷新最新数据查看";
    private static final String STATUS_CHANGED_MESSAGE = "工单已处于新状态中，可能重复点击，不继续操作";
    private static final String TASK_TIMEOUT_MESSAGE = "任务：%s延期后仍然超时";

    // Thread-safe cache for frequently accessed data
    private final Map<String, MaintTask> taskCache = new ConcurrentHashMap<>();
    private final ReentrantReadWriteLock cacheLock = new ReentrantReadWriteLock();

    @Autowired
    private TaskServiceClient taskServiceClient;
    @Autowired
    private ResourceBundleMessageSource messageSource;
    @Autowired
    private IMaintTaskHistoryService maintTaskHistoryService;
    @Autowired
    @Lazy
    private IMaintTaskService maintTaskService;
    @Autowired
    @Lazy
    private IMaintTaskItemService maintTaskItemService;
    @Autowired
    BaseServiceClient baseServiceClient;

    @Value("${heroin.env.isdev:false}")
    public Boolean isDev;

    /**
     * Submit workflow with comprehensive validation and error handling
     *
     * @param taskCompleteParam workflow completion parameters
     * @param taskId task identifier
     * @param newStatus new status to set
     * @param forceSubmit whether to force submission bypassing user validation
     * @throws GlobalServiceException if validation fails or operation cannot be completed
     */
    @Transactional(propagation = Propagation.REQUIRED, isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    public void submitActiviti(@NotNull TaskCompleteParam taskCompleteParam,
                              @NotBlank String taskId,
                              @NotNull Integer newStatus,
                              Boolean forceSubmit) {
        validateInputParameters(taskCompleteParam, taskId, newStatus);

        try {
            log.info("提交流程处理：taskId={}, newStatus={}, forceSubmit={}", taskId, newStatus, forceSubmit);

            UserBaseInfo userBaseInfo = validateAndGetUserContext();
            List<String> assigns = extractAssignsList(taskCompleteParam.getVariables());
            MaintTask maintTask = getAndValidateTask(taskId, newStatus, userBaseInfo, forceSubmit);

            // Insert workflow history nodes
            insertWorkflowNodes(maintTask, userBaseInfo, taskId, newStatus, assigns, taskCompleteParam.getComment());

            log.info("流程提交成功：taskId={}, newStatus={}", taskId, newStatus);

        } catch (GlobalServiceException e) {
            log.error("流程提交失败：taskId={}, error={}", taskId, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("流程提交异常：taskId={}, error={}", taskId, e.getMessage(), e);
            throw new GlobalServiceException(GlobalResultMessage.of("流程提交失败，请稍后重试"));
        }
    }

    /**
     * Submit workflow (overloaded method without forceSubmit parameter)
     *
     * @param taskCompleteParam workflow completion parameters
     * @param taskId task identifier
     * @param newStatus new status to set
     */
    @Transactional(propagation = Propagation.REQUIRED, isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    public void submitActiviti(@NotNull TaskCompleteParam taskCompleteParam,
                              @NotBlank String taskId,
                              @NotNull Integer newStatus) {
        // Delegate to the main method with forceSubmit = false
        submitActiviti(taskCompleteParam, taskId, newStatus, false);
    }

    /**
     * Reject workflow with comprehensive validation and error handling
     *
     * @param taskAuditParam audit parameters containing rejection details
     * @param taskId task identifier
     * @param newStatus new status after rejection
     * @throws GlobalServiceException if validation fails or operation cannot be completed
     */
    @Transactional(propagation = Propagation.REQUIRED, isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    public void rejectActiviti(@NotNull PartTaskAuditParam taskAuditParam,
                              @NotBlank String taskId,
                              @NotNull Integer newStatus) {
        Assert.notNull(taskAuditParam, "TaskAuditParam cannot be null");
        Assert.hasText(taskId, "TaskId cannot be null or empty");
        Assert.notNull(newStatus, "NewStatus cannot be null");

        try {
            log.info("提交流程驳回：taskId={}, newStatus={}", taskId, newStatus);

            UserBaseInfo userBaseInfo = validateAndGetUserContext();
            MaintTask task = getTaskWithCache(taskId);

            if (task == null) {
                throw new GlobalServiceException(GlobalResultMessage.of("工单不存在或已被删除"));
            }

            // Insert workflow history nodes for rejection
            maintTaskHistoryService.insertDoneNode(
                task.getProcessInstanceId(),
                userBaseInfo,
                taskId,
                task.getStatus(),
                task.getStatus(),
                OPERATION_REJECT,
                taskAuditParam.getComment()
            );

            maintTaskHistoryService.insertRejectStatusNode(
                task.getProcessInstanceId(),
                userBaseInfo,
                taskId,
                task.getStatus(),
                newStatus,
                CANDIDATE_TASK
            );

            // Invalidate cache after successful operation
            invalidateTaskCache(taskId);

            log.info("流程驳回成功：taskId={}, newStatus={}", taskId, newStatus);

        } catch (GlobalServiceException e) {
            log.error("流程驳回失败：taskId={}, error={}", taskId, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("流程驳回异常：taskId={}, error={}", taskId, e.getMessage(), e);
            throw new GlobalServiceException(GlobalResultMessage.of("流程驳回失败，请稍后重试"));
        }
    }

    /**
     * Start workflow process with MaintTask entity
     *
     * @param processStartParam process start parameters
     * @param maintTask maintenance task entity
     * @param taskStatus initial task status
     * @return RestResponse containing process instance ID
     */
    public RestResponse<String> startProcess(@NotNull ProcessStartParam processStartParam,
                                           @NotNull MaintTask maintTask,
                                           @NotNull Integer taskStatus) {
        Assert.notNull(processStartParam, "ProcessStartParam cannot be null");
        Assert.notNull(maintTask, "MaintTask cannot be null");
        Assert.hasText(maintTask.getId(), "MaintTask ID cannot be null or empty");
        Assert.notNull(taskStatus, "TaskStatus cannot be null");

        try {
            log.info("启动流程：taskId={}, taskStatus={}", maintTask.getId(), taskStatus);

            UserBaseInfo userBaseInfo = validateAndGetUserContext();
            List<String> assigns = extractAssignsList(processStartParam.getVariables());

            String processInstanceId = maintTaskHistoryService.initFirstNode(userBaseInfo, maintTask.getId());
            maintTaskHistoryService.insertStatusNode(
                processInstanceId,
                userBaseInfo,
                maintTask,
                0,
                taskStatus,
                assigns,
                CANDIDATE_TASK
            );

            log.info("流程启动成功：taskId={}, processInstanceId={}", maintTask.getId(), processInstanceId);
            return RestResponse.ok(processInstanceId);

        } catch (Exception e) {
            e.printStackTrace();
            log.error("启动流程失败：taskId={}, error={}", maintTask.getId(), e.getMessage(), e);
            return RestResponse.failed("启动流程失败：" + e.getMessage());
        }
    }

    /**
     * Start workflow process with task ID
     *
     * @param processStartParam process start parameters
     * @param taskId task identifier
     * @param taskStatus initial task status
     * @return RestResponse containing process instance ID
     */
    public RestResponse<String> startProcess(@NotNull ProcessStartParam processStartParam,
                                           @NotBlank String taskId,
                                           @NotNull Integer taskStatus) {
        Assert.notNull(processStartParam, "ProcessStartParam cannot be null");
        Assert.hasText(taskId, "TaskId cannot be null or empty");
        Assert.notNull(taskStatus, "TaskStatus cannot be null");

        try {
            log.info("启动流程：taskId={}, taskStatus={}", taskId, taskStatus);

            UserBaseInfo userBaseInfo = validateAndGetUserContext();
            List<String> assigns = extractAssignsList(processStartParam.getVariables());

            String processInstanceId = maintTaskHistoryService.initFirstNode(userBaseInfo, taskId);
            maintTaskHistoryService.insertStatusNode(
                processInstanceId,
                userBaseInfo,
                taskId,
                0,
                taskStatus,
                assigns,
                CANDIDATE_TASK
            );

            log.info("流程启动成功：taskId={}, processInstanceId={}", taskId, processInstanceId);
            return RestResponse.ok(processInstanceId);

        } catch (Exception e) {
            log.error("启动流程失败：taskId={}, error={}", taskId, e.getMessage(), e);
            return RestResponse.failed("启动流程失败：" + e.getMessage());
        }
    }

    @Async("closeOrder")
    public void trashTask(@NotNull TaskOptionParam taskOptionParam,
                          @NotBlank String taskId,
                          @NotNull Integer newStatus,UserBaseInfo userBaseInfo){
        UserContextHolder.switchContext(userBaseInfo);
        this.trashTask(taskOptionParam, taskId, newStatus);
    }

    /**
     * Trash/close task with comprehensive validation and error handling
     *
     * @param taskOptionParam task option parameters
     * @param taskId task identifier
     * @param newStatus new status after closing
     * @throws GlobalServiceException if validation fails or operation cannot be completed
     */
    @Transactional(propagation = Propagation.REQUIRED, isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    public void trashTask(@NotNull TaskOptionParam taskOptionParam,
                         @NotBlank String taskId,
                         @NotNull Integer newStatus) {
        Assert.notNull(taskOptionParam, "TaskOptionParam cannot be null");
        Assert.hasText(taskId, "TaskId cannot be null or empty");
        Assert.notNull(newStatus, "NewStatus cannot be null");

        try {
            log.info("提交流程中止：taskId={}, newStatus={}", taskId, newStatus);

            UserBaseInfo userBaseInfo = validateAndGetUserContext();
            MaintTask task = getTaskWithCache(taskId);

            if (task == null) {
                throw new GlobalServiceException(GlobalResultMessage.of("工单不存在或已被删除"));
            }

            // Insert workflow history nodes for task closure
            maintTaskHistoryService.insertDoneNode(
                task.getProcessInstanceId(),
                userBaseInfo,
                taskId,
                task.getStatus(),
                task.getStatus(),
                OPERATION_CLOSE,
                taskOptionParam.getComment()
            );

            maintTaskHistoryService.insertTrashStatusNode(
                task.getProcessInstanceId(),
                userBaseInfo,
                taskId,
                task.getStatus(),
                newStatus,
                EXCEPTION_CLOSE
            );

            // Invalidate cache after successful operation
            invalidateTaskCache(taskId);

            log.info("流程中止成功：taskId={}, newStatus={}", taskId, newStatus);

        } catch (GlobalServiceException e) {
            log.error("流程中止失败：taskId={}, error={}", taskId, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("流程中止异常：taskId={}, error={}", taskId, e.getMessage(), e);
            throw new GlobalServiceException(GlobalResultMessage.of("流程中止失败，请稍后重试"));
        }
    }

    /**
     * Transfer task to multiple users with comprehensive validation and error handling
     *
     * @param transferParam transfer parameters containing assignee information
     * @param taskId task identifier
     * @param newStatus new status after transfer
     * @throws GlobalServiceException if validation fails or operation cannot be completed
     */
    @Transactional(propagation = Propagation.REQUIRED, isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    public void multiTransferTask(@NotNull MultiTransferParam transferParam,
                                 @NotBlank String taskId,
                                 @NotNull Integer newStatus) {
        Assert.notNull(transferParam, "TransferParam cannot be null");
        Assert.hasText(taskId, "TaskId cannot be null or empty");
        Assert.notNull(newStatus, "NewStatus cannot be null");
        Assert.notEmpty(transferParam.getAssigneeIds(), "AssigneeIds cannot be null or empty");

        try {
            log.info("提交流程转派：taskId={}, newStatus={}, assignees={}",
                    taskId, newStatus, transferParam.getAssigneeIds());

            UserBaseInfo userBaseInfo = validateAndGetUserContext();
            MaintTask task = getTaskWithCache(taskId);

            if (task == null) {
                throw new GlobalServiceException(GlobalResultMessage.of("工单不存在或已被删除"));
            }

            // Insert workflow history nodes for task transfer
            maintTaskHistoryService.insertDoneNode(
                task.getProcessInstanceId(),
                userBaseInfo,
                taskId,
                task.getStatus(),
                task.getStatus(),
                OPERATION_TRANSFER,
                transferParam.getComment()
            );

            maintTaskHistoryService.insertStatusNode(
                task.getProcessInstanceId(),
                userBaseInfo,
                taskId,
                task.getStatus(),
                newStatus,
                transferParam.getAssigneeIds(),
                CANDIDATE_TASK
            );

            // Invalidate cache after successful operation
            invalidateTaskCache(taskId);

            log.info("流程转派成功：taskId={}, newStatus={}", taskId, newStatus);

        } catch (GlobalServiceException e) {
            log.error("流程转派失败：taskId={}, error={}", taskId, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("流程转派异常：taskId={}, error={}", taskId, e.getMessage(), e);
            throw new GlobalServiceException(GlobalResultMessage.of("流程转派失败，请稍后重试"));
        }
    }

    /**
     * Delete task by task ID with comprehensive validation
     *
     * @param taskId task identifier
     * @return true if deletion successful, false otherwise
     */
    public Boolean delete(@NotBlank String taskId) {
        if (StringUtils.isBlank(taskId)) {
            log.error("删除工作流失败：taskId为空");
            return false;
        }

        try {
            log.info("删除工作流：taskId={}", taskId);

            TaskOptionParam taskOptionParam = new TaskOptionParam();
            taskOptionParam.setComment(OPERATION_DELETE);
            this.trashTask(taskOptionParam, taskId, 0);

            log.info("删除工作流成功：taskId={}", taskId);
            return true;

        } catch (Exception e) {
            log.error("删除工作流失败：taskId={}, error={}", taskId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * Delete task by process ID with comprehensive validation and error handling
     *
     * @param processId process instance identifier
     * @throws GlobalServiceException if validation fails or operation cannot be completed
     */
    public void deleteByProcessId(@NotBlank String processId) {
        if (StringUtils.isBlank(processId)) {
            log.error("删除工作流失败：processId为空");
            throw new GlobalServiceException(GlobalResultMessage.of("流程ID不能为空"));
        }

        try {
            log.info("根据流程ID删除工作流：processId={}", processId);

            AuditActivitiServiceResult processTaskUserId = this.getProcessTaskUserId(processId);

            if (processTaskUserId == null || StringUtils.isBlank(processTaskUserId.getTaskId())) {
                log.error("删除工作流失败：无法获取taskId, processId={}", processId);
                throw new GlobalServiceException(GlobalResultMessage.of("无法获取任务信息"));
            }

            String taskId = processTaskUserId.getTaskId();
            TaskOptionParam taskOptionParam = new TaskOptionParam();
            taskOptionParam.setTaskId(taskId);
            taskOptionParam.setComment(OPERATION_DELETE);

            this.trashTask(taskOptionParam, taskId, 0);

            log.info("根据流程ID删除工作流成功：processId={}, taskId={}", processId, taskId);

        } catch (GlobalServiceException e) {
            log.error("根据流程ID删除工作流失败：processId={}, error={}", processId, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("根据流程ID删除工作流异常：processId={}, error={}", processId, e.getMessage(), e);
            throw new GlobalServiceException(GlobalResultMessage.of("删除工作流失败，请稍后重试"));
        }
    }

    /**
     * Get process task user information with comprehensive validation and error handling
     *
     * @param processInstanceId process instance identifier
     * @return AuditActivitiServiceResult containing task and user information
     * @throws GlobalServiceException if processInstanceId is invalid
     */
    public AuditActivitiServiceResult getProcessTaskUserId(@NotBlank String processInstanceId) {
        if (StringUtils.isBlank(processInstanceId)) {
            throw new GlobalServiceException(GlobalResultMessage.of("流程实例ID不能为空"));
        }

        try {
            log.debug("获取流程任务用户信息：processInstanceId={}", processInstanceId);

            MaintTaskHistory taskHistory = (MaintTaskHistory) maintTaskHistoryService.getOne(
                new QueryWrapper<MaintTaskHistory>().lambda()
                    .eq(MaintTaskHistory::getProcessInstanceId, processInstanceId)
                    .orderByDesc(MaintTaskHistory::getSort),
                false
            );

            if (taskHistory != null) {
                String assigneeUid = taskHistory.getAssigneeUid();
                String[] processUserArray = null;

                if (StringUtils.isNotBlank(assigneeUid)) {
                    processUserArray = assigneeUid.split(",");
                }

                return AuditActivitiServiceResult.builder()
                        .processUser(assigneeUid)
                        .processUserArray(processUserArray)
                        .taskId(StringUtils.defaultString(taskHistory.getTaskId(), ""))
                        .taskName(StringUtils.defaultString(taskHistory.getActivityName(), ""))
                        .build();
            } else {
                log.warn("未找到流程任务历史记录：processInstanceId={}", processInstanceId);
                return AuditActivitiServiceResult.builder()
                        .processUser(null)
                        .processUserArray(null)
                        .taskId("")
                        .taskName("")
                        .build();
            }

        } catch (Exception e) {
            log.error("获取流程任务用户信息失败：processInstanceId={}, error={}", processInstanceId, e.getMessage(), e);
            throw new GlobalServiceException(GlobalResultMessage.of("获取流程信息失败"));
        }
    }


    public static void dealContext(String currentUid, String tenantId) {
        UserContextHolder.defaultContext();
        UserContext context = UserContextHolder.getContext();
        UserBaseInfo userBaseInfo = context.getUserBaseInfo();
        userBaseInfo.setUid(currentUid);
        userBaseInfo.setTenantId(tenantId);
        UserContextHolder.switchContext(userBaseInfo);
    }

    /**
     * Calculate working time with comprehensive validation and optimized performance
     *
     * @param maintTaskId maintenance task identifier
     * @throws GlobalServiceException if validation fails or calculation cannot be completed
     */
    public void touchWorkingTimeCal(@NotBlank String maintTaskId) {
        if (StringUtils.isBlank(maintTaskId)) {
            log.warn("计算工作时间失败：maintTaskId为空");
            return;
        }

        try {
            log.info("开始计算工作时间：maintTaskId={}", maintTaskId);

            List<MaintTaskHistory> historyList = getTaskHistoryList(maintTaskId);
            if (CollectionUtils.isEmpty(historyList) || historyList.size() < 3) {
                log.info("历史记录不足，跳过工作时间计算：maintTaskId={}, historyCount={}",
                        maintTaskId, CollectionUtils.size(historyList));
                return;
            }

            BigDecimal totalDownTime = calculateDownTime(historyList);
            long totalTimeInMinutes = calculateTotalTimeInMinutes(historyList);
            BigDecimal workingTimeMinutes = new BigDecimal(totalTimeInMinutes).subtract(totalDownTime);

            updateTaskWorkingTime(maintTaskId, workingTimeMinutes.longValue());

            log.info("工作时间计算完成：maintTaskId={}, 总时间={}分钟, 挂起时间={}分钟, 工作时间={}分钟",
                    maintTaskId, totalTimeInMinutes, totalDownTime, workingTimeMinutes);

        } catch (Exception e) {
            log.error("计算工作时间异常：maintTaskId={}, error={}", maintTaskId, e.getMessage(), e);
            // Don't throw exception to avoid breaking the main workflow
        }
    }

    /**
     * Get task history list with error handling
     */
    private List<MaintTaskHistory> getTaskHistoryList(String maintTaskId) {
        try {
            return maintTaskHistoryService.list(
                new QueryWrapper<MaintTaskHistory>().lambda()
                    .eq(MaintTaskHistory::getMaintTaskId, maintTaskId)
                    .orderByAsc(MaintTaskHistory::getSort)
            );
        } catch (Exception e) {
            log.error("查询任务历史记录失败：maintTaskId={}", maintTaskId, e);
            return new ArrayList<>();
        }
    }

    /**
     * Calculate total down time from history records
     */
    private BigDecimal calculateDownTime(List<MaintTaskHistory> historyList) {
        Map<Integer, MaintTaskHistory> sortMap = historyList.stream()
            .filter(history -> history.getSort() != null)
            .collect(Collectors.toMap(MaintTaskHistory::getSort, history -> history, (existing, replacement) -> existing));

        BigDecimal totalDownTime = BigDecimal.ZERO;
        String handUpStatus = String.valueOf(TaskStatusType.HAND_UP_AUDIT_PASS.getValue());
        String processingStatus = String.valueOf(TaskStatusType.PROCESSING.getValue());

        for (MaintTaskHistory history : historyList) {
            if (isResumeFromHandUp(history, handUpStatus, processingStatus)) {
                BigDecimal downTime = calculateSingleDownTime(history, sortMap, handUpStatus);
                if (downTime.compareTo(BigDecimal.ZERO) > 0) {
                    totalDownTime = totalDownTime.add(downTime);
                    log.debug("本次挂起时间：{}分钟", downTime);
                }
            }
        }

        return totalDownTime;
    }

    /**
     * Check if this history record represents resuming from hand-up status
     */
    private boolean isResumeFromHandUp(MaintTaskHistory history, String handUpStatus, String processingStatus) {
        return history.getOldStatus() != null && history.getNewStatus() != null &&
               handUpStatus.equals(history.getOldStatus()) &&
               processingStatus.equals(history.getNewStatus());
    }

    /**
     * Calculate down time for a single hand-up period
     */
    private BigDecimal calculateSingleDownTime(MaintTaskHistory currentHistory,
                                             Map<Integer, MaintTaskHistory> sortMap,
                                             String handUpStatus) {
        try {
            Integer currentSort = currentHistory.getSort();
            Integer previousSort = currentSort - 2;
            MaintTaskHistory previousHistory = sortMap.get(previousSort);

            if (previousHistory != null &&
                handUpStatus.equals(previousHistory.getNewStatus()) &&
                previousHistory.getStartTime() != null &&
                currentHistory.getStartTime() != null) {

                long minutes = DateUtil.between(
                    previousHistory.getStartTime(),
                    currentHistory.getStartTime(),
                    DateUnit.MINUTE,
                    true
                );
                return new BigDecimal(minutes);
            }
        } catch (Exception e) {
            log.warn("计算单次挂起时间失败：{}", e.getMessage());
        }

        return BigDecimal.ZERO;
    }

    /**
     * Calculate total time in minutes from first to last history record
     */
    private long calculateTotalTimeInMinutes(List<MaintTaskHistory> historyList) {
        MaintTaskHistory first = historyList.get(0);
        MaintTaskHistory last = historyList.get(historyList.size() - 1);

        if (first.getStartTime() == null || last.getEndTime() == null) {
            log.warn("历史记录时间信息不完整，无法计算总时间");
            return 0L;
        }

        long totalSeconds = DateUtil.between(first.getStartTime(), last.getEndTime(), DateUnit.SECOND, true);

        // Convert seconds to minutes with ceiling
        return (totalSeconds + 59) / 60; // Equivalent to Math.ceil(totalSeconds / 60.0)
    }

    /**
     * Update task working time with error handling
     */
    private void updateTaskWorkingTime(String maintTaskId, long workingTimeMinutes) {
        try {
            maintTaskService.update(
                new UpdateWrapper<MaintTask>().lambda()
                    .eq(MaintTask::getId, maintTaskId)
                    .set(MaintTask::getWorkingTime, workingTimeMinutes)
            );
        } catch (Exception e) {
            log.error("更新任务工作时间失败：maintTaskId={}, workingTime={}", maintTaskId, workingTimeMinutes, e);
            throw new GlobalServiceException(GlobalResultMessage.of("更新工作时间失败"));
        }
    }

    /**
     * Reopen tasks with comprehensive validation and optimized performance
     *
     * @param request reopen request containing task IDs and parameters
     * @throws GlobalServiceException if validation fails or operation cannot be completed
     */
    @Transactional(propagation = Propagation.REQUIRED, isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    public void reopenTask(@NotNull MaintTaskReopenDto request) {
        Assert.notNull(request, "MaintTaskReopenDto cannot be null");

        if (CollectionUtils.isEmpty(request.getTaskIdList())) {
            log.warn("重新打开任务失败：任务ID列表为空");
            return;
        }

        try {
            log.info("开始重新打开任务：taskIds={}, timeOffset={}", request.getTaskIdList(), request.getTimeOffset());

            UserBaseInfo userBaseInfo = validateAndGetUserContext();
            Date now = new Date();

            // Fetch tasks and items in batch for better performance
            List<MaintTask> originalTasks = maintTaskService.listByIds(request.getTaskIdList());

            for (MaintTask originalTask : originalTasks) {
                processTaskReopen(originalTask, request, now, userBaseInfo);
            }
    

            // Map<String, List<MaintTaskItem>> taskItemsMap = getTaskItemsMap(request.getTaskIdList());

            // List<MaintTask> newTaskList = new ArrayList<>();
            // List<MaintTaskItem> newTaskItemList = new ArrayList<>();

            // for (MaintTask originalTask : originalTasks) {
            //     MaintTask newTask = processTaskReopen(originalTask, request, now, userBaseInfo);
            //     newTaskList.add(newTask);

            //     // Process task items
            //     List<MaintTaskItem> newItems = processTaskItems(originalTask.getId(), newTask.getId(), taskItemsMap);
            //     newTaskItemList.addAll(newItems);
            // }

            // // Batch save for better performance
            // saveBatchWithValidation(newTaskList, newTaskItemList);
            log.info("重新打开任务成功：处理任务数={}", originalTasks.size());
            // log.info("重新打开任务成功：处理任务数={}, 处理任务项数={}", newTaskList.size(), newTaskItemList.size());

        } catch (GlobalServiceException e) {
            log.error("重新打开任务失败：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("重新打开任务异常：taskIds={}, error={}", request.getTaskIdList(), e.getMessage(), e);
            throw new GlobalServiceException(GlobalResultMessage.of("重新打开任务失败，请稍后重试"));
        }
    }

    /**
     * Get task items map for batch processing
     */
    private Map<String, List<MaintTaskItem>> getTaskItemsMap(List<String> taskIdList) {
        try {
            List<MaintTaskItem> allTaskItems = maintTaskItemService.list(
                new QueryWrapper<MaintTaskItem>().lambda()
                    .in(MaintTaskItem::getTaskId, taskIdList)
            );
            return allTaskItems.stream()
                .collect(Collectors.groupingBy(MaintTaskItem::getTaskId));
        } catch (Exception e) {
            log.error("获取任务项失败：taskIds={}", taskIdList, e);
            return new HashMap<>();
        }
    }

    /**
     * Process single task reopen with validation
     */
    private MaintTask processTaskReopen(MaintTask originalTask, MaintTaskReopenDto request,
                                      Date now, UserBaseInfo userBaseInfo) {
        // 校验截止时间
        Date originalDeadline = originalTask.getTaskDeadlineDate();
        if (originalDeadline == null) {
            throw new GlobalServiceException(GlobalResultMessage.of("任务截止时间不能为空：" + originalTask.getCode()));
        }

        DateTime newDeadline = DateUtil.offset(originalDeadline, DateField.HOUR, request.getTimeOffset());
        if (newDeadline.before(now)) {
            throw new GlobalServiceException(GlobalResultMessage.of(
                String.format(TASK_TIMEOUT_MESSAGE, originalTask.getCode())
            ));
        }

        // 记录重开前的状态
        Integer oldStatus = originalTask.getStatus();

        originalTask.setTaskDeadlineDate(newDeadline);
        originalTask.setReopenReason(request.getReopenReason());
        originalTask.setStatus(TaskStatusType.RECEIVING.getValue());

        if (ObjectUtil.isEmpty(originalTask.getReopenCount())) {
            originalTask.setReopenCount(1);
        } else {
            originalTask.setReopenCount(originalTask.getReopenCount() + 1);
        }
        setTaskAssignees(originalTask, originalTask.getId(), userBaseInfo);
        // 保存修改
        maintTaskService.updateById(originalTask);

        // 添加审批记录
        String processInstanceId = originalTask.getProcessInstanceId();
        List<String> assignees = originalTask.getUids() == null ? new ArrayList<>() : Arrays.asList(originalTask.getUids());
        maintTaskHistoryService.insertStatusNode(
            processInstanceId,
            userBaseInfo,
            originalTask.getId(),
            oldStatus,
            originalTask.getStatus(),
            assignees,
            CANDIDATE_TASK
        );

        return originalTask;
    }

    /**
     * Set task assignees based on history or staff configuration
     */
    private void setTaskAssignees(MaintTask newTask, String originalTaskId, UserBaseInfo userBaseInfo) {
        try {
            // Try to get assignees from history
            MaintTaskHistory history = (MaintTaskHistory) maintTaskHistoryService.getOne(
                new QueryWrapper<MaintTaskHistory>().lambda()
                    .eq(MaintTaskHistory::getMaintTaskId, originalTaskId)
                    .eq(MaintTaskHistory::getNewStatus, TaskStatusType.RECEIVING.getValue())
                    .eq(MaintTaskHistory::getOldStatus, TaskStatusType.DISPATCH.getValue()),
                false
            );

            if (history != null && StringUtils.isNotBlank(history.getAssigneeUid())) {
                String[] assignees = history.getAssigneeUid().split(",");
                newTask.setUids(assignees);
            } else {
                // Fallback to staff configuration
                setTaskAssigneesFromStaffConfig(newTask);
            }
        } catch (Exception e) {
            log.warn("设置任务分配人失败，使用默认配置：taskId={}", newTask.getId(), e);
            setTaskAssigneesFromStaffConfig(newTask);
        }
    }

    /**
     * Set task assignees from staff configuration
     */
    private void setTaskAssigneesFromStaffConfig(MaintTask task) {
        try {
            buildAllStaffIds(task);
            if (StringUtils.isNotBlank(task.getAllStaffIds())) {
                RestResponse<List<String>> response = baseServiceClient.getUidsByIds(task.getAllStaffIds().split(","));
                if (response.isOk() && CollectionUtils.isNotEmpty(response.getData())) {
                    List<String> uids = response.getData();
                    task.setUids(uids.toArray(new String[0]));
                }
            }
        } catch (Exception e) {
            log.error("从员工配置设置分配人失败：taskId={}", task.getId(), e);
            // Set empty array to avoid null pointer
            task.setUids(new String[0]);
        }
    }

    /**
     * Process task items for the new task
     */
    private List<MaintTaskItem> processTaskItems(String originalTaskId, String newTaskId,
                                               Map<String, List<MaintTaskItem>> taskItemsMap) {
        List<MaintTaskItem> originalItems = taskItemsMap.getOrDefault(originalTaskId, new ArrayList<>());
        if (CollectionUtils.isEmpty(originalItems)) {
            return new ArrayList<>();
        }

        try {
            List<MaintTaskItem> newItems = CopyDataUtil.copyList(originalItems, MaintTaskItem.class);
            newItems.forEach(item -> item.setTaskId(newTaskId));
            return newItems;
        } catch (Exception e) {
            log.error("处理任务项失败：originalTaskId={}, newTaskId={}", originalTaskId, newTaskId, e);
            return new ArrayList<>();
        }
    }

    /**
     * Save tasks and items in batch with validation
     */
    private void saveBatchWithValidation(List<MaintTask> taskList, List<MaintTaskItem> taskItemList) {
        try {
            if (CollectionUtils.isNotEmpty(taskList)) {
                maintTaskService.saveBatch(taskList);
                log.info("批量保存任务成功：数量={}", taskList.size());
            }

            if (CollectionUtils.isNotEmpty(taskItemList)) {
                maintTaskItemService.saveBatch(taskItemList);
                log.info("批量保存任务项成功：数量={}", taskItemList.size());
            }
        } catch (Exception e) {
            log.error("批量保存失败：tasks={}, items={}", taskList.size(), taskItemList.size(), e);
            throw new GlobalServiceException(GlobalResultMessage.of("保存任务数据失败"));
        }
    }

    private void buildAllStaffIds(MaintTask maintTask) {
        boolean staffsFlag = null != maintTask.getStaffIds() && maintTask.getStaffIds().length > StaticValue.ZERO;
        boolean teamsFlag = null != maintTask.getTeamIds() && maintTask.getTeamIds().length > StaticValue.ZERO;
        if (staffsFlag || teamsFlag) {
            log.info("已设置维护人员或维护班组，生成权限id集合");
            List<String> personIds = new ArrayList<>();
            if (teamsFlag) {
                RestResponse<List<String>> restResponse = baseServiceClient.getPersonIdsByTeamIds(maintTask.getTeamIds());
                if (!restResponse.isSuccess()) {
                    log.error("远程调用base-service出错");
                } else {
                    personIds.addAll(restResponse.getData());
                }
            }

            if (staffsFlag) {
                personIds.addAll(Arrays.asList(maintTask.getStaffIds()));
            }
            if (CollectionUtils.isNotEmpty(personIds)) {
                personIds = personIds.stream()
                        .distinct()
                        .collect(Collectors.toList());
                maintTask.setAllStaffIds(StringUtils.join(personIds.toArray(), ","));
            }

        }
    }

    // ==================== PRIVATE HELPER METHODS ====================

    /**
     * Validate input parameters for workflow operations
     */
    private void validateInputParameters(TaskCompleteParam taskCompleteParam, String taskId, Integer newStatus) {
        Assert.notNull(taskCompleteParam, "TaskCompleteParam cannot be null");
        Assert.hasText(taskId, "TaskId cannot be null or empty");
        Assert.notNull(newStatus, "NewStatus cannot be null");
        Assert.notNull(taskCompleteParam.getVariables(), "Variables cannot be null");
    }

    /**
     * Validate and get user context with proper error handling
     */
    private UserBaseInfo validateAndGetUserContext() {
        try {
            UserContext context = UserContextHolder.getContext();
            if (context == null) {
                throw new GlobalServiceException(GlobalResultMessage.of("用户上下文未初始化"));
            }

            UserBaseInfo userBaseInfo = context.getUserBaseInfo();
            if (userBaseInfo == null || StringUtils.isBlank(userBaseInfo.getUid())) {
                throw new GlobalServiceException(GlobalResultMessage.of("用户信息不完整"));
            }

            return userBaseInfo;
        } catch (Exception e) {
            log.error("获取用户上下文失败", e);
            throw new GlobalServiceException(GlobalResultMessage.of("用户认证失败，请重新登录"));
        }
    }

    /**
     * Extract assigns list from variables with type safety
     */
    private List<String> extractAssignsList(Map<String, Object> variables) {
        if (variables == null) {
            return new ArrayList<>();
        }

        Object assignsObj = variables.get("assigns");
        List<String> assigns = new ArrayList<>();

        try {
            if (assignsObj instanceof List) {
                List<?> list = (List<?>) assignsObj;
                assigns = list.stream()
                    .filter(Objects::nonNull)
                    .map(Object::toString)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
            } else if (assignsObj instanceof String) {
                String assignStr = (String) assignsObj;
                if (StringUtils.isNotBlank(assignStr)) {
                    assigns.add(assignStr);
                }
            } else if (assignsObj instanceof String[]) {
                String[] assignArray = (String[]) assignsObj;
                assigns = Arrays.stream(assignArray)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.warn("提取assigns列表失败，使用空列表: {}", e.getMessage());
        }

        return assigns;
    }

    /**
     * Get and validate task with comprehensive checks
     */
    private MaintTask getAndValidateTask(String taskId, Integer newStatus, UserBaseInfo userBaseInfo, Boolean forceSubmit) {
        MaintTask task = getTaskWithCache(taskId);

        if (task == null) {
            throw new GlobalServiceException(GlobalResultMessage.of("工单不存在或已被删除"));
        }

        // Check if status has already changed (prevent duplicate operations)
        if (Objects.equals(task.getStatus(), newStatus)) {
            log.warn(STATUS_CHANGED_MESSAGE + ",taskId={},newStatus={}", taskId, newStatus);
            throw new GlobalServiceException(GlobalResultMessage.of(STATUS_CHANGED_MESSAGE));
        }

        // Check user permissions unless force submit is enabled
        if (!Boolean.TRUE.equals(forceSubmit)) {
            validateUserPermissions(task, userBaseInfo);
        }

        return task;
    }

    /**
     * Get task with caching mechanism for performance optimization
     */
    private MaintTask getTaskWithCache(String taskId) {
        cacheLock.readLock().lock();
        try {
            MaintTask cachedTask = taskCache.get(taskId);
            if (cachedTask != null) {
                // Verify cache validity by checking if task still exists
                MaintTask freshTask = (MaintTask) maintTaskService.getOne(
                    new QueryWrapper<MaintTask>().lambda().select(MaintTask::getStatus).eq(MaintTask::getId, taskId)
                );
                if (freshTask != null && Objects.equals(cachedTask.getStatus(), freshTask.getStatus())) {
                    return cachedTask;
                }
            }
        } finally {
            cacheLock.readLock().unlock();
        }

        // Cache miss or invalid, fetch fresh data
        cacheLock.writeLock().lock();
        try {
            MaintTask task = (MaintTask) maintTaskService.getOne(
                new QueryWrapper<MaintTask>().lambda().eq(MaintTask::getId, taskId)
            );
            if (task != null) {
                taskCache.put(taskId, task);
            }
            return task;
        } catch (Exception e) {
            log.error("查询工单失败: taskId={}", taskId, e);
            throw new GlobalServiceException(GlobalResultMessage.of("查询工单信息失败"));
        } finally {
            cacheLock.writeLock().unlock();
        }
    }

    /**
     * Validate user permissions for task operations
     */
    private void validateUserPermissions(MaintTask task, UserBaseInfo userBaseInfo) {
        if (task.getUids() != null && task.getUids().length > 0) {
            List<String> allowedUsers = Arrays.asList(task.getUids());
            if (!allowedUsers.contains(userBaseInfo.getUid())) {
                throw new GlobalServiceException(GlobalResultMessage.of(PROCESS_CHANGED_MESSAGE));
            }
        }
    }

    /**
     * Insert workflow history nodes with error handling
     */
    private void insertWorkflowNodes(MaintTask task, UserBaseInfo userBaseInfo, String taskId,
                                   Integer newStatus, List<String> assigns, String comment) {
        try {
            // Insert done node
            maintTaskHistoryService.insertDoneNode(
                task.getProcessInstanceId(),
                userBaseInfo,
                taskId,
                task.getStatus(),
                task.getStatus(),
                OPERATION_SUBMIT,
                comment
            );

            // Insert status node
            maintTaskHistoryService.insertStatusNode(
                task.getProcessInstanceId(),
                userBaseInfo,
                taskId,
                task.getStatus(),
                newStatus,
                assigns,
                CANDIDATE_TASK
            );

            // Invalidate cache after successful operation
            invalidateTaskCache(taskId);

        } catch (Exception e) {
            log.error("插入工作流历史节点失败: taskId={}", taskId, e);
            throw new GlobalServiceException(GlobalResultMessage.of("工作流操作失败"));
        }
    }

    /**
     * Invalidate task cache entry
     */
    private void invalidateTaskCache(String taskId) {
        cacheLock.writeLock().lock();
        try {
            taskCache.remove(taskId);
        } finally {
            cacheLock.writeLock().unlock();
        }
    }
}
