package cn.getech.ehm.task.dto.task.repair;

import cn.getech.ehm.common.mp.handler.StringArrayTypeHandler;
import cn.getech.poros.framework.common.param.ApiParam;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

/**
 * 工单处理故障维修编辑参数
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "TaskFaultEditParam", description = "工单处理故障维修编辑参数")
public class TaskFaultEditParam extends ApiParam {

    @ApiModelProperty(value = "关联表id")
    private String id;

    @ApiModelProperty(value = "工单id")
    private String taskId;

    @ApiModelProperty(value = "实际停机开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginDowntime;

    @ApiModelProperty(value = "实际停机结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDowntime;

    @ApiModelProperty(value = "故障现象ids")
    private String[] faultPhenomenonIds;

    @ApiModelProperty(value = "故障现象扩展")
    private String faultPhenomenonRemark;

    @ApiModelProperty(value = "故障影响")
    private String[] faultInfluences;

    @ApiModelProperty(value = "故障原因ids")
    private String[] faultReasonIds;

    @ApiModelProperty(value = "故障原因扩展")
    private String faultReasonRemark;

    @ApiModelProperty(value = "处理措施ids")
    private String[] faultMeasuresIds;

    @ApiModelProperty(value = "处理措施扩展")
    private String faultMeasuresRemark;

    @ApiModelProperty(value = "附件id集合")
    private String[] mediaIds;

    private String[] faultStructureIds;

    @ApiModelProperty(value = "LOTO标志1是2否")
    private Integer lotoFlag;

    @ApiModelProperty(value = "LOTO选项")
    private String lotoContent;

    @ApiModelProperty("损坏原因")
    private String damageReason;

    private String[] tmpFileIds;

    private String[] lotoFileIds;
}
