package cn.getech.ehm.task.service;

import cn.getech.ehm.task.dto.plan.*;
import cn.getech.ehm.task.entity.PlanEquipmentTime;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.framework.common.service.IBaseService;
import java.util.List;
import java.util.Map;

/**
 * 计划任务维保时间 服务类
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
public interface IPlanEquipmentTimeService extends IBaseService<PlanEquipmentTime> {

        /**
         * 批量修改
         * @param rows
         */
        Boolean saveOrUpdateDto(List<PlanEquipmentTimeDto> rows, String planId, Boolean add);

        /**
         * 根据计划id获取维保对象集合
         * @param planId
         * @return
         */
        List<PlanEquipmentTimeDto> getListByPlanId(String planId);

        /**
         * 构造计划发单日期
         * @param planId
         * @return
         */
        Boolean createTriggerTime(String planId);

        /**
         * 构造下次计划发单日期
         * @param planId
         * @return
         */
        Boolean createNextTriggerTime(String planId, String equipmentId);

        /**
         * 获取计划单对应维保对象
         * @param planIds
         * @return
         */
        Map<String, List<SynPlanEquipmentTimeDto>> getMapByPlanIds(List<String> planIds);

        public void createNextTriggerTime(String planId, String equipmentId, UserBaseInfo userBaseInfo);

}