package cn.getech.ehm.task.dto.task.performance;

import cn.getech.ehm.common.mp.handler.StringArrayTypeHandler;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 个人工单信息
 *
 * <AUTHOR>
 * @date 2021-01-19
 */
@Data
public class PersonalTaskInfoDto {
    /**
     * 用户Id
     */
//    String uid;
    /**
     * 工单类型(1故障单2维保单)
     */
    Integer type;
    /**
     * 评分
     */
    Integer grade;

    /**
     * 开始维修时间
     */
    Date beginMaintTime;

    /**
     * 结束维修时间
     */
    Date endMaintTime;

    /**
     * 维护人员Ids
     */
    String allStaffIds;

    @TableField(typeHandler = StringArrayTypeHandler.class)
    String[] handler;

    @ApiModelProperty(value = "验收结果0未通过1通过")
    private Integer checkAcceptResult;
}
