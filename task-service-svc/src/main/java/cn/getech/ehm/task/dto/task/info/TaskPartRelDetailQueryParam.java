package cn.getech.ehm.task.dto.task.info;

import cn.getech.poros.framework.common.param.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 备件耗用成本统计 分页查询参数对象
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "备件耗用详情查询", description = "备件耗用详情查询参数")
public class TaskPartRelDetailQueryParam extends PageParam {

    @ApiModelProperty(value = "统计月份开始")
    @JsonFormat(pattern = "yyyy-MM", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM")
    private Date beginTime;

    @ApiModelProperty(value = "统计月份结束")
    @JsonFormat(pattern = "yyyy-MM", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM")
    private Date endTime;

    @ApiModelProperty(value = "维护设备id")
    @NotNull(message = "设备id不能为空")
    private String equipmentId;
}
