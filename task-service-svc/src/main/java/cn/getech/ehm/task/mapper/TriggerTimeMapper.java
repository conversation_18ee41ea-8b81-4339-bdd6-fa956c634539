package cn.getech.ehm.task.mapper;

import cn.getech.ehm.task.dto.plan.MaintPlanCountDto;
import cn.getech.ehm.task.dto.plan.TriggerTimeDto;
import cn.getech.ehm.task.entity.PlanTriggerTime;
import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 计划发单时间 Mapper 接口
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
@Repository
public interface TriggerTimeMapper extends BaseMapper<PlanTriggerTime> {
    /**
     * 获取截止日期内可开单集合
     * @param expireTime
     * @return
     */
    @SqlParser(filter = true)
    List<TriggerTimeDto> canCreateTaskDtos(@Param("expireTime") Date expireTime);

    /**
     * 获取下一次维保日期
     * @param planIds
     * @return
     */
    List<MaintPlanCountDto> getNextPlanDate(@Param("planIds") List<String> planIds, @Param("date") Date date);

    /**
     * 更新状态
     * @param triggerTimeIds
     * @param status
     * @return
     */
    @SqlParser(filter = true)
    Integer changeStatus(@Param("triggerTimeIds") List<String> triggerTimeIds, @Param("status") Integer status);

    /**
     * 获取可开单日期
     * @param beginTime
     * @param endTime
     * @return
     */
    List<PlanTriggerTime> getNormalListByTime(@Param("beginTime") Date beginTime, @Param("endTime") Date endTime);

}
