package cn.getech.ehm.task.service.impl;

import cn.getech.ehm.base.client.BaseServiceClient;
import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.common.context.UserContextHolder;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.task.dto.plan.*;
import cn.getech.ehm.task.entity.PlanEquipmentTime;
import cn.getech.ehm.task.enums.TaskJobType;
import cn.getech.ehm.task.mapper.PlanEquipmentTimeMapper;
import cn.getech.ehm.task.service.*;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.poros.framework.common.utils.StringPool;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 维保对象计划时间 服务实现类
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
@Slf4j
@Service
public class PlanEquipmentTimeServiceImpl extends BaseServiceImpl<PlanEquipmentTimeMapper, PlanEquipmentTime> implements IPlanEquipmentTimeService {

    @Autowired
    private PlanEquipmentTimeMapper planEquipmentTimeMapper;
    @Autowired
    private BaseServiceClient baseServiceClient;
    @Autowired
    @Lazy
    private IMaintPlanService maintPlanService;
    @Autowired
    @Lazy
    private ITriggerTimeService triggerTimeService;

    @Override
    public Boolean saveOrUpdateDto(List<PlanEquipmentTimeDto> rows, String planId, Boolean add) {
        this.deleteByPlanId(planId);
        if(CollectionUtils.isNotEmpty(rows)) {
            List<PlanEquipmentTime> planEquipmentTimes = new ArrayList<>(rows.size());
            for (PlanEquipmentTimeDto row : rows) {
                PlanEquipmentTime planEquipmentTime = CopyDataUtil.copyObject(row, PlanEquipmentTime.class);
                if(add){
                    //复制新增设置id为空
                    planEquipmentTime.setId(null);
                }
                planEquipmentTime.setPlanId(planId);
                planEquipmentTimes.add(planEquipmentTime);
            }
            return saveOrUpdateBatch(planEquipmentTimes);
        }
        return true;
    }

    @Override
    public List<PlanEquipmentTimeDto> getListByPlanId(String planId) {
        List<PlanEquipmentTimeDto> dtos = new ArrayList<>();

        LambdaQueryWrapper<PlanEquipmentTime> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(PlanEquipmentTime::getPlanId, planId);
        List<PlanEquipmentTime> planEquipmentTimes = planEquipmentTimeMapper.selectList(wrapper);
        if(CollectionUtils.isNotEmpty(planEquipmentTimes)){
            List<String> maintainerIds = new ArrayList<>();
            List<String> teamIds = new ArrayList<>();
            for(PlanEquipmentTime planEquipmentTime : planEquipmentTimes) {
                if(null != planEquipmentTime.getMaintainerIds() && planEquipmentTime.getMaintainerIds().length > 0){
                    maintainerIds.addAll(Arrays.asList(planEquipmentTime.getMaintainerIds()));
                }
                if(null != planEquipmentTime.getTeamIds() && planEquipmentTime.getTeamIds().length > 0){
                    teamIds.addAll(Arrays.asList(planEquipmentTime.getTeamIds()));
                }
            }
            maintainerIds = maintainerIds.stream().distinct().collect(Collectors.toList());
            teamIds = teamIds.stream().distinct().collect(Collectors.toList());
            Map<String, String> personNameMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(maintainerIds)) {
                RestResponse<Map<String, String>> personRes = baseServiceClient.getPersonMapByIds(maintainerIds.toArray(new String[maintainerIds.size()]));
                if(personRes.isOk()){
                    personNameMap = personRes.getData();
                }else{
                    log.error("获取指定维护人员失败");
                }
            }
            Map<String, String> teamNameMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(teamIds)) {
                RestResponse<Map<String, String>> teamRes = baseServiceClient.getTeamMapByIds(teamIds.toArray(new String[maintainerIds.size()]));
                if(teamRes.isOk()){
                    teamNameMap = teamRes.getData();
                }else{
                    log.error("获取指定维护班组失败");
                }
            }

            for(PlanEquipmentTime planEquipmentTime : planEquipmentTimes) {
                PlanEquipmentTimeDto dto = CopyDataUtil.copyObject(planEquipmentTime, PlanEquipmentTimeDto.class);
                if(null != dto.getMaintainerIds() && dto.getMaintainerIds().length > 0){
                    buildPersonNames(dto.getMaintainerIds(), personNameMap, dto);
                }
                if(null != dto.getTeamIds() && dto.getTeamIds().length > 0){
                    buildTeamNames(dto.getTeamIds(), teamNameMap, dto);
                }
                dtos.add(dto);
            }
        }
        return dtos;
    }

    /**
     * 构造维护人员名称集合
     * @param maintainerIds
     * @return
     */
    private void buildPersonNames(String[] maintainerIds, Map<String, String> personNameMap, PlanEquipmentTimeDto planEquipmentTimeDto){
        List<String> names = new ArrayList<>();
        List<PlanEquipmentDetailDto> detailDtos = new ArrayList<>();
        for(String maintainerId : maintainerIds){
            String name = personNameMap.get(maintainerId);
            if(StringUtils.isNotBlank(name)){
                names.add(name);
                PlanEquipmentDetailDto detailDto = new PlanEquipmentDetailDto();
                detailDto.setId(maintainerId);
                detailDto.setName(name);
                detailDtos.add(detailDto);
            }
        }
        if(CollectionUtils.isNotEmpty(names)){
            //拼接找到的名称
            planEquipmentTimeDto.setMaintainerNames(StringUtils.join(names.toArray(), StringPool.COMMA));
            planEquipmentTimeDto.setMaintainerDtos(detailDtos);
        }
    }

    /**
     * 构造维护班组名称集合
     * @param teamIds
     * @return
     */
    private void buildTeamNames(String[] teamIds, Map<String, String> teamNameMap, PlanEquipmentTimeDto planEquipmentTimeDto){
        List<String> names = new ArrayList<>();
        List<PlanEquipmentDetailDto> detailDtos = new ArrayList<>();
        for(String teamId : teamIds){
            String name = teamNameMap.get(teamId);
            if(StringUtils.isNotBlank(name)){
                names.add(name);
                PlanEquipmentDetailDto detailDto = new PlanEquipmentDetailDto();
                detailDto.setId(teamId);
                detailDto.setName(name);
                detailDtos.add(detailDto);
            }
        }
        if(CollectionUtils.isNotEmpty(names)){
            //拼接找到的名称
            planEquipmentTimeDto.setTeamNames(StringUtils.join(names.toArray(), StringPool.COMMA));
            planEquipmentTimeDto.setTeamDtos(detailDtos);
        }
    }

    private Boolean deleteByPlanId(String planId){
        LambdaQueryWrapper<PlanEquipmentTime> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(PlanEquipmentTime::getPlanId, planId);
        return planEquipmentTimeMapper.delete(wrapper) > 0;
    }
    @Override
    public Map<String, List<SynPlanEquipmentTimeDto>> getMapByPlanIds(List<String> planIds){
        Map<String, List<SynPlanEquipmentTimeDto>> map = new HashMap<>();
        List<SynPlanEquipmentTimeDto> synPlanEquipmentTimeDtos = planEquipmentTimeMapper.getListByPlanIds(planIds);
        if(CollectionUtils.isNotEmpty(synPlanEquipmentTimeDtos)) {
            map = synPlanEquipmentTimeDtos.stream().collect(Collectors.groupingBy(SynPlanEquipmentTimeDto::getPlanId));
        }
        return map;
    }

    @Override
    public Boolean createTriggerTime(String planId){
        //编辑后再次审核通过，需要清理未生成的计划单
        List<String> planIds = new ArrayList<>();
        planIds.add(planId);
        triggerTimeService.deleteByPlanIds(planIds);

        List<TriggerTimeDto> triggerTimeDtos = new ArrayList<>();
        Map<String, List<SynPlanEquipmentTimeDto>> planEquipmentTimeMap = this.getMapByPlanIds(planIds);
        //获取对象计划单信息以及维保计划信息
        SynMaintPlanDto synMaintPlanDto = maintPlanService.getSynById(planId);
        if(null != synMaintPlanDto) {
            if(synMaintPlanDto.getJobType().equals(TaskJobType.CBM.getValue())){
                //CBM不进行计划释放
                return true;
            }
            List<SynPlanEquipmentTimeDto> planEquipmentTimeDtos = planEquipmentTimeMap.get(planId);
            if (synMaintPlanDto.getTriggerType() == StaticValue.ONE) {
                //按上次工单日期开始，只需要计算一次开单日期
                buildLastTime(planEquipmentTimeDtos, synMaintPlanDto, triggerTimeDtos);
            } else if (synMaintPlanDto.getTriggerType() == StaticValue.TWO) {
                if (synMaintPlanDto.getPeriod().equals("0")) {
                    buildSingleTime(planEquipmentTimeDtos, synMaintPlanDto, triggerTimeDtos, null);
                } else{
                    //日、周、月
                    buildTime(planEquipmentTimeDtos, synMaintPlanDto, triggerTimeDtos);
                }
            }
            if (CollectionUtils.isNotEmpty(triggerTimeDtos)) {
                triggerTimeService.saveByParam(triggerTimeDtos);
            }
        }
        return true;
    }

    @Async("closeOrder")
    public void createNextTriggerTime(String planId, String equipmentId, UserBaseInfo userBaseInfo){
        UserContextHolder.switchContext(userBaseInfo);
        this.createNextTriggerTime(planId, equipmentId);
    }

    @Override
    public Boolean createNextTriggerTime(String planId, String equipmentId){
        List<String> planIds = new ArrayList<>();
        planIds.add(planId);

        List<TriggerTimeDto> triggerTimeDtos = new ArrayList<>();
        Map<String, List<SynPlanEquipmentTimeDto>> planEquipmentTimeMap = this.getMapByPlanIds(planIds);
        SynMaintPlanDto synMaintPlanDto = maintPlanService.getSynById(planId);
        //按上次工单日期开始，完成后计算下一次
        if(null != synMaintPlanDto && synMaintPlanDto.getTriggerType() == StaticValue.ONE) {
            //以上次只有一条
            SynPlanEquipmentTimeDto planEquipmentTimeDto = planEquipmentTimeMap.get(planId).get(0);

            List<SynPlanEquipmentTimeDto> dtos = new ArrayList<>();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());
            calendar.add(Calendar.DATE, synMaintPlanDto.getInterVal());
            planEquipmentTimeDto.setPlanDay(calendar.getTime());
            planEquipmentTimeDto.setPlanTime(calendar.getTime());
            dtos.add(planEquipmentTimeDto);
            buildSingleTime(dtos,synMaintPlanDto,triggerTimeDtos,Arrays.asList(new String[]{equipmentId}));
            if (CollectionUtils.isNotEmpty(triggerTimeDtos)) {
                triggerTimeService.saveByParam(triggerTimeDtos);
            }
        }
        return true;
    }

    private static Calendar initCalendar(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar;
    }

    /**
     * 已上次工单完成日期(需要按照设备定开单日期)
     */
    private void buildLastTime(List<SynPlanEquipmentTimeDto> planEquipmentTimeDtos, SynMaintPlanDto synMaintPlanDto, List<TriggerTimeDto> triggerTimeDtos ){
        List<String> equipmentIds = new ArrayList<>();
        if(null == synMaintPlanDto.getEquipmentIds() || synMaintPlanDto.getEquipmentIds().length <= 0){
            //没有设备id，为根据类型或位置取全部设备
            List<String> peEquipmentIds = maintPlanService.getEquipmentIds(synMaintPlanDto.getInfoLocationId(), synMaintPlanDto.getInfoCategoryId());
            if(CollectionUtils.isNotEmpty(peEquipmentIds)){
                equipmentIds.addAll(peEquipmentIds);
            }
        }else{
            equipmentIds.addAll(Arrays.asList(synMaintPlanDto.getEquipmentIds()));
        }
        if(CollectionUtils.isNotEmpty(equipmentIds)){
            buildSingleTime(planEquipmentTimeDtos,synMaintPlanDto,triggerTimeDtos,equipmentIds);
        }

    }

    /**
     * 单次
     */
    private void buildSingleTime(List<SynPlanEquipmentTimeDto> planEquipmentTimeDtos, SynMaintPlanDto synMaintPlanDto, List<TriggerTimeDto> triggerTimeDtos, List<String> equipmentIds ){
        //截止日期
        Date expireDate = null;
        if(null != synMaintPlanDto.getExpiryTime()){
            //将截止日期设置为最大小时分钟数
            expireDate = DateUtil.endOfDay(synMaintPlanDto.getExpiryTime());
        }
        for(SynPlanEquipmentTimeDto timeDto : planEquipmentTimeDtos) {
            //拼接日期
            Calendar planCalendar = initCalendar(timeDto.getPlanDay());
            Calendar timeCalendar = initCalendar(timeDto.getPlanTime());
            planCalendar.set(Calendar.HOUR_OF_DAY, timeCalendar.get(Calendar.HOUR_OF_DAY));
            planCalendar.set(Calendar.MINUTE, timeCalendar.get(Calendar.MINUTE));
            planCalendar.set(Calendar.SECOND, planCalendar.getActualMinimum(Calendar.SECOND));

            Date planDate = planCalendar.getTime();
            if(null != expireDate && planDate.after(expireDate)){
                //计划日期大于截止日期，直接停止
                continue;
            }
            TriggerTimeDto triggerTimeDto = new TriggerTimeDto();
            triggerTimeDto.setPlanId(synMaintPlanDto.getId());
            triggerTimeDto.setPlanEquipmentTimeId(timeDto.getId());
            triggerTimeDto.setPlanMaintTime(planDate);
            Date triggerTime = DateUtil.offsetHour(planDate, -synMaintPlanDto.getAdvanceDay());
            triggerTimeDto.setTriggerTime(triggerTime);

            if(CollectionUtils.isNotEmpty(equipmentIds)){
                for(String equipmentId : equipmentIds){
                    TriggerTimeDto dto = CopyDataUtil.copyObject(triggerTimeDto, TriggerTimeDto.class);
                    dto.setEquipmentId(equipmentId);
                    triggerTimeDtos.add(dto);
                }
            }else{
                triggerTimeDtos.add(triggerTimeDto);
            }
        }
    }

    /**
     * 每日
     * 每周 execVal中为1-7
     * 每月 execVal中为1-31，last;last为每月最后一天
     */
    private void buildTime(List<SynPlanEquipmentTimeDto> planEquipmentTimeDtos, SynMaintPlanDto synMaintPlanDto, List<TriggerTimeDto> triggerTimeDtos){

        //将截止日期设置为最大小时分钟数
        Date expireDate = DateUtil.endOfDay(synMaintPlanDto.getExpiryTime());
        //定时开单的日期(到天)都一样
        Calendar planCalendar = initCalendar(planEquipmentTimeDtos.get(0).getPlanDay());
        Date planDate = planCalendar.getTime();

        List<String> weeks = new ArrayList<>();
        List<String> monthDays = new ArrayList<>();
        List<String> months = new ArrayList<>();
        Boolean haveLast = false;
        if(synMaintPlanDto.getPeriod().equals("2")) {
            weeks = this.transformWeek(synMaintPlanDto.getExecVal());
        }else if(synMaintPlanDto.getPeriod().equals("3")){
            if(null != synMaintPlanDto.getExecVal() && synMaintPlanDto.getExecVal().length > 0) {
                monthDays = Arrays.asList(synMaintPlanDto.getExecVal());
            }
            //是否选择最后一天，需要根据每月时间自动计算
            haveLast = monthDays.contains("last");

            //设置了月份
            if(null != synMaintPlanDto.getMonthExecVal() && synMaintPlanDto.getMonthExecVal().length > 0){
                months = Arrays.asList(synMaintPlanDto.getMonthExecVal());
            }
        }

        //当前日期
        Date nowDate = new Date();

        //标记当前这一周是否正常生成记录
        Boolean weekFlag = false;
        //计划维护时间->截止日期，一直出单;跳过小于今天的单
        while(planDate.before(expireDate)) {
            if(synMaintPlanDto.getPeriod().equals("2")){
                int week = planCalendar.get(Calendar.DAY_OF_WEEK);
                if(!weeks.contains(String.valueOf(week))){
                    if(weekFlag && week == 7){
                        //周日，增加周期
                        planCalendar.add(Calendar.DATE, (synMaintPlanDto.getInterVal() - 1) * 7 + 1);
                    }else{
                        planCalendar.add(Calendar.DATE, 1);
                    }
                    planDate = planCalendar.getTime();
                    continue;
                }
            }else if(synMaintPlanDto.getPeriod().equals("3")){
                int day = planCalendar.get(Calendar.DAY_OF_MONTH);
                int month = planCalendar.get(Calendar.MONTH) + 1;
                int planDateLast = -1;
                if(haveLast){
                    //获取计划日期所属月份最大日期
                    planDateLast = planCalendar.getActualMaximum(Calendar.DAY_OF_MONTH);
                }
                //当前日期不在所选月份中
                if(!months.contains(String.valueOf(month))){
                    planCalendar.add(Calendar.DATE, 1);
                    planDate = planCalendar.getTime();
                    continue;
                }
                //所选日期不在集合内，且集合内设置last时不为当月最后一天
                if(!monthDays.contains(String.valueOf(day)) && planDateLast != day){
                    planCalendar.add(Calendar.DATE, 1);
                    planDate = planCalendar.getTime();
                    continue;
                }
            }

            //计划日期在明天凌晨之后，比审批通过时间早的日期不再开单
            for(SynPlanEquipmentTimeDto timeDto : planEquipmentTimeDtos) {
                Calendar timeCalendar = initCalendar(timeDto.getPlanTime());
                planCalendar.set(Calendar.HOUR_OF_DAY, timeCalendar.get(Calendar.HOUR_OF_DAY));
                planCalendar.set(Calendar.MINUTE, timeCalendar.get(Calendar.MINUTE));
                planCalendar.set(Calendar.SECOND, planCalendar.getActualMinimum(Calendar.SECOND));
                planDate = planCalendar.getTime();
                if (planDate.after(nowDate)) {
                    //出单日期和计划日期差距为间隔天数
                    Date triggerTime = DateUtil.offsetHour(planDate, -synMaintPlanDto.getAdvanceDay());
                    TriggerTimeDto triggerTimeDto = new TriggerTimeDto();
                    triggerTimeDto.setPlanId(synMaintPlanDto.getId());
                    triggerTimeDto.setPlanEquipmentTimeId(timeDto.getId());
                    triggerTimeDto.setTriggerTime(triggerTime);
                    triggerTimeDto.setPlanMaintTime(planDate);
                    triggerTimeDtos.add(triggerTimeDto);
                    weekFlag = true;
                }
            }
            //按照计划开单日期、间隔天数，计算后续开单时间
            if(synMaintPlanDto.getPeriod().equals("1") && synMaintPlanDto.getInterVal() > 1){
                //每日触发且间隔天数大于1
                planCalendar.add(Calendar.DATE, synMaintPlanDto.getInterVal());
            }else if(synMaintPlanDto.getPeriod().equals("2") && synMaintPlanDto.getInterVal() > 1){
                //每周触发且间隔天数大于1
                int week = planCalendar.get(Calendar.DAY_OF_WEEK);
                if(weekFlag && week == 7){
                    //周日，增加周期
                    planCalendar.add(Calendar.DATE, (synMaintPlanDto.getInterVal() - 1) * 7 + 1);
                }else{
                    planCalendar.add(Calendar.DATE, 1);
                }
            }else{
                planCalendar.add(Calendar.DATE, 1);
            }
            planDate = planCalendar.getTime();
        }
    }

    /**
     * 周转换，国际化中周日起始
     * @param weeks
     * @return
     */
    private List<String> transformWeek(String[] weeks){
        List<String> list = new ArrayList<>(weeks.length);
        for(String week : weeks){
            if(week.equals("7")){
                //周日标记转换
                list.add("1");
            }else{
                int numWeek = Integer.valueOf(week) + 1;
                list.add(String.valueOf(numWeek));
            }
        }
        return list;
    }
}
