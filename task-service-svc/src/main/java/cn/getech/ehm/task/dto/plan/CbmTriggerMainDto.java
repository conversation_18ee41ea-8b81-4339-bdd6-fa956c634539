package cn.getech.ehm.task.dto.plan;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * cbm触发器分组dto
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "CbmTriggerMainDto", description = "cbm触发器分组dto")
public class CbmTriggerMainDto {

    @ApiModelProperty(value = "触发器分组num")
    private Integer groupNum;

    @ApiModelProperty(value = "触发器详情列表")
    List<CbmTriggerDetailDto> detailDtos;

}