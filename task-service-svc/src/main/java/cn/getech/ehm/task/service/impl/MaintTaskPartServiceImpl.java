package cn.getech.ehm.task.service.impl;

import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.part.dto.PartDetailDto;
import cn.getech.ehm.part.dto.PartSearchDto;
import cn.getech.ehm.task.dto.task.info.TaskPartRelDetailQueryParam;
import cn.getech.ehm.task.dto.task.info.TaskPartRelQueryParam;
import cn.getech.ehm.task.dto.task.info.TaskPartRelReportDto;
import cn.getech.ehm.task.dto.task.part.TaskPartDto;
import cn.getech.ehm.task.dto.task.part.TaskPartEditDto;
import cn.getech.ehm.task.entity.MaintTaskPart;
import cn.getech.ehm.task.mapper.MaintTaskPartMapper;
import cn.getech.ehm.task.service.IMaintTaskPartService;
import cn.getech.ehm.part.client.PartClient;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 维护工单、备件关联表 服务实现类
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
@Slf4j
@Service
public class MaintTaskPartServiceImpl extends BaseServiceImpl<MaintTaskPartMapper, MaintTaskPart> implements IMaintTaskPartService {

    @Autowired
    private MaintTaskPartMapper maintTaskPartMapper;
    @Autowired
    private PartClient partClient;

    @Override
    public Boolean saveOrUpdateList(List<TaskPartEditDto> parts, String taskId) {
        this.deleteByTaskId(taskId);
        if(CollectionUtils.isNotEmpty(parts)) {
            List<MaintTaskPart> entities = CopyDataUtil.copyList(parts, MaintTaskPart.class);
            entities.stream().forEach(entity -> entity.setTaskId(taskId));
            return this.saveOrUpdateBatch(entities);
        }
        return true;
    }

    @Override
    public Boolean deleteByTaskId(String taskId) {
        LambdaQueryWrapper<MaintTaskPart> wrapper = Wrappers.<MaintTaskPart>lambdaQuery();
        wrapper.eq(MaintTaskPart::getTaskId, taskId);
        return maintTaskPartMapper.delete(wrapper) > 0;
    }

    @Override
    public List<TaskPartDto> getByTaskId(String taskId) {
        List<TaskPartDto> taskPartDtos = maintTaskPartMapper.getListByTaskId(taskId);
        //获取备件详情
        if(CollectionUtils.isNotEmpty(taskPartDtos)) {
            List<String> partIds = taskPartDtos.stream().map(TaskPartDto::getPartId)
                    .collect(Collectors.toList()).stream().collect(Collectors.toList());
            PartSearchDto searchDto = new PartSearchDto();
            searchDto.setPartIds(partIds);
            searchDto.setObtainProp(false);
            RestResponse<Map<String, PartDetailDto>> listRestResponse = partClient.getPartMapByIds(searchDto);
            if (!listRestResponse.isSuccess()) {
                log.error("远程调用part-service出错");
            } else {
                Map<String, PartDetailDto> stringListMap = listRestResponse.getData();
                taskPartDtos.stream().forEach(dto -> {
                    PartDetailDto part = stringListMap.get(dto.getPartId());
                    if (null != part) {
                        dto.setPartName(part.getPartName());
                        dto.setPartCategoryName(part.getPartCategoryName());
                        dto.setPartSpecification(part.getPartSpecification());
                        dto.setPicId(part.getPicId());
                        dto.setPicUrl(part.getPicUrl());
                    }
                });
            }
        }

        return taskPartDtos;
    }

    @Override
    public PageResult<TaskPartRelReportDto> getTaskPartRelPageList(TaskPartRelQueryParam taskPartRelQueryParam) {

        taskPartRelQueryParam.setSortValue(buildTaskPartRelSql(taskPartRelQueryParam.getSortPros()));

        if (StringUtils.isNotEmpty(taskPartRelQueryParam.getInnerEquipmentCategory())){
            taskPartRelQueryParam.setEquipmentCategory(taskPartRelQueryParam.getInnerEquipmentCategory().split(","));
        }

        if (StringUtils.isNotEmpty(taskPartRelQueryParam.getInnerEquipmentLocation())){
            taskPartRelQueryParam.setEquipmentLocation(taskPartRelQueryParam.getInnerEquipmentLocation().split(","));
        }

        Page<TaskPartRelReportDto> page = new Page<>(taskPartRelQueryParam.getPageNo(), taskPartRelQueryParam.getLimit());
        Page<TaskPartRelReportDto> taskPartRelPageDtoPage = maintTaskPartMapper.getTaskPartRelPageList(page, taskPartRelQueryParam);
        PageResult<TaskPartRelReportDto> result =PageResult.<TaskPartRelReportDto>builder().records(taskPartRelPageDtoPage.getRecords()).total(taskPartRelPageDtoPage.getTotal()).build();
        return Optional.ofNullable(result).orElse(new PageResult<>());
    }

    @Override
    public PageResult<TaskPartRelReportDto> getTaskPartRelDetailPageList(TaskPartRelDetailQueryParam taskPartRelDetailQueryParam) {
        Page<TaskPartRelReportDto> page = new Page<>(taskPartRelDetailQueryParam.getPageNo(), taskPartRelDetailQueryParam.getLimit());
        Page<TaskPartRelReportDto> taskPartRelPageDtoPage = maintTaskPartMapper.getTaskPartRelDetailPageList(page, taskPartRelDetailQueryParam);
        PageResult<TaskPartRelReportDto> result =PageResult.<TaskPartRelReportDto>builder().records(taskPartRelPageDtoPage.getRecords()).total(taskPartRelPageDtoPage.getTotal()).build();

        return Optional.ofNullable(result).orElse(new PageResult<>());
    }

    @Override
    public List<String> checkPartUsed(String[] partIds) {
        return maintTaskPartMapper.checkPartUsed(partIds);
    }

    /**
     * 构建排序sql
     *
     * @param sortPros
     */
    private String buildTaskPartRelSql(Map<String, String> sortPros) {
        if (null == sortPros || sortPros.isEmpty()) {
            return " ORDER BY e.update_time DESC";
        }
        //是否已有排序字段
        Boolean flag = false;
        StringBuffer sb = new StringBuffer(" ORDER BY ");
        for (Map.Entry<String, String> entity : sortPros.entrySet()) {
            if (flag) {
                sb.append(", ");
            }
            if (entity.getKey().equals("createTime")) {
                flag = true;
                sb.append("e.create_time ").append(entity.getValue());
            } else if (entity.getKey().equals("equipmentCode")) {
                flag = true;
                sb.append("e.`code` ").append(entity.getValue());
            } else if (entity.getKey().equals("equipmentName")) {
                flag = true;
                sb.append("e.`name` ").append(entity.getValue());
            } else if (entity.getKey().equals("equipmentLocation")) {
                flag = true;
                sb.append("e.location_id ").append(entity.getValue());
            } else if (entity.getKey().equals("equipmentCategory")) {
                flag = true;
                sb.append("e.category_id ").append(entity.getValue());
            } else if (entity.getKey().equals("totalActualQty")) {
                flag = true;
                sb.append("totalActualQty ").append(entity.getValue());
            } else if (entity.getKey().equals("totalPrice")) {
                flag = true;
                sb.append("totalPrice ").append(entity.getValue());
            }
        }
        if (flag) {
            return sb.toString();
        } else {
            return " ORDER BY e.update_time DESC";
        }
    }

}
