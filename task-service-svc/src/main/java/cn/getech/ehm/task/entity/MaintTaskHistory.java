package cn.getech.ehm.task.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import cn.getech.poros.framework.common.annotation.Excel;

/**
 * <p>
 * 工单历史
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("maint_task_history")
public class MaintTaskHistory extends BaseEntity {

    private static final long serialVersionUID=1L;

    @TableField("deleted")
    private Integer deleted;

    @TableField("tenant_id")
    private String tenantId;

    /**
     * 流程id
     */
    @TableField("process_instance_id")
    private String processInstanceId;

    /**
     * 环节id
     */
    @TableField("activity_id")
    private String activityId;

    /**
     * 环节名
     */
    @TableField("activity_name")
    private String activityName;

    /**
     * 任务id
     */
    @TableField("task_id")
    private String taskId;

    /**
     * 候选人/接单人
     */
    @TableField("assignee_name")
    private String assigneeName;

    private String assigneeUid;

    /**
     * 备注
     */
    @TableField("comment")
    private String comment;

    /**
     * 开始时间
     */
    @TableField("start_time")
    private Date startTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    private Date endTime;

    /**
     * 处理状态
     */
    @TableField("operator")
    private String operator;

    /**
     * 已读状态
     */
    @TableField("read_status")
    private String readStatus;

    /**
     * 任务处理中/任务已完成
     */
    @TableField("task_status")
    private String taskStatus;

    private String maintTaskId;

    private Integer sort;

    private String oldStatus;

    private String newStatus;


}
