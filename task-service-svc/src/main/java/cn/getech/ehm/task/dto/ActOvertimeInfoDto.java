package cn.getech.ehm.task.dto;

import cn.getech.poros.framework.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;


/**
 * <pre>
 * 节点时限记录 返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2023-03-15
 */
@Data
@ApiModel(value = "ActOvertimeInfoDto", description = "节点时限记录返回数据模型")
public class ActOvertimeInfoDto{

    @ApiModelProperty(value = "")
    @Excel(name="",cellType = Excel.ColumnType.STRING)
    private String id;

    @ApiModelProperty(value = "remark")
    @Excel(name="remark",cellType = Excel.ColumnType.STRING )
    private String remark;

    /**
     * 流程id
     */
    @TableField("proc_ins_id")
    private String procInsId;

    /**
     * 任务id
     */
    @TableField("task_id")
    private String taskId;

    /**
     * 任务名
     */
    @TableField("task_name")
    private String taskName;

    /**
     * 任务开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date taskBeginTime;

    /**
     * 任务逾期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date taskEndTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date taskRealTime;

    /**
     * 流程key
     */
    @TableField("proc_def_key")
    private String procDefKey;

    /**
     * 流程开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date procBeginTime;

    /**
     * 流程逾期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date procEndTime;

}