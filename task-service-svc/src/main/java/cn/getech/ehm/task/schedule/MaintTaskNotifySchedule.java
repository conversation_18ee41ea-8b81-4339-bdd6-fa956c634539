package cn.getech.ehm.task.schedule;

import cn.getech.ehm.base.client.BaseServiceClient;
import cn.getech.ehm.base.dto.MaintPersonDto;
import cn.getech.ehm.common.context.UserContextHolder;
import cn.getech.ehm.system.client.NotifyClient;
import cn.getech.ehm.task.entity.MaintTask;
import cn.getech.ehm.task.entity.MaintTaskConfig;
import cn.getech.ehm.task.entity.MaintTaskHistory;
import cn.getech.ehm.task.enums.TaskOperationType;
import cn.getech.ehm.task.enums.TaskStatusType;
import cn.getech.ehm.task.enums.TaskType;
import cn.getech.ehm.task.handler.ActivitiTaskNotifyHandler;
import cn.getech.ehm.task.service.IMaintTaskConfigService;
import cn.getech.ehm.task.service.IMaintTaskHistoryService;
import cn.getech.ehm.task.service.IMaintTaskService;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
public class MaintTaskNotifySchedule {
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private IMaintTaskService maintTaskService;
    @Autowired
    private IMaintTaskConfigService maintTaskConfigService;
    @Autowired
    private IMaintTaskHistoryService maintTaskHistoryService;
    @Autowired
    @Lazy
    private ActivitiTaskNotifyHandler activitiTaskNotifyHandler;
    @Autowired
    NotifyClient notifyClient;
    @Autowired
    private BaseServiceClient baseServiceClient;

    private final static String PLAN_RELEASE_TASK_LOCK = "EHM:TASK:NOTIFY_LOCK";

    /**
     * 每分钟触发一次扫描
     */
    @Scheduled(cron = "0 0/1 * * * ?")
    public void realDeal() {
        RLock lock = redissonClient.getLock(PLAN_RELEASE_TASK_LOCK);
        try {
            boolean b = lock.tryLock(0, -1, TimeUnit.SECONDS);
            if (!b) {
                log.error("未获取到锁");
                return;
            }
        } catch (Exception e) {
            log.error("锁竞争失败，跳过释放:{}", e.getMessage());
            return;
        }
        try {
            this.scanTask();
        } catch (Exception e) {
            e.printStackTrace();
            log.error("程序执行报错");
        } finally {
            //判断是否有锁对象，以及是否是同一个锁
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();  //解锁
            }
        }
    }

    private final static String TASK_SEND_FALG = "EHM:TASK:NOTIFY_SEND_FLAG";

    public void scanTask() {
        UserContextHolder.defaultContext();
        UserBaseInfo userBaseInfo = UserContextHolder.getContext().getUserBaseInfo();
        Date now = new Date();
        Calendar calendar = Calendar.getInstance();
        // 设置时间为20:00:00，并清除毫秒
        calendar.set(Calendar.HOUR_OF_DAY, 20);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        // 转换为Date对象
        Date begin = calendar.getTime();
        calendar.add(Calendar.DAY_OF_YEAR, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 8);
        Date end = calendar.getTime();
        MaintTaskConfig durationConfig = maintTaskConfigService.getConfig("" + TaskType.BREAKDOWN.getValue(), "" + TaskOperationType.NOTIFY_DURATION_RULE.getValue());
        MaintTaskConfig freqConfig = maintTaskConfigService.getConfig("" + TaskType.BREAKDOWN.getValue(), "" + TaskOperationType.NOTIFY_FREQ_RULE.getValue());
        String durationValue = durationConfig != null ? durationConfig.getConfigContent() : "0";
        String freqValue = freqConfig != null ? freqConfig.getConfigContent() : "1";
        List<MaintTask> list = maintTaskService.list(new QueryWrapper<MaintTask>().lambda()
                .eq(MaintTask::getType, TaskType.BREAKDOWN.getValue())
                .notIn(MaintTask::getStatus, TaskStatusType.CLOSED.getValue(), TaskStatusType.EXCEPTION_CLOSED.getValue(),
                        TaskStatusType.DISPATCH.getValue(), TaskStatusType.HANDLE.getValue(), TaskStatusType.PROCESSING.getValue(),
                        TaskStatusType.HAND_UP_AUDIT_PASS.getValue()));
        List<String> taskIdList = list.stream().map(item -> item.getProcessInstanceId()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(taskIdList)) {
            log.info("未找到需要发送通知的工单，本轮跳过");
            return;
        }
        List<MaintTaskHistory> taskHistoryList = maintTaskHistoryService.list(new QueryWrapper<MaintTaskHistory>().lambda()
                .in(MaintTaskHistory::getProcessInstanceId, taskIdList)
                .eq(MaintTaskHistory::getTaskStatus, 0));
        Map<String, MaintTaskHistory> collect = taskHistoryList.stream().collect(Collectors.toMap(item -> item.getMaintTaskId(), item -> item, (v1, v2) -> v1));
        List<MaintTask> finalResult = Lists.newArrayList();
        for (MaintTask maintTask : list) {
            MaintTaskHistory maintTaskHistory = collect.get(maintTask.getId());
            if (ObjectUtils.isEmpty(maintTaskHistory)) {
                continue;
            }
            Boolean b = redisTemplate.opsForValue().setIfAbsent(TASK_SEND_FALG + maintTask.getId(), "1", Long.parseLong(freqValue), TimeUnit.MINUTES);
            if (!b) {
                //已经发送过，本次不发送，保证持续周期内只发送一次得。
                continue;
            }
            long between = DateUtil.between(maintTaskHistory.getStartTime(), now, DateUnit.MINUTE);
            if (new BigDecimal(between).compareTo(new BigDecimal(freqValue)) == 1) {
                //持续时长大于配置时长
                finalResult.add(maintTask);
            }
        }
        Boolean flag = false;
        if (now.after(begin) && now.before(end)) {
            flag = true;
            log.info("当前不在厂务人员通知时效内");
        }
        if (CollectionUtils.isNotEmpty(finalResult)) {
            log.info("本次需要通知{}个工单", finalResult.size());
            List<String> uids = finalResult.stream().map(item -> Arrays.asList(item.getUids())).flatMap(item -> item.stream()).collect(Collectors.toList());
            RestResponse<List<MaintPersonDto>> maintPersonDtoByUids = baseServiceClient.getMaintPersonDtoByUids(uids.toArray(new String[uids.size()]));
            if (!maintPersonDtoByUids.isOk()) {
                return;
            }
            List<MaintPersonDto> data = maintPersonDtoByUids.getData();
            Map<String, MaintPersonDto> personMap = data.stream().collect(Collectors.toMap(item -> item.getUid(), item -> item, (v1, v2) -> v1));
            for (MaintTask maintTask : finalResult) {
                if (ArrayUtil.isEmpty(maintTask.getUids())) {
                    continue;
                }
                MaintTaskHistory maintTaskHistory = collect.get(maintTask.getId());
                List<String> targetUids = Lists.newArrayList();
                for (String uid : Arrays.asList(maintTask.getUids())) {
                    MaintPersonDto maintPersonDto = personMap.get(uid);
                    //专业类型不是厂务的才发送
                    if (!flag && maintPersonDto.getMajor() != 20) {
                        targetUids.add(uid);
                    }
                }
                if (CollectionUtils.isEmpty(targetUids)) {
                    continue;
                }
                activitiTaskNotifyHandler.send(maintTask, maintTaskHistory, userBaseInfo, Long.parseLong(durationValue), targetUids);
            }
        }
    }
}
