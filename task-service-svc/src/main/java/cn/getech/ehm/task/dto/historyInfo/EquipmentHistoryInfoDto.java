package cn.getech.ehm.task.dto.historyInfo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * <pre>
 *  返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-12-06
 */
@Data
@ApiModel(value = "EquipmentHistoryInfoDto", description = "返回数据模型")
public class EquipmentHistoryInfoDto{
    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty("工单id")
    private String taskId;

    @ApiModelProperty("工单code")
    private String taskCode;

    @ApiModelProperty("工单名称")
    private String taskName;

    @ApiModelProperty("变更前设备id")
    private String oldEquipmentId;

    @ApiModelProperty("变更前设备名称")
    private String oldEquipmentName;

    @ApiModelProperty("变更前设备code")
    private String oldEquipmentCode;

    @ApiModelProperty("变更前设备状态")
    private String oldEquipmentStatus;

    @ApiModelProperty("变更后设备id")
    private String newEquipmentId;

    @ApiModelProperty("变更后设备名称")
    private String newEquipmentName;

    @ApiModelProperty("变更后设备code")
    private String newEquipmentCode;

    @ApiModelProperty("变更后设备状态")
    private String newEquipmentStatus;

    @ApiModelProperty("是否变更(0false1true)")
    private Integer changed;

    @ApiModelProperty("租户ID")
    private String tenantId;

    @ApiModelProperty("是否删除(0false1true)")
    private Integer deleted;

    @ApiModelProperty("变更原因")
    private String remark;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty("事前沟通人id")
    private String communicationerId;

    @ApiModelProperty("事前沟通人名称")
    private String communicationerName;

    @ApiModelProperty("变更人")
    private String creater;
}