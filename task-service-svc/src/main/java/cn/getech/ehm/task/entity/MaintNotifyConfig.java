package cn.getech.ehm.task.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import cn.getech.poros.framework.common.annotation.Excel;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("maint_notify_config")
public class MaintNotifyConfig extends BaseEntity {

    private static final long serialVersionUID=1L;

    @TableField("deleted")
    private Integer deleted;

    @TableField("tenant_id")
    private String tenantId;

    /**
     * 配置类型
     */
    @TableField("type")
    private Integer type;

    /**
     * 触发方式
     */
    @TableField("time_type")
    private Integer timeType;

    /**
     * 时间间隙
     */
    @TableField("time_gap")
    private Integer timeGap;

    /**
     * 提醒目标用户uids
     */
    @TableField("target_uids")
    private String targetUids;

    /**
     * 临期时间
     */
    @TableField("deadline_time")
    private Integer deadlineTime;

    /**
     * 临期推送用户uid
     */
    @TableField("deadline_uids")
    private String deadlineUids;


}
