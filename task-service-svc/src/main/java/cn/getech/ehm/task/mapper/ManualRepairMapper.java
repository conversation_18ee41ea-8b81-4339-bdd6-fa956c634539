package cn.getech.ehm.task.mapper;

import cn.getech.ehm.task.dto.repair.*;
import cn.getech.ehm.task.entity.ManualRepair;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import cn.getech.ehm.system.dto.CustomerInfoEquipmentDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.util.Date;
import java.util.List;

/**
 * 人工报修 Mapper 接口
 *
 * <AUTHOR>
 * @since 2020-08-06
 */
@Repository
public interface ManualRepairMapper extends BaseMapper<ManualRepair> {

    /**
     * pc获取故障报修列表
     * @param page
     * @param manualRepairQueryParam
     * @return
     */
    Page<ManualRepairListDto> manualPageDto(Page<ManualRepairDto> page,
                                        @Param("param") ManualRepairQueryParam manualRepairQueryParam);


    /**
     * 查询导出数据
     * @param queryParam
     * @return
     */
    List<ManualRepairExcelDto> manualExcelDto(@Param("param") ManualRepairQueryParam queryParam);
    /**
     * 维保获取故障单列表
     * @param page
     * @param queryParam
     * @return
     */
    Page<ManualRepairDetailDto> detailList(Page<DetailQueryParam> page,
                                            @Param("param") DetailQueryParam queryParam);


    /**
     * app获取远程诊断列表
     * @param page
     * @param repairAppQueryParam
     * @return
     */
    Page<ManualRepairAppDto> appManualPageDto(Page<ManualRepairAppDto> page,
                                              @Param("param") RepairAppQueryParam repairAppQueryParam);

    /**
     * 获取查询
     * @param list
     * @return
     */
    List getManualRepairCount(@Param("list") List<CustomerInfoEquipmentDto> list,
                              @Param("startTime") Date startTime,
                              @Param("endTime") Date endTime);

}
