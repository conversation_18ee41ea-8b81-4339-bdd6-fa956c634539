package cn.getech.ehm.task.entity;

import cn.getech.ehm.common.mp.handler.IntegerArrayTypeHandler;
import cn.getech.ehm.common.mp.handler.StringArrayTypeHandler;
import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

import java.util.Date;

/**
 * 通知
 * <AUTHOR>
 * @since 2020-07-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("maint_notify")
public class MaintNotify extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 类型1临期通知2超期3异常
     */
    @TableField("type")
    private Integer type;

    /**
     * 来源类型1维保计划2工单
     */
    @TableField("source_type")
    private Integer sourceType;

    /**
     * 关联类型id
     */
    @TableField("source_id")
    private String sourceId;

    /**
     * 计划是否启用/工单是否未通知
     */
    @TableField("enable")
    private Boolean enable;

    /**
     * 提前释放小时
     */
    @TableField("advance_hour")
    private Integer advanceHour;

    /**
     * 通知方式
     */
    @TableField(value = "notify_methods", jdbcType = JdbcType.VARCHAR, typeHandler = IntegerArrayTypeHandler.class)
    private Integer[] notifyMethods;

    /**
     * 通知对象
     */
    @TableField(value = "notify_objects", jdbcType = JdbcType.VARCHAR, typeHandler = IntegerArrayTypeHandler.class)
    private Integer[] notifyObjects;

    /**
     * 设备管理人员
     */
    @TableField(value = "equipment_manager", jdbcType = JdbcType.VARCHAR, typeHandler = IntegerArrayTypeHandler.class)
    private Integer[] equipmentManager;

    /**
     * 指定维护人员
     */
    @TableField(value = "maintainer_ids", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] maintainerIds;

    /**
     * 指定维护班组
     */
    @TableField(value = "team_ids", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] teamIds;

    /**
     * 自定义通知用户
     */
    @TableField(value = "custom_uids", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] customUids;

    /**
     * 自定义通知角色
     */
    @TableField(value = "custom_roles", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] customRoles;

    /**
     * 临期/超期通知时间
     */
    @TableField("notify_time")
    private Date notifyTime;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

}
