package cn.getech.ehm.task.dto.task.info;

import cn.getech.ehm.common.util.excel.FormExcel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 工单缺陷单导出
 */
@Data
@ApiModel(value = "MaintTaskDefectExcelDto", description = "工单缺陷单导出")
public class MaintTaskDefectExcelDto {

    @ApiModelProperty(value = "工单名称/编号")
    @FormExcel(name = "工单编号/名称", cellType = FormExcel.ColumnType.STRING)
    private String codeAndName;

    @ApiModelProperty(value = "工单状态")
    @FormExcel(name = "工单状态", cellType = FormExcel.ColumnType.STRING)
    private String status;

    @ApiModelProperty(value = "紧急程度")
    @FormExcel(name = "紧急程度", cellType = FormExcel.ColumnType.STRING)
    private String urgencyName;

    @ApiModelProperty(value = "专业类别")
    @FormExcel(name = "专业类别", cellType = FormExcel.ColumnType.STRING)
    private String majorName;

    @ApiModelProperty(value = "设备名称")
    @FormExcel(name = "设备名称", cellType = FormExcel.ColumnType.STRING)
    private String equipmentName;

    @ApiModelProperty(value = "设备编码")
    @FormExcel(name = "设备编码", cellType = FormExcel.ColumnType.STRING)
    private String equipmentCode;

    @ApiModelProperty(value = "位置")
    @FormExcel(name = "位置", cellType = FormExcel.ColumnType.STRING)
    private String parentAllName;

    @ApiModelProperty(value = "缺陷单名称")
    @FormExcel(name = "缺陷名称", cellType = FormExcel.ColumnType.STRING)
    private String defectName;

    @ApiModelProperty("是否超期")
    @FormExcel(name = "是否超期", cellType = FormExcel.ColumnType.STRING, readConverterExp = "false=否,true=是")
    private Boolean isOverTime;

    @ApiModelProperty(value = "计划维保日期时间")
    @FormExcel(name = "计划维保日期时间", cellType = FormExcel.ColumnType.STRING, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date planMaintTime;

    @ApiModelProperty(value = "创建时间")
    @FormExcel(name = "创建时间", cellType = FormExcel.ColumnType.STRING, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty("工单截止时间")
    @FormExcel(name = "截止时间", cellType = FormExcel.ColumnType.STRING, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date taskDeadlineDate;

    @ApiModelProperty("工单开始时间")
    @FormExcel(name = "缺陷开始时间", cellType = FormExcel.ColumnType.STRING, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date beginMaintTime;

    @ApiModelProperty("工单结束时间")
    @FormExcel(name = "缺陷结束时间", cellType = FormExcel.ColumnType.STRING, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endMaintTime;

    @ApiModelProperty("实际工时(去除挂起时间)分钟")
    private Long workingTime;

    @ApiModelProperty("实际工时(去除挂起时间)小时")
    @FormExcel(name = "工单用时(小时)", cellType = FormExcel.ColumnType.NUMERIC)
    private BigDecimal workingTimeHour;

    @ApiModelProperty("关闭原因/转缺陷原因")
    @FormExcel(name = "关闭原因", cellType = FormExcel.ColumnType.STRING)
    private String closeReason;

    @ApiModelProperty("接单人/处理人name")
    @FormExcel(name = "处理人员", cellType = FormExcel.ColumnType.STRING)
    private String handlerName;

    @ApiModelProperty(value = "更新时间")
    @FormExcel(name = "更新时间", cellType = FormExcel.ColumnType.STRING, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}