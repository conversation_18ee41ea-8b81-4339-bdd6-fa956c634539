package cn.getech.ehm.task.dto.task.info;

import cn.getech.ehm.task.dto.task.assist.MaintTaskAssistListDto;
import cn.getech.ehm.task.dto.task.item.TaskItemDetailDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 维护工单离线编辑 返回数据模型
 */
@Data
@ApiModel(value = "MaintTaskCacheEditDto", description = "维护工单离线编辑返回数据模型")
public class MaintTaskCacheEditDto {

    @ApiModelProperty(value = "工单id")
    @NotBlank(message = "工单id不能为空")
    private String id;

    @ApiModelProperty(value = "状态")
    @NotNull(message = "工单状态不能为空")
    private Integer status;

    @ApiModelProperty(value = "关闭原因")
    private String closeReason;

    @ApiModelProperty("点检异常处理方式")
    private Integer ecErrorResult;

    @ApiModelProperty("点检异常处理结果")
    private String ecErrorDealContent;

    @ApiModelProperty(value = "设备id")
    @NotBlank(message = "设备id不能为空")
    private String equipmentId;

    @ApiModelProperty(value = "设备运行状态")
    private Integer equipmentRunningStatus;

    @ApiModelProperty(value = "紧急程度/优先程度")
    private String urgency;

    @ApiModelProperty("实际停机开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginDowntime;

    @ApiModelProperty("实际停机结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDowntime;

    @ApiModelProperty(value = "工单开始处理时间", required = true)
    @NotNull(message = "工单开始处理时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginMaintTime;

    @ApiModelProperty("工单结束处理时间")
    @NotNull(message = "工单结束处理时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endMaintTime;

    @ApiModelProperty("完工附件")
    private String[] finishFileIds;

    @ApiModelProperty("辅助人员信息")
    private List<MaintTaskAssistListDto>  assistPeopleList;

    @ApiModelProperty(value = "工单作业项详情")
    private List<TaskItemDetailDto> itemDetailDtos;

}