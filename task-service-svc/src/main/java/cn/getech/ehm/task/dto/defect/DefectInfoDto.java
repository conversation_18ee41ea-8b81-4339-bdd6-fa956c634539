package cn.getech.ehm.task.dto.defect;

import cn.getech.ehm.base.dto.MaintPersonDto;
import cn.getech.ehm.equipment.dto.EquipmentInfoDto;
import cn.getech.ehm.equipment.dto.EquipmentListDto;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import cn.getech.poros.framework.common.annotation.Excel;

import java.util.Date;
import java.util.List;


/**
 * <pre>
 * 缺陷信息 返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-02-21
 */
@Data
@ApiModel(value = "DefectInfoDto", description = "缺陷信息返回数据模型")
public class DefectInfoDto {

    @ApiModelProperty(value = "")
    @Excel(name = "", cellType = Excel.ColumnType.STRING)
    private String id;

    @ApiModelProperty(value = "createBy")
    @Excel(name = "createBy", cellType = Excel.ColumnType.STRING)
    private String createBy;

    @ApiModelProperty(value = "updateBy")
    @Excel(name = "updateBy", cellType = Excel.ColumnType.STRING)
    private String updateBy;

    @ApiModelProperty(value = "createTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @Excel(name = "createTime", cellType = Excel.ColumnType.STRING, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "updateTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @Excel(name = "updateTime", cellType = Excel.ColumnType.STRING, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(value = "remark")
    @Excel(name = "remark", cellType = Excel.ColumnType.STRING)
    private String remark;

    private String equipmentId;

    @ApiModelProperty(value = "设备图片地址")
    @TableField(exist = false)
    private List<String> equipmentPicUrls;

    /**
     * 缺陷名称
     */
    @ApiModelProperty(value = "缺陷名称")
    private String defectName;

    /**
     * 缺陷内容
     */
    @ApiModelProperty(value = "缺陷内容")
    private String defectContent;

    /**
     * 影响描述
     */
    @ApiModelProperty(value = "影响描述")
    private String affectContent;

    /**
     * 缺陷种类
     */
    @ApiModelProperty(value = "缺陷种类")
    private Integer defectType;
    @ApiModelProperty(value = "缺陷种类")
    private String defectTypeName;


    /**
     * 专业类别
     */
    @ApiModelProperty(value = "专业类别")
    private Integer majorType;

    /**
     * 现场图片/视频
     */
    @ApiModelProperty(value = "现场图片/视频")
    private String[] liveMediaIds;

    /**
     * 缺陷状态
     */
    @ApiModelProperty(value = "缺陷状态")
    private Integer defectStatus;
    @ApiModelProperty(value = "缺陷状态")
    private String defectStatusName;

    /**
     * 验收状态
     */
    @ApiModelProperty(value = "验收状态")
    private Integer checkStatus;
    @ApiModelProperty(value = "验收状态")
    private String checkStatusName;

    /**
     * 截止日期
     */
    @ApiModelProperty(value = "截止日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date endTime;

    /**
     * 处理人id
     */
    @ApiModelProperty(value = "处理人id")
    private String dealPersonIds;
    @ApiModelProperty(value = "处理人id")
    private List<MaintPersonDto> dealPersonInfos;

    /**
     * 建议处理方案
     */
    @ApiModelProperty(value = "建议处理方案")
    private String suggestDealContent;

    /**
     * 实际处理方案
     */
    @ApiModelProperty(value = "实际处理方案")
    private String realDealContent;

    /**
     * 工单id
     */
    @ApiModelProperty(value = "工单id")
    private String maintTaskId;

    @ApiModelProperty(value = "工单code")
    private String maintTaskCode;

    @ApiModelProperty(value = "设备信息-列表用")
    private EquipmentListDto equipmentListDto;
    @ApiModelProperty(value = "设备信息-详情用")
    private EquipmentInfoDto equipmentInfoDto;

    @ApiModelProperty(value = "是否超时")
    private Integer hasOverTime;
    @ApiModelProperty(value = "是否超时")
    private String hasOverTimeName;

    @ApiModelProperty(value = "关闭原因")
    private String closeReason;

    @ApiModelProperty(value = "关闭时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date closeDate;

    @ApiModelProperty(value = "处理时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date dealDate;

    @ApiModelProperty(value = "验收说明")
    private String checkExplain;

    @ApiModelProperty(value = "实际停机开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginDowntime;

    @ApiModelProperty(value = "实际停机结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDowntime;

    @ApiModelProperty("停机耗时")
    private String downTimeCost;

    @ApiModelProperty(value = "是否停机(0未1是)")
    private Integer stopped;

    @ApiModelProperty(value = "来源工单id")
    private String sourceTaskId;

    @ApiModelProperty(value = "来源工单code")
    private String sourceTaskCode;

    private String defectReason;

    private Boolean permissionFlag = false;

    private String[] uids;

}