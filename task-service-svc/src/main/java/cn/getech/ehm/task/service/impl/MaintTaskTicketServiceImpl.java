package cn.getech.ehm.task.service.impl;

import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.task.dto.task.ticket.TaskTicketItemDto;
import cn.getech.ehm.task.entity.MaintTaskTicket;
import cn.getech.ehm.task.mapper.MaintTaskTicketMapper;
import cn.getech.ehm.task.service.IMaintTaskTicketService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 工单作业票
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
@Slf4j
@Service
public class MaintTaskTicketServiceImpl extends BaseServiceImpl<MaintTaskTicketMapper, MaintTaskTicket> implements IMaintTaskTicketService {

    @Autowired
    private MaintTaskTicketMapper taskTicketMapper;

    @Override
    public List<TaskTicketItemDto> getListByTaskId(String taskId) {
        LambdaQueryWrapper<MaintTaskTicket> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(MaintTaskTicket::getTaskId, taskId);
        wrapper.orderByAsc(MaintTaskTicket::getSort);
        wrapper.select(MaintTaskTicket::getId, MaintTaskTicket::getTaskId, MaintTaskTicket::getConfirm,
                MaintTaskTicket::getContent, MaintTaskTicket::getContent, MaintTaskTicket::getContentType);
        return CopyDataUtil.copyList(taskTicketMapper.selectList(wrapper), TaskTicketItemDto.class);
    }

    @Override
    public Boolean saveOrUpdateBatch(List<TaskTicketItemDto> itemDtos, String taskId){
        if(CollectionUtils.isNotEmpty(itemDtos)){
            List<MaintTaskTicket> maintTaskTickets = new ArrayList<>(itemDtos.size());
            int i = 1;
            for(TaskTicketItemDto itemDto : itemDtos){
                MaintTaskTicket maintTaskTicket = CopyDataUtil.copyObject(itemDto, MaintTaskTicket.class);
                maintTaskTicket.setSort(i++);
                maintTaskTicket.setTaskId(taskId);
                maintTaskTickets.add(maintTaskTicket);
            }
            return saveOrUpdateBatch(maintTaskTickets);
        }
        return true;
    }
}
