package cn.getech.ehm.task.dto.task.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 名称详情Dto
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "NameDetailDto", description = "名称详情Dto")
public class NameDetailDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

}