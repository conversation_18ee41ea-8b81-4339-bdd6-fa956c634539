package cn.getech.ehm.task.dto.repair;

import cn.getech.poros.framework.common.param.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 人工报修 分页查询参数对象
 *
 * <AUTHOR>
 * @date 2020-08-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ManualRepairQueryParam", description = "人工报修查询参数")
public class ManualRepairQueryParam extends PageParam {

    @ApiModelProperty(value = "设备id")
    private String equipmentId;

    @ApiModelProperty(value = "报修开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date beginCreateTime;

    @ApiModelProperty(value = "报修结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date endCreateTime;

    @ApiModelProperty(value = "报修开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date innerBeginCreateTime;

    @ApiModelProperty(value = "报修结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date innerEndCreateTime;

    @ApiModelProperty(value = "报修人uid/创建人uid")
    private String createBy;

    @ApiModelProperty(value = "报修人/创建人")
    private String createUserName;

    @ApiModelProperty(value = "设备名称")
    private String equipmentName;

    @ApiModelProperty(value = "设备编码")
    private String equipmentCode;

    @ApiModelProperty(value = "紧急程度")
    private String urgency;

    @ApiModelProperty(value = "维修状态(0未维修1维修中2已维修)")
    private Integer status;

    @ApiModelProperty(value = "维修状态(-1关闭,0待维修,1已维修)")
    private String innerStatus;

    @ApiModelProperty(value = "修复开始时间(yyyy-MM-dd HH:mm)")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date beginRepairTime;

    @ApiModelProperty(value = "修复结束时间(yyyy-MM-dd HH:mm)")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date endRepairTime;

    @ApiModelProperty(value = "工单编号")
    private String taskCode;

    @ApiModelProperty(value = "排序[属性:排序(ASC升序DESC降序)]", example = "createTime:ASC")
    private Map<String, String> sortPros;

    @ApiModelProperty(value = "排序sql",hidden = true)
    private String sortValue;

    @ApiModelProperty(value = "设备id集合")
    private List<String> equipmentIds;

    //截止日期
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deadlineDate;

}
