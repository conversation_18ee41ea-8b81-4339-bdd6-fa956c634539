package cn.getech.ehm.task.dto.task.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <pre>
 * 工单完成动态 返回数据模型
 * <AUTHOR>
 * @date 2020-08-14
 */
@Data
@ApiModel(value = "TaskCompleteDynamicDto", description = "工单完成动态返回数据模型")
public class TaskCompleteDynamicDto {
    @ApiModelProperty(value = "工单id")
    private String id;

    @ApiModelProperty(value = "工单名称")
    private String name;

    @ApiModelProperty(value = "工单编号")
    private String code;

    @ApiModelProperty(value = "工单类型(1故障单2维保单)")
    private Integer type;

    @ApiModelProperty(value = "工单处理人")
    private String finisher;

    @ApiModelProperty(value = "距离当前时间")
    private String intervalCurrentTime;

}
