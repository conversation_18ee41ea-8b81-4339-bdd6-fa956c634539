package cn.getech.ehm.task.dto.job;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 作业标准更新参数
 * <AUTHOR>
 * @date 2020-08-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "JobStandardEditParam", description = "作业标准更新参数")
public class JobStandardEditParam extends ApiParam {

    @ApiModelProperty(value = "id", required = true)
    @NotBlank(message = "id不能为空")
    private String id;

    @ApiModelProperty(value = "名称", required = true)
    @NotBlank(message = "名称不能为空")
    private String name;

    @ApiModelProperty(value = "设备类型id集合", required = true)
    @NotNull(message = "设备类型不能为空")
    private String[] infoCategoryIds;

    @ApiModelProperty(value = "设备位置id集合", required = true)
    @NotNull(message = "设备位置不能为空")
    private String[] infoLocationIds;

    @ApiModelProperty(value = "专业类别集合", required = true)
    @NotNull(message = "专业类别不能为空")
    private String[] majors;

    @ApiModelProperty(value = "作业类别")
    private String jobType;

    @ApiModelProperty(value = "是否发布")
    private Boolean published;

    @ApiModelProperty(value = "是否停机")
    private Boolean stopped;

    @ApiModelProperty(value = "描述")
    private String remark;

    @ApiModelProperty(value = "作业项目集合")
    List<JobStandardItemDto> itemDtos;

    @ApiModelProperty(value = "设备类型")
    private String equipType;
}