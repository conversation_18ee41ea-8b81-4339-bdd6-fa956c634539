package cn.getech.ehm.task.service.impl;

import cn.getech.ehm.task.entity.MaintTask;
import cn.getech.ehm.task.entity.MaintTaskConfig;
import cn.getech.ehm.task.enums.*;
import cn.getech.ehm.task.mapper.MaintTaskConfigMapper;
import cn.getech.ehm.task.service.IMaintTaskConfigService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.ehm.task.dto.taskConfig.MaintTaskConfigQueryParam;
import cn.getech.ehm.task.dto.taskConfig.MaintTaskConfigAddParam;
import cn.getech.ehm.task.dto.taskConfig.MaintTaskConfigEditParam;
import cn.getech.ehm.task.dto.taskConfig.MaintTaskConfigParamMapper;
import cn.getech.ehm.task.dto.taskConfig.MaintTaskConfigDto;
import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.QueryChainWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.ResultCode;

import java.util.Arrays;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;

import java.util.List;


/**
 * <pre>
 * 工单配置信息 服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2022-04-21
 */
@Slf4j
@Service
public class MaintTaskConfigServiceImpl extends BaseServiceImpl<MaintTaskConfigMapper, MaintTaskConfig> implements IMaintTaskConfigService {

    @Autowired
    private MaintTaskConfigParamMapper maintTaskConfigParamMapper;

    private static final Integer SendRuleInt = 100;

    private static final String defaultContent = "0";

    @Override
    public PageResult<MaintTaskConfigDto> pageDto(MaintTaskConfigQueryParam maintTaskConfigQueryParam) {
        Wrapper<MaintTaskConfig> wrapper = getPageSearchWrapper(maintTaskConfigQueryParam);
        PageResult<MaintTaskConfigDto> result = maintTaskConfigParamMapper.pageEntity2Dto(page(maintTaskConfigQueryParam, wrapper));
        List<MaintTaskConfigType> taskTypeList = MaintTaskConfigType.getTaskTypeList();
        List<MaintTaskConfigDto> temp = Lists.newArrayList();
        for (MaintTaskConfigType maintTaskConfigType : taskTypeList) {
            Optional<MaintTaskConfigDto> any = result.getRecords().stream().filter(item -> item.getConfigType() == maintTaskConfigType.getTaskOperateType() && item.getTaskType() == maintTaskConfigType.getTaskType()).findAny();
            if (any.isPresent()) {
                temp.add(any.get());
            } else {
                temp.add(MaintTaskConfigDto.builder().taskType(maintTaskConfigType.getTaskType()).configType(maintTaskConfigType.getTaskOperateType()).configContent(maintTaskConfigType.getContent()).build());
            }
        }
        result.setRecords(temp);
        return Optional.ofNullable(result).orElse(new PageResult<>());
    }

    public PageResult<MaintTaskConfigDto> pageDtoOfSendRule(MaintTaskConfigQueryParam maintTaskConfigQueryParam) {
        Wrapper<MaintTaskConfig> wrapper = getPageSearchWrapper(maintTaskConfigQueryParam);
        PageResult<MaintTaskConfigDto> result = maintTaskConfigParamMapper.pageEntity2Dto(page(maintTaskConfigQueryParam, wrapper));
        List<MaintTaskSendRuleConfigType> taskTypeList = MaintTaskSendRuleConfigType.getTaskTypeList();
        List<MaintTaskConfigDto> temp = Lists.newArrayList();
        for (MaintTaskSendRuleConfigType maintTaskConfigType : taskTypeList) {
            Optional<MaintTaskConfigDto> any = result.getRecords().stream().filter(item -> item.getConfigType() == maintTaskConfigType.getTaskOperateType() && item.getTaskType() == maintTaskConfigType.getTaskType()).findAny();
            if (any.isPresent()) {
                temp.add(any.get());
            } else {
                temp.add(MaintTaskConfigDto.builder().taskType(maintTaskConfigType.getTaskType()).configType(maintTaskConfigType.getTaskOperateType()).configContent(maintTaskConfigType.getContent()).build());
            }
        }
        result.setRecords(temp);
        return Optional.ofNullable(result).orElse(new PageResult<>());
    }

    @Override
    public MaintTaskConfig getConfig(String taskType, String configType) {
        LambdaQueryWrapper<MaintTaskConfig> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(MaintTaskConfig::getTaskType, taskType);
        wrapper.eq(MaintTaskConfig::getConfigType, configType);
        return (MaintTaskConfig) this.getOne(wrapper);
    }

    @Override
    public PageResult<MaintTaskConfigDto> pageDtoOfOvertimeCheckRule(MaintTaskConfigQueryParam maintTaskConfigQueryParam) {
        Wrapper<MaintTaskConfig> wrapper = getPageSearchWrapper(maintTaskConfigQueryParam);
        PageResult<MaintTaskConfigDto> result = maintTaskConfigParamMapper.pageEntity2Dto(page(maintTaskConfigQueryParam, wrapper));
        List<MaintTaskOvertimeCheckConfigType> taskTypeList = MaintTaskOvertimeCheckConfigType.getTaskTypeList();
        List<MaintTaskConfigDto> temp = Lists.newArrayList();
        for (MaintTaskOvertimeCheckConfigType maintTaskConfigType : taskTypeList) {
            Optional<MaintTaskConfigDto> any = result.getRecords().stream().filter(item -> item.getConfigType() == maintTaskConfigType.getTaskOperateType() && item.getTaskType() == maintTaskConfigType.getTaskType()).findAny();
            if (any.isPresent()) {
                temp.add(any.get());
            } else {
                temp.add(MaintTaskConfigDto.builder().taskType(maintTaskConfigType.getTaskType()).configType(maintTaskConfigType.getTaskOperateType()).configContent(maintTaskConfigType.getContent()).build());
            }
        }
        result.setRecords(temp);
        return Optional.ofNullable(result).orElse(new PageResult<>());
    }

    @Override
    public PageResult<MaintTaskConfigDto> listNotifyRule(MaintTaskConfigQueryParam maintTaskConfigQueryParam) {
        Wrapper<MaintTaskConfig> wrapper = getPageSearchWrapper(maintTaskConfigQueryParam);
        PageResult<MaintTaskConfigDto> result = maintTaskConfigParamMapper.pageEntity2Dto(page(maintTaskConfigQueryParam, wrapper));
        List<MaintTaskNotifyRuleConfigType> taskTypeList = MaintTaskNotifyRuleConfigType.getTaskTypeList();
        List<MaintTaskConfigDto> temp = Lists.newArrayList();
        for (MaintTaskNotifyRuleConfigType maintTaskConfigType : taskTypeList) {
            Optional<MaintTaskConfigDto> any = result.getRecords().stream().filter(item -> item.getConfigType() == maintTaskConfigType.getTaskOperateType() && item.getTaskType() == maintTaskConfigType.getTaskType()).findAny();
            if (any.isPresent()) {
                temp.add(any.get());
            } else {
                temp.add(MaintTaskConfigDto.builder().taskType(maintTaskConfigType.getTaskType()).configType(maintTaskConfigType.getTaskOperateType()).configContent(maintTaskConfigType.getContent()).build());
            }
        }
        result.setRecords(temp);
        return Optional.ofNullable(result).orElse(new PageResult<>());
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveByParam(MaintTaskConfigAddParam maintTaskConfigAddParam) {
        MaintTaskConfig maintTaskConfig = maintTaskConfigParamMapper.addParam2Entity(maintTaskConfigAddParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, maintTaskConfig);
        return save(maintTaskConfig);
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean updateByParam(MaintTaskConfigEditParam maintTaskConfigEditParam) {
        MaintTaskConfig maintTaskConfig = maintTaskConfigParamMapper.editParam2Entity(maintTaskConfigEditParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, maintTaskConfig);
        if (maintTaskConfig.getId() != null) {
            return updateById(maintTaskConfig);
        } else {
            return this.save(maintTaskConfig);
        }
    }

    public boolean updateManualRepairSendRule(MaintTaskConfigEditParam maintTaskConfigEditParam) {
        MaintTaskConfig maintTaskConfig = maintTaskConfigParamMapper.editParam2Entity(maintTaskConfigEditParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, maintTaskConfig);
        maintTaskConfig.setTaskType(TaskSourceType.BREAKDOWN.getValue());
        //定义100为指派策略
        maintTaskConfig.setConfigType(SendRuleInt);
        if (maintTaskConfig.getId() != null) {
            return updateById(maintTaskConfig);
        } else {
            return this.save(maintTaskConfig);
        }
    }

    public String getManualRepairSendRule() {
        MaintTaskConfig one = (MaintTaskConfig) this.getOne(new QueryWrapper<MaintTaskConfig>().lambda().eq(MaintTaskConfig::getTaskType, TaskSourceType.BREAKDOWN.getValue()).eq(MaintTaskConfig::getConfigType, SendRuleInt), false);
        return one != null ? one.getConfigContent() : defaultContent;
    }


    @Override
    public MaintTaskConfigDto getDtoById(Long id) {
        return maintTaskConfigParamMapper.entity2Dto((MaintTaskConfig) this.getById(id));
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveDtoBatch(List<MaintTaskConfigDto> rows) {
        return saveBatch(maintTaskConfigParamMapper.dtoList2Entity(rows));
    }

    private Wrapper<MaintTaskConfig> getPageSearchWrapper(MaintTaskConfigQueryParam maintTaskConfigQueryParam) {
        LambdaQueryWrapper<MaintTaskConfig> wrapper = Wrappers.<MaintTaskConfig>lambdaQuery();
        wrapper.like(StringUtils.isNotBlank(maintTaskConfigQueryParam.getId()),
                MaintTaskConfig::getId, maintTaskConfigQueryParam.getId());
        if (BaseEntity.class.isAssignableFrom(MaintTaskConfig.class)) {
            wrapper.orderByDesc(MaintTaskConfig::getUpdateTime, MaintTaskConfig::getCreateTime);
        }
        wrapper.eq(maintTaskConfigQueryParam.getTaskType() != null, MaintTaskConfig::getTaskType, maintTaskConfigQueryParam.getTaskType());
//        if (maintTaskConfigQueryParam.getConfigType() == null) {
//            wrapper.in(MaintTaskConfig::getConfigType, Arrays.asList(0, 1));
//        } else {
//            wrapper.eq(MaintTaskConfig::getConfigType, maintTaskConfigQueryParam.getConfigType());
//        }
        return wrapper;
    }

    public Integer getByTaskTypeAndConfigType(Integer taskSourceType, Integer configType) {
        LambdaQueryWrapper<MaintTaskConfig> wrapper = Wrappers.<MaintTaskConfig>lambdaQuery();
        wrapper.eq(MaintTaskConfig::getTaskType, taskSourceType);
        wrapper.eq(MaintTaskConfig::getConfigType, configType);
        MaintTaskConfig one = (MaintTaskConfig) this.getOne(wrapper);
        if (one != null) {
            return Integer.parseInt(one.getConfigContent());
        } else {
            if(configType == TaskOperationType.REC_TASK.getValue()){
                //接单截止设置10分钟
                return 10;
            }
            return 30;
        }

    }
}
