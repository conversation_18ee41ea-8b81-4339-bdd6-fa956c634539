package cn.getech.ehm.task.service.impl;

import cn.getech.ehm.task.entity.TaskNotifyConfig;
import cn.getech.ehm.task.mapper.TaskNotifyConfigMapper;
import cn.getech.ehm.task.service.ITaskNotifyConfigService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.ehm.task.dto.TaskNotifyConfigQueryParam;
import cn.getech.ehm.task.dto.TaskNotifyConfigAddParam;
import cn.getech.ehm.task.dto.TaskNotifyConfigEditParam;
import cn.getech.ehm.task.dto.TaskNotifyConfigParamMapper;
import cn.getech.ehm.task.dto.TaskNotifyConfigDto;
import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.ResultCode;

import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

import cn.hutool.core.util.ArrayUtil;


/**
 * <pre>
 * 工单通知配置 服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2022-11-01
 */
@Slf4j
@Service
public class TaskNotifyConfigServiceImpl extends BaseServiceImpl<TaskNotifyConfigMapper, TaskNotifyConfig> implements ITaskNotifyConfigService {

    @Autowired
    private TaskNotifyConfigParamMapper taskNotifyConfigParamMapper;

    @Override
    public PageResult<TaskNotifyConfigDto> pageDto(TaskNotifyConfigQueryParam taskNotifyConfigQueryParam) {
        Wrapper<TaskNotifyConfig> wrapper = getPageSearchWrapper(taskNotifyConfigQueryParam);
        PageResult<TaskNotifyConfigDto> result = taskNotifyConfigParamMapper.pageEntity2Dto(page(taskNotifyConfigQueryParam, wrapper));

        return Optional.ofNullable(result).orElse(new PageResult<>());
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveByParam(TaskNotifyConfigAddParam taskNotifyConfigAddParam) {
        TaskNotifyConfig taskNotifyConfig = taskNotifyConfigParamMapper.addParam2Entity(taskNotifyConfigAddParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, taskNotifyConfig);
        return save(taskNotifyConfig);
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean updateByParam(TaskNotifyConfigEditParam taskNotifyConfigEditParam) {
        TaskNotifyConfig taskNotifyConfig = taskNotifyConfigParamMapper.editParam2Entity(taskNotifyConfigEditParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, taskNotifyConfig);
        return saveOrUpdate(taskNotifyConfig);
    }


    @Override
    public TaskNotifyConfigDto getDtoById(Long id) {
        return taskNotifyConfigParamMapper.entity2Dto((TaskNotifyConfig) this.getById(id));
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveDtoBatch(List<TaskNotifyConfigDto> rows) {
        return saveBatch(taskNotifyConfigParamMapper.dtoList2Entity(rows));
    }

    private Wrapper<TaskNotifyConfig> getPageSearchWrapper(TaskNotifyConfigQueryParam taskNotifyConfigQueryParam) {
        LambdaQueryWrapper<TaskNotifyConfig> wrapper = Wrappers.<TaskNotifyConfig>lambdaQuery();
        wrapper.like(StringUtils.isNotBlank(taskNotifyConfigQueryParam.getId()),
                TaskNotifyConfig::getId, taskNotifyConfigQueryParam.getId());
        if (BaseEntity.class.isAssignableFrom(TaskNotifyConfig.class)) {
            wrapper.orderByDesc(TaskNotifyConfig::getUpdateTime, TaskNotifyConfig::getCreateTime);
        }
        return wrapper;
    }

    public TaskNotifyConfig getDefault() {
        TaskNotifyConfig one = (TaskNotifyConfig) this.getOne(new QueryWrapper<TaskNotifyConfig>(), false);
        if (one != null) {
            return one;
        } else {
            return new TaskNotifyConfig();
        }
    }
}
