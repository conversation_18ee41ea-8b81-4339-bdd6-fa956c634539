package cn.getech.ehm.task.service;

import cn.getech.ehm.task.dto.task.info.TaskPartRelDetailQueryParam;
import cn.getech.ehm.task.dto.task.info.TaskPartRelQueryParam;
import cn.getech.ehm.task.dto.task.info.TaskPartRelReportDto;
import cn.getech.ehm.task.dto.task.part.TaskPartDto;
import cn.getech.ehm.task.dto.task.part.TaskPartEditDto;
import cn.getech.ehm.task.entity.MaintTaskPart;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.IBaseService;
import java.util.List;

/**
 * 维护工单、备件关联表 服务类
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
public interface IMaintTaskPartService extends IBaseService<MaintTaskPart> {

    /**
     * 保存备件
     * @param taskId
     * @return
     */
    Boolean saveOrUpdateList(List<TaskPartEditDto> parts, String taskId);
    /**
     * 根据工单id删除
     * @param taskId
     * @return
     */
    Boolean deleteByTaskId(String taskId);

    /**
     * 根据工单id获取数据
     * @param taskId
     * @return
     */
    List<TaskPartDto> getByTaskId(String taskId);
    /**
     * 分页获取备件耗用成本列表
     * @param taskPartRelQueryParam
     * @return
     */
    PageResult<TaskPartRelReportDto> getTaskPartRelPageList(TaskPartRelQueryParam taskPartRelQueryParam);

    /**
     * 分页获取备件耗用详情列表
     * @param taskPartRelDetailQueryParam
     * @return
     */
    PageResult<TaskPartRelReportDto> getTaskPartRelDetailPageList(TaskPartRelDetailQueryParam taskPartRelDetailQueryParam);

    /**
     * 校验备件是否被维护工单相关使用
     *
     * @param partIds
     * @return
     */
    List<String> checkPartUsed(String[] partIds);
}