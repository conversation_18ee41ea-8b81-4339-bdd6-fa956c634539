package cn.getech.ehm.task.service.impl;

import cn.getech.ehm.base.client.BaseServiceClient;
import cn.getech.ehm.base.dto.DictionaryItemDto;
import cn.getech.ehm.common.enums.DeletedType;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.task.dto.ticket.JobTicketItemDto;
import cn.getech.ehm.task.entity.JobTicketItem;
import cn.getech.ehm.task.mapper.JobTicketItemMapper;
import cn.getech.ehm.task.service.IJobTicketItemService;
import cn.getech.ehm.task.service.IJobTicketService;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.poros.framework.common.utils.StringPool;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 作业票内容imp
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
@Slf4j
@Service
public class JobTicketItemServiceImpl extends BaseServiceImpl<JobTicketItemMapper, JobTicketItem> implements IJobTicketItemService {

    @Autowired
    private JobTicketItemMapper ticketItemMapper;
    @Autowired
    private BaseServiceClient baseServiceClient;
    @Autowired
    private IJobTicketService jobTicketService;

    @Override
    public String saveByParam(JobTicketItemDto addDto) {
        JobTicketItem jobTicketItem = CopyDataUtil.copyObject(addDto, JobTicketItem.class);
        if(StringUtils.isBlank(addDto.getTicketId())){
            jobTicketItem.setTicketId(jobTicketService.getTicketId());
        }
        jobTicketItem.setSort(ticketItemMapper.getMaxSort() + 1);
        save(jobTicketItem);
        return jobTicketItem.getId();
    }

    @Override
    public Boolean updateByParam(JobTicketItemDto editDto){
        JobTicketItem jobTicketItem = CopyDataUtil.copyObject(editDto, JobTicketItem.class);
        return updateById(jobTicketItem);
    }

    @Override
    public Boolean saveBatchByParams(List<JobTicketItemDto> itemDtos, String ticketId) {
        List<JobTicketItem> jobTicketItems = new ArrayList<>(itemDtos.size());
        int sort = 1;
        for(JobTicketItemDto itemDto : itemDtos){
            JobTicketItem jobTicketItem = CopyDataUtil.copyObject(itemDto, JobTicketItem.class);
            jobTicketItem.setSort(sort++);
            jobTicketItem.setTicketId(ticketId);
            jobTicketItems.add(jobTicketItem);
        }
        return saveBatch(jobTicketItems);
    }

    @Override
    public Boolean deleteById(String id) {
        LambdaUpdateWrapper<JobTicketItem> wrapper = Wrappers.lambdaUpdate();
        wrapper.eq(JobTicketItem::getId, id);
        wrapper.set(JobTicketItem::getDeleted, DeletedType.YES.getValue());
        return update(wrapper);
    }

    @Override
    public List<JobTicketItemDto> getListByTicketId(String ticketId){
        LambdaQueryWrapper<JobTicketItem> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(JobTicketItem::getTicketId, ticketId);
        wrapper.eq(JobTicketItem::getDeleted, DeletedType.NO.getValue());
        wrapper.orderByAsc(JobTicketItem::getSort);
        List<JobTicketItemDto> itemDtos = CopyDataUtil.copyList(ticketItemMapper.selectList(wrapper), JobTicketItemDto.class);
        if(CollectionUtils.isNotEmpty(itemDtos)) {
            RestResponse<Map<String, DictionaryItemDto>> restResponse = baseServiceClient.getItemMapByCode("job_ticket_type");
            if (restResponse.isOk()) {
                Map<String, DictionaryItemDto> dicMap = restResponse.getData();
                for(JobTicketItemDto dto : itemDtos){
                    String[] tickTypes = dto.getTicketTypes();
                    if(null != tickTypes && tickTypes.length > 0){
                        StringBuffer ticketTypeNames = new StringBuffer();

                        for(int i = 0; i < tickTypes.length; i++){
                            DictionaryItemDto dictionaryItemDto = dicMap.get(tickTypes[i]);
                            if(null != dictionaryItemDto){
                                ticketTypeNames.append(dictionaryItemDto.getName());
                                if(i != tickTypes.length - 1){
                                    ticketTypeNames.append(StringPool.SLASH);
                                }
                            }
                        }
                        dto.setTicketTypeNames(ticketTypeNames.toString());
                    }

                }
            } else {
                log.info("连接base-service获取作业票类型字典表失败");
            }
        }
        return itemDtos;
    }
}
