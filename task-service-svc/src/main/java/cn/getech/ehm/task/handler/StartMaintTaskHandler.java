package cn.getech.ehm.task.handler;

import cn.getech.ehm.base.client.BaseServiceClient;
import cn.getech.ehm.common.context.UserContextHolder;
import cn.getech.ehm.task.entity.MaintTask;
import cn.getech.ehm.task.enums.TaskOperationType;
import cn.getech.ehm.task.enums.TaskSourceType;
import cn.getech.ehm.task.enums.TaskStatusType;
import cn.getech.ehm.task.enums.TaskType;
import cn.getech.ehm.task.service.IJobTicketService;
import cn.getech.ehm.task.service.IMaintNotifyService;
import cn.getech.ehm.task.service.IMaintTaskConfigService;
import cn.getech.ehm.task.service.IMaintTaskService;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.framework.common.context.PorosContextHolder;
import cn.getech.poros.permission.client.PorosSecGrantClient;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class StartMaintTaskHandler {
    @Value("${task.receive.auto:false}")
    private Boolean taskReceiveAuto;
    /**
     * 工单验收员角色
     */
    @Value("${fixed.role.evaluate.code:web-admin_evaluate}")
    private String evaluateRoleCode;
    /**
     * 派单员
     */
    @Value("${fixed.role.assing.code:web-admin_assing}")
    private String assingRoleCode;
    /**
     * 派单审核员
     */
    @Value("${fixed.role.dispatcher.code:web-admin_dispatcher}")
    private String dispatcherRoleCode;
    @Autowired
    @Lazy
    private IMaintTaskService maintTaskService;
    @Autowired
    private PorosSecGrantClient porosSecGrantClient;
    @Autowired
    @Lazy
    private IJobTicketService jobTicketService;
    @Autowired
    private BaseServiceClient baseServiceClient;
    @Autowired
    @Lazy
    private IMaintTaskConfigService maintTaskConfigService;
    @Autowired
    private IMaintNotifyService notifyService;

    public MaintTask startDefectTask(MaintTask maintTask, Map<String, Object> variables){
        List<String> finalAssigns = Lists.newArrayList();
        RestResponse<List<String>> personResponse = baseServiceClient.getUidsByIds(maintTask.getAllStaffIds().split(","));
        if (personResponse.isOk() && CollectionUtils.isNotEmpty(personResponse.getData())) {
            finalAssigns.addAll(finalAssigns);
            variables.put("assigned", true);
            variables.put("assigns", personResponse.getData());
            maintTask.setStatus(TaskStatusType.RECEIVING.getValue());
        }else {
            RestResponse<List<String>> dispatchRoleResponse = porosSecGrantClient.getUidsByRoleCode(dispatcherRoleCode);
            if (dispatchRoleResponse.isOk() && CollectionUtils.isNotEmpty(dispatchRoleResponse.getData())) {
                List<String> roleUids = dispatchRoleResponse.getData();
                if (CollectionUtils.isEmpty(roleUids)) {
                    //不设置审核角色，设置默认
                    roleUids.add("-1");
                }
                maintTask.setDispatchReviewer(roleUids.toArray(new String[roleUids.size()]));
                variables.put("assigns", roleUids);
            } else {
                log.error("获取中台工单派单审核员角色人员失败->" + JSONObject.toJSON(dispatchRoleResponse));
            }
            maintTask.setStatus(TaskStatusType.DISPATCH.getValue());
        }
        //跳过审批前置，推入至工作流，直接不走audit这一步
        Boolean repairAudit = false;
        Boolean jumpAudit = false;
        variables.put("jumpAudit", jumpAudit);
        return maintTask;
    }

    public MaintTask startBreakdownTask(MaintTask maintTask, Map<String, Object> variables){
        //默认设置派单人为角色集合
        RestResponse<List<String>> roleResponse = porosSecGrantClient.getUidsByRoleCode(assingRoleCode);
        if (roleResponse.isOk() && CollectionUtils.isNotEmpty(roleResponse.getData())) {
            List<String> roleUids = roleResponse.getData();
            maintTask.setDispatchHandler(roleUids.toArray(new String[roleUids.size()]));
        } else {
            log.error("获取中台工单派单角色人员失败->" + JSONObject.toJSON(roleResponse));
        }
        //开启自动派单，但未获取到可派单人，也进入到派单环节
        if (StringUtils.isEmpty(maintTask.getAllStaffIds())) {
            variables.put("assigned", false);
            maintTask.setStatus(TaskStatusType.DISPATCH.getValue());
            RestResponse<List<String>> dispatchRoleResponse = porosSecGrantClient.getUidsByRoleCode(dispatcherRoleCode);
            if (dispatchRoleResponse.isOk() && CollectionUtils.isNotEmpty(dispatchRoleResponse.getData())) {
                List<String> roleUids = dispatchRoleResponse.getData();
                if (CollectionUtils.isEmpty(roleUids)) {
                    //不设置审核角色，设置默认
                    roleUids.add("-1");
                }
                maintTask.setDispatchReviewer(roleUids.toArray(new String[roleUids.size()]));
                variables.put("assigns", roleUids);
            } else {
                log.error("获取中台工单派单审核员角色人员失败->" + JSONObject.toJSON(dispatchRoleResponse));
            }
        } else {
            //有可派单人的直接派单
            List<String> finalAssigns = Lists.newArrayList();
            UserBaseInfo userBaseInfo = UserContextHolder.getContext().getUserBaseInfo();
            maintTask.setDispatchHandler(new String[]{userBaseInfo.getUid()});
            maintTask.setStatus(TaskStatusType.REPAIR_AUDIT.getValue());
            RestResponse<List<String>> personResponse = baseServiceClient.getUidsByIds(maintTask.getAllStaffIds().split(","));
            if (personResponse.isOk() && CollectionUtils.isNotEmpty(personResponse.getData())) {
                finalAssigns.addAll(finalAssigns);
                variables.put("assigned", true);
                variables.put("assigns", personResponse.getData());
                maintTask.setStatus(TaskStatusType.REPAIR_AUDIT.getValue());
            }
        }

        //跳过审批前置，推入至工作流，直接不走audit这一步
        Boolean repairAudit = false;
        Boolean jumpAudit = false;
        repairAudit = jobTicketService.getRepairTaskAudit();
        //如果走完上面的判断之后，流程处于待审批，则判断是否需要跳过审批节点
        //非缺陷单都可以跳过审批节点
        if (maintTask.getStatus() == TaskStatusType.REPAIR_AUDIT.getValue() && !repairAudit) {
            if (StringUtils.isNotEmpty(maintTask.getAllStaffIds())) {
                RestResponse<List<String>> personResponse = baseServiceClient.getUidsByIds(maintTask.getAllStaffIds().split(","));
                if (personResponse.isOk()) {
                    jumpAudit = true;
                    variables.put("assigns", personResponse.getData());
                    maintTask.setStatus(TaskStatusType.RECEIVING.getValue());
                    //派单时间，设置接单截止时间
                    this.setMaintTaskSendTaskDate(maintTask);
                }
            } else {

            }
        }
        variables.put("jumpAudit", jumpAudit);
        return maintTask;
    }

    public MaintTask setMaintTaskSendTaskDate(MaintTask maintTask) {
        maintTask.setSendTaskDate(new Date());
        Integer offsetDays = maintTaskConfigService.getByTaskTypeAndConfigType(maintTask.getSourceType(), TaskOperationType.REC_TASK.getValue());
        maintTask.setRecTaskDeadlineDate(DateUtil.offset(new Date(), DateField.MINUTE, offsetDays));
        if (maintTask.getStatus() == TaskStatusType.RECEIVING.getValue()) {
            notifyService.saveRepairTaskReceiveNotify(maintTask.getId(), maintTask.getRecTaskDeadlineDate());
        }
        return maintTask;
    }
}
