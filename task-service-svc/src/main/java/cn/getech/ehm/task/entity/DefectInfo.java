package cn.getech.ehm.task.entity;

import cn.getech.ehm.common.mp.handler.StringArrayTypeHandler;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import cn.getech.poros.framework.common.annotation.Excel;
import org.apache.ibatis.type.JdbcType;

/**
 * <p>
 * 缺陷信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("defect_info")
public class DefectInfo extends BaseEntity {

    private static final long serialVersionUID=1L;

    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    @TableField("tenant_id")
    private String tenantId;

    /**
     * 设备id
     */
    @TableField("equipment_id")
    private String equipmentId;

    /**
     * 缺陷名称
     */
    @TableField("defect_name")
    private String defectName;

    /**
     * 缺陷内容
     */
    @TableField("defect_content")
    private String defectContent;

    /**
     * 影响描述
     */
    @TableField("affect_content")
    private String affectContent;

    /**
     * 缺陷种类
     */
    @TableField("defect_type")
    private Integer defectType;

    /**
     * 专业类别
     */
    @TableField("major_type")
    private Integer majorType;

    /**
     * 现场图片/视频
     */
    @TableField(value = "live_media_ids", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] liveMediaIds;

    /**
     * 缺陷状态
     */
    @TableField("defect_status")
    private Integer defectStatus;

    /**
     * 验收状态
     */
    @TableField("check_status")
    private Integer checkStatus;

    /**
     * 验收说明
     */
    @TableField("check_explain")
    private String checkExplain;

    /**
     * 截止日期
     */
    @TableField("end_time")
    private Date endTime;

    /**
     * 处理人ids
     */
    @TableField("deal_person_ids")
    private String dealPersonIds;

    /**
     * 建议处理方案
     */
    @TableField("suggest_deal_content")
    private String suggestDealContent;

    /**
     * 实际处理方案
     */
    @TableField("real_deal_content")
    private String realDealContent;

    /**
     * 工单id
     */
    @TableField("maint_task_id")
    private String maintTaskId;

    @TableField("maint_task_code")
    private String maintTaskCode;

    @TableField("close_reason")
    private String closeReason;

    @TableField("close_date")
    private Date closeDate;

    @TableField("deal_date")
    private Date dealDate;

    private String sourceTaskId;

    private String sourceTaskCode;

    private String defectReason;

    @TableField(typeHandler = StringArrayTypeHandler.class)
    private String[] uids;

}
