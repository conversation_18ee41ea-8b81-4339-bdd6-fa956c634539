package cn.getech.ehm.task.service.impl;

import cn.getech.ehm.base.client.BaseServiceClient;
import cn.getech.ehm.base.dto.DictionaryItemDto;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.task.dto.job.*;
import cn.getech.ehm.task.entity.JobStandardItem;
import cn.getech.ehm.task.mapper.JobStandardItemMapper;
import cn.getech.ehm.task.service.IJobStandardItemService;
import cn.getech.ehm.task.service.IJobStandardService;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.poros.framework.common.utils.StringPool;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 作业项目imp
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
@Slf4j
@Service
public class JobStandardItemServiceImpl extends BaseServiceImpl<JobStandardItemMapper, JobStandardItem> implements IJobStandardItemService {

    @Autowired
    private JobStandardItemMapper jobStandardItemMapper;
    @Autowired
    private BaseServiceClient baseServiceClient;
    @Autowired
    private IJobStandardService jobStandardService;

    @Override
    public PageResult<JobStandardItemDto> pageList(ItemQueryParam queryParam) {
        Page<ItemQueryParam> page = new Page<>(queryParam.getPageNo(), queryParam.getLimit());
        LambdaQueryWrapper<JobStandardItem> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(JobStandardItem::getJobStandardId, queryParam.getStandardId());
        wrapper.orderByDesc(JobStandardItem::getSort);
        IPage<JobStandardItemDto> result = this.page(page, wrapper);
        return Optional.ofNullable(PageResult.<JobStandardItemDto>builder()
                .records(CopyDataUtil.copyList(result.getRecords(), JobStandardItemDto.class))
                .total(result.getTotal())
                .build())
                .orElse(new PageResult<>());
    }

    @Override
    public List<JobStandardItemDto> getListByStandardId(String standardId) {
        LambdaQueryWrapper<JobStandardItem> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(JobStandardItem::getJobStandardId, standardId);
        wrapper.orderByAsc(JobStandardItem::getSort);
        return CopyDataUtil.copyList(jobStandardItemMapper.selectList(wrapper), JobStandardItemDto.class);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean editItems(List<JobStandardItemDto> itemDtos, String standardId, Boolean add) {
        this.deleteByStandardId(standardId);
        if(CollectionUtils.isNotEmpty(itemDtos)){
            List<JobStandardItem> jobStandardItems = new ArrayList<>(itemDtos.size());
            int i = 1;
            for(JobStandardItemDto itemDto : itemDtos){
                JobStandardItem jobStandardItem = CopyDataUtil.copyObject(itemDto, JobStandardItem.class);
                if(add){
                    jobStandardItem.setId(null);
                }
                jobStandardItem.setJobStandardId(standardId);
                jobStandardItem.setSort(i++);
                jobStandardItems.add(jobStandardItem);
            }
            return saveBatch(jobStandardItems);
        }
        return true;
    }

    @Override
    public Boolean deleteByStandardId(String standardId) {
        LambdaQueryWrapper<JobStandardItem> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(JobStandardItem::getJobStandardId, standardId);
        return jobStandardItemMapper.delete(wrapper) > 0;
    }

    @Override
    public Map<String, ItemCountDto> getCountMap(List<String> standardIds) {
        Map<String, ItemCountDto> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(standardIds)) {
            List<ItemCountDto> itemCountDtos = jobStandardItemMapper.getCountList(standardIds);
            map = itemCountDtos.stream().collect(Collectors.toMap(ItemCountDto::getStandardId, v -> v, (v1, v2) -> v1));
        }
        return map;
    }

    @Override
    public Map<String, List<String>> getComboMap(){
        Map<String, List<String>> map = new HashMap<>();
        RestResponse<Map<String, DictionaryItemDto>> unitResponse = baseServiceClient.getItemMapByCode("unit");
        if(unitResponse.isOk()){
            List<String> unitList = unitResponse.getData().values().stream().map(DictionaryItemDto::getName).distinct().collect(Collectors.toList());
            map.put("unit", unitList);
        }else{
            log.info("获取单位失败");
        }

        RestResponse<Map<String, DictionaryItemDto>> jobItemResultTypeResponse = baseServiceClient.getItemMapByCode("job_item_result_type");
        if(jobItemResultTypeResponse.isOk()){
            List<String> jobItemResultTypeList = jobItemResultTypeResponse.getData().values().stream().map(DictionaryItemDto::getName).distinct().collect(Collectors.toList());
            map.put("jobItemResultType", jobItemResultTypeList);
        }else{
            log.info("获取结果类型失败");
        }

        RestResponse<Map<String, DictionaryItemDto>> specialRequirementsResponse = baseServiceClient.getItemMapByCode("special_requirements");
        if(specialRequirementsResponse.isOk()){
            List<String> specialRequirementsList = specialRequirementsResponse.getData().values().stream().map(DictionaryItemDto::getName).distinct().collect(Collectors.toList());
            map.put("specialRequirements", specialRequirementsList);
        }else{
            log.info("获取特殊要求失败");
        }

        return map;
    }

    @Override
    public JobStandardExcelResDto excelImport(List<StandardItemExcelDto> rows, String jobType){
        JobStandardExcelResDto jobStandardExcelResDto = new JobStandardExcelResDto();
        List<JobStandardItemDto> itemDtos = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(rows)){
            Map<String, String> unitMap = new HashMap<>();
            RestResponse<Map<String, DictionaryItemDto>> unitResponse = baseServiceClient.getItemMapByCode("unit");
            if(unitResponse.isOk()){
                unitMap = unitResponse.getData().values().stream().collect(Collectors.toMap(DictionaryItemDto::getName, DictionaryItemDto::getValue, (v1, v2) -> v1));
            }else{
                log.info("获取单位失败");
            }

            Map<String, String> itemResultMap = new HashMap<>();
            RestResponse<Map<String, DictionaryItemDto>> jobItemResultTypeRes = baseServiceClient.getItemMapByCode("job_item_result_type");
            if(jobItemResultTypeRes.isOk()){
                itemResultMap = jobItemResultTypeRes.getData().values().stream().collect(Collectors.toMap(DictionaryItemDto::getName, DictionaryItemDto::getValue));;
            }else{
                log.info("获取结果类型失败");
            }

            Map<String, String> specialRequirementsMap = new HashMap<>();
            RestResponse<Map<String, DictionaryItemDto>> specialRequirementsRes = baseServiceClient.getItemMapByCode("special_requirements");
            if(specialRequirementsRes.isOk()){
                specialRequirementsMap = specialRequirementsRes.getData().values().stream().collect(Collectors.toMap(DictionaryItemDto::getName, DictionaryItemDto::getValue));;
            }else{
                log.info("获取特殊要求失败");
            }
            Map<String, String> jobTypeLevelMap = new HashMap<>();
            RestResponse<Map<String, DictionaryItemDto>> jobTypeLevelRes = baseServiceClient.getItemMapByCode("job_type_level");
            if(jobTypeLevelRes.isOk()){
                jobTypeLevelMap = jobTypeLevelRes.getData().values().stream().filter(dto -> jobType.equals(dto.getLabel())).collect(Collectors.toMap(DictionaryItemDto::getName, DictionaryItemDto::getValue));;
            }else{
                log.info("获取作业类型等级失败");
            }

            //导入的报表大类小类是合并的单元格，所以下面的行需要继承最顶端合并格的名称
            String largeCategory = "";
            String subCategory = "";
            for(StandardItemExcelDto row : rows){
                JobStandardItemDto itemDto = CopyDataUtil.copyObject(row, JobStandardItemDto.class);
                if(StringUtils.isNotBlank(row.getLargeCategory())){
                    largeCategory = row.getLargeCategory();
                }
                itemDto.setLargeCategory(largeCategory);
                if(StringUtils.isNotBlank(row.getSubCategory())){
                    subCategory = row.getSubCategory();
                }
                itemDto.setSubCategory(subCategory);
                if(StringUtils.isNotBlank(row.getJobTypeLevel())){
                    //中文，换成英文,
                    String[] levelNames = row.getJobTypeLevel().replace("，", StringPool.COMMA).split(StringPool.COMMA);
                    List<String> levelValues = new ArrayList<>();
                    //解析作业类型等级
                    for(String name : levelNames){
                        String value = jobTypeLevelMap.get(name);
                        if(StringUtils.isNotBlank(value) && !levelValues.contains(value)){
                            levelValues.add(value);
                        }
                    }
                    if(CollectionUtils.isNotEmpty(levelValues)){
                        itemDto.setJobTypeLevel(levelValues.toArray(new String[levelValues.size()]));
                    }
                }
                if(StringUtils.isNotBlank(row.getJobItemResultType())){
                    itemDto.setJobItemResultType(itemResultMap.get(row.getJobItemResultType()));
                }
                if(StringUtils.isNotBlank(row.getUnit())){
                    itemDto.setUnit(unitMap.get(row.getUnit()));
                }
                if(StringUtils.isNotBlank(row.getSpecialRequirements())){
                    itemDto.setSpecialRequirements(specialRequirementsMap.get(row.getSpecialRequirements()));
                }

                itemDtos.add(itemDto);
            }
        }
        jobStandardExcelResDto.setItemDtos(itemDtos);
        ItemCountDto itemCountDto = jobStandardService.buildCountDto(itemDtos);
        jobStandardExcelResDto.setCountDto(itemCountDto);
        return jobStandardExcelResDto;
    }
}
