package cn.getech.ehm.task.controller;


import cn.getech.ehm.task.dto.taskConfig.MaintTaskConfigDto;
import cn.getech.ehm.task.dto.taskConfig.MaintTaskConfigEditParam;
import cn.getech.ehm.task.dto.taskConfig.MaintTaskConfigQueryParam;
import cn.getech.ehm.task.service.IMaintTaskConfigService;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 工单配置控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-21
 */
@RestController
@RequestMapping("/maintTaskConfig")
@Api(tags = "工单配置服务接口")
public class MaintTaskConfigController {

    @Autowired
    private IMaintTaskConfigService maintTaskConfigService;

    /**
     * 分页获取工单配置列表
     */
    @ApiOperation("获取工单超时配置")
    @PostMapping("/list")
    //@Permission("maint:task:config:list")
    public RestResponse<PageResult<MaintTaskConfigDto>> pageList(@Valid @RequestBody MaintTaskConfigQueryParam maintTaskConfigQueryParam) {
        return RestResponse.ok(maintTaskConfigService.pageDto(maintTaskConfigQueryParam));
    }

    @ApiOperation("获取工单指派策略配置")
    @PostMapping("/list/option")
    //@Permission("maint:task:config:list")
    public RestResponse<PageResult<MaintTaskConfigDto>> searchWithParam(@Valid @RequestBody MaintTaskConfigQueryParam maintTaskConfigQueryParam) {
        return RestResponse.ok(maintTaskConfigService.pageDtoOfSendRule(maintTaskConfigQueryParam));
    }

    @ApiOperation("获取工单指派策略配置")
    @PostMapping("/list/overtime/check")
    //@Permission("maint:task:config:list")
    public RestResponse<PageResult<MaintTaskConfigDto>> pageDtoOfOvertimeCheckRule(@Valid @RequestBody MaintTaskConfigQueryParam maintTaskConfigQueryParam) {
        return RestResponse.ok(maintTaskConfigService.pageDtoOfOvertimeCheckRule(maintTaskConfigQueryParam));
    }

    @ApiOperation("获取工单通知配置")
    @PostMapping("/list/notify/rule")
    //@Permission("maint:task:config:list")
    public RestResponse<PageResult<MaintTaskConfigDto>> listNotifyRule(@Valid @RequestBody MaintTaskConfigQueryParam maintTaskConfigQueryParam) {
        return RestResponse.ok(maintTaskConfigService.listNotifyRule(maintTaskConfigQueryParam));
    }

    /**
     * 新增工单配置
     */
//    @ApiOperation("新增工单配置")
//    @AuditLog(title = "工单配置",desc = "新增工单配置",businessType = BusinessType.INSERT)
//    @PostMapping
//    //@Permission("maint:task:config:update")
//    public RestResponse<Boolean> add(@RequestBody @Valid MaintTaskConfigAddParam maintTaskConfigAddParam) {
//        return RestResponse.ok(maintTaskConfigService.saveByParam(maintTaskConfigAddParam));
//    }

    /**
     * 修改工单配置
     */
    @ApiOperation(value = "修改工单配置")
    @AuditLog(title = "工单配置", desc = "修改工单配置", businessType = BusinessType.UPDATE)
    @PutMapping
    //@Permission("maint:task:config:update")
    public RestResponse<Boolean> update(@RequestBody @Valid MaintTaskConfigEditParam maintTaskConfigEditParam) {
        return RestResponse.ok(maintTaskConfigService.updateByParam(maintTaskConfigEditParam));
    }

    @ApiOperation(value = "批量修改工单配置")
    @AuditLog(title = "批量修改工单配置", desc = "批量修改工单配置", businessType = BusinessType.UPDATE)
    @PutMapping("/batchUpdate")
    public RestResponse<Boolean> batchUpdate(@RequestBody List<MaintTaskConfigEditParam> editParams) {
        if (CollectionUtils.isNotEmpty(editParams)) {
            editParams.forEach(param -> maintTaskConfigService.updateByParam(param));
        }
        return RestResponse.ok(true);
    }

    @ApiOperation("修改故障工单指派策略")
    @PostMapping("/update/manualRepair/send/rule")
    public RestResponse updateManualRepairSendRule(@RequestBody @Valid MaintTaskConfigEditParam maintTaskConfigEditParam) {
        return RestResponse.ok(maintTaskConfigService.updateManualRepairSendRule(maintTaskConfigEditParam));
    }

    /**
     * 根据id删除工单配置
     */
//    @ApiOperation(value="根据id删除工单配置")
//    @AuditLog(title = "工单配置",desc = "工单配置",businessType = BusinessType.DELETE)
//    @DeleteMapping("/{ids}")
//    //@Permission("maint:task:config:delete")
//    public RestResponse<Boolean> delete(@PathVariable("ids") @NotEmpty Long[] ids) {
//        return RestResponse.ok(maintTaskConfigService.removeByIds(ids));
//    }
//
//    /**
//     * 根据id获取工单配置
//     */
//    @ApiOperation(value = "根据id获取工单配置")
//    @GetMapping(value = "/{id}")
//    //@Permission("maint:task:config:list")
//    public RestResponse<MaintTaskConfigDto> get(@PathVariable  Long id) {
//        return RestResponse.ok(maintTaskConfigService.getDtoById(id));
//    }
//
//    /**
//     * 导出工单配置列表
//     */
//    @ApiOperation(value = "导出工单配置列表")
//    @AuditLog(title = "工单配置",desc = "导出工单配置列表",businessType = BusinessType.EXPORT)
//    @GetMapping("/export")
//   // @Permission("maint:task:config:export")
//    public void excelExport(@Valid MaintTaskConfigQueryParam maintTaskConfigQueryParam, HttpServletResponse response){
//        PageResult<MaintTaskConfigDto> pageResult  = maintTaskConfigService.pageDto(maintTaskConfigQueryParam);
//        ExcelUtils<MaintTaskConfigDto> util = new ExcelUtils<>(MaintTaskConfigDto.class);
//
//        util.exportExcel(pageResult.getRecords(), "工单配置",response);
//    }
//
//    /**
//     * Excel导入工单配置
//     */
//    @ApiOperation(value = "Excel导入工单配置")
//    @AuditLog(title = "工单配置",desc = "Excel导入工单配置",businessType = BusinessType.INSERT)
//    @PostMapping("/import")
//    //@Permission("maint:task:config:import")
//    public RestResponse<Boolean> excelImport(@RequestParam("file") MultipartFile file){
//        ExcelUtils<MaintTaskConfigDto> util = new ExcelUtils<>(MaintTaskConfigDto.class);
//        List<MaintTaskConfigDto> rows = util.importExcel(file);
//        if (CollectionUtils.isEmpty(rows)){
//            return RestResponse.failed();
//        }
//        return RestResponse.ok(maintTaskConfigService.saveDtoBatch(rows));
//    }

}
