package cn.getech.ehm.task.dto.defect;

import cn.getech.poros.framework.common.param.ApiParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <pre>
 * 缺陷信息 编辑参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-02-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DefectInfoSubmitDealPersonAndMaintParam", description = "缺陷信息编辑参数")
public class DefectInfoSubmitDealPersonAndMaintParam extends ApiParam {


    @ApiModelProperty(value = "截止日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "缺陷处理人ids")
    @NotEmpty
    private String[] dealPersonIds;

    @ApiModelProperty(value = "缺陷处理人-工单缺陷处理人 ids")
    @NotEmpty
    private String[] maintDealPersonIds;

    @ApiModelProperty(value = "缺陷处理人-工单缺陷处理人 ids")
    private String[] staffIds;

    @ApiModelProperty("是否需要自动分派当班人员")
    private Boolean needAutoCalander;

    @ApiModelProperty(value = "建议处理方案")
    private String suggestDealContent;

    @ApiModelProperty(value = "工单编号")
    private String maintCode;

    @ApiModelProperty(value = "工单名称")
    //@NotBlank(message = "工单名称不能为空")
    private String maintName;

    @ApiModelProperty(value = "工单类别")
    //@NotBlank(message = "工单类别不能为空")
    private String maintType;

    @ApiModelProperty(value = "工单紧急程度")
    //@NotBlank(message = "工单紧急程度不能为空")
    private String maintUrgency;

    @ApiModelProperty(value = "工单截止日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    //@NotNull(message = "工单截止日期不能为空")
    private Date maintEndTIme;

    @ApiModelProperty(value = "设备id")
    @NotBlank(message = "设备id不能为空")
    private String equipmentId;

    @ApiModelProperty(value = "缺陷单id")
    private String defectInfoId;

    @ApiModelProperty(value = "来源工单id")
    private String sourceTaskId;

    @ApiModelProperty(value = "缺陷原因")
    private String defectReason;

}
