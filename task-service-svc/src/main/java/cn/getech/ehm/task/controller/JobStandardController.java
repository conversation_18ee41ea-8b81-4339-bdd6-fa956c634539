package cn.getech.ehm.task.controller;

import cn.getech.ehm.common.dto.EnumListDto;
import cn.getech.ehm.common.util.excel.FormExcelUtils;
import cn.getech.ehm.task.dto.job.*;
import cn.getech.ehm.task.enums.TaskIotPushType;
import cn.getech.ehm.task.service.IJobStandardItemService;
import cn.getech.ehm.task.service.IJobStandardService;
import cn.getech.poros.framework.common.annotation.CreateByAndUpdateBy;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.ArrayList;
import java.util.List;

/**
 * 作业标准接口
 *
 * <AUTHOR>
 * @since 2020-08-06
 */
@RestController
@RequestMapping("/jobStandard")
@Api(tags = "作业标准接口")
public class JobStandardController {

    @Autowired
    private IJobStandardService jobStandardService;
    @Autowired
    private IJobStandardItemService standardItemService;

    /**
     * 分页获取作业标准列表
     */
    @ApiOperation("分页获取作业标准列表")
    @PostMapping("/pageList")
    public RestResponse<PageResult<JobStandardListDto>> pageList(@RequestBody JobStandardQueryParam queryParam) {
        return RestResponse.ok(jobStandardService.pageList(queryParam));
    }

    /**
     * 分页获取作业标准详细信息
     */
    @ApiOperation("弹框分页获取作业标准详细信息")
    @PostMapping("/detailList")
    public RestResponse<PageResult<JobStandardDetailDto>> detailList(@RequestBody JobStandardQueryParam queryParam) {
        return RestResponse.ok(jobStandardService.detailList(queryParam));
    }

    /**
     * 新增作业标准
     */
    @ApiOperation("新增作业标准")
    @AuditLog(title = "作业标准", desc = "新增作业标准", businessType = BusinessType.INSERT)
    @PostMapping
    public RestResponse<Boolean> add(@RequestBody @Valid JobStandardAddParam addParam) {
        return RestResponse.ok(jobStandardService.saveByParam(addParam));
    }

    /**
     * 修改作业标准
     */
    @ApiOperation("修改作业标准")
    @AuditLog(title = "作业标准", desc = "修改作业标准", businessType = BusinessType.UPDATE)
    @PutMapping
    //@Permission("manual:repair:update")
    public RestResponse<Boolean> edit(@RequestBody @Valid JobStandardEditParam editParam) {
        return RestResponse.ok(jobStandardService.editByParam(editParam));
    }

    /**
     * 根据id获取作业标准
     */
    @ApiOperation(value = "根据id获取作业标准")
    @GetMapping(value = "/{id}")
    @CreateByAndUpdateBy(value = true)
    public RestResponse<JobStandardDto> get(@PathVariable String id) {
        return RestResponse.ok(jobStandardService.getDtoById(id));
    }

    /**
     * 分页获取作业项目列表
     */
    @ApiOperation("分页获取作业项目列表")
    @PostMapping("/detailPageList")
    public RestResponse<PageResult<JobStandardItemDto>> detailPageList(@RequestBody ItemQueryParam queryParam) {
        return RestResponse.ok(standardItemService.pageList(queryParam));
    }

    /**
     * 根据id删除作业标准
     */
    @ApiOperation(value="根据id删除作业标准")
    @AuditLog(title = "作业标准",desc = "作业标准",businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    //@Permission("equipment:delete")
    public RestResponse<Boolean> delete(@PathVariable("ids") @NotEmpty String[] ids) {
        return RestResponse.ok(jobStandardService.deleteByIds(ids));
    }

    /**
     * 导出作业项目模板
     */
    @ApiOperation(value = "导出作业项目模板")
    @AuditLog(title = "作业项目",desc = "导出作业项目模板",businessType = BusinessType.EXPORT)
    @PostMapping("/exportModel")
    public void exportModel(HttpServletResponse response){
        FormExcelUtils<StandardItemExcelDto> util = new FormExcelUtils<>(StandardItemExcelDto.class);
        util.exportExcel(new ArrayList<>(), "作业项目导入模板", response,
                null, standardItemService.getComboMap(), null);

    }

    /**
     * Excel导入作业项目
     */
    @ApiOperation(value = "Excel导入作业项目")
    @AuditLog(title = "作业项目",desc = "Excel导入作业项目",businessType = BusinessType.INSERT)
    @ApiImplicitParams({
            @ApiImplicitParam(name="jobType",value="作业类型",dataType="String", paramType = "query")
    })
    @PostMapping("/import")
    public RestResponse<JobStandardExcelResDto> excelImport(@RequestPart("file") MultipartFile file, @RequestParam("jobType") String jobType){
        FormExcelUtils<StandardItemExcelDto> util = new FormExcelUtils<>(StandardItemExcelDto.class);
        List<StandardItemExcelDto> rows = util.importExcel(file, null);
        return RestResponse.ok(standardItemService.excelImport(rows, jobType));
    }

    @ApiOperation(value = "是否推送iot枚举")
    @GetMapping(value = "/getTaskIotPushType")
    public RestResponse<List<EnumListDto>> getTaskIotPushType() {
        return RestResponse.ok(TaskIotPushType.getList());
    }
}
