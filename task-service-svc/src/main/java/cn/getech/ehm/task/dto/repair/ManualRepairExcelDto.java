package cn.getech.ehm.task.dto.repair;

import cn.getech.poros.framework.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

/**
 * 故障报修导出列表
 */
@Data
@EqualsAndHashCode()
@ApiModel(value = "ManualRepairExcelDto", description = "故障报修导出列表")
public class ManualRepairExcelDto {

    @ApiModelProperty(value = "报修/提报时间")
    @Excel(name="报修时间",cellType = Excel.ColumnType.STRING,dateFormat="yyyy-MM-dd HH:mm:ss" )
    private Date createTime;

    @ApiModelProperty(value = "报修人/创建人")
    @Excel(name="报修人",cellType = Excel.ColumnType.STRING )
    private String createUserName;

    @ApiModelProperty(value = "设备id")
    private String equipmentId;

    @ApiModelProperty(value = "设备名称")
    @Excel(name="故障设备",cellType = Excel.ColumnType.STRING )
    private String equipmentName;

    @ApiModelProperty(value = "设备编码")
    @Excel(name="设备编码",cellType = Excel.ColumnType.STRING )
    private String equipmentCode;

    @ApiModelProperty(value = "紧急程度字典值")
    private String urgency;

    @ApiModelProperty(value = "紧急程度")
    @Excel(name="紧急程度",cellType = Excel.ColumnType.STRING )
    private String urgencyName;

    @ApiModelProperty(value = "状态(0未维修1维修中2已维修)")
    @Excel(name="维修状态",cellType = Excel.ColumnType.STRING,readConverterExp = "0=未维修,1=维修中,2=已维修")
    private Integer status;

    @ApiModelProperty(value = "故障现象ids")
    private String[] faultPhenomenonIds;

    @ApiModelProperty(value = "故障现象扩展")
    @Excel(name="故障现象",cellType = Excel.ColumnType.STRING )
    private String faultPhenomenonRemark;

    @ApiModelProperty(value = "修复/完成时间")
    @Excel(name="修复时间",cellType = Excel.ColumnType.STRING,dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date repairTime;

    @ApiModelProperty(value = "工单Code")
    @Excel(name="维修工单",cellType = Excel.ColumnType.STRING )
    private String taskCode;

}
