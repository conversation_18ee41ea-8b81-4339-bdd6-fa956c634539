package cn.getech.ehm.task.entity;

import cn.getech.ehm.common.mp.handler.StringArrayTypeHandler;
import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

/**
 * 人工报修
 *
 * <AUTHOR>
 * @since 2020-08-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("manual_repair")
public class ManualRepair extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 设备id
     */
    @TableField("equipment_id")
    private String equipmentId;

    /**
     * 故障类别
     */
    @TableField("fault_type")
    private String faultType;

    /**
     * 专业
     */
    @TableField("major")
    private String major;

    /**
     * 紧急程度
     */
    @TableField("urgency")
    private String urgency;

    /**
     * 影响程度
     */
    @TableField("influence")
    private String influence;

    /**
     * 故障现象ids
     */
    @TableField(value = "fault_phenomenon_ids", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] faultPhenomenonIds;

    /**
     * 故障现象扩展
     */
    @TableField("fault_phenomenon_remark")
    private String faultPhenomenonRemark;

    /**
     * 故障原因ids
     */
    @TableField(value = "fault_reason_ids", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] faultReasonIds;

    /**
     * 故障原因扩展
     */
    @TableField("fault_reason_remark")
    private String faultReasonRemark;

    /**
     * 处理措施ids
     */
    @TableField(value = "fault_measures_ids", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] faultMeasuresIds;

    /**
     * 处理措施扩展
     */
    @TableField("fault_measures_remark")
    private String faultMeasuresRemark;

    /**
     * 工单id
     */
    @TableField("task_id")
    private String taskId;

    /**
     * 维护工单编号
     */
    @TableField("task_code")
    private String taskCode;

    /**
     * 状态(0未维修1维修中2已维修)
     */
    @TableField("status")
    private Integer status;

    /**
     * 0新增1工单生成2告警日志生成
     */
    @TableField("source")
    private Integer source;

    /**
     * 关联id
     * 目前只有设备参数关联表id
     */
    @TableField("source_id")
    private String sourceId;

    /**
     * 附件id集合
     */
    @TableField("media_ids")
    private String mediaIds;

    /**
     * 修复时间
     */
    @TableField("repair_time")
    private Date repairTime;

    /**
     * 故障时间
     */
    @TableField("fault_time")
    private Date faultTime;

    /**
     * 是否删除(0false1true)
     */
    @TableField("deleted")
    private Integer deleted;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 提报人名称
     * 为了前端查询，冗余
     */
    @TableField("create_user_name")
    private String createUserName;

    //截止日期
    private Date deadlineDate;

}
