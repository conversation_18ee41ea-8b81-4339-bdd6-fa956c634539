package cn.getech.ehm.task.entity;

import cn.getech.poros.framework.common.bean.BaseEntity;
import cn.getech.ehm.common.mp.handler.StringArrayTypeHandler;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

/**
 * 作业票
 *
 * <AUTHOR>
 * @since 2020-08-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("job_ticket_item")
public class JobTicketItem extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 作业票id
     */
    @TableField("ticket_id")
    private String ticketId;

    /**
     * 安全确认内容
     */
    @TableField(value = "content")
    private String content;

    /**
     * 内容类型1风险提示2安全措施
     */
    @TableField(value = "content_type")
    private Integer contentType;

    /**
     * 已选择作业类型
     */
    @TableField(value = "ticket_types", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] ticketTypes;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 是否删除(0false1true)
     */
    @TableField("deleted")
    private Integer deleted;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;
}
