package cn.getech.ehm.task.controller;

import cn.getech.ehm.common.dto.EnumListDto;
import cn.getech.ehm.task.dto.shield.MaintTaskShieldDto;
import cn.getech.ehm.task.enmu.TaskShieldWeekType;
import cn.getech.ehm.task.service.IMaintTaskShieldService;
import cn.getech.poros.framework.common.api.RestResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import java.util.List;

/**
 * 工单屏蔽
 */
@RestController
@RequestMapping("/taskShield")
@Api(tags = "工单屏蔽接口")
public class MaintTaskShieldController {

    @Autowired
    private IMaintTaskShieldService taskShieldService;

    @ApiOperation("修改工单屏蔽信息")
    @PostMapping("/edit")
    public RestResponse<Boolean> edit(@RequestBody @Valid MaintTaskShieldDto dto) {
        return RestResponse.ok(taskShieldService.updateByParam(dto));
    }

    @ApiOperation(value = "获取工单屏蔽信息")
    @GetMapping
    public RestResponse<MaintTaskShieldDto> get() {
        return RestResponse.ok(taskShieldService.getDto());
    }

    @ApiOperation("获取周枚举")
    @GetMapping("/getWeekType")
    public RestResponse<List<EnumListDto>> getWeekType() {
        return RestResponse.ok(TaskShieldWeekType.getList());
    }
}
