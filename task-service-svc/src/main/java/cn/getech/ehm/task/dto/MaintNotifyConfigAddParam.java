package cn.getech.ehm.task.dto;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;

import java.util.Date;

/**
 * <pre>
 *  新增参数
 * </pre>
 *
 * <AUTHOR>
 * @date 2025-02-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MaintNotifyConfig新增", description = "新增参数")
public class MaintNotifyConfigAddParam extends ApiParam {

    @ApiModelProperty(value = "")
    private String createBy;
    @ApiModelProperty(value = "")
    private String updateBy;
    @ApiModelProperty(value = "")
    private Date createTime;
    @ApiModelProperty(value = "")
    private Date updateTime;
    @ApiModelProperty(value = "")
    private String remark;
}