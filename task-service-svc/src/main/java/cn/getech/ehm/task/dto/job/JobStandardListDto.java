package cn.getech.ehm.task.dto.job;

import cn.getech.ehm.common.constant.StaticValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;

/**
 * 作业标准列表
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "JobStandardListDto", description = "作业标准列表")
public class JobStandardListDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "设备类型id集合")
    private String[] infoCategoryIds;

    @ApiModelProperty(value = "设备类型名称")
    private String infoCategoryNames;

    @ApiModelProperty(value = "设备位置id集合")
    private String[] infoLocationIds;

    @ApiModelProperty(value = "设备位置名称")
    private String infoLocationNames;

    @ApiModelProperty(value = "专业类别集合")
    private String[] majors;

    @ApiModelProperty(value = "专业类别名称")
    private String majorNames;

    @ApiModelProperty(value = "作业类别")
    private String jobType;

    @ApiModelProperty(value = "作业类别名称")
    private String jobTypeName;

    @ApiModelProperty(value = "发布状态")
    private Boolean published;

    @ApiModelProperty(value = "使用状态1使用中2未使用")
    private Integer status;

    @ApiModelProperty(value = "标准工时统计")
    private BigDecimal standardTimeCount;

    @ApiModelProperty(value = "作业时间统计")
    private BigDecimal workingTimeCount;

    @ApiModelProperty(value = "描述")
    private String remark;

    @ApiModelProperty("设备类型-权限划分用")
    private String equipType;

    @ApiModelProperty("设备类型-权限划分用")
    private String equipTypeName;
}