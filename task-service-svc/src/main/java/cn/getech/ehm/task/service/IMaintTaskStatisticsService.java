package cn.getech.ehm.task.service;

import cn.getech.ehm.task.dto.task.statistics.TaskStatisticsResultDto;
import cn.getech.ehm.task.dto.task.statistics.TaskStatisticsSearchDto;
import cn.getech.ehm.task.entity.MaintTask;
import cn.getech.poros.framework.common.service.IBaseService;

import java.util.List;

/**
 * 维护工单主表 服务类
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
public interface IMaintTaskStatisticsService extends IBaseService<MaintTask> {
    public List<TaskStatisticsResultDto> byLocationId(TaskStatisticsSearchDto param);

    public List<TaskStatisticsResultDto> byTime(TaskStatisticsSearchDto param);

    public List<TaskStatisticsResultDto> byStaff(TaskStatisticsSearchDto param);

    public List<TaskStatisticsResultDto> byStructure(TaskStatisticsSearchDto param);
//
    public List<TaskStatisticsResultDto> byStructurePlato(TaskStatisticsSearchDto param);

    public List<TaskStatisticsResultDto> byRepairTime(TaskStatisticsSearchDto param);

    public List<TaskStatisticsResultDto> analysis(TaskStatisticsSearchDto param);
}