package cn.getech.ehm.task.dto.plan;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;


/**
 * <pre>
 * 维护计划APP 返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "MaintPlanAppDto", description = "维护计划APP返回数据模型")
public class MaintPlanAppDto {

    @ApiModelProperty(value = "计划名称")
    private String name;

    @ApiModelProperty(value = "计划类别 0 点检 1 预防维护 3 维护改进 4 转产")
    private Integer planType;

    @ApiModelProperty(value = "紧急程度 " +
            "0：未定义" +
            "1：紧急" +
            "2：高" +
            "3：中" +
            "4：低")
    private Integer priority;

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "下次计划执行时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date planTime;

    @ApiModelProperty(value = "上次维护时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastTime;

    //截止天数
    private Integer deadlineDays;

}