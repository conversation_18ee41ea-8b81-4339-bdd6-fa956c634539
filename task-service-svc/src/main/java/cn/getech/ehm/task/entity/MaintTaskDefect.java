package cn.getech.ehm.task.entity;

    import cn.getech.ehm.common.mp.handler.StringArrayTypeHandler;
    import com.baomidou.mybatisplus.annotation.TableName;
    import java.util.Date;
    import com.baomidou.mybatisplus.annotation.TableField;
    import cn.getech.poros.framework.common.bean.BaseEntity;
    import io.swagger.annotations.ApiModel;
    import io.swagger.annotations.ApiModelProperty;
    import lombok.Data;
    import lombok.EqualsAndHashCode;
    import lombok.experimental.Accessors;
import cn.getech.poros.framework.common.annotation.Excel;

/**
 * <p>
 * 缺陷记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("maint_task_defect")
public class MaintTaskDefect extends BaseEntity {

    private static final long serialVersionUID=1L;

    @TableField("deleted")
    private Integer deleted;

    @TableField("tenant_id")
    private String tenantId;

    /**
     * 设备id
     */
    @TableField("equipment_id")
    private String equipmentId;

    /**
     * 缺陷名称
     */
    @TableField("defect_name")
    private String defectName;

    /**
     * 缺陷内容
     */
    @TableField("defect_content")
    private String defectContent;

    /**
     * 影响描述
     */
    @TableField("affect_content")
    private String affectContent;

    /**
     * 缺陷种类
     */
    @TableField("defect_type")
    private Integer defectType;

    /**
     * 专业类别
     */
    @TableField("major_type")
    private Integer majorType;

    /**
     * 现场图片/视频
     */
    @TableField(value = "live_media_ids",typeHandler = StringArrayTypeHandler.class)
    private String[] liveMediaIds;

    /**
     * 缺陷状态
     */
    @TableField("defect_status")
    private Integer defectStatus;

    /**
     * 验收状态
     */
    @TableField("check_status")
    private Integer checkStatus;

    /**
     * 截止日期
     */
    @TableField("end_time")
    private Date endTime;

    /**
     * 处理人ids
     */
    @TableField("deal_person_ids")
    private String dealPersonIds;

    /**
     * 建议处理方案
     */
    @TableField("suggest_deal_content")
    private String suggestDealContent;

    /**
     * 工单id
     */
    @TableField("maint_task_id")
    private String maintTaskId;

    /**
     * 工单code
     */
    @TableField("maint_task_code")
    private String maintTaskCode;

    /**
     * 验收说明
     */
    @TableField("check_explain")
    private String checkExplain;

    /**
     * 实际处理方案
     */
    @TableField("real_deal_content")
    private String realDealContent;

    /**
     * 关闭原因
     */
    @TableField("close_reason")
    private String closeReason;

    /**
     * 处理时间
     */
    @TableField("deal_date")
    private Date dealDate;

    /**
     * 关闭时间
     */
    @TableField("close_date")
    private Date closeDate;

    /**
     * 来源工单id
     */
    @TableField("source_task_id")
    private String sourceTaskId;

    /**
     * 缺陷原因
     */
    @TableField("defect_reason")
    private String defectReason;

    /**
     * 来源工单编码
     */
    @TableField("source_task_code")
    private String sourceTaskCode;

    private String rejectReason;

}
