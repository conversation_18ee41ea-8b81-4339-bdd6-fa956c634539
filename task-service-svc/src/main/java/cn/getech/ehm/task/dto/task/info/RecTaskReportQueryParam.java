package cn.getech.ehm.task.dto.task.info;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 工单统计查询参数对象
 *
 */
@Data
@EqualsAndHashCode()
@ApiModel(value = "工单统计查询参数对象", description = "工单统计查询参数对象")
public class RecTaskReportQueryParam {
    @ApiModelProperty("用户名")
    private String keyword;

    @ApiModelProperty("当前用户")
    private String handler;

    @ApiModelProperty(value = "查询开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "查询开始时间不能为空")
    private Date beginTime;

    @ApiModelProperty(value = "查询结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "查询结束时间不能为空")
    private Date endTime;
}
