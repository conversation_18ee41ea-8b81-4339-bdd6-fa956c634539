package cn.getech.ehm.task.dto.task.history;

import cn.getech.poros.framework.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * <pre>
 * 工单历史 返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2025-02-05
 */
@Data
@ApiModel(value = "MaintTaskHistoryDto", description = "工单历史返回数据模型")
public class MaintTaskHistoryDto {

    @ApiModelProperty(value = "")
    @Excel(name = "", cellType = Excel.ColumnType.STRING)
    private String id;

    @ApiModelProperty(value = "createBy")
    @Excel(name = "createBy", cellType = Excel.ColumnType.STRING)
    private String createBy;

    @ApiModelProperty(value = "updateBy")
    @Excel(name = "updateBy", cellType = Excel.ColumnType.STRING)
    private String updateBy;

    @ApiModelProperty(value = "createTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "createTime", cellType = Excel.ColumnType.STRING, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "updateTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "updateTime", cellType = Excel.ColumnType.STRING, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(value = "remark")
    @Excel(name = "remark", cellType = Excel.ColumnType.STRING)
    private String remark;

    @ApiModelProperty(value = "流程id")
    private String processInstanceId;

    @ApiModelProperty(value = "环节id")
    private String activityId;

    @ApiModelProperty(value = "环节名")
    private String activityName;

    @ApiModelProperty(value = "任务id")
    private String taskId;

    @ApiModelProperty(value = "候选人/接单人")
    private String assigneeName;

    @ApiModelProperty(value = "候选人/接单人")
    private String assigneeUid;

    @ApiModelProperty(value = "备注")
    private String comment;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "处理状态")
    private String operator;

    @ApiModelProperty(value = "已读状态")
    private String readStatus;

    @ApiModelProperty(value = "任务处理中/任务已完成")
    private String taskStatus;

    @ApiModelProperty(value = "工单id")
    private String maintTaskId;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "旧状态")
    private String oldStatus;

    @ApiModelProperty(value = "新状态")
    private String newStatus;

    @ApiModelProperty(value = "状态变化")
    private String statusChange;

}