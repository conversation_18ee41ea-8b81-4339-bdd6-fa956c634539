package cn.getech.ehm.task.dto.screen;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WorkCalendarAppResult {

    @JsonFormat(pattern = "MM-dd", timezone = "GMT+8")
    public String date;

    public WorkCalendarResult result;
}
