package cn.getech.ehm.task.dto.task.history;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;

/**
 * <pre>
 * 工单历史 新增参数
 * </pre>
 *
 * <AUTHOR>
 * @date 2025-02-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MaintTaskHistory新增", description = "工单历史新增参数")
public class MaintTaskHistoryAddParam extends ApiParam {

    @ApiModelProperty(value = "")
    private String createBy;
    @ApiModelProperty(value = "")
    private String updateBy;
    @ApiModelProperty(value = "")
    private Date createTime;
    @ApiModelProperty(value = "")
    private Date updateTime;
    @ApiModelProperty(value = "")
    private String remark;
    @ApiModelProperty(value = "流程id")
    private String processInstanceId;

    @ApiModelProperty(value = "环节id")
    private String activityId;

    @ApiModelProperty(value = "环节名")
    private String activityName;

    @ApiModelProperty(value = "任务id")
    private String taskId;

    @ApiModelProperty(value = "候选人/接单人")
    private String assigneeName;

    @ApiModelProperty(value = "候选人/接单人")
    private String assigneeUid;

    @ApiModelProperty(value = "备注")
    private String comment;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    @ApiModelProperty(value = "处理状态")
    private String operator;

    @ApiModelProperty(value = "已读状态")
    private String readStatus;

    @ApiModelProperty(value = "任务处理中/任务已完成")
    private String taskStatus;

    @ApiModelProperty(value = "工单id")
    private String maintTaskId;
}