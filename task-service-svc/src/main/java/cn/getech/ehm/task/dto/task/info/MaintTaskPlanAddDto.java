package cn.getech.ehm.task.dto.task.info;

import cn.getech.ehm.task.dto.task.notify.MaintNotifyDto;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * 计划生成工单dto
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "MaintTaskPlanAddDto", description = "计划生成工单dto")
public class MaintTaskPlanAddDto{

    @ApiModelProperty(value = "工单名称", required = true)
    private String name;

    @ApiModelProperty(value = "工单编码", required = true)
    private String code;

    @ApiModelProperty(value = "维护设备id", required = true)
    private String equipmentId;

    @ApiModelProperty(value = "维保类型", required = true)
    private String jobType;

    @ApiModelProperty(value = "维保等级")
    private Integer jobLevel;

    @ApiModelProperty(value = "计划维护时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date planMaintTime;

    @ApiModelProperty(value = "优先程度", required = true)
    private String urgency;

    @ApiModelProperty(value = "专业", required = true)
    private String major;

    @ApiModelProperty(value = "维护人员id集合")
    private String[] staffIds;

    @ApiModelProperty(value = "维护班组id集合")
    private String[] teamIds;

    @ApiModelProperty(value = "计划单id", required = true)
    private String sourceId;

    @ApiModelProperty(value = "计划单名称")
    private String sourceName;

    @ApiModelProperty(value = "是否允许自由扫码接单(0否1是)")
    private Integer freeTask;

    @ApiModelProperty(value = "维保作业id")
    private String standardId;

    @ApiModelProperty(value = "维保作业内容")
    private String content;

    @ApiModelProperty(value = "截止天数/小时")
    private Integer deadlineDays;

    @ApiModelProperty(value = "工单截止日期")
    private Date taskDeadlineDate;

    /*@ApiModelProperty(value = "排班标记")
    private String sourcePlanEquipmentId;

    @ApiModelProperty("业务类型")
    private String bussinessType;

    @ApiModelProperty("区域")
    private String areaType;

    @ApiModelProperty("工序")
    private String processType;

    @ApiModelProperty(value = "人员策略为排班")
    private Boolean schedulePersonStrategy;*/

    @ApiModelProperty(value = "cbm触发频率")
    private Integer cbmFrequency;

    @ApiModelProperty(value = "超期后处理方式")
    private Integer overdueHandlingMethod;

    @ApiModelProperty(value = "临期通知")
    private MaintNotifyDto adventNotify;

    @ApiModelProperty(value = "设备是否停机(运行状态停机、设备同步机况暂停)")
    private Boolean equipmentStop;

    //来源维保计划细则id
    private String sourcePlanEquipmentId;

    private UserBaseInfo userBaseInfo;
}
