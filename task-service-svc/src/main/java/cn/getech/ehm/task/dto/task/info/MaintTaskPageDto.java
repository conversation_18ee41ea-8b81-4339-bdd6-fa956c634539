package cn.getech.ehm.task.dto.task.info;

import cn.getech.poros.framework.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 维护工单分页dto
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "MaintTaskPageDto", description = "维护工单分页dto")
public class MaintTaskPageDto {

    @ApiModelProperty(value = "工单id")
    private String id;

    @ApiModelProperty(value = "工单编号")
    private String code;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "状态名称")
    private String statusName;

    @ApiModelProperty(value = "紧急程度")
    private String urgency;

    @ApiModelProperty(value = "来源(1故障报修2维保计划)")
    private Integer sourceType;

    @ApiModelProperty(value = "专业/工单类别")
    private String major;

    @ApiModelProperty(value = "维护设备id")
    private String equipmentId;

    @ApiModelProperty(value = "设备编码")
    private String equipmentCode;

    @ApiModelProperty(value = "设备名称")
    private String equipmentName;

    @ApiModelProperty(value = "设备类型")
    private String equipmentCategoryName;

    @ApiModelProperty(value = "设备运行状态")
    private Integer equipmentRunningStatus;

    @ApiModelProperty(value = "故障/维保内容")
    private String content;

    @ApiModelProperty(value = "故障现象")
    private String faultPhenomenonRemark;

    @ApiModelProperty(value = "故障影响")
    private String[] faultInfluences;

    @ApiModelProperty(value = "故障影响字符串")
    private String faultInfluenceRemark;

    @ApiModelProperty(value = "故障原因")
    private String faultReasonRemark;

    @ApiModelProperty(value = "处理措施")
    private String faultMeasuresRemark;

    @ApiModelProperty(value = "创建人(uid转名称)")
    private String createBy;

    @ApiModelProperty(value = "创建人(uid转名称)")
    private String createByUid;

    /*@ApiModelProperty(value = "报修人/创建人")
    private String createUserName;*/

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value = "维护人员id集合")
    private String allStaffIds;

    @ApiModelProperty(value = "维护人员名称集合")
    private String staffNames;

    @ApiModelProperty(value = "缺陷单id")
    private String defectId;

    @ApiModelProperty(value = "缺陷单名称")
    private String defectName;

    @ApiModelProperty("派单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date sendTaskDate;

    @ApiModelProperty("接单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date recTaskDate;

    @ApiModelProperty("派单截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date sendTaskDeadlineDate;

    @ApiModelProperty("接单截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date recTaskDeadlineDate;

    @ApiModelProperty("工单截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date taskDeadlineDate;

    @ApiModelProperty("是否超时")
    private Boolean isOverTime;

    @ApiModelProperty("维保类型")
    private String jobType;

    @ApiModelProperty("维保等级")
    private Integer jobLevel;

    @ApiModelProperty("是否异常")
    private Boolean ecError;

    @ApiModelProperty("点检异常数量")
    private Integer ecErrorNum;

    @ApiModelProperty("点检异常处理方式")
    private Integer ecErrorResult;

    @ApiModelProperty(value = "异常处理")
    @Excel(name = "异常处理", cellType = Excel.ColumnType.STRING)
    private String ecErrorResultName;

    @ApiModelProperty("点检异常处理结果")
    private String ecErrorDealContent;

    @ApiModelProperty("ec错误关联工单id")
    private String ecErrorTaskId;

    @ApiModelProperty("ec错误关联工单号")
    private String ecErrorTaskCode;

    @ApiModelProperty("ec错误关联工单名称")
    private String ecErrorTaskName;

    @ApiModelProperty("接单人id")
    private String handler;

    @ApiModelProperty("接单人/处理人name")
    private String handlerName;

    @ApiModelProperty(value = "首张图片id")
    private String picId;

    @ApiModelProperty(value = "图片url")
    private String picUrl;

    @ApiModelProperty("班组id")
    private String[] teamIds;

    @ApiModelProperty("班组名")
    private String maintTeamName;

    @ApiModelProperty("实际停机开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date beginDowntime;

    @ApiModelProperty("实际停机结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date endDowntime;

    @ApiModelProperty("工单耗时")
    private String maintTimeCost;

    @ApiModelProperty("停机耗时")
    private String downTimeCost;

    @ApiModelProperty("工单开始处理时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date beginMaintTime;

    @ApiModelProperty("工单结束处理时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date endMaintTime;

    @ApiModelProperty("实际工时(去除挂起时间)分钟")
    private Long workingTime;

    @ApiModelProperty("实际工时(去除挂起时间)小时")
    private BigDecimal workingTimeHour;

    @ApiModelProperty("区域")
    private String areaType;

    @ApiModelProperty("工序")
    private String processType;

    @ApiModelProperty(value = "计划维保日期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date planMaintTime;

    @ApiModelProperty(value = "工作流任务id")
    private String activityId;

    @ApiModelProperty(value = "流程实例id")
    private String processInstanceId;

    @ApiModelProperty(value = "工单类型(1故障2维保3缺陷单)")
    private Integer type;

    @ApiModelProperty(value = "工单类型名称")
    private String typeName;

    @ApiModelProperty(value = "参与人员")
    private String userName;

    @ApiModelProperty("是否安全确认")
    private Boolean confirm;

    @ApiModelProperty("结构位置名称(向上三级)")
    private String parentAllName;

    @ApiModelProperty("设备类型树")
    private String categoryAllName;

    @ApiModelProperty("关闭原因/转缺陷原因")
    private String closeReason;

    @ApiModelProperty(value = "序号")
    @Excel(name = "序号", cellType = Excel.ColumnType.STRING)
    private String taskNo;

    @ApiModelProperty(value = "工单名称/编号")
    @Excel(name = "工单名称/编号", cellType = Excel.ColumnType.STRING)
    private String nameStr;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @Excel(name = "更新时间", cellType = Excel.ColumnType.STRING)
    private String updateTimeStr;

    @ApiModelProperty(value = "超时内容")
    private String overTimeContent;

    @ApiModelProperty(value = "作业分类")
    @Excel(name = "作业分类", cellType = Excel.ColumnType.STRING)
    private String maintTaskItemCategoryStr;

    @Excel(name = "作业内容", cellType = Excel.ColumnType.STRING)
    private String maintTaskItemContent;

    @Excel(name = "区间", cellType = Excel.ColumnType.STRING)
    private String targetStr;

    @ApiModelProperty(value = "维保作业id")
    private String standardId;

    @ApiModelProperty(value = "结果")
    private String result;

    @ApiModelProperty(value = "来源工单id")
    private String sourceTaskId;

    @ApiModelProperty(value = "来源工单code")
    private String sourceTaskCode;

    @ApiModelProperty("是否生成了缺陷")
    private Boolean defectFlag;

    @ApiModelProperty("损坏原因")
    private String damageReason;

    @ApiModelProperty("是否有挂起")
    private Boolean hasHangUp;

    @ApiModelProperty("退回原因")
    private String returnReason;

    @ApiModelProperty("重新打开次数")
    private Integer reopenCount;
}