package cn.getech.ehm.task.entity;

import cn.getech.poros.framework.common.bean.BaseEntity;
import cn.getech.ehm.common.mp.handler.StringArrayTypeHandler;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;
import java.util.Date;

/**
 * 维保计划时间
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("plan_equipment_time")
public class PlanEquipmentTime extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 计划id
     */
    @TableField("plan_id")
    private String planId;

    /**
     * 计划维护日期(天)
     */
    @TableField(value = "plan_day")
    private Date planDay;

    /**
     * 计划维护日期(小时:分钟)
     */
    @TableField(value = "plan_time")
    private Date planTime;

    /**
     * 维护人员id字符串
     */
    @TableField(value = "maintainer_ids", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] maintainerIds;

    /**
     * 维护人员id字符串
     */
    @TableField(value = "team_ids", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] teamIds;

    /**
     * 人员指派策略 0 根据设备 1 人工配置 2 自由扫码 3排班
     */
    @TableField("person_strategy")
    private Integer personStrategy;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    @TableField(typeHandler = StringArrayTypeHandler.class)
    private String[] acceptMaintainerIds;

    @TableField(typeHandler = StringArrayTypeHandler.class)
    private String[] acceptTeamIds;
}
