package cn.getech.ehm.task.entity;

import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 工单作业票
 *
 * <AUTHOR>
 * @since 2020-08-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("maint_task_ticket")
public class MaintTaskTicket extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 工单id
     */
    @TableField("task_id")
    private String taskId;

    /**
     * 安全确认内容
     */
    @TableField(value = "content")
    private String content;

    /**
     * 内容类型1风险提示2安全措施
     */
    @TableField(value = "content_type")
    private Integer contentType;

    /**
     * 是否确认
     */
    @TableField("confirm")
    private Boolean confirm;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;
}
