package cn.getech.ehm.task.entity;

import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 维护计划CBM详情
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("maint_plan_cbm")
public class MaintPlanCbm extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 计划id
     */
    @TableField("plan_id")
    private String planId;

    /**
     * 设备类型测点下参数id
     */
    @TableField("category_parameter_id")
    private String categoryParameterId;

    /**
     * 操作类型
     */
    @TableField("operation_type")
    private Integer operationType;

    /**
     * 门限值
     */
    @TableField("threshold_value")
    private Double thresholdValue;

    /**
     * 触发器分组num
     */
    @TableField("group_num")
    private Integer groupNum;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;


}
