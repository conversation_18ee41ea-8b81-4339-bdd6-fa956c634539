package cn.getech.ehm.task.dto.repair;

import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;

/**
 * 故障报修 分页查询参数对象
 *
 * <AUTHOR>
 * @date 2020-08-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "RepairAppQueryParam", description = "故障报修查询参数")
public class RepairAppQueryParam extends PageParam {

    @ApiModelProperty(value = "设备名称/编码")
    private String keyword;

    @ApiModelProperty(value = "是否设备属性0否1是")
    private Integer isRelEquipment;

    @ApiModelProperty(value = "设备id")
    private String equipmentId;

    @ApiModelProperty(value = "状态(0未维修1维修中2已维修)")
    private Integer status;

    @ApiModelProperty(value = "创建人", hidden = true)
    private String createBy;

    @ApiModelProperty(value = "设备ids", hidden = true)
    private List<String> equipmentIds;

}
