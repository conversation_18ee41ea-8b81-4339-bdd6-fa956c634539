package cn.getech.ehm.task.service.impl;

import cn.getech.ehm.base.client.BaseServiceClient;
import cn.getech.ehm.base.dto.AttachmentClientDto;
import cn.getech.ehm.base.dto.DictionaryItemDto;
import cn.getech.ehm.common.context.UserContextHolder;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.iot.client.ParameterClient;
import cn.getech.ehm.iot.dto.parameter.TaskIotPushDto;
import cn.getech.ehm.task.dto.job.JobStandardItemDto;
import cn.getech.ehm.task.dto.task.item.ItemTotalCountDto;
import cn.getech.ehm.task.dto.task.item.MaintTaskItemDto;
import cn.getech.ehm.task.dto.task.item.TaskItemDetailDto;
import cn.getech.ehm.task.entity.MaintTaskItem;
import cn.getech.ehm.task.enums.JobItemResultType;
import cn.getech.ehm.task.enums.TaskIotPushType;
import cn.getech.ehm.task.handler.CommonGetHandler;
import cn.getech.ehm.task.handler.FileGetHandler;
import cn.getech.ehm.task.mapper.MaintTaskItemMapper;
import cn.getech.ehm.task.service.IJobStandardItemService;
import cn.getech.ehm.task.service.IMaintTaskItemService;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * 维护工单、任务关联表 服务实现类
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
@Slf4j
@Service
public class MaintTaskItemServiceImpl extends BaseServiceImpl<MaintTaskItemMapper, MaintTaskItem> implements IMaintTaskItemService {

    @Autowired
    private MaintTaskItemMapper maintTaskItemMapper;
    @Autowired
    private BaseServiceClient baseServiceClient;
    @Autowired
    private IJobStandardItemService standardItemService;
    @Autowired
    private FileGetHandler fileGetHandler;
    @Autowired
    private ParameterClient parameterClient;
    @Autowired
    private CommonGetHandler commonGetHandler;

    @Autowired
    private ExecutorService executorService;

    @Override
    public Boolean saveOrUpdateList(List<TaskItemDetailDto> itemDetailDtos, String taskId){
        this.deleteByTaskId(taskId);
        if(CollectionUtils.isNotEmpty(itemDetailDtos)) {
            List<MaintTaskItem> entities = new ArrayList<>(itemDetailDtos.size());
            int i = 1;
            for(TaskItemDetailDto itemDto :  itemDetailDtos) {
                if (ArrayUtils.isEmpty(itemDto.getFileIds())&&CollectionUtils.isNotEmpty(itemDto.getFileInfoList())){
                    itemDto.setFileIds(itemDto.getFileInfoList().stream().map(item->item.getId()).toArray(String[]::new));
                }
                MaintTaskItem maintTaskItem = CopyDataUtil.copyObject(itemDto,  MaintTaskItem.class);
                maintTaskItem.setTaskId(taskId);
                maintTaskItem.setSort(i++);
                entities.add(maintTaskItem);
            }
            return saveBatch(entities);
        }
        return true;
    }

    @Override
    public MaintTaskItemDto getByTaskId(String taskId, String standardId, Boolean onlyError) {
        MaintTaskItemDto maintTaskItemDto = new MaintTaskItemDto();
        maintTaskItemDto.setTaskId(taskId);

        LambdaQueryWrapper<MaintTaskItem> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(MaintTaskItem::getTaskId, taskId);
        wrapper.eq(onlyError != null && onlyError, MaintTaskItem::getAbnormal,true);
        wrapper.orderByAsc(MaintTaskItem::getSort);
        List<MaintTaskItem> maintTaskItems = maintTaskItemMapper.selectList(wrapper);
        List<TaskItemDetailDto> itemDetailDtos = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(maintTaskItems)) {
            itemDetailDtos = CopyDataUtil.copyList(maintTaskItems,TaskItemDetailDto.class );
        }else {
            List<JobStandardItemDto> jobStandardItemDtos = standardItemService.getListByStandardId(standardId);
            if(CollectionUtils.isNotEmpty(jobStandardItemDtos)){
                for(JobStandardItemDto jobStandardItemDto : jobStandardItemDtos) {
                    TaskItemDetailDto detailDto = CopyDataUtil.copyObject(jobStandardItemDto, TaskItemDetailDto.class);
                    detailDto.setJobStandardItemId(jobStandardItemDto.getId());
                    String newId = UUID.randomUUID().toString().replace(StringPool.DASH, StringPool.EMPTY);
                    detailDto.setId(newId);
                    itemDetailDtos.add(detailDto);
                }
            }
        }

        if(CollectionUtils.isNotEmpty(itemDetailDtos)){
            List<TaskItemDetailDto> sortDtos = new ArrayList<>(itemDetailDtos.size());
            Map<String, DictionaryItemDto> unitMap = new HashMap<>();
            RestResponse<Map<String, DictionaryItemDto>> unitResponse = baseServiceClient.getItemMapByCode("unit");
            if(unitResponse.isOk()){
                unitMap = unitResponse.getData();
            }else{
                log.info("获取单位失败");
            }
            Map<String, DictionaryItemDto> resultTypeMap = new HashMap<>();
            RestResponse<Map<String, DictionaryItemDto>> resultTypeResponse = baseServiceClient.getItemMapByCode("job_item_result_type");
            if(resultTypeResponse.isOk()){
                resultTypeMap = resultTypeResponse.getData();
            }else{
                log.info("获取结果类型失败");
            }
            Map<String, DictionaryItemDto> specialRequirementsMap = new HashMap<>();
            RestResponse<Map<String, DictionaryItemDto>> specialRequirementsResponse = baseServiceClient.getItemMapByCode("special_requirements");
            if(specialRequirementsResponse.isOk()){
                specialRequirementsMap = specialRequirementsResponse.getData();
            }else{
                log.info("获取特殊要求失败");
            }
            Map<String, DictionaryItemDto> jobTypeLevelMap = new HashMap<>();
            RestResponse<Map<String, DictionaryItemDto>> jobTypeLevelRes = baseServiceClient.getItemMapByCode("job_type_level");
            if(jobTypeLevelRes.isOk()){
                jobTypeLevelMap = jobTypeLevelRes.getData();
            }else{
                log.info("获取作业类型等级失败");
            }

            Map<String, List<TaskItemDetailDto>> largerMap = itemDetailDtos.stream().collect(Collectors.groupingBy(TaskItemDetailDto::getLargeCategory));
            ItemTotalCountDto countDto = new ItemTotalCountDto();
            List<String> largeCategories = new ArrayList<>();
            //大类跟小类集合键值对
            Map<String, List<String>> largerSubMap = new HashMap<>();
            BigDecimal standardTimeCount = new BigDecimal(0);
            BigDecimal workingTimeCount = new BigDecimal(0);
            Integer exceptionCount = 0;
            for(Map.Entry<String, List<TaskItemDetailDto>> entity : largerMap.entrySet()){
                List<TaskItemDetailDto> children = entity.getValue().stream().sorted(Comparator.comparing(TaskItemDetailDto::getSubCategory)).collect(Collectors.toList());

                largeCategories.add(entity.getKey());
                for(TaskItemDetailDto maintTaskItem : children) {
                    if(null != maintTaskItem.getAbnormal() && maintTaskItem.getAbnormal()){
                        exceptionCount++;
                    }
                    if(StringUtils.isNotBlank(maintTaskItem.getUnit())){
                        DictionaryItemDto dictionaryItemDto = unitMap.get(maintTaskItem.getUnit());
                        maintTaskItem.setUnitName(null != dictionaryItemDto ? dictionaryItemDto.getName() : null);
                    }
                    if(StringUtils.isNotBlank(maintTaskItem.getJobItemResultType())){
                        DictionaryItemDto dictionaryItemDto = resultTypeMap.get(maintTaskItem.getJobItemResultType());
                        maintTaskItem.setJobItemResultTypeName(null != dictionaryItemDto ? dictionaryItemDto.getName() : null);
                    }
                    if(StringUtils.isNotBlank(maintTaskItem.getSpecialRequirements())){
                        DictionaryItemDto dictionaryItemDto = specialRequirementsMap.get(maintTaskItem.getSpecialRequirements());
                        maintTaskItem.setSpecialRequirementsName(null != dictionaryItemDto ? dictionaryItemDto.getName() : null);
                    }
                    if(null != maintTaskItem.getJobTypeLevel() && maintTaskItem.getJobTypeLevel().length > 0){
                        List<String> levelNames = new ArrayList<>();
                        //解析作业类型等级
                        for(String jobTypeLevel : maintTaskItem.getJobTypeLevel()){
                            DictionaryItemDto dictionaryItemDto = jobTypeLevelMap.get(jobTypeLevel);
                            if(null != dictionaryItemDto){
                                levelNames.add(dictionaryItemDto.getName());
                            }
                        }
                        if(CollectionUtils.isNotEmpty(levelNames)){
                            maintTaskItem.setJobTypeLevelNames(StringUtils.join(levelNames, StringPool.COMMA));
                        }
                    }

                    //不同大类可能有相同小类名称，只需要相同大类内小类去重，不同大类间需要多次统计
                    List<String> subCategories = new ArrayList<>();
                    if (largerSubMap.containsKey(maintTaskItem.getLargeCategory())) {
                        subCategories = largerSubMap.get(maintTaskItem.getLargeCategory());
                    }
                    if(!subCategories.contains(maintTaskItem.getSubCategory())) {
                        subCategories.add(maintTaskItem.getSubCategory());
                        largerSubMap.put(maintTaskItem.getLargeCategory(), subCategories);
                    }
                    standardTimeCount = standardTimeCount.add(null != maintTaskItem.getStandardTime() ? maintTaskItem.getStandardTime() : new BigDecimal(0));
                    workingTimeCount = workingTimeCount.add(null != maintTaskItem.getWorkingTime() ? maintTaskItem.getWorkingTime() : new BigDecimal(0));
                    sortDtos.add(maintTaskItem);
                }
            }
            countDto.setLargeCategoryCount(largeCategories.size());
            List<String> allSubCategories = new ArrayList<>();
            for(Map.Entry<String, List<String>> entity : largerSubMap.entrySet()){
                allSubCategories.addAll(entity.getValue());
            }
            countDto.setSubCategoryCount(allSubCategories.size());
            countDto.setContentCount(itemDetailDtos.size());
            countDto.setStandardTimeCount(standardTimeCount);
            countDto.setWorkingTimeCount(workingTimeCount);
            countDto.setExceptionCount(exceptionCount);

            //首次拉取的是维保计划单数据，需要重新聚合排序，后续按照最新排序
            if(CollectionUtils.isNotEmpty(maintTaskItems)) {
                maintTaskItemDto.setItemDetailDtos(itemDetailDtos);
            }else {

                maintTaskItemDto.setItemDetailDtos(sortDtos);
            }
            maintTaskItemDto.setCountDto(countDto);
        }
        if (CollectionUtils.isNotEmpty(maintTaskItemDto.getItemDetailDtos())){
            for (TaskItemDetailDto detailDto :maintTaskItemDto.getItemDetailDtos()){
                if (detailDto!=null&& ArrayUtils.isNotEmpty(detailDto.getFileIds())){
                    List<AttachmentClientDto> attachmentList = fileGetHandler.getAttachmentList(Arrays.asList(detailDto.getFileIds()));
                    detailDto.setFileInfoList(attachmentList);
                }
                if (detailDto!=null&& ArrayUtils.isNotEmpty(detailDto.getMediaIds())){
                    List<AttachmentClientDto> attachmentList = fileGetHandler.getAttachmentList(Arrays.asList(detailDto.getMediaIds()));
                    detailDto.setMediaList(attachmentList);
                }
            }
        }
        return maintTaskItemDto;
    }

    private Boolean deleteByTaskId(String taskId){
        LambdaQueryWrapper<MaintTaskItem> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(MaintTaskItem::getTaskId, taskId);
        return maintTaskItemMapper.delete(wrapper) > 0;
    }

    @Override
    public Boolean pushToIot(String taskId){
        RestResponse<Boolean> pushFlagRes = baseServiceClient.iotPushFlag();
        if(pushFlagRes.isOk() && pushFlagRes.getData()){
            List<TaskIotPushDto> dtos = maintTaskItemMapper.getCanIotPushData(taskId, new Integer[]{TaskIotPushType.POINT.getValue(), TaskIotPushType.WAV.getValue()});
            if(CollectionUtils.isNotEmpty(dtos)){
                List<TaskIotPushDto> pushDtos = new ArrayList<>();
                Map<String, String> unitMap = new HashMap<>();
                RestResponse<Map<String, DictionaryItemDto>> unitResponse = baseServiceClient.getItemMapByCode("unit");
                if(unitResponse.isOk()){
                    unitMap = unitResponse.getData().values()
                            .stream().collect(Collectors.toMap(DictionaryItemDto::getValue, DictionaryItemDto::getName, (v1, v2) -> v1));
                }else{
                    log.info("获取单位失败");
                }
                for(TaskIotPushDto dto : dtos){
                    if(StringUtils.isBlank(dto.getJobStandardItemId())){
                        //历史数据不存在作业项id过滤
                        continue;
                    }
                    try{
                        Double value = Double.valueOf(dto.getResult());
                        dto.setValue(value);
                    }catch (Exception e){
                        log.error("作业项数值转换失败");
                        continue;
                    }
                    dto.setUnitName(StringUtils.isNotBlank(dto.getUnit()) ? unitMap.get(dto.getUnit()) : null);
                    pushDtos.add(dto);
                }
                if(CollectionUtils.isNotEmpty(pushDtos)) {
                    parameterClient.taskIotPush(pushDtos);
                }
            }
        }else{
            log.error("项目未开启工单推送参数监控数据的功能");
        }
        return true;
    }

    public List<TaskItemDetailDto> getByTaskIds(List<String> taskIds, Boolean onlyError) {
        LambdaQueryWrapper<MaintTaskItem> wrapper = Wrappers.lambdaQuery();
        wrapper.in(MaintTaskItem::getTaskId, taskIds);
        wrapper.eq(onlyError != null && onlyError, MaintTaskItem::getAbnormal,true);
        wrapper.orderByAsc(MaintTaskItem::getSort);
        List<MaintTaskItem> maintTaskItems = maintTaskItemMapper.selectList(wrapper);
        List<TaskItemDetailDto> itemDetailDtos = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(maintTaskItems)) {
            itemDetailDtos = CopyDataUtil.copyList(maintTaskItems,TaskItemDetailDto.class );
        }

        if(CollectionUtils.isNotEmpty(itemDetailDtos)){
            List<TaskItemDetailDto> sortDtos = new ArrayList<>(itemDetailDtos.size());
            Map<String, DictionaryItemDto> unitMap = new HashMap<>();
            RestResponse<Map<String, DictionaryItemDto>> unitResponse = baseServiceClient.getItemMapByCode("unit");
            if(unitResponse.isOk()){
                unitMap = unitResponse.getData();
            }else{
                log.info("获取单位失败");
            }
            Map<String, DictionaryItemDto> resultTypeMap = new HashMap<>();
            RestResponse<Map<String, DictionaryItemDto>> resultTypeResponse = baseServiceClient.getItemMapByCode("job_item_result_type");
            if(resultTypeResponse.isOk()){
                resultTypeMap = resultTypeResponse.getData();
            }else{
                log.info("获取结果类型失败");
            }
            Map<String, DictionaryItemDto> specialRequirementsMap = new HashMap<>();
            RestResponse<Map<String, DictionaryItemDto>> specialRequirementsResponse = baseServiceClient.getItemMapByCode("special_requirements");
            if(specialRequirementsResponse.isOk()){
                specialRequirementsMap = specialRequirementsResponse.getData();
            }else{
                log.info("获取特殊要求失败");
            }
            Map<String, DictionaryItemDto> jobTypeLevelMap = new HashMap<>();
            RestResponse<Map<String, DictionaryItemDto>> jobTypeLevelRes = baseServiceClient.getItemMapByCode("job_type_level");
            if(jobTypeLevelRes.isOk()){
                jobTypeLevelMap = jobTypeLevelRes.getData();
            }else{
                log.info("获取作业类型等级失败");
            }

            Map<String, List<TaskItemDetailDto>> largerMap = itemDetailDtos.stream().collect(Collectors.groupingBy(TaskItemDetailDto::getLargeCategory));
            ItemTotalCountDto countDto = new ItemTotalCountDto();
            List<String> largeCategories = new ArrayList<>();
            //大类跟小类集合键值对
            Map<String, List<String>> largerSubMap = new HashMap<>();
            BigDecimal standardTimeCount = new BigDecimal(0);
            BigDecimal workingTimeCount = new BigDecimal(0);
            Integer exceptionCount = 0;
            for(Map.Entry<String, List<TaskItemDetailDto>> entity : largerMap.entrySet()){
                List<TaskItemDetailDto> children = entity.getValue().stream().sorted(Comparator.comparing(TaskItemDetailDto::getSubCategory)).collect(Collectors.toList());

                largeCategories.add(entity.getKey());
                for(TaskItemDetailDto maintTaskItem : children) {
                    if(null != maintTaskItem.getAbnormal() && maintTaskItem.getAbnormal()){
                        exceptionCount++;
                    }
                    if(StringUtils.isNotBlank(maintTaskItem.getUnit())){
                        DictionaryItemDto dictionaryItemDto = unitMap.get(maintTaskItem.getUnit());
                        maintTaskItem.setUnitName(null != dictionaryItemDto ? dictionaryItemDto.getName() : null);
                    }
                    if(StringUtils.isNotBlank(maintTaskItem.getJobItemResultType())){
                        DictionaryItemDto dictionaryItemDto = resultTypeMap.get(maintTaskItem.getJobItemResultType());
                        maintTaskItem.setJobItemResultTypeName(null != dictionaryItemDto ? dictionaryItemDto.getName() : null);
                    }
                    if(StringUtils.isNotBlank(maintTaskItem.getSpecialRequirements())){
                        DictionaryItemDto dictionaryItemDto = specialRequirementsMap.get(maintTaskItem.getSpecialRequirements());
                        maintTaskItem.setSpecialRequirementsName(null != dictionaryItemDto ? dictionaryItemDto.getName() : null);
                    }
                    if(null != maintTaskItem.getJobTypeLevel() && maintTaskItem.getJobTypeLevel().length > 0){
                        List<String> levelNames = new ArrayList<>();
                        //解析作业类型等级
                        for(String jobTypeLevel : maintTaskItem.getJobTypeLevel()){
                            DictionaryItemDto dictionaryItemDto = jobTypeLevelMap.get(jobTypeLevel);
                            if(null != dictionaryItemDto){
                                levelNames.add(dictionaryItemDto.getName());
                            }
                        }
                        if(CollectionUtils.isNotEmpty(levelNames)){
                            maintTaskItem.setJobTypeLevelNames(StringUtils.join(levelNames, StringPool.COMMA));
                        }
                    }

                    //不同大类可能有相同小类名称，只需要相同大类内小类去重，不同大类间需要多次统计
                    List<String> subCategories = new ArrayList<>();
                    if (largerSubMap.containsKey(maintTaskItem.getLargeCategory())) {
                        subCategories = largerSubMap.get(maintTaskItem.getLargeCategory());
                    }
                    if(!subCategories.contains(maintTaskItem.getSubCategory())) {
                        subCategories.add(maintTaskItem.getSubCategory());
                        largerSubMap.put(maintTaskItem.getLargeCategory(), subCategories);
                    }
                    standardTimeCount = standardTimeCount.add(null != maintTaskItem.getStandardTime() ? maintTaskItem.getStandardTime() : new BigDecimal(0));
                    workingTimeCount = workingTimeCount.add(null != maintTaskItem.getWorkingTime() ? maintTaskItem.getWorkingTime() : new BigDecimal(0));
                    sortDtos.add(maintTaskItem);
                }
            }
            countDto.setLargeCategoryCount(largeCategories.size());
            List<String> allSubCategories = new ArrayList<>();
            for(Map.Entry<String, List<String>> entity : largerSubMap.entrySet()){
                allSubCategories.addAll(entity.getValue());
            }
            countDto.setSubCategoryCount(allSubCategories.size());
            countDto.setContentCount(itemDetailDtos.size());
            countDto.setStandardTimeCount(standardTimeCount);
            countDto.setWorkingTimeCount(workingTimeCount);
            countDto.setExceptionCount(exceptionCount);
        }
        return itemDetailDtos;
    }

    private List<TaskItemDetailDto> sortDtos(List<TaskItemDetailDto> itemDetailDtos){
        if(CollectionUtils.isNotEmpty(itemDetailDtos)) {
            List<TaskItemDetailDto> sortDtos = new ArrayList<>(itemDetailDtos.size());
            Map<String, String> unitMap = commonGetHandler.getDictNameMap("unit");
            Map<Integer, String> resultTypeMap = JobItemResultType.getNameMapByValue();
            Map<String, String> specialRequirementsMap = commonGetHandler.getDictNameMap("special_requirements");
            Map<String, String> jobTypeLevelMap = commonGetHandler.getDictNameMap("job_type_level");

            Map<String, List<TaskItemDetailDto>> largerMap = itemDetailDtos.stream().collect(Collectors.groupingBy(TaskItemDetailDto::getLargeCategory));
            for (Map.Entry<String, List<TaskItemDetailDto>> entity : largerMap.entrySet()) {
                List<TaskItemDetailDto> children = entity.getValue().stream().sorted(Comparator.comparing(TaskItemDetailDto::getSubCategory)).collect(Collectors.toList());
                for (TaskItemDetailDto maintTaskItem : children) {
                    maintTaskItem.setUnitName(StringUtils.isNotBlank(maintTaskItem.getUnit()) ? unitMap.get(maintTaskItem.getUnit()) : null);
                    maintTaskItem.setJobItemResultTypeName(null != maintTaskItem.getJobItemResultType() ? resultTypeMap.get(maintTaskItem.getJobItemResultType()) : null);
                    maintTaskItem.setSpecialRequirementsName(StringUtils.isNotBlank(maintTaskItem.getSpecialRequirements()) ? specialRequirementsMap.get(maintTaskItem.getSpecialRequirements()) : null);
                    if (null != maintTaskItem.getJobTypeLevel() && maintTaskItem.getJobTypeLevel().length > 0) {
                        List<String> levelNames = new ArrayList<>();
                        //解析作业类型等级
                        for (String jobTypeLevel : maintTaskItem.getJobTypeLevel()) {
                            String levelName = jobTypeLevelMap.get(jobTypeLevel);
                            if (StringUtils.isNotBlank(levelName)) {
                                levelNames.add(levelName);
                            }
                        }
                        if (CollectionUtils.isNotEmpty(levelNames)) {
                            maintTaskItem.setJobTypeLevelNames(StringUtils.join(levelNames, StringPool.COMMA));
                        }
                    }
                    if (ArrayUtils.isNotEmpty(maintTaskItem.getFileIds())){
                        List<AttachmentClientDto> attachmentList = fileGetHandler.getAttachmentList(Arrays.asList(maintTaskItem.getFileIds()));
                        maintTaskItem.setFileInfoList(attachmentList);
                    }
                    if (ArrayUtils.isNotEmpty(maintTaskItem.getMediaIds())){
                        List<AttachmentClientDto> attachmentList = fileGetHandler.getAttachmentList(Arrays.asList(maintTaskItem.getMediaIds()));
                        maintTaskItem.setMediaList(attachmentList);
                    }
                    sortDtos.add(maintTaskItem);
                }
            }
            return sortDtos;
        }
        return Collections.emptyList();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public List<TaskItemDetailDto> offlineCache(String taskId, String standardId){
        List<TaskItemDetailDto> itemDetailDtos = new ArrayList<>();

        LambdaQueryWrapper<MaintTaskItem> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(MaintTaskItem::getTaskId, taskId);
        wrapper.orderByAsc(MaintTaskItem::getSort);
        List<MaintTaskItem> maintTaskItems = maintTaskItemMapper.selectList(wrapper);
        if(CollectionUtils.isNotEmpty(maintTaskItems)) {
            itemDetailDtos = CopyDataUtil.copyList(maintTaskItems,TaskItemDetailDto.class );
        }else {
            List<JobStandardItemDto> jobStandardItemDtos = standardItemService.getListByStandardId(standardId);
            if(CollectionUtils.isNotEmpty(jobStandardItemDtos)){
                for(JobStandardItemDto jobStandardItemDto : jobStandardItemDtos) {
                    TaskItemDetailDto detailDto = CopyDataUtil.copyObject(jobStandardItemDto, TaskItemDetailDto.class);
                    detailDto.setJobStandardItemId(jobStandardItemDto.getId());
                    String newId = UUID.randomUUID().toString().replace(StringPool.DASH, StringPool.EMPTY);
                    detailDto.setId(newId);
                    detailDto.setTaskId(taskId);
                    itemDetailDtos.add(detailDto);
                }
            }
        }
        List<TaskItemDetailDto> sortDtos = this.sortDtos(itemDetailDtos);
        //首次拉取的是维保计划单数据，需要重新聚合排序，后续按照最新排序
        if(CollectionUtils.isEmpty(maintTaskItems) && CollectionUtils.isNotEmpty(sortDtos)) {
            itemDetailDtos = sortDtos;
            List<MaintTaskItem> items = new ArrayList<>(sortDtos.size());
            Integer sort = 1;
            for(TaskItemDetailDto sortDto : sortDtos) {
                MaintTaskItem item = CopyDataUtil.copyObject(sortDto,MaintTaskItem.class);
                item.setSort(sort++);
                items.add(item);
            }
            this.saveBatch(items);
        }
        return itemDetailDtos;
    }


}
