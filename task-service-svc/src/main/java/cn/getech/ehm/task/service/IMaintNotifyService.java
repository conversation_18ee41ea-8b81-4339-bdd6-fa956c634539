package cn.getech.ehm.task.service;

import cn.getech.ehm.task.dto.task.notify.MaintNotifyDto;
import cn.getech.ehm.task.entity.MaintNotify;
import cn.getech.poros.framework.common.service.IBaseService;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 通知服务类
 */
public interface IMaintNotifyService extends IBaseService<MaintNotify> {

    /**
     * 编辑
     */
    Boolean editByParam(MaintNotifyDto dto, Integer sourceType, String sourceId,Boolean add);

    /**
     * 删除对应来源的数据
     * @return
     */
    Boolean deleteBySourceIds(List<String> sourceIds);

    /**
     * 获取sourceId对应通知对象
     * @param sourceId
     * @return
     */
    Map<Integer, MaintNotifyDto> getMapBySourceId(String sourceId);

    /**
     * 获取启动的通知
     */
    Map<String, MaintNotifyDto> getPlanEnableNotifyBySourceId(List<String> sourceIds);

    /**
     * 保存巡检单任务
     */
    Boolean saveMaintTaskNotify(MaintNotifyDto adventNotify, Integer sourceType, String sourceId, Date planMaintTime, Date deadlineTime, Integer overdueHandlingMethod);

    /**
     * 关闭巡检任务单通知
     * @param ids
     * @return
     */
    Boolean closedTaskNotify(List<String> ids);

    /**
     * 保存故障单通知
     */
    Boolean saveRepairTaskReceiveNotify(String taskId, Date notifyTime);

}