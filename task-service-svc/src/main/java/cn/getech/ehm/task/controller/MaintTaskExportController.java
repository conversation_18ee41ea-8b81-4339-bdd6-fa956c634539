package cn.getech.ehm.task.controller;

import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.common.util.ExcelUtil;
import cn.getech.ehm.task.dto.task.info.*;
import cn.getech.ehm.task.enums.TaskOperationType;
import cn.getech.ehm.task.service.IMaintTaskService;
import cn.getech.poros.framework.common.utils.ExcelUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("/maintTask/export")
@Api(tags = "工单导出相关接口")
@Slf4j
public class MaintTaskExportController {
    @Autowired
    IMaintTaskService maintTaskService;

    @ApiOperation("超时异常导出")
    @PostMapping("/overtime")
    //@Permission("maint:task:list")
    public void exportOvertimeTask(@RequestBody @Valid MaintTaskQueryParam maintTaskQueryParam, HttpServletResponse response) {
        maintTaskQueryParam.setOverTimeType(Arrays.asList(new String[]{"" + TaskOperationType.ANY_OVERTIME_TASK.getValue()}));
        maintTaskService.exportOvertimeTask(maintTaskQueryParam, response);
    }

    @ApiOperation("维护异常导出")
    @PostMapping("/ecerror")
    //@Permission("maint:task:list")
    public void exportEcErrorTask(@RequestBody @Valid MaintTaskQueryParam maintTaskQueryParam, HttpServletResponse response) {
        maintTaskQueryParam.setOverTimeType(Arrays.asList(new String[]{"" + TaskOperationType.EC_ERROR_TASK.getValue()}));
        maintTaskService.exportEcErrorTask(maintTaskQueryParam, response);
    }

    @ApiOperation("处理异常导出")
    @PostMapping("/error")
    //@Permission("maint:task:list")
    public void exportErrorTask(@RequestBody @Valid MaintTaskQueryParam maintTaskQueryParam, HttpServletResponse response) {
        maintTaskQueryParam.setOverTimeType(Arrays.asList(new String[]{"" + TaskOperationType.DEAL_ERROR_TASK.getValue()}));
        maintTaskService.exportErrorTask(maintTaskQueryParam, response);
    }
}
