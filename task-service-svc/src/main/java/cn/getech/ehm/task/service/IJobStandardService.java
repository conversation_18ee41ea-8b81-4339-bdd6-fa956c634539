package cn.getech.ehm.task.service;

import cn.getech.ehm.task.dto.job.*;
import cn.getech.ehm.task.entity.JobStandard;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.IBaseService;

import java.util.List;

/**
 * 作业标准service
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
public interface IJobStandardService extends IBaseService<JobStandard> {

    /**
     * 分页查询
     * @param queryParam
     * @return
     */
    PageResult<JobStandardListDto> pageList(JobStandardQueryParam queryParam);

    /**
     * 弹框分页查询
     * @param queryParam
     * @return
     */
    PageResult<JobStandardDetailDto> detailList(JobStandardQueryParam queryParam);

    /**
     * 新增
     * @param addParam
     * @return
     */
    Boolean saveByParam(JobStandardAddParam addParam);

    /**
     * 编辑
     * @param editParam
     * @return
     */
    Boolean editByParam(JobStandardEditParam editParam);

    /**
     * 删除
     * @param ids
     * @return
     */
    Boolean deleteByIds(String[] ids);

    /**
     * 查询
     * @param id
     * @return
     */
    JobStandardDto getDtoById(String id);

    /**
     * 构造作业项统计
     * @param itemDtos
     * @return
     */
    ItemCountDto buildCountDto(List<JobStandardItemDto> itemDtos);

    /**
     * 更新状态
     * @param standardIds
     * @param status
     * @return
     */
    Boolean updateStatus(List<String> standardIds, Integer status);
}