package cn.getech.ehm.task;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 * @date 2020/2/19
 */
@SpringBootApplication
@EnableDiscoveryClient
@ComponentScan(basePackages={"cn.getech"})
@EnableFeignClients(basePackages={"cn.getech"})
@EnableScheduling
@MapperScan("cn.getech.ehm.task.mapper")
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
