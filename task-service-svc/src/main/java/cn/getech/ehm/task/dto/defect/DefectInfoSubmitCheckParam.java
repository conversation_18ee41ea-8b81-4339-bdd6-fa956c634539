package cn.getech.ehm.task.dto.defect;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <pre>
 * 缺陷信息 编辑参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-02-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DefectInfoSubmitCheckParam", description = "缺陷信息编辑参数")
public class DefectInfoSubmitCheckParam extends ApiParam {


    @ApiModelProperty(value = "验收状态")
    @NotNull
    private Integer checkStatus;

    @ApiModelProperty(value = "验收说明")
    private String checkExplain;

    @ApiModelProperty(value = "")
    private String id;

}
