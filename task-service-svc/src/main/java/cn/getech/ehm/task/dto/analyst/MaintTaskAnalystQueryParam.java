package cn.getech.ehm.task.dto.analyst;

import cn.getech.poros.framework.common.param.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * <pre>
 * 报修工单统计 分页查询参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MaintTaskAnalyst查询", description = "报修工单统计查询参数")
public class MaintTaskAnalystQueryParam extends PageParam {

    @ApiModelProperty(value = "")
    private Long deleted;

    @ApiModelProperty(value = "")
    private String tenantId;

    @ApiModelProperty(value = "工单id")
    private String taskId;

    @ApiModelProperty(value = "工单编号")
    private String taskCode;

    @ApiModelProperty(value = "工单状态")
    private Integer taskStatus;

    @ApiModelProperty(value = "设备id")
    private String equipId;

    @ApiModelProperty(value = "设备编号")
    private String equipCode;

    @ApiModelProperty(value = "设备位置id")
    private String equipLocationId;

    @ApiModelProperty(value = "设备位置")
    private String equipLocationName;

    @ApiModelProperty(value = "设备类型id")
    private String equipCategoryId;

    @ApiModelProperty(value = "设备类型")
    private String equipCategotyName;

    @ApiModelProperty(value = "报修人")
    private String reportUid;

    @ApiModelProperty(value = "报修人")
    private String reportName;

    @ApiModelProperty(value = "处理人")
    private String handlerUid;

    @ApiModelProperty(value = "报修人")
    private String handlerName;

    @ApiModelProperty(value = "是否改派")
    private Long transferFlag;

    @ApiModelProperty(value = "工单创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date taskCreateTimeBeginParam;

    @ApiModelProperty(value = "工单创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date taskCreateTimeEndParam;
//
//    @ApiModelProperty(value = "工单接单时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date taskReceiveTimeBeginParam;
//
//    @ApiModelProperty(value = "工单接单时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date taskReceiveTimeEndParam;
//
//    @ApiModelProperty(value = "工单关闭时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date taskCloseTimeBeginParam;
//
//    @ApiModelProperty(value = "工单关闭时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date taskCloseTimeEndParam;
//
//    @ApiModelProperty(value = "工单实际耗时")
//    private Long taskWorkingTimeStr;
//
//    @ApiModelProperty(value = "工单检修耗时")
//    private Long taskMaintTimeStr;
//
//    @ApiModelProperty(value = "接单时长")
//    private Long receiveStr;
//
//    @ApiModelProperty(value = "挂起审批发起时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date handupSubmitTimeBeginParam;
//
//    @ApiModelProperty(value = "挂起审批发起时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date handupSubmitTimeEndParam;
//
//    @ApiModelProperty(value = "挂起审批审批时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date handupAuditTimeBeginParam;
//
//    @ApiModelProperty(value = "挂起审批审批时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date handupAuditTimeEndParam;
//
//    @ApiModelProperty(value = "挂起审批耗时")
//    private Long handupCostStr;
//
//    @ApiModelProperty(value = "")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date handupSubmitTime2BeginParam;
//
//    @ApiModelProperty(value = "")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date handupSubmitTime2EndParam;
//
//    @ApiModelProperty(value = "")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date handupAuditTime2BeginParam;
//
//    @ApiModelProperty(value = "")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date handupAuditTime2EndParam;
//
//    @ApiModelProperty(value = "")
//    private Long handupCostStr2;
//
//    @ApiModelProperty(value = "挂起总时长")
//    private Long handupCostTotal;
//
//    @ApiModelProperty(value = "运行转缺陷发起时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date runDefectSubmitTimeBeginParam;
//
//    @ApiModelProperty(value = "运行转缺陷发起时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date runDefectSubmitTimeEndParam;
//
//    @ApiModelProperty(value = "运行转缺陷审批时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date runDefectAuditTimeBeginParam;
//
//    @ApiModelProperty(value = "运行转缺陷审批时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date runDefectAuditTimeEndParam;
//
//    @ApiModelProperty(value = "运行转缺陷耗时")
//    private Long runDefectCostStr;
//
//    @ApiModelProperty(value = "")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date runDefectSubmitTime2BeginParam;
//
//    @ApiModelProperty(value = "")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date runDefectSubmitTime2EndParam;
//
//    @ApiModelProperty(value = "")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date runDefectAuditTime2BeginParam;
//
//    @ApiModelProperty(value = "")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date runDefectAuditTime2EndParam;
//
//    @ApiModelProperty(value = "")
//    private Long runDefectCostStr2;
//
//    @ApiModelProperty(value = "验收转缺陷发起时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date checkDefectSubmitTimeBeginParam;
//
//    @ApiModelProperty(value = "验收转缺陷发起时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date checkDefectSubmitTimeEndParam;
//
//    @ApiModelProperty(value = "验收转缺陷审批时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date checkDefectAuditTimeBeginParam;
//
//    @ApiModelProperty(value = "验收转缺陷审批时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date checkDefectAuditTimeEndParam;
//
//    @ApiModelProperty(value = "验收转缺陷耗时")
//    private Long checkDefectCostStr;
//
//    @ApiModelProperty(value = "")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date checkDefectSubmitTime2BeginParam;
//
//    @ApiModelProperty(value = "")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date checkDefectSubmitTime2EndParam;
//
//    @ApiModelProperty(value = "")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date checkDefectAuditTime2BeginParam;
//
//    @ApiModelProperty(value = "")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date checkDefectAuditTime2EndParam;
//
//    @ApiModelProperty(value = "")
//    private Long checkDefectCostStr2;
//
//    @ApiModelProperty(value = "验收发起时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date checkSubmitTimeBeginParam;
//
//    @ApiModelProperty(value = "验收发起时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date checkSubmitTimeEndParam;
//
//    @ApiModelProperty(value = "验收审批时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date checkAuditTimeBeginParam;
//
//    @ApiModelProperty(value = "验收审批时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date checkAuditTimeEndParam;
//
//    @ApiModelProperty(value = "验收耗时")
//    private Long checkCostStr;
//
//    @ApiModelProperty(value = "")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date checkSubmitTime2BeginParam;
//
//    @ApiModelProperty(value = "")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date checkSubmitTime2EndParam;
//
//    @ApiModelProperty(value = "")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date checkAuditTime2BeginParam;
//
//    @ApiModelProperty(value = "")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date checkAuditTime2EndParam;
//
//    @ApiModelProperty(value = "")
//    private Long checkCostStr2;
//
//    @ApiModelProperty(value = "")
//    private String id;
//
//    @ApiModelProperty(value = "")
//    private String createBy;
//
//    @ApiModelProperty(value = "")
//    private String updateBy;
//
//    @ApiModelProperty(value = "")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date createTimeBeginParam;
//
//    @ApiModelProperty(value = "")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date createTimeEndParam;
//
//    @ApiModelProperty(value = "")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date updateTimeBeginParam;
//
//    @ApiModelProperty(value = "")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date updateTimeEndParam;
//
//    @ApiModelProperty(value = "")
//    private String remark;


//    @ApiModelProperty(value = "设备编码/名称")
//    private String equipmentCodeOrName;
//
//    @ApiModelProperty(value = "设备名称")
//    private String equipmentName;
//
//    @ApiModelProperty(value = "设备编码")
//    private String equipmentCode;

    @ApiModelProperty(value = "设备类型Ids")
    private List<String> categoryIds;

    @ApiModelProperty(value = "设备位置Ids")
    private List<String> locationIds;

}
