package cn.getech.ehm.task.enmu;

import cn.getech.ehm.common.dto.EnumListDto;

import java.util.ArrayList;
import java.util.List;

/**
 * 通知设备管理人员枚举
 * <AUTHOR>
 * @date 2020/3/3
 */
public enum NotifyEquipmentManagerType {
    MANAGE_DEPART(1, "管理部门"),
    MANAGE_PRINCIPAL(2, "管理责任人"),
    USER_DEPART(3, "使用部门"),
    USER_PRINCIPAL(4, "使用责任人"),
    TEAM(5, "设备维保班组"),
    MAINTAINER(6, "设备维保责任人");


    NotifyEquipmentManagerType(int value, String name) {
        this.value = value;
        this.name = name;
    }

    private int value;

    private String name;

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static List<EnumListDto> getList(){
        List<EnumListDto> enumListDtos = new ArrayList<>();
        for(NotifyEquipmentManagerType equipmentManagerType : NotifyEquipmentManagerType.values()){
            EnumListDto enumListDto = new EnumListDto();
            enumListDto.setValue(equipmentManagerType.value);
            enumListDto.setName(equipmentManagerType.name);
            enumListDtos.add(enumListDto);
        }
        return enumListDtos;
    }
}
