package cn.getech.ehm.task.mapper;

import cn.getech.ehm.task.dto.*;
import cn.getech.ehm.task.dto.TaskStatisticsDto;
import cn.getech.ehm.task.dto.TaskStatisticsReqDto;
import cn.getech.ehm.task.dto.plan.MaintPlanCountDto;
import cn.getech.ehm.task.dto.screen.EquipmentTaskQueryParam;
import cn.getech.ehm.task.dto.task.notify.MaintTaskNotifyDto;
import cn.getech.ehm.task.dto.task.performance.PersonalTaskInfoDto;
import cn.getech.ehm.task.dto.task.info.*;
import cn.getech.ehm.task.entity.MaintTask;
import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 维护工单主表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
@Repository
public interface MaintTaskMapper extends BaseMapper<MaintTask> {

    /**
     * 获取最大编号
     *
     * @param prex
     * @return
     */
    String getMaxCode(@Param("prex") String prex);

    /**
     * 根据id获取主表信息
     *
     * @param id
     * @return
     */
    MaintTaskDto getDtoById(@Param("id") String id);

    /**
     * 获取今日工单数量
     *
     * @return
     */
    Integer getTodayTaskQty();

    /**
     * 获取超期工单数量
     *
     * @return
     */
    Integer getOverdueTaskQty();

    /**
     * 获取今日已完成工单数量
     *
     * @return
     */
    Integer getTodayCompletedTaskQty();

    /**
     * 获取工单完成动态（返回最近5条）
     *
     * @return
     */
    List<TaskCompleteDynamicDto> getTaskCompleteDynamic();

    /**
     * 获取工单总览列表
     *
     * @param status 工单状态
     * @return
     */
    List<MaintTaskDto> getTaskOverview(Integer status);

    /**
     * 获取已评价工单统计
     *
     * @return
     */
    List<Map<Integer, Integer>> getTaskEvaluated();

    /**
     * 获取工单待办
     *
     * @return
     */
    List<MaintTaskDto> getTaskTodoList();

    /**
     * 统计
     *
     * @param reqDto
     * @return
     */
    List<TaskStatisticsDto> getStatistics(@Param("reqDto") TaskStatisticsReqDto reqDto);

    /**
     * 根据用户Ids查询个人绩效
     *
     * @param staffIds  维护人员Id
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return
     */
    List<PersonalTaskInfoDto> getPersonalPerformance(List<String> staffIds, List<String> teamIds, Date beginTime, Date endTime, List<String> handlerIds);


    /**
     * 故障设备信息统计
     *
     * @return
     */
    List<FaultStatisticsDto> faultEquipmentStatistics(@Param("param") FaultStatisticsParam param);

    /**
     * 所有故障设备信息统计
     *
     * @return
     */
    List<FaultStatisticsDto> allFaultEquipmentStatistics(@Param("param") FaultStatisticsParam param);

    /**
     * 工单类型统计-饼图
     *
     * @param maintTaskReportQueryParam
     * @return
     */
    List<NameNode> typeReport(@Param("param") MaintTaskReportQueryParam maintTaskReportQueryParam);

    /**
     * 工单类型月度统计-柱状图
     *
     * @param maintTaskReportQueryParam
     * @return
     */
    List<TypeMonthNode> typeMonthlyReport(@Param("param") MaintTaskReportQueryParam maintTaskReportQueryParam);

    /**
     * 工单状态统计-饼图
     *
     * @param maintTaskReportQueryParam
     * @return
     */
    List<NameNode> statusReport(@Param("param") MaintTaskReportQueryParam maintTaskReportQueryParam);

    /**
     * 工单工时月度统计-折线图
     *
     * @param maintTaskReportQueryParam
     * @return
     */
    List<MonthNode> maintTimeMonthlyReport(@Param("param") MaintTaskReportQueryParam maintTaskReportQueryParam);

    /**
     * 获取计划单上次执行日期
     *
     * @param planIds
     * @return
     */
    List<MaintPlanCountDto> getLastPlanDate(@Param("planIds") List<String> planIds);

    /**
     * 获取计划单上次执行日期
     *
     * @param planIds
     * @return
     */
    List<MaintPlanCountDto> getTaskCount(@Param("planIds") List<String> planIds);

    /**
     * 统计接单数量
     * @param queryParam
     * @return
     */
    List<NameNode> recTaskReport(@Param("param") RecTaskReportQueryParam queryParam);

    /**
     * 统计接单完成数量
     * @param queryParam
     * @return
     */
    List<NameNode> recTaskCompleteReport(@Param("param") RecTaskReportQueryParam queryParam);

    /**
     * 获取工单状态
     *
     * @param id
     * @return
     */
    Integer getStatus(@Param("id") String id);


    List<MaintTask> findTaskByEquipmentName(@Param("param") EquipmentTaskQueryParam queryParam);

    /**
     * 获取报警关联工单
     */
    Page<WarnTaskPageDto> getListByWarnId(Page<WarnTaskPageDto> page,
                                            @Param("param") WarnQueryParam queryParam);

    Page<MaintTaskDto> getOverTimeTaskList(@Param("page") Page<MaintTaskDto> page, @Param("param") MaintTaskQueryParam queryParam);

    /**
     * 获取临期巡检单
     * @return
     */
    @SqlParser
    List<MaintTaskNotifyDto> getNotifyTask(@Param("notifyType") Integer notifyType, @Param("date") Date date);

    /**
     * 获取超期单id
     */
    @SqlParser
    List<MaintTaskNotifyDto> getOverdueTaskIds();
}
