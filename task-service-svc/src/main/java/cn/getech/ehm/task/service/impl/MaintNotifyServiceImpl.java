package cn.getech.ehm.task.service.impl;

import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.task.dto.task.notify.MaintNotifyDto;
import cn.getech.ehm.task.dto.task.info.NameDetailDto;
import cn.getech.ehm.task.enmu.MaintNotifySourceType;
import cn.getech.ehm.task.enmu.OverdueHandlingType;
import cn.getech.ehm.task.entity.MaintNotify;
import cn.getech.ehm.task.enmu.MaintNotifyType;
import cn.getech.ehm.task.enums.TaskSourceType;
import cn.getech.ehm.task.handler.CommonGetHandler;
import cn.getech.ehm.task.mapper.MaintNotifyMapper;
import cn.getech.ehm.task.service.IMaintNotifyService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.poros.permission.dto.RoleDto;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 通知服务实现类
 */
@Slf4j
@Service
public class MaintNotifyServiceImpl extends BaseServiceImpl<MaintNotifyMapper, MaintNotify> implements IMaintNotifyService {

    @Autowired
    private MaintNotifyMapper notifyMapper;
    @Autowired
    private CommonGetHandler commonGetHandler;

    @Override
    public Boolean editByParam(MaintNotifyDto dto, Integer sourceType, String sourceId,Boolean add){
        this.deleteBySourceIds(Collections.singletonList(sourceId));
        if(null != dto) {
            MaintNotify maintNotify = CopyDataUtil.copyObject(dto, MaintNotify.class);
            if (add) {
                maintNotify.setId(null);
            }
            maintNotify.setSourceType(sourceType);
            maintNotify.setSourceId(sourceId);
            return saveOrUpdate(maintNotify);
        }
        return true;
    }

    @Override
    public Boolean deleteBySourceIds(List<String> sourceIds){
        LambdaQueryWrapper<MaintNotify> wrapper = Wrappers.lambdaQuery();
        wrapper.in(MaintNotify::getSourceId, sourceIds);
        return notifyMapper.delete(wrapper) > StaticValue.ZERO;
    }

    @Override
    public Map<Integer, MaintNotifyDto> getMapBySourceId(String sourceId){
        Map<Integer, MaintNotifyDto> map = new HashMap<>();
        LambdaQueryWrapper<MaintNotify> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(MaintNotify::getSourceId, sourceId);
        List<MaintNotify> maintNotifies = notifyMapper.selectList(wrapper);
        if(CollectionUtils.isNotEmpty(maintNotifies)){
            List<String> uids = new ArrayList<>();
            List<String> roleIds = new ArrayList<>();
            for(MaintNotify maintNotify : maintNotifies){
                if(null != maintNotify.getCustomUids() && maintNotify.getCustomUids().length > 0){
                    uids.addAll(Arrays.asList(maintNotify.getCustomUids()));
                }
                if(null != maintNotify.getCustomRoles() && maintNotify.getCustomRoles().length > 0){
                    roleIds.addAll(Arrays.asList(maintNotify.getCustomRoles()));
                }
            }
            Map<String, String> uidNameMap =  commonGetHandler.getUidNameMap(uids.stream().distinct().collect(Collectors.toList()));
            Map<String, RoleDto> roleMap = commonGetHandler.getRoleMap(roleIds);
            for(MaintNotify maintNotify : maintNotifies){
                MaintNotifyDto dto = CopyDataUtil.copyObject(maintNotify, MaintNotifyDto.class);
                List<NameDetailDto> uidDtos = new ArrayList<>();
                if(null != maintNotify.getCustomUids() && maintNotify.getCustomUids().length > 0){
                    for(String uid : maintNotify.getCustomUids()){
                        String userName = uidNameMap.get(uid);
                        if(StringUtils.isNotBlank(userName)){
                            NameDetailDto uidDto = new NameDetailDto();
                            uidDto.setId(uid);
                            uidDto.setName(userName);
                            uidDtos.add(uidDto);
                        }
                    }
                }
                dto.setUidDtos(uidDtos);

                List<NameDetailDto> roleDtos = new ArrayList<>();
                if(null != maintNotify.getCustomRoles() && maintNotify.getCustomRoles().length > 0){
                    for(String roleId : maintNotify.getCustomRoles()){
                        RoleDto role = roleMap.get(roleId);
                        if(null != role){
                            NameDetailDto roleDto = new NameDetailDto();
                            roleDto.setId(role.getId());
                            roleDto.setName(role.getName());
                            roleDtos.add(roleDto);
                        }
                    }
                }
                dto.setRoleDtos(roleDtos);
                map.put(dto.getType(), dto);
            }
        }
        return map;
    }

    @Override
    public Map<String, MaintNotifyDto> getPlanEnableNotifyBySourceId(List<String> sourceIds){
        List<MaintNotify> maintNotifies = notifyMapper.getPlanEnableNotifyBySourceId(sourceIds);
        if(CollectionUtils.isNotEmpty(maintNotifies)){
            return CopyDataUtil.copyList(maintNotifies, MaintNotifyDto.class).stream().collect(Collectors.toMap(MaintNotifyDto::getSourceId, v -> v, (v1, v2) -> v1));
        }
        return Collections.emptyMap();
    }

    @Override
    public Boolean saveMaintTaskNotify(MaintNotifyDto adventNotify, Integer sourceType, String sourceId, Date planMaintTime, Date deadlineTime, Integer overdueHandlingMethod){
        this.deleteBySourceIds(Collections.singletonList(sourceId));
        List<MaintNotify> maintNotifies = new ArrayList<>();
        if(null != adventNotify && null != deadlineTime) {
            MaintNotify maintNotify = CopyDataUtil.copyObject(adventNotify, MaintNotify.class);
            maintNotify.setId(null);
            //临期需要记录时间;因为都是整点定时器通知，所以前30分钟的需要舍弃多余分钟,保证最少提前30分钟通知到;
            // 比如，提前一小时通知,9:20在8点通知9:40在9点通知
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(deadlineTime);
            calendar.add(Calendar.MINUTE, -30);
            calendar.add(Calendar.HOUR_OF_DAY, -adventNotify.getAdvanceHour());
            maintNotify.setNotifyTime(calendar.getTime());
            maintNotify.setSourceType(sourceType);
            maintNotify.setSourceId(sourceId);
            maintNotifies.add(maintNotify);
        }
        //目前不需要超期通知
        /*if(null != overdueHandlingMethod && overdueHandlingMethod == OverdueHandlingType.EXCEPTION_CLOSE.getValue() && null != deadlineTime) {
            MaintNotify overdueNotify = new MaintNotify();
            overdueNotify.setId(null);
            overdueNotify.setType(MaintNotifyType.OVERDUE.getValue());
            overdueNotify.setEnable(true);
            overdueNotify.setNotifyTime(deadlineTime);
            overdueNotify.setSourceType(sourceType);
            overdueNotify.setSourceId(sourceId);
            maintNotifies.add(overdueNotify);
        }*/
        if(CollectionUtils.isNotEmpty(maintNotifies)){
            return saveBatch(maintNotifies);
        }
        return true;
    }

    @Override
    /**
     * 关闭巡检任务单通知
     * @param ids
     * @return
     */
    public Boolean closedTaskNotify(List<String> ids){
        notifyMapper.closedTaskNotify(ids);
        return true;
    }

    @Override
    public Boolean saveRepairTaskReceiveNotify(String taskId, Date notifyTime){
        MaintNotify maintNotify = new MaintNotify();
        maintNotify.setType(MaintNotifyType.REPAIR_RECEIVE.getValue());
        maintNotify.setNotifyTime(notifyTime);
        maintNotify.setEnable(true);
        maintNotify.setSourceType(MaintNotifySourceType.TASK.getValue());
        maintNotify.setSourceId(taskId);
        return save(maintNotify);
    }
}
