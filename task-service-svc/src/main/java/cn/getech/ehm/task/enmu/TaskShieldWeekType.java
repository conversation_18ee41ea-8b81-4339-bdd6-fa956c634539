package cn.getech.ehm.task.enmu;


import cn.getech.ehm.common.dto.EnumListDto;
import java.util.ArrayList;
import java.util.List;

/**
 * 工单过滤日期(按国际周的值)
 */
public enum TaskShieldWeekType {
    ALL(0, "所有时间"),
    MON(2, "周一"),
    TUES(3, "周二"),
    WED(4, "周三"),
    THUR(5, "周四"),
    FRI(6, "周五"),
    SAT(7, "周六"),
    SUN(1, "周日");


    TaskShieldWeekType(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public static List<EnumListDto> getList(){
        List<EnumListDto> enumListDtos = new ArrayList<>();
        for(TaskShieldWeekType frequencyType : TaskShieldWeekType.values()){
            EnumListDto enumListDto = new EnumListDto();
            enumListDto.setValue(frequencyType.value);
            enumListDto.setName(frequencyType.name);
            enumListDtos.add(enumListDto);
        }
        return enumListDtos;
    }

    private int value;

    private String name;

    public int getValue() { return value; }

    public void setValue(int value) { this.value = value; }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
