package cn.getech.ehm.task.dto.screen;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WorkCalendarResult {

    @JsonFormat(pattern = "MM-dd", timezone = "GMT+8")
    public Date date;
    @ApiModelProperty("故障工单完成数")
    public Integer breanDownCompleteCount;
    @ApiModelProperty("故障工单数")
    public Integer breanDownCount;
    @ApiModelProperty("维保工单完成数")
    public Integer planCompleteCount;
    @ApiModelProperty("维保工单数")
    public Integer planCount;
    @ApiModelProperty("点检工单完成数")
    public Integer ecCompleteCount;
    @ApiModelProperty("点检工单数")
    public Integer ecCount;
    @ApiModelProperty("缺陷工单完成数")
    public Integer defectCompleteCount;
    @ApiModelProperty("缺陷工单数")
    public Integer defectCount;
    @ApiModelProperty("维护计划数")
    public Integer planTaskCount;
}
