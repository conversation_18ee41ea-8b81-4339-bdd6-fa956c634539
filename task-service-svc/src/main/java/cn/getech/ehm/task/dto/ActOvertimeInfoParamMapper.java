package cn.getech.ehm.task.dto;

import cn.getech.ehm.task.entity.ActOvertimeInfo;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.bean.BaseEntity;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <pre>
 * 节点时限记录 参数实体映射
 * </pre>
 *
 * <AUTHOR>
 * @date 2023-03-15
 */
@Mapper(componentModel = "spring",imports = {BaseEntity.class})
public interface  ActOvertimeInfoParamMapper{

    /**
     * 新增参数转换为实体
     *
     * @param actOvertimeInfoAddParam
     * @return
     */
    ActOvertimeInfo addParam2Entity(ActOvertimeInfoAddParam actOvertimeInfoAddParam);

    /**
     * 编辑参数转换为实体
     * @param actOvertimeInfoEditParam
     * @return
     */
    ActOvertimeInfo editParam2Entity(ActOvertimeInfoEditParam actOvertimeInfoEditParam);

    /**
     * 实体转换为Dto
     * @param actOvertimeInfo
     * @return
     */
    ActOvertimeInfoDto entity2Dto(ActOvertimeInfo actOvertimeInfo);

    /**
     * 分页实体转DTO
     * @param page
     * @return
     */
    PageResult<ActOvertimeInfoDto> pageEntity2Dto(PageResult<ActOvertimeInfo> page);


    /**
     * dto集合转entity集合
     * @param rows
     * @return
     */
    List<ActOvertimeInfo> dtoList2Entity(List<ActOvertimeInfoDto> rows);

}
