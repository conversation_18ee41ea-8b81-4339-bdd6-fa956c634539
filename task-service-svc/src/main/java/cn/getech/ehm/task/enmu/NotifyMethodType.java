package cn.getech.ehm.task.enmu;

import cn.getech.ehm.common.dto.EnumListDto;
import java.util.ArrayList;
import java.util.List;

/**
 * 通知方式枚举
 * <AUTHOR>
 * @date 2020/3/3
 */
public enum NotifyMethodType {
    //EMAIL(1, "邮件");
    //APP(2, "APP推送");
    DING(3, "钉钉个人"),
    DING_GROUP(4, "钉钉群");


    NotifyMethodType(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public static List<EnumListDto> getList(){
        List<EnumListDto> enumListDtos = new ArrayList<>();
        for(NotifyMethodType notifyMethodType : NotifyMethodType.values()){
            EnumListDto enumListDto = new EnumListDto();
            enumListDto.setValue(notifyMethodType.value);
            enumListDto.setName(notifyMethodType.name);
            enumListDtos.add(enumListDto);
        }
        return enumListDtos;
    }

    private int value;

    private String name;

    public int getValue() { return value; }

    public void setValue(int value) { this.value = value; }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
