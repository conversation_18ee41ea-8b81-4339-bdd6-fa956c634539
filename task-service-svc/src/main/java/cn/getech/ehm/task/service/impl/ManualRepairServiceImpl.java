package cn.getech.ehm.task.service.impl;

import cn.getech.ehm.base.client.BaseServiceClient;
import cn.getech.ehm.base.dto.DictionaryItemDto;
import cn.getech.ehm.base.dto.FaultKnowledgeResDto;
import cn.getech.ehm.base.dto.FaultKnowledgeSearchDto;
import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.common.enums.TimeType;
import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.common.util.SortUtil;
import cn.getech.ehm.equipment.client.EquipmentClient;
import cn.getech.ehm.equipment.dto.EquipmentInfoSearchDto;
import cn.getech.ehm.equipment.dto.EquipmentListDto;
import cn.getech.ehm.iot.client.ParameterClient;
import cn.getech.ehm.system.client.SystemClient;
import cn.getech.ehm.system.dto.CustomerInfoEquipmentDto;
import cn.getech.ehm.task.dto.ManualRepairAddDto;
import cn.getech.ehm.task.dto.repair.*;
import cn.getech.ehm.task.dto.screen.ManualRepairGroupCustomerDto;
import cn.getech.ehm.task.dto.screen.TimeQueryParam;
import cn.getech.ehm.task.dto.task.info.MaintTaskDto;
import cn.getech.ehm.task.dto.task.info.MaintTaskRepairAddDto;
import cn.getech.ehm.task.entity.DefectInfo;
import cn.getech.ehm.task.entity.ManualRepair;
import cn.getech.ehm.task.mapper.ManualRepairMapper;
import cn.getech.ehm.task.service.IDefectInfoService;
import cn.getech.ehm.task.service.IMaintTaskService;
import cn.getech.ehm.task.service.IManualRepairService;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.api.ResultCode;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.framework.common.context.PorosContextHolder;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.poros.framework.common.utils.StringPool;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 人工报修 服务实现类
 *
 * <AUTHOR>
 * @since 2020-08-06
 */
@Slf4j
@Service
public class ManualRepairServiceImpl extends BaseServiceImpl<ManualRepairMapper, ManualRepair> implements IManualRepairService {

    @Autowired
    private ManualRepairMapper manualRepairMapper;
    @Autowired
    private EquipmentClient equipmentClient;
    @Autowired
    private IMaintTaskService maintTaskService;
    @Autowired
    private SystemClient systemClient;
    @Autowired
    private BaseServiceClient baseServiceClient;
    @Autowired
    private ResourceBundleMessageSource messageSource;
    @Autowired
    private ParameterClient parameterClient;
    @Autowired
    private IDefectInfoService defectInfoService;


    @Override
    public PageResult<ManualRepairListDto> manualPageDto(ManualRepairQueryParam manualRepairQueryParam) {
        //根据设备名称模糊查询设备id
        EquipmentInfoSearchDto equipmentInfoSearchDto = new EquipmentInfoSearchDto();
        equipmentInfoSearchDto.setKeyword(manualRepairQueryParam.getEquipmentName());
        RestResponse<List<String>> response = equipmentClient.getEquipmentIdsByParam(equipmentInfoSearchDto);
        if (response.isOk()) {
            List<String> equipmentIds = response.getData();
            if (CollectionUtils.isEmpty(equipmentIds)) {
                return new PageResult<>();
            }else {
                manualRepairQueryParam.setEquipmentIds(equipmentIds);
            }
        } else {
            log.error("查询设备信息失败");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("find_info_error", null, LocaleContextHolder.getLocale())));
        }

        Page<ManualRepairDto> page = new Page<>(manualRepairQueryParam.getPageNo(), manualRepairQueryParam.getLimit());
        manualRepairQueryParam.setSortValue(SortUtil.buildSortValue(manualRepairQueryParam.getSortPros(), ManualRepair.class, "repair"));
        Page<ManualRepairListDto> manualRepairDtoPage = manualRepairMapper.manualPageDto(page, manualRepairQueryParam);
        List<ManualRepairListDto> manualRepairDtos = manualRepairDtoPage.getRecords();
        if (CollectionUtils.isNotEmpty(manualRepairDtos)) {
            String[] equipmentIds = manualRepairDtos.stream().map(ManualRepairListDto::getEquipmentId)
                    .distinct().collect(Collectors.toList()).stream().toArray(String[]::new);
            Map<String, EquipmentListDto> equipmentMap = getEquipmentMap(equipmentIds);
            List<String[]> faultPhenomenonIdList = manualRepairDtos.stream()
                    .filter(dto -> (null != dto.getFaultPhenomenonIds() && dto.getFaultPhenomenonIds().length > 0))
                    .map(ManualRepairListDto::getFaultPhenomenonIds).distinct().collect(Collectors.toList());
            List<String> faultPhenomenonIds = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(faultPhenomenonIdList)) {
                for (String[] faultPhenomenonIdStr : faultPhenomenonIdList) {
                    faultPhenomenonIds.addAll(Arrays.asList(faultPhenomenonIdStr));
                }
            }
            Map<String, FaultKnowledgeResDto> faultPhenomenonMap = getFaultKnowledgeMap(faultPhenomenonIds, StaticValue.ONE);

            for(ManualRepairListDto dto : manualRepairDtos){
                EquipmentListDto equipment = equipmentMap.get(dto.getEquipmentId());
                if (null != equipment) {
                    dto.setEquipmentName(equipment.getEquipmentName());
                    dto.setEquipmentCode(equipment.getEquipmentCode());
                    dto.setEquipmentCategory(equipment.getCategoryName());
                    dto.setEquipmentLocation(equipment.getParentName());
                }
                dto.setActivityId(maintTaskService.getProcessTaskId(dto.getProcessInstanceId(), false));

                if(null != dto.getFaultPhenomenonIds() && dto.getFaultPhenomenonIds().length > 0){

                    dto.setFaultPhenomenonRemark(buildPhenomenon(dto.getFaultPhenomenonIds(), dto.getFaultPhenomenonRemark(), faultPhenomenonMap));
                }

            }
        }
        PageResult<ManualRepairListDto> result = PageResult.<ManualRepairListDto>builder()
                .records(manualRepairDtoPage.getRecords()).total(manualRepairDtoPage.getTotal()).build();
        return Optional.ofNullable(result).orElse(new PageResult<>());
    }

    @Override
    public List<ManualRepairExcelDto> manualExcelDto(ManualRepairQueryParam queryParam) {
        EquipmentInfoSearchDto equipmentInfoSearchDto = new EquipmentInfoSearchDto();
        equipmentInfoSearchDto.setKeyword(queryParam.getEquipmentName());
        RestResponse<List<String>> response = equipmentClient.getEquipmentIdsByParam(equipmentInfoSearchDto);
        if (response.isOk()) {
            List<String> equipmentIds = response.getData();
            if (CollectionUtils.isEmpty(equipmentIds)) {
                return new ArrayList<>();
            }else {
                queryParam.setEquipmentIds(equipmentIds);
            }
        } else {
            log.error("查询设备信息失败");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("find_info_error", null, LocaleContextHolder.getLocale())));
        }

        queryParam.setSortValue(SortUtil.buildSortValue(queryParam.getSortPros(), ManualRepair.class, "repair"));
        List<ManualRepairExcelDto> manualRepairDtos = manualRepairMapper.manualExcelDto(queryParam);
        if (CollectionUtils.isNotEmpty(manualRepairDtos)) {
            String[] equipmentIds = manualRepairDtos.stream().map(ManualRepairExcelDto::getEquipmentId)
                    .distinct().collect(Collectors.toList()).stream().toArray(String[]::new);
            Map<String, EquipmentListDto> equipmentMap = getEquipmentMap(equipmentIds);
            List<String[]> faultPhenomenonIdList = manualRepairDtos.stream()
                    .filter(dto -> (null != dto.getFaultPhenomenonIds() && dto.getFaultPhenomenonIds().length > 0))
                    .map(ManualRepairExcelDto::getFaultPhenomenonIds).distinct().collect(Collectors.toList());
            List<String> faultPhenomenonIds = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(faultPhenomenonIdList)) {
                for (String[] faultPhenomenonIdStr : faultPhenomenonIdList) {
                    faultPhenomenonIds.addAll(Arrays.asList(faultPhenomenonIdStr));
                }
            }
            Map<String, FaultKnowledgeResDto> faultPhenomenonMap = getFaultKnowledgeMap(faultPhenomenonIds, StaticValue.ONE);

            Map<String, DictionaryItemDto> urgencyMap = new HashMap<>();
            RestResponse<Map<String, DictionaryItemDto>> urgencyRes = baseServiceClient.getItemMapByCode("urgency");
            if (urgencyRes.isOk()) {
                urgencyMap = urgencyRes.getData();
            } else {
                log.info("连接base-service获取紧急程度字典表失败");
            }

            for(ManualRepairExcelDto dto : manualRepairDtos){
                EquipmentListDto equipment = equipmentMap.get(dto.getEquipmentId());
                if (null != equipment) {
                    dto.setEquipmentName(equipment.getEquipmentName());
                    dto.setEquipmentCode(equipment.getEquipmentCode());
                }
                if(null != dto.getFaultPhenomenonIds() && dto.getFaultPhenomenonIds().length > 0){

                    dto.setFaultPhenomenonRemark(buildPhenomenon(dto.getFaultPhenomenonIds(), dto.getFaultPhenomenonRemark(), faultPhenomenonMap));
                }
                if(StringUtils.isNotBlank(dto.getUrgency())){
                    DictionaryItemDto dictionaryItemDto = urgencyMap.get(dto.getUrgency());
                    dto.setUrgencyName(null != dictionaryItemDto ? dictionaryItemDto.getName() : null);
                }

            }
        }
        return manualRepairDtos;
    }

    private Map<String, EquipmentListDto> getEquipmentMap(String[] equipmentIds){
        Map<String, EquipmentListDto> equipmentMap = new HashMap<>();
        RestResponse<Map<String, EquipmentListDto>> listRestResponse = equipmentClient.getListByIds(equipmentIds);
        if (!listRestResponse.isSuccess()) {
            log.error("远程调用equipment-service出错");
        } else {
            equipmentMap = listRestResponse.getData();
        }
        return equipmentMap;
    }

    private Map<String, FaultKnowledgeResDto> getFaultKnowledgeMap(List<String> ids, Integer type){
        Map<String, FaultKnowledgeResDto> faultKnowledgeMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(ids)){
            FaultKnowledgeSearchDto searchDto = new FaultKnowledgeSearchDto();
            searchDto.setType(type);
            searchDto.setIds(ids);
            RestResponse<Map<String, FaultKnowledgeResDto>> restResponse = baseServiceClient.faultKnowledgeMap(searchDto);
            if(restResponse.isOk()){
                faultKnowledgeMap = restResponse.getData();
            }else{
                log.error("获取故障现象失败");
            }
        }
        return faultKnowledgeMap;
    }

    private String buildPhenomenon(String[] phenomenonIds, String phenomenonRemark, Map<String, FaultKnowledgeResDto> faultPhenomenonMap){
        String faultPhenomenonRemark = null;
        if(null != phenomenonIds && phenomenonIds.length > 0) {
            for (String faultPhenomenonId : phenomenonIds) {
                FaultKnowledgeResDto faultKnowledgeResDto = faultPhenomenonMap.get(faultPhenomenonId);
                if (ObjectUtils.isNull(faultKnowledgeResDto)){
                    continue;
                }
                if (StringUtils.isNotBlank(faultPhenomenonRemark)) {
                    faultPhenomenonRemark += ";" + faultKnowledgeResDto.getName();
                } else {
                    faultPhenomenonRemark = faultKnowledgeResDto.getName();
                }
            }
        }
        if(StringUtils.isNotBlank(faultPhenomenonRemark)){
            if(StringUtils.isNotBlank(phenomenonRemark)){
                faultPhenomenonRemark += ";" +  phenomenonRemark;
            }
        }else{
            faultPhenomenonRemark = phenomenonRemark;
        }
        return faultPhenomenonRemark;
    }

    @Override
    public PageResult<ManualRepairDetailDto> detailList(DetailQueryParam queryParam){
        EquipmentInfoSearchDto equipmentInfoSearchDto = new EquipmentInfoSearchDto();
        equipmentInfoSearchDto.setKeyword(queryParam.getEquipmentName());
        RestResponse<List<String>> response = equipmentClient.getEquipmentIdsByParam(equipmentInfoSearchDto);
        if (response.isOk()) {
            List<String> equipmentIds = response.getData();
            if (CollectionUtils.isEmpty(equipmentIds)) {
                return new PageResult<>();
            }else {
                queryParam.setEquipmentIds(equipmentIds);
            }
        } else {
            log.error("查询设备信息失败");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("find_info_error", null, LocaleContextHolder.getLocale())));
        }

        Page<DetailQueryParam> page = new Page<>(queryParam.getPageNo(), queryParam.getLimit());
        queryParam.setSortValue(SortUtil.buildSortValue(queryParam.getSortPros(), ManualRepair.class, "repair"));
        Page<ManualRepairDetailDto> detailDtoPage = manualRepairMapper.detailList(page, queryParam);
        List<ManualRepairDetailDto> detailDtos = detailDtoPage.getRecords();
        if (CollectionUtils.isNotEmpty(detailDtos)) {
            String[] equipmentIds = detailDtos.stream().map(ManualRepairDetailDto::getEquipmentId)
                    .distinct().collect(Collectors.toList()).stream().toArray(String[]::new);
            List<String[]> faultPhenomenonIdList = detailDtos.stream()
                    .filter(dto -> (null != dto.getFaultPhenomenonIds() && dto.getFaultPhenomenonIds().length > 0))
                    .map(ManualRepairDetailDto::getFaultPhenomenonIds).distinct().collect(Collectors.toList());
            Map<String, FaultKnowledgeResDto> faultPhenomenonMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(faultPhenomenonIdList)){
                List<String> faultPhenomenonIds = new ArrayList<>();
                for(String[] faultPhenomenonIdStr : faultPhenomenonIdList){
                    faultPhenomenonIds.addAll(Arrays.asList(faultPhenomenonIdStr));
                }

                faultPhenomenonMap = getFaultKnowledgeMap(faultPhenomenonIds, StaticValue.ONE);
            }
            Map<String, EquipmentListDto> equipmentMap = getEquipmentMap(equipmentIds);

            for(ManualRepairDetailDto dto : detailDtos){
                EquipmentListDto equipment = equipmentMap.get(dto.getEquipmentId());
                if (null != equipment) {
                    dto.setEquipmentName(equipment.getEquipmentName());
                    dto.setEquipmentCode(equipment.getEquipmentCode());
                    dto.setCategoryId(equipment.getCategoryId());
                    dto.setCategoryName(equipment.getCategoryName());
                    dto.setLocationId(equipment.getLocationId());
                    dto.setLocationName(equipment.getLocationName());
                }
                if(null != dto.getFaultPhenomenonIds() && dto.getFaultPhenomenonIds().length > 0){
                    List<FaultKnowledgeResDto> faultPhenomenonDtos = new ArrayList<>();
                    for(String faultPhenomenonId : dto.getFaultPhenomenonIds()){
                        FaultKnowledgeResDto faultKnowledgeResDto = faultPhenomenonMap.get(faultPhenomenonId);
                        if(null != faultKnowledgeResDto){
                            faultPhenomenonDtos.add(faultKnowledgeResDto);
                        }
                    }
                    dto.setFaultPhenomenonDtos(faultPhenomenonDtos);
                }

            }
        }
        return Optional.ofNullable(PageResult.<ManualRepairDetailDto>builder()
                .records(detailDtos)
                .total(detailDtoPage.getTotal())
                .build())
                .orElse(new PageResult<>());
    }

    @Override
    public PageResult<ManualRepairAppDto> appManualPageDto(RepairAppQueryParam repairAppQueryParam) {
        EquipmentInfoSearchDto param = new EquipmentInfoSearchDto();
        param.setKeyword(repairAppQueryParam.getKeyword());
        RestResponse<List<String>> listRestResponse = equipmentClient.getEquipmentIdsByParam(param);
        if (!listRestResponse.isOk()) {
            log.error("远程调用equipment-service出错");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("feign_error", null, LocaleContextHolder.getLocale())));
        } else {
            List<String> equipmentIds = listRestResponse.getData();
            if(CollectionUtils.isEmpty(equipmentIds)){
                return new PageResult<>();
            }
            repairAppQueryParam.setEquipmentIds(equipmentIds);
        }
        Page<ManualRepairAppDto> page = new Page<>(repairAppQueryParam.getPageNo(), repairAppQueryParam.getLimit());
        Page<ManualRepairAppDto> manualRepairDtoPage = manualRepairMapper.appManualPageDto(page, repairAppQueryParam);
        List<ManualRepairAppDto> manualRepairDtos = manualRepairDtoPage.getRecords();
        buildAppEquipment(manualRepairDtos);
        //修改状态
        List<ManualRepairAppDto> manualRepairAppDtos = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(manualRepairDtos)){
            List<String> faultPhenomenonIdList = manualRepairDtos.stream()
                    .filter(dto -> StringUtils.isNotBlank(dto.getFaultPhenomenonIds()))
                    .map(ManualRepairAppDto::getFaultPhenomenonIds).distinct().collect(Collectors.toList());

            List<String> faultPhenomenonIds = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(faultPhenomenonIdList)){

                for(String faultPhenomenonIdStr : faultPhenomenonIdList){
                    faultPhenomenonIds.addAll(Arrays.asList(faultPhenomenonIdStr.split(StringPool.COMMA)));
                }

            }
            Map<String, FaultKnowledgeResDto> faultPhenomenonMap = getFaultKnowledgeMap(faultPhenomenonIds, StaticValue.ONE);
            for(ManualRepairAppDto dto : manualRepairDtos) {
                ManualRepairAppDto manualRepairAppDto = CopyDataUtil.copyObject(dto, ManualRepairAppDto.class);
                if(StringUtils.isNotBlank(manualRepairAppDto.getFaultPhenomenonIds())){
                    String[] phenomenonIds = manualRepairAppDto.getFaultPhenomenonIds().split(StringPool.COMMA);
                    manualRepairAppDto.setFaultPhenomenonRemark(buildPhenomenon(phenomenonIds, manualRepairAppDto.getFaultPhenomenonRemark(), faultPhenomenonMap));
                }
                manualRepairAppDtos.add(manualRepairAppDto);
            }
        }
        PageResult<ManualRepairAppDto> result = PageResult.<ManualRepairAppDto>builder()
                .records(manualRepairAppDtos)
                .total(manualRepairDtoPage.getTotal()).build();
        return Optional.ofNullable(result).orElse(new PageResult<>());
    }

    private void buildAppEquipment(List<ManualRepairAppDto> manualRepairDtos) {
        if (CollectionUtils.isNotEmpty(manualRepairDtos)) {
            String[] equipmentIds = manualRepairDtos.stream().map(ManualRepairAppDto::getEquipmentId)
                    .distinct().collect(Collectors.toList()).stream().toArray(String[]::new);
            Map<String, EquipmentListDto> equipmentMap = getEquipmentMap(equipmentIds);
            for(ManualRepairAppDto dto : manualRepairDtos){
                EquipmentListDto equipment = equipmentMap.get(dto.getEquipmentId());
                if (null != equipment) {
                    dto.setEquipmentName(equipment.getEquipmentName());
                    dto.setEquipmentCategory(equipment.getCategoryName());
                    dto.setEquipmentLocation(equipment.getParentName());
                    dto.setEquipmentCode(equipment.getEquipmentCode());
                    dto.setEquipmentPrincipal(equipment.getManagePrincipal());
                    dto.setEquipmentRunningStatus(equipment.getRunningStatus());
                    dto.setPicId(equipment.getPicId());
                    dto.setPicUrl(equipment.getPicUrl());
                }
            }
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean saveManualRepair(ManualRepairAddParam manualRepairAddParam) {
        ManualRepair manualRepair = CopyDataUtil.copyObject(manualRepairAddParam, ManualRepair.class);
        UserBaseInfo user = PorosContextHolder.getCurrentUser();
        manualRepair.setCreateUserName(user.getName());
        if(null != manualRepairAddParam.getDeadlineDate()){
            manualRepair.setDeadlineDate(DateUtil.endOfDay(manualRepairAddParam.getDeadlineDate()));
        }
        if(null == manualRepairAddParam.getFaultTime()){
            manualRepair.setFaultTime(new Date());
        }

        Assert.notNull(ResultCode.PARAM_VALID_ERROR, manualRepair);

        String id = UUID.randomUUID().toString().replace("-","");
        manualRepair.setId(id);
        if(StringUtils.isNotBlank(manualRepairAddParam.getEquipmentWarnId())){
            manualRepair.setSource(2);
            manualRepair.setSourceId(manualRepairAddParam.getEquipmentWarnId());
            //更新告警单状态
            parameterClient.updateRepairStatus(manualRepairAddParam.getEquipmentWarnId());
        }
        Map<String, FaultKnowledgeResDto> faultKnowledgeMap = new HashMap<>();
        if(null != manualRepair.getFaultPhenomenonIds() && manualRepair.getFaultPhenomenonIds().length > 0){
            faultKnowledgeMap = getFaultKnowledgeMap(Arrays.asList(manualRepair.getFaultPhenomenonIds()), StaticValue.ONE);
        }
        String content = buildPhenomenon(manualRepair.getFaultPhenomenonIds(), manualRepair.getFaultPhenomenonRemark(), faultKnowledgeMap);
        return saveByParam(manualRepair, content, manualRepairAddParam.getFaultStructureIds(), manualRepairAddParam.getSourceTaskId(), manualRepairAddParam.getSourceTaskCode(), manualRepairAddParam.getSourceType(),manualRepairAddParam.getDefectId());
    }

    @Override
    public Boolean editManualRepair(ManualRepairEditParam manualRepairEditParam){
        ManualRepair manualRepair = CopyDataUtil.copyObject(manualRepairEditParam, ManualRepair.class);
        return updateById(manualRepair);
    }

    @Override
    public Boolean saveWarnRepair(ManualRepairAddDto addDto){
        ManualRepair manualRepair = CopyDataUtil.copyObject(addDto, ManualRepair.class);
        UserBaseInfo user = PorosContextHolder.getCurrentUser();
        //告警定时生成的没有创建人
        manualRepair.setCreateUserName(StringUtils.isNotBlank(user.getName()) ? user.getName() : "告警自动报修");
        manualRepair.setUrgency("1");
        manualRepair.setSource(StaticValue.TWO);
        String id = UUID.randomUUID().toString().replace("-","");
        manualRepair.setId(id);
        manualRepair.setFaultPhenomenonRemark(addDto.getFaultDesc());
        manualRepair.setFaultTime(addDto.getFaultTime());
        manualRepair.setDeadlineDate(DateUtil.offsetDay(addDto.getFaultTime(), 1));
        return saveByParam(manualRepair, addDto.getFaultDesc());
    }

    private Boolean saveByParam(ManualRepair manualRepair, String content,String[] faultStructureIds,String sourceTaskId,String sourceTaskCode,Integer sourceType,String defectId){
        MaintTaskRepairAddDto addDto = CopyDataUtil.copyObject(manualRepair, MaintTaskRepairAddDto.class);
        addDto.setSourceId(manualRepair.getId());
        addDto.setContent(content);
        addDto.setTaskDeadlineDate(manualRepair.getDeadlineDate());
        addDto.setFaultStructureIds(faultStructureIds);
        addDto.setSourceTaskId(sourceTaskId);
        addDto.setSourceTaskCode(sourceTaskCode);
        addDto.setSourceType(sourceType);
        MaintTaskDto maintTaskDto = maintTaskService.saveRepairTask(addDto);
        if (StringUtils.isNotBlank(defectId)){
            defectInfoService.update(new UpdateWrapper<DefectInfo>().lambda().eq(DefectInfo::getId, defectId)
                    .set(DefectInfo::getMaintTaskCode, maintTaskDto.getCode()).set(DefectInfo::getMaintTaskId, maintTaskDto.getId()));
        }

        //更新关联工单
        manualRepair.setTaskId(maintTaskDto.getId());
        manualRepair.setTaskCode(maintTaskDto.getCode());
        return save(manualRepair);
    }

    /**
     *
     * @param manualRepair
     * @return
     */
    private Boolean saveByParam(ManualRepair manualRepair, String content){
        MaintTaskRepairAddDto addDto = CopyDataUtil.copyObject(manualRepair, MaintTaskRepairAddDto.class);
        addDto.setSourceId(manualRepair.getId());
        addDto.setContent(content);
        addDto.setTaskDeadlineDate(manualRepair.getDeadlineDate());
        MaintTaskDto maintTaskDto = maintTaskService.saveRepairTask(addDto);

        //更新关联工单
        manualRepair.setTaskId(maintTaskDto.getId());
        manualRepair.setTaskCode(maintTaskDto.getCode());
        return save(manualRepair);
    }

    @Override
    public ManualRepairDto getManualDtoById(String id) {
        ManualRepairDto manualRepairDto = CopyDataUtil.copyObject(this.getById(id), ManualRepairDto.class);
        if (null != manualRepairDto) {
            EquipmentListDto equipmentListDto = maintTaskService.getEquipment(manualRepairDto.getEquipmentId());
            if (null != equipmentListDto) {
                manualRepairDto.setEquipmentName(equipmentListDto.getEquipmentName());
                manualRepairDto.setEquipmentCategory(equipmentListDto.getCategoryName());
                manualRepairDto.setEquipmentLocation(equipmentListDto.getParentName());
                manualRepairDto.setEquipmentCode(equipmentListDto.getEquipmentCode());
                manualRepairDto.setEquipmentRunningStatus(equipmentListDto.getRunningStatus());
            }
            FaultKnowledgeSearchDto searchDto = new FaultKnowledgeSearchDto();
            if(null != manualRepairDto.getFaultPhenomenonIds() && manualRepairDto.getFaultPhenomenonIds().length > 0){
                searchDto.setType(StaticValue.ONE);
                searchDto.setIds(Arrays.asList(manualRepairDto.getFaultPhenomenonIds()));
                RestResponse<Map<String, FaultKnowledgeResDto>> restResponse = baseServiceClient.faultKnowledgeMap(searchDto);
                if(restResponse.isOk()){
                    Map<String, FaultKnowledgeResDto> map = restResponse.getData();
                    List<FaultKnowledgeResDto> faultPhenomenonDtos = new ArrayList<>();
                    for(String faultPhenomenonId : manualRepairDto.getFaultPhenomenonIds()){
                        FaultKnowledgeResDto faultKnowledgeResDto = map.get(faultPhenomenonId);
                        if(null != faultKnowledgeResDto){
                            faultPhenomenonDtos.add(faultKnowledgeResDto);
                        }
                    }
                    manualRepairDto.setFaultPhenomenonDtos(faultPhenomenonDtos);
                }else{
                    log.error("获取故障现象失败");
                }
            }
            if(null != manualRepairDto.getFaultReasonIds() && manualRepairDto.getFaultReasonIds().length > 0){
                searchDto.setType(StaticValue.TWO);
                searchDto.setIds(Arrays.asList(manualRepairDto.getFaultReasonIds()));
                RestResponse<Map<String, FaultKnowledgeResDto>> restResponse = baseServiceClient.faultKnowledgeMap(searchDto);
                if(restResponse.isOk()){
                    Map<String, FaultKnowledgeResDto> map = restResponse.getData();
                    List<FaultKnowledgeResDto> faultReasonDtos = new ArrayList<>();
                    for(String reasonId : manualRepairDto.getFaultReasonIds()){
                        FaultKnowledgeResDto faultKnowledgeResDto = map.get(reasonId);
                        if(null != faultKnowledgeResDto){
                            faultReasonDtos.add(faultKnowledgeResDto);
                        }
                    }
                    manualRepairDto.setFaultReasonDtos(faultReasonDtos);
                }else{
                    log.error("获取故障原因失败");
                }
            }
            if(null != manualRepairDto.getFaultMeasuresIds() && manualRepairDto.getFaultMeasuresIds().length > 0){
                searchDto.setType(StaticValue.THREE);
                searchDto.setIds(Arrays.asList(manualRepairDto.getFaultMeasuresIds()));
                RestResponse<Map<String, FaultKnowledgeResDto>> restResponse = baseServiceClient.faultKnowledgeMap(searchDto);
                if(restResponse.isOk()){
                    Map<String, FaultKnowledgeResDto> map = restResponse.getData();
                    List<FaultKnowledgeResDto> faultMeasuresDtos = new ArrayList<>();
                    for(String measuresId : manualRepairDto.getFaultMeasuresIds()){
                        FaultKnowledgeResDto faultKnowledgeResDto = map.get(measuresId);
                        if(null != faultKnowledgeResDto){
                            faultMeasuresDtos.add(faultKnowledgeResDto);
                        }
                    }
                    manualRepairDto.setFaultMeasuresDtos(faultMeasuresDtos);
                }else{
                    log.error("获取处理措施失败");
                }
            }
        }
        return manualRepairDto;
    }

    @Override
    public Boolean updateStatus(ManualRepairEditParam manualRepairEditParam) {
        ManualRepair manualRepair = CopyDataUtil.copyObject(manualRepairEditParam, ManualRepair.class);
        return updateById(manualRepair);
    }

    @Override
    public ManualRepairGroupCustomerDto getManualRepairGroupCustomerDto(TimeQueryParam timeQueryParam) {
        RestResponse<List<CustomerInfoEquipmentDto>> restResponse = systemClient.getAllCustomerEqui();

        Date startTime = DateUtil.beginOfWeek(new Date());
        Date endTime = DateUtil.endOfWeek(new Date());


        if (TimeType.WEEK.getValue().equals(timeQueryParam.getTime())) {
            startTime = DateUtil.beginOfWeek(new Date());
            endTime = DateUtil.endOfWeek(new Date());
        } else if (TimeType.MONTH.getValue().equals(timeQueryParam.getTime())) {
            startTime = DateUtil.beginOfMonth(new Date());
            endTime = DateUtil.endOfMonth(new Date());
        } else if (TimeType.YEAR.getValue().equals(timeQueryParam.getTime())) {
            startTime = DateUtil.beginOfYear(new Date());
            endTime = DateUtil.endOfYear(new Date());
        }

        if (restResponse != null && restResponse.getCode() == 0 && restResponse.getData().size() != 0) {
            ManualRepairGroupCustomerDto resultDto = new ManualRepairGroupCustomerDto();
            List<CustomerInfoEquipmentDto> data = restResponse.getData();
            List oldmanualRepairCount = manualRepairMapper.getManualRepairCount(data, startTime, endTime);
            Map<String,Integer> newmanualRepairCount = new TreeMap<>();
            parseManualRepairCount(oldmanualRepairCount, newmanualRepairCount, data);

            List<Map.Entry<String,Integer>> sortlist = new ArrayList<>(newmanualRepairCount.entrySet());
            //然后通过比较器来实现排序
            //升序排序
            Collections.sort(sortlist, (o1, o2) -> o2.getValue() - o1.getValue());

            //默认显示5个
            int i = 0;
            while (i < 5 && i < sortlist.size()) {
                resultDto.getNames().add(sortlist.get(i).getKey());
                resultDto.getValues().add(sortlist.get(i).getValue());
                i++;
            }
            return resultDto;
        }

        return null;
    }

    @Override
    public List<String> getUsedInfoIds(){
        LambdaQueryWrapper<ManualRepair> wrapper = Wrappers.lambdaQuery();
        wrapper.select(ManualRepair::getEquipmentId);
        return manualRepairMapper.selectList(wrapper).stream().map(ManualRepair::getEquipmentId).distinct().collect(Collectors.toList());
    }

    @Override
    public Boolean checkHaveRepair(String infoParamId){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, calendar.getActualMinimum(Calendar.HOUR_OF_DAY));
        calendar.set(Calendar.MINUTE, calendar.getActualMinimum(Calendar.MINUTE));
        calendar.set(Calendar.SECOND, calendar.getActualMinimum(Calendar.SECOND));
        Date beginDate = calendar.getTime();
        calendar.set(Calendar.HOUR_OF_DAY, calendar.getActualMaximum(Calendar.HOUR_OF_DAY));
        calendar.set(Calendar.MINUTE, calendar.getActualMaximum(Calendar.MINUTE));
        calendar.set(Calendar.SECOND, calendar.getActualMaximum(Calendar.SECOND));
        Date endDate = calendar.getTime();

        LambdaQueryWrapper<ManualRepair> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ManualRepair::getSourceId, infoParamId);
        wrapper.eq(ManualRepair::getSource, StaticValue.TWO);
        wrapper.between(ManualRepair::getCreateTime, beginDate, endDate);
        return manualRepairMapper.selectCount(wrapper) > StaticValue.ZERO;
    }

    /**
     * 转化mybatis union all 查出来的数据 两层list 的问题
     *
     * @param oldmanualRepairCount
     * @param newmanualRepairCount
     */
    private void parseManualRepairCount(List oldmanualRepairCount, Map<String,Integer> newmanualRepairCount, List<CustomerInfoEquipmentDto> data) {
        for (int i = 0; i < oldmanualRepairCount.size(); i++) {
            newmanualRepairCount.put(data.get(i).getName(), Integer.valueOf(((ArrayList) oldmanualRepairCount.get(i)).get(0).toString()));
        }
    }

    @Override
    public Boolean scadaSaveWarnRepair(List<ManualRepairAddDto> addDtos){
        for(ManualRepairAddDto addDto : addDtos){
            this.saveWarnRepair(addDto);
        }
        return true;
    }
}
