package cn.getech.ehm.task.dto.plan;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;


/**
 * 发单日期dto
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "PlanTriggerTimeDto", description = "发单日期dto")
public class PlanTriggerTimeDto {

    @ApiModelProperty(value = "计划id")
    private String planId;

    @ApiModelProperty(value = "维保计划时间id")
    private String planEquipmentTimeId;

    @ApiModelProperty(value = "发单日期")
    private Date triggerTime;

}