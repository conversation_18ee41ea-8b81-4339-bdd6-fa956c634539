package cn.getech.ehm.task.dto.taskConfig;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import cn.getech.poros.framework.common.annotation.Excel;
import lombok.NoArgsConstructor;

import java.util.Date;


/**
 * <pre>
 * 工单配置信息 返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-04-21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "MaintTaskConfigDto", description = "工单配置信息返回数据模型")
public class MaintTaskConfigDto{

    @ApiModelProperty(value = "")
    @Excel(name="",cellType = Excel.ColumnType.STRING)
    private String id;

    @ApiModelProperty(value = "createBy")
    @Excel(name="createBy",cellType = Excel.ColumnType.STRING )
    private String createBy;

    @ApiModelProperty(value = "updateBy")
    @Excel(name="updateBy",cellType = Excel.ColumnType.STRING )
    private String updateBy;

    @ApiModelProperty(value = "createTime")
    @Excel(name="createTime",cellType = Excel.ColumnType.STRING ,dateFormat="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "updateTime")
    @Excel(name="updateTime",cellType = Excel.ColumnType.STRING ,dateFormat="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(value = "remark")
    @Excel(name="remark",cellType = Excel.ColumnType.STRING )
    private String remark;

    @ApiModelProperty("工单类型")
    private Integer taskType;

    @ApiModelProperty("配置类型")
    private Integer configType;

    @ApiModelProperty("配置内容")
    private String configContent;

    @ApiModelProperty("开关")
    private Boolean isAlert;

}