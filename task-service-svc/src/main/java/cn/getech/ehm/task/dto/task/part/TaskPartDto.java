package cn.getech.ehm.task.dto.task.part;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 维护工单、备件关联表 返回数据模型
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "TaskPartRelDto", description = "维护工单、备件关联表返回数据模型")
public class TaskPartDto {

    @ApiModelProperty(value = "关联表id")
    private String id;

    @ApiModelProperty(value = "工单id")
    private String taskId;

    @ApiModelProperty(value = "备件id")
    private String partId;

    @ApiModelProperty(value = "备件名称")
    private String partName;

    @ApiModelProperty(value = "备件类型")
    private String partCategoryName;

    @ApiModelProperty(value = "备件规格")
    private String partSpecification;

    @ApiModelProperty(value = "计划数量")
    private Integer planQty;

    @ApiModelProperty(value = "实际数量")
    private Integer actualQty;

    @ApiModelProperty(value = "图片id")
    private String picId;

    @ApiModelProperty(value = "图片url")
    private String picUrl;

}