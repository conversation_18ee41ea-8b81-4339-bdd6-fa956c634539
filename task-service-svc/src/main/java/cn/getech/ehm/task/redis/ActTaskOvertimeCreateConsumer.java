package cn.getech.ehm.task.redis;

import cn.getech.ehm.task.entity.ActOvertimeInfo;
import cn.getech.ehm.task.handler.ActivitiHandler;
import cn.getech.ehm.task.service.IActOvertimeInfoService;
import cn.getech.poros.bpm.callback.ProcessConstant;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ActTaskOvertimeCreateConsumer implements RedisConsumer {
    @Autowired
    RedisTemplate redisTemplate;
    @Autowired
    @Lazy
    IActOvertimeInfoService actOvertimeInfoService;
    @Autowired
    ActivitiHandler activitiHandler;

    @Value("${flow.maint.code:E0004}")
    private String maintFlowCode;

    @Override
    public String getKey() {
        return ProcessConstant.OVERTIME_NOTIFY_CREATE + maintFlowCode;
    }

    @Override
    public void Consumer(Object data, ZSetOperations<String, Object> zSet) {
        try {
            log.info("工单超时记录创建被消费：【key=" + getKey() + "】===>" + data);
            String s = data.toString();
            ActOvertimeInfo actOvertimeInfo = JSON.parseObject(s, ActOvertimeInfo.class);
            activitiHandler.dealContext(actOvertimeInfo.getCreateBy(), actOvertimeInfo.getTenantId());
            actOvertimeInfoService.save(actOvertimeInfo);
            zSet.remove(getKey(), data);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
