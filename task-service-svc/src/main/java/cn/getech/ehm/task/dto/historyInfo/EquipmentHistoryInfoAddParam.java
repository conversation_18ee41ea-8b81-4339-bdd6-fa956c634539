package cn.getech.ehm.task.dto.historyInfo;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <pre>
 *  新增参数
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-12-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentHistoryInfo新增", description = "新增参数")
public class EquipmentHistoryInfoAddParam extends ApiParam {
    @ApiModelProperty("工单id")
    private String taskId;

    @ApiModelProperty("变更前设备id")
    private String oldEquipmentId;

    @ApiModelProperty("变更后设备id")
    private String newEquipmentId;

    @ApiModelProperty("是否变更(0false1true)")
    private Integer changed;

    @ApiModelProperty("租户ID")
    private String tenantId;

    @ApiModelProperty("是否删除(0false1true)")
    private Integer deleted;
}