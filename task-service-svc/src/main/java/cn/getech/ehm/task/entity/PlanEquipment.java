package cn.getech.ehm.task.entity;

import cn.getech.ehm.common.mp.handler.StringArrayTypeHandler;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;
import java.util.Date;

/**
 * 维保对象
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("plan_equipment")
public class PlanEquipment extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 计划id
     */
    @TableField("plan_id")
    private String planId;

    /**
     * 设备类型id
     */
    @TableField(value = "info_category_id")
    private String infoCategoryId;

    /**
     * 设备位置id
     */
    @TableField(value = "info_location_id")
    private String infoLocationId;

    /**
     * 设备id集合
     */
    @TableField(value = "equipment_ids", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] equipmentIds;

    /**
     * 维护人员id字符串
     */
    @TableField(value = "maintainer_ids", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] maintainerIds;

    /**
     * 维护人员id字符串
     */
    @TableField(value = "team_ids", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] teamIds;

    /**
     * 人员指派策略 0 根据设备 1 人工配置 2 自由扫码
     */
    @TableField("person_strategy")
    private Integer personStrategy;

    /**
     * 作业标准id
     */
    @TableField("standard_id")
    private String standardId;

    /**
     * 计划维护日期
     */
    @TableField("plan_maint_time")
    private Date planMaintTime;

    /**
     * 故障现象ids
     */
    @TableField(value = "fault_phenomenon_ids", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] faultPhenomenonIds;

    /**
     * 故障现象扩展
     */
    @TableField("fault_phenomenon_remark")
    private String faultPhenomenonRemark;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    @TableField(typeHandler = StringArrayTypeHandler.class)
    private String[] acceptMaintainerIds;

    @TableField(typeHandler = StringArrayTypeHandler.class)
    private String[] acceptTeamIds;

}
