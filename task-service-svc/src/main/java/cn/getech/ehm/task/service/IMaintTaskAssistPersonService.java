package cn.getech.ehm.task.service;

import cn.getech.ehm.task.dto.task.assist.MaintTaskAssisEditParam;
import cn.getech.ehm.task.dto.task.assist.MaintTaskAssistAddParam;
import cn.getech.ehm.task.dto.task.assist.MaintTaskAssistListDto;
import cn.getech.ehm.task.entity.MaintTaskAssistPerson;
import cn.getech.poros.framework.common.service.IBaseService;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 工单辅助人员
 *
 * <AUTHOR>
 * @since 2022-06-08
 */
public interface IMaintTaskAssistPersonService extends IBaseService<MaintTaskAssistPerson> {

    Boolean addTaskAssistPerson(MaintTaskAssistAddParam addParam);

    Boolean updateTaskAssistPerson(MaintTaskAssisEditParam editParam);

    Boolean deleteTaskAssistPerson(String id);

    Boolean deleteList(List<String> ids);

    List<MaintTaskAssistListDto> getAssistPersonList(String taskId);

    List<MaintTaskAssistPerson> getAssistPersonListByTaskId(String taskId, Integer timeMode);

    Boolean saveOrUpdateAssistPersons(List<MaintTaskAssisEditParam> editParamList);

    List<MaintTaskAssistListDto> getAssistNameList(List<String> ids);
    /**
     * 离线缓存提交
     */
    Boolean offlineCacheSubmit(List<MaintTaskAssistListDto>  assistPeopleList, String taskId, Date startTime, Date endTime);

}
