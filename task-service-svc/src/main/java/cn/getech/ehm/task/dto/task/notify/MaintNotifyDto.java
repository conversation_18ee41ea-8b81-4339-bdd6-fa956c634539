package cn.getech.ehm.task.dto.task.notify;

import cn.getech.ehm.task.dto.task.info.NameDetailDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * 通知dto
 * <AUTHOR>
 * @date 2023-08-28
 */
@Data
@ApiModel(value = "MaintNotifyDto", description = "通知dto")
public class MaintNotifyDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "类型1临期通知")
    private Integer type;

    @ApiModelProperty(value = "来源类型1维保计划2工单")
    private Integer sourceType;

    @ApiModelProperty(value = "关联类型id")
    private String sourceId;

    @ApiModelProperty(value = "是否启用")
    private Boolean enable;

    @ApiModelProperty(value = "提前释放小时")
    private Integer advanceHour;

    @ApiModelProperty(value = "通知方式")
    private Integer[] notifyMethods;

    @ApiModelProperty(value = "通知对象")
    private Integer[] notifyObjects;

    @ApiModelProperty(value = "自定义通知用户UIDS")
    private String[] customUids;

    @ApiModelProperty(value = "自定义用户dtos")
    private List<NameDetailDto> uidDtos;

    @ApiModelProperty(value = "自定义通知角色(id)")
    private String[] customRoles;

    @ApiModelProperty(value = "自定义角色集合")
    private List<NameDetailDto> roleDtos;

    @ApiModelProperty(value = "设备管理人员")
    private Integer[] equipmentManager;

    @ApiModelProperty(value = "指定维护人员")
    private String[] maintainerIds;

    @ApiModelProperty(value = "指定维护班组")
    private String[] teamIds;

    @ApiModelProperty(value = "临期/超期通知时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date notifyTime;

}
