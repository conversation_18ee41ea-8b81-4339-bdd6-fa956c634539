package cn.getech.ehm.task.dto.plan;

import cn.getech.ehm.common.mp.handler.StringArrayTypeHandler;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;


/**
 * 维保对象编辑dto
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "PlanEquipmentEditDto", description = "维保对象编辑dto")
public class PlanEquipmentEditDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "计划id")
    private String planId;

    @ApiModelProperty(value = "设备类型id")
    private String infoCategoryId;

    @ApiModelProperty(value = "设备位置id")
    private String infoLocationId;

    @ApiModelProperty(value = "设备id集合")
    private String[] equipmentIds;

    @ApiModelProperty(value = "作业标准id")
    private String standardId;

    @ApiModelProperty(value = "维护人员id集合")
    private String[] maintainerIds;

    @ApiModelProperty(value = "维护班组id集合")
    private String[] teamIds;

    @ApiModelProperty(value = "人员指派策略 0 根据设备 1 人工配置 2 自由扫码")
    private Integer personStrategy;

    @ApiModelProperty(value = "计划维护日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date planMaintTime;

    @ApiModelProperty(value = "故障现象ids")
    private String[] faultPhenomenonIds;

    @ApiModelProperty(value = "故障现象扩展")
    private String faultPhenomenonRemark;

    @ApiModelProperty(value = "说明")
    private String remark;

    @TableField(typeHandler = StringArrayTypeHandler.class)
    @ApiModelProperty(value = "验收维护人员集合")
    private String[] acceptMaintainerIds;

    @TableField(typeHandler = StringArrayTypeHandler.class)
    @ApiModelProperty(value = "验收维护班组集合")
    private String[] acceptTeamIds;

}