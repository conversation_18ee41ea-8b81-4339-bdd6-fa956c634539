package cn.getech.ehm.task.dto.repair;

import cn.getech.ehm.base.dto.FaultKnowledgeResDto;
import cn.getech.poros.framework.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import java.util.List;

/**
 * 人工报修
 */
@Data
@EqualsAndHashCode()
public class ManualRepairDto {

    @ApiModelProperty(value = "报修单id")
    private String id;

    @ApiModelProperty(value = "设备id")
    private String equipmentId;

    @ApiModelProperty(value = "故障类别")
    private String faultType;

    @ApiModelProperty(value = "专业")
    private String major;

    @ApiModelProperty(value = "紧急程度")
    private String urgency;

    @ApiModelProperty(value = "影响程度")
    private String influence;

    @ApiModelProperty(value = "故障现象ids")
    private String[] faultPhenomenonIds;

    @ApiModelProperty(value = "故障现象Dtos")
    private List<FaultKnowledgeResDto> faultPhenomenonDtos;

    @ApiModelProperty(value = "故障现象扩展")
    private String faultPhenomenonRemark;

    @ApiModelProperty(value = "故障原因ids")
    private String[] faultReasonIds;

    @ApiModelProperty(value = "故障原因Dtos")
    private List<FaultKnowledgeResDto> faultReasonDtos;

    @ApiModelProperty(value = "故障原因扩展")
    private String faultReasonRemark;

    @ApiModelProperty(value = "处理措施ids")
    private String[] faultMeasuresIds;

    @ApiModelProperty(value = "处理措施Dtos")
    private List<FaultKnowledgeResDto> faultMeasuresDtos;

    @ApiModelProperty(value = "处理措施扩展")
    private String faultMeasuresRemark;

    @ApiModelProperty(value = "报修/提报时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "报修人/创建人")
    private String createUserName;

    @ApiModelProperty(value = "故障设备")
    @Excel(name="故障设备",cellType = Excel.ColumnType.STRING )
    private String equipmentName;

    @ApiModelProperty(value = "故障描述")
    @Excel(name="故障描述",cellType = Excel.ColumnType.STRING )
    private String faultDesc;

    @ApiModelProperty(value = "故障日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date faultTime;

    @ApiModelProperty(value = "状态(0未维修1维修中2已维修)")
    private Integer status;

    @ApiModelProperty(value = "修复/完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date repairTime;

    @ApiModelProperty(value = "客户工程师uid/报修人uid")
    private String createBy;

    @ApiModelProperty(value = "接单人")
    private String handler;

    @ApiModelProperty(value = "设备编码")
    private String equipmentCode;

    @ApiModelProperty(value = "维护设备类型")
    private String equipmentCategory;

    @ApiModelProperty(value = "维护设备位置")
    private String equipmentLocation;

    @ApiModelProperty(value = "维护设备状态")
    private Integer equipmentRunningStatus;

    @ApiModelProperty(value = "工单id")
    private String taskId;

    @ApiModelProperty(value = "流程实例id")
    private String processInstanceId;

    @ApiModelProperty(value = "工作流id")
    private String activityId;

    @ApiModelProperty(value = "状态(0待派单1待接单2待执行3开始确认4安全确认中5已确认6执行中7执行中已挂起8待验收9正常关闭10异常关闭)")
    private Integer taskStatus;

    @ApiModelProperty(value = "工单编号")
    @Excel(name="维修工单",cellType = Excel.ColumnType.STRING )
    private String taskCode;

    @ApiModelProperty(value = "类型(0场内报修1远程诊断)")
    private Integer type;

    @ApiModelProperty(value = "附件id集合")
    private String mediaIds;

    @ApiModelProperty(value = "备注")
    private String remark;

    //截止日期
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deadlineDate;

}
