package cn.getech.ehm.task.schedule;

import cn.getech.ehm.base.client.BaseServiceClient;
import cn.getech.ehm.base.dto.DictionaryItemDto;
import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.common.context.UserContextHolder;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.task.config.RabbitMQConfig;
import cn.getech.ehm.task.dto.plan.SynMaintPlanDto;
import cn.getech.ehm.task.dto.plan.TriggerTimeDto;
import cn.getech.ehm.task.dto.task.info.MaintTaskPlanAddDto;
import cn.getech.ehm.task.dto.task.notify.MaintNotifyDto;
import cn.getech.ehm.task.enmu.StopHandlingType;
import cn.getech.ehm.task.enums.TaskSourceType;
import cn.getech.ehm.task.service.IMaintNotifyService;
import cn.getech.ehm.task.service.IMaintPlanService;
import cn.getech.ehm.task.service.IMaintTaskService;
import cn.getech.ehm.task.service.ITriggerTimeService;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 维保计划定时任务
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
@Slf4j
@Component
public class MaintPlanSchedule {
    @Autowired
    private IMaintPlanService maintPlanService;
    @Autowired
    private ITriggerTimeService triggerTimeService;
    @Autowired
    private BaseServiceClient baseServiceClient;
    @Autowired
    private IMaintTaskService maintTaskService;
    @Autowired
    private IMaintNotifyService notifyService;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    @Qualifier("schedulerExecutorService")
    ExecutorService schedulerExecutorService;
    @Autowired
    AmqpTemplate amqpTemplate;

    private final static String PLAN_RELEASE_TASK_LOCK = "EHM:TASK:PLAN_RELEASE_LOCK";

    /**
     * 每小时触发一次扫描
     */
    @Scheduled(cron = "0 0/10 * * * ?")
    public void realDeal() {
        RLock lock = redissonClient.getLock(PLAN_RELEASE_TASK_LOCK);
        try {
            lock.lock();
        } catch (Exception e) {
            log.error("未获取到锁:{}", e.getMessage());
            return;
        }
        try {
            this.releaseTask();
        } catch (Exception e) {
            e.printStackTrace();
            log.error("程序执行报错");
        } finally {
            //判断是否有锁对象，以及是否是同一个锁
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();  //解锁
            }
        }
    }


    public void releaseTask() {
        log.info("定时执行释放工单任务开始");

        // 获取需要释放的计划列表
        List<TriggerTimeDto> canCreateTaskDtos = triggerTimeService.canCreateTaskDtos();
        if (CollectionUtils.isNotEmpty(canCreateTaskDtos)) {

            //根据维保计划id获取集合
            Map<String, List<TriggerTimeDto>> tenantTriggerMap = canCreateTaskDtos.stream().collect(Collectors.groupingBy(TriggerTimeDto::getTenantId));
            for (Map.Entry<String, List<TriggerTimeDto>> tenantTrigger : tenantTriggerMap.entrySet()) {
                List<TriggerTimeDto> tenantTriggerTimeDtos = tenantTrigger.getValue();
                //按租户处理 设置租户信息
                UserBaseInfo baseInfo = JSONObject.parseObject("{\"uid\":\"admin\"," +
                        "\"tenantId\":\"" + tenantTrigger.getKey() + "\"}", UserBaseInfo.class);
                UserContextHolder.getContext().setUserBaseInfo(baseInfo);

                Map<String, DictionaryItemDto> jobTypeMap = new HashMap<>();
                RestResponse<Map<String, DictionaryItemDto>> jobTypeRes = baseServiceClient.getItemMapByCode("job_type");
                if (jobTypeRes.isOk()) {
                    jobTypeMap = jobTypeRes.getData();
                } else {
                    log.info("连接base-service获取作业类别字典表失败");
                }

                releaseTenantTask(tenantTriggerTimeDtos, tenantTrigger.getKey(), jobTypeMap);

            }
        }

        log.info("定时执行释放工单任务结束");
    }

    private void releaseTenantTask(List<TriggerTimeDto> tenantTriggerTimeDtos, String tenantId, Map<String, DictionaryItemDto> jobTypeMap) {
        //生成工单成功的计划时间表id(一个维保单多个设备，有一台成功就会更新状态)
        List<String> triggerTimeIds = new ArrayList<>();

        //以上次工单完成日期开单,开单时候如果设备暂停,日期延后一天
        List<TriggerTimeDto> stopUpdateTimeDtos = new ArrayList<>();

        //根据维保计划id获取集合
        Map<String, List<TriggerTimeDto>> planTriggerMap = tenantTriggerTimeDtos.stream().collect(Collectors.groupingBy(TriggerTimeDto::getPlanId));
        List<String> planIds = tenantTriggerTimeDtos.stream().map(TriggerTimeDto::getPlanId).distinct().collect(Collectors.toList());
        Map<String, SynMaintPlanDto> planDtoMap = maintPlanService.getSynListByIds(planIds);
        Map<String, MaintNotifyDto> notifyDtoMap = notifyService.getPlanEnableNotifyBySourceId(planIds);
        log.info("租户:{}定时计划需要释放的计划数：{}, 计划ID列表：{}", tenantId,
                tenantTriggerTimeDtos.size(),
                JSONObject.toJSON(planIds));
        for (Map.Entry<String, List<TriggerTimeDto>> entity : planTriggerMap.entrySet()) {
            String planId = entity.getKey();
            List<TriggerTimeDto> triggerTimeDtos = entity.getValue();

            SynMaintPlanDto synMaintPlanDto = planDtoMap.get(planId);
            if (null == synMaintPlanDto || synMaintPlanDto.getEnabled() == 0) {
                //未查询到维保信息或维保对象，跳过,并且释放掉需要释放的工单
                if (CollectionUtils.isNotEmpty(triggerTimeDtos)) {
                    List<String> updateIdList = triggerTimeDtos.stream().map(TriggerTimeDto::getId).distinct().collect(Collectors.toList());
                    triggerTimeService.changeStatus(updateIdList, 1);
                }
                continue;
            }

            //按租户处理 设置租户信息
            UserBaseInfo baseInfo = JSONObject.parseObject("{\"uid\":\"" + synMaintPlanDto.getCreateBy() + "\"," +
                    "\"tenantId\":\"" + synMaintPlanDto.getTenantId() + "\"}", UserBaseInfo.class);
            UserContextHolder.getContext().setUserBaseInfo(baseInfo);
            DictionaryItemDto dictionaryItemDto = jobTypeMap.get(synMaintPlanDto.getJobType());
            String jobTypeName = "";
            if (null != dictionaryItemDto) {
                jobTypeName = dictionaryItemDto.getName();
            }
            //计划通知设置
            MaintNotifyDto adventNotify = notifyDtoMap.get(synMaintPlanDto.getId());

            if (synMaintPlanDto.getTriggerType() == StaticValue.ONE) {
                //以上次的开始，有设备id，可以直接开单
                List<String> equipmentIds = triggerTimeDtos.stream().map(TriggerTimeDto::getEquipmentId).distinct().collect(Collectors.toList());
                List<MaintTaskPlanAddDto> addDtos = maintPlanService.createLastTaskOrder(synMaintPlanDto, equipmentIds);
                if (CollectionUtils.isNotEmpty(addDtos)) {
                    //以上次的同一设备当前只有一单未释放的
                    Map<String, TriggerTimeDto> triggerTimeMap = triggerTimeDtos.stream().collect(Collectors.toMap(TriggerTimeDto::getEquipmentId, v -> v, (v1, v2) -> v1));
                    Map<String, String> codeMap = new HashMap<>();
                    for (MaintTaskPlanAddDto addDto : addDtos) {
                        TriggerTimeDto triggerTimeDto = triggerTimeMap.get(addDto.getEquipmentId());
                        if (null != triggerTimeDto) {
                            if (synMaintPlanDto.getStopHandlingMethod() == StopHandlingType.NOT_ADD_TASK.getValue() && addDto.getEquipmentStop()) {
                                //设备停机不释放工单, 将日期延后一天
                                TriggerTimeDto updateTimeDto = new TriggerTimeDto();
                                updateTimeDto.setId(triggerTimeDto.getId());
                                updateTimeDto.setTriggerTime(DateUtil.offset(triggerTimeDto.getTriggerTime(), DateField.DAY_OF_MONTH, 1));
                                updateTimeDto.setPlanMaintTime(DateUtil.offset(triggerTimeDto.getPlanMaintTime(), DateField.DAY_OF_MONTH, 1));
                                stopUpdateTimeDtos.add(updateTimeDto);
                                continue;
                            }
                            String sourceTypeStr = TaskSourceType.PLAN.getPrex();
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmm");
                            String dateStr = sdf.format(triggerTimeDto.getTriggerTime());
                            String prex = sourceTypeStr + dateStr;
                            String maxCode = maintTaskService.getMaxCode(prex);
                            Integer num = Integer.valueOf(maxCode);
                            MaintTaskPlanAddDto newDto = CopyDataUtil.copyObject(addDto, MaintTaskPlanAddDto.class);
                            String code = prex + String.format("%04d", num);
                            newDto.setCode(code);
                            String name = addDto.getName() + jobTypeName + (num) + "工单";
                            newDto.setName(name);
                            maintPlanService.buildTeamPerson(triggerTimeDto, newDto);
                            newDto.setAdventNotify(adventNotify);
                            newDto.setSourcePlanEquipmentId(triggerTimeDto.getPlanEquipmentTimeId());
                            try {
                                this.dealInfo(newDto, UserContextHolder.getContext().getUserBaseInfo(), Lists.newArrayList());
                                triggerTimeIds.add(triggerTimeDto.getId());
                                codeMap.put(prex, code);
                            } catch (Exception e) {
                                log.error("生成工单失败,triggerTimeId为{}, equipmentId为{},错误信息为{}", triggerTimeDto.getId(), addDto.getEquipmentId(), JSONObject.toJSON(e));
                            }
                        }
                    }
                }
            } else {
                //获取所有开单数据
                List<MaintTaskPlanAddDto> addDtos = maintPlanService.createTaskOrder(synMaintPlanDto);

                for (TriggerTimeDto triggerTimeDto : triggerTimeDtos) {
                    saveTask(triggerTimeDto, addDtos, triggerTimeIds, jobTypeName, adventNotify, synMaintPlanDto.getStopHandlingMethod());
                }
            }
        }
        if (CollectionUtils.isNotEmpty(triggerTimeIds)) {
            triggerTimeIds = triggerTimeIds.stream().distinct().collect(Collectors.toList());
            triggerTimeService.changeStatus(triggerTimeIds, 1);
        }
        if (CollectionUtils.isNotEmpty(stopUpdateTimeDtos)) {
            triggerTimeService.stopUpdateTime(stopUpdateTimeDtos);
        }
    }

//    private Integer buildCodeNum(String maxCode) {
//        Integer num = 1;
//        if (StringUtils.isNotBlank(maxCode)) {
//            String suffix = maxCode;
//            try {
//                num = Integer.valueOf(suffix) + 1;
//            } catch (Exception e) {
//                log.error("生成编码失败");
//            }
//        }
//        return num;
//    }

    private void saveTask(TriggerTimeDto triggerTimeDto, List<MaintTaskPlanAddDto> addDtos, List<String> triggerTimeIds,
                          String jobTypeName, MaintNotifyDto adventNotify, Integer stopHandlingMethod) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("" + jobTypeName);
        String sourceTypeStr = TaskSourceType.PLAN.getPrex();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmm");
        String dateStr = sdf.format(triggerTimeDto.getTriggerTime());
        List<CompletableFuture<Boolean>> futureList = Lists.newArrayList();
        //同一个维保计划累积多个单需要开，信息公用
        for (MaintTaskPlanAddDto addDto : addDtos) {
            String maxCode = maintTaskService.getMaxCode(sourceTypeStr + dateStr);
            Integer num = Integer.valueOf(maxCode);
            if (stopHandlingMethod == StopHandlingType.NOT_ADD_TASK.getValue() && addDto.getEquipmentStop()) {
                //设备停机不释放工单
                triggerTimeIds.add(triggerTimeDto.getId());
                continue;
            }
            MaintTaskPlanAddDto newDto = CopyDataUtil.copyObject(addDto, MaintTaskPlanAddDto.class);
            String code = sourceTypeStr + dateStr + String.format("%04d", num);
            newDto.setCode(code);
            String name = addDto.getName() + jobTypeName + (num) + "工单";
            newDto.setName(name);
            newDto.setSourcePlanEquipmentId(triggerTimeDto.getPlanEquipmentTimeId());
            maintPlanService.buildTeamPerson(triggerTimeDto, newDto);
            newDto.setAdventNotify(adventNotify);
            try {
                futureList = this.dealInfo(newDto, UserContextHolder.getContext().getUserBaseInfo(), futureList);
                UserContextHolder.defaultContext();
                triggerTimeIds.add(triggerTimeDto.getId());
            } catch (Exception e) {
                log.error("生成工单失败,triggerTimeId为{}, equipmentId为{}", triggerTimeDto.getId(), addDto.getEquipmentId());
                e.printStackTrace();
            }
        }
//        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()])).join();
        stopWatch.stop();
        ;
        log.info("\n本次释放工单：{} 个\n{}", futureList.size(), stopWatch.prettyPrint());
    }

    public List<CompletableFuture<Boolean>> dealInfo(MaintTaskPlanAddDto newDto, UserBaseInfo userBaseInfo, List<CompletableFuture<Boolean>> futureList) {

        CompletableFuture<Boolean> booleanCompletableFuture = CompletableFuture.supplyAsync(() -> {
            newDto.setUserBaseInfo(userBaseInfo);
            //maintTaskService.savePlanTask(newDto, userBaseInfo);
            amqpTemplate.convertAndSend(RabbitMQConfig.DIRECT_EXCHANGE,RabbitMQConfig.DIRECT_ROUTING_KEY, JSON.toJSONString(newDto));
            return true;
        }, schedulerExecutorService).exceptionally(ex -> {
            ex.printStackTrace();
            return false;
        });
        futureList.add(booleanCompletableFuture);
        return futureList;
    }


}
