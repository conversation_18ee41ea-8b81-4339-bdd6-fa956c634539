package cn.getech.ehm.task.entity;

import cn.getech.ehm.common.mp.handler.StringArrayTypeHandler;
import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

import java.util.Date;

/**
 * 维护工单主表
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("maint_task")
public class MaintTask extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 工单编号
     */
    @TableField("code")
    private String code;

    /**
     * 工单名称
     */
    @TableField("name")
    private String name;

    /**
     * 维护设备id
     */
    @TableField("equipment_id")
    private String equipmentId;

    /**
     * 作业标准id
     */
    @TableField("standard_id")
    private String standardId;

    /**
     * 作业标准名称/故障详细
     */
    @TableField("content")
    private String content;

    /**
     * 维保类型/作业类型
     */
    @TableField("job_type")
    private String jobType;

    /**
     * 维保等级(一级保养/二级保养/三级保养)
     */
    @TableField("job_level")
    private Integer jobLevel;

    /**
     * 状态(0待派单1待接单2待执行3开始确认4安全确认中5已确认6执行中7执行中已挂起8待验收9正常关闭10异常关闭)
     */
    @TableField("status")
    private Integer status;

    /**
     * 来源(1故障报修2维保计划)
     */
    @TableField("source_type")
    private Integer sourceType;

    /**
     * 工单类型(1故障单2维保单3缺陷单)
     */
    @TableField("type")
    private Integer type;

    /**
     * 相关联id(报修单、计划单)
     */
    @TableField("source_id")
    private String sourceId;

    /**
     * 相关联名称(报修单、计划单)
     */
    @TableField("source_name")
    private String sourceName;

    /**
     * 计划维护时间
     */
    @TableField("plan_maint_time")
    private Date planMaintTime;

    /**
     * 专业/工单类别
     */
    @TableField("major")
    private String major;

    /**
     * 影响程度
     */
    @TableField("influence")
    private String influence;

    /**
     * 紧急程度
     */
    @TableField("urgency")
    private String urgency;

    /**
     * 是否允许自由扫码接单(0否1是)
     */
    @TableField("free_task")
    private Integer freeTask;

    /**
     * 维护人员id集合(目前先限制20个)
     */
    @TableField(value = "staff_ids", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] staffIds;

    /**
     * 维护班组id集合
     */
    @TableField(value = "team_ids", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] teamIds;

    /**
     * 人员和班组组合人员ids(可做权限)
     */
    @TableField(value = "all_staff_ids")
    private String allStaffIds;

    /**
     * 派单处理人
     */
    @TableField(value = "dispatch_handler", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] dispatchHandler;

    /**
     * 派单审核员
     */
    @TableField(value = "dispatch_reviewer", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] dispatchReviewer;

    /**
     * 接单人/处理人
     */
    @TableField("handler")
    private String handler;

    /**
     * 审核人员
     */
    @TableField(value = "reviewer", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] reviewer;

    /**
     * 已审核人员
     */
    @TableField(value = "already_reviewer", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] alreadyReviewer;

    /**
     * 评价处理人
     * 待评价是集合
     * 已评价是处理人
     */
    @TableField("evaluate_handler")
    private String[] evaluateHandler;

    /**
     * 处理情况
     */
    @TableField("handle_information")
    private String handleInformation;

    /**
     * 结果描述
     */
    @TableField("handle_result")
    private String handleResult;

    /**
     * 评分
     */
    @TableField("grade")
    private Integer grade;

    /**
     * 评语
     */
    @TableField("comment")
    private String comment;

    /**
     * 验收结果0未通过1通过
     */
    @TableField("check_accept_result")
    private Integer checkAcceptResult;

    /**
     * 实际维护开始时间
     */
    @TableField("begin_maint_time")
    private Date beginMaintTime;

    /**
     * 实际维护结束时间
     */
    @TableField("end_maint_time")
    private Date endMaintTime;

    /**
     * 工时(挂起后停止计时，重新打开后继续计时)
     * 分钟
     */
    @TableField("working_time")
    private Long workingTime;

    /**
     * 是否挂起
     */
    @TableField("hang_up")
    private Boolean hangUp;

    /**
     * 启动时间
     * 配合挂起，计算工时
     */
    @TableField("start_time")
    private Date startTime;

    /**
     * 是否安全确认
     */
    @TableField("confirm")
    private Boolean confirm;

    /**
     * 审核意见
     */
    @TableField(value = "review_content")
    private String reviewContent;

    /**
     * 是否显示工况/风险信息
     */
    @TableField(value = "show_information")
    private Boolean showInformation;

    /**
     * 工况/风险信息
     */
    @TableField(value = "ticket_information")
    private String ticketInformation;

    /**
     * 是否显示作业票类型
     */
    @TableField(value = "show_ticket_type")
    private Boolean showTicketType;

    /**
     * 已选择作业票类型
     */
    @TableField(value = "ticket_types")
    private String[] ticketTypes;

    /**
     * 作业票附件id集合
     */
    @TableField(value = "ticket_media_ids", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] ticketMediaIds;

    /**
     * 关闭原因/转缺陷原因
     */
    @TableField("close_reason")
    private String closeReason;

    /**
     * 是否缺陷
     */
    @TableField("defect")
    private Boolean defect;

    /**
     * 生成的缺陷单id
     */
    @TableField("defect_id")
    private String defectId;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 是否删除(0false1true)
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    /**
     * 流程实例ID
     */
    @TableField("process_instance_id")
    private String processInstanceId;

    //派单日期
    private Date sendTaskDate;

    //接单日期
    private Date recTaskDate;

    //派单截止日期
    private Date sendTaskDeadlineDate;

    //接单截止日期
    private Date recTaskDeadlineDate;

    //工单截止日期
    private Date taskDeadlineDate;

    //完工附件
    @TableField(typeHandler = StringArrayTypeHandler.class)
    private String[] finishFileIds;

    //点检异常数量
    private Integer ecErrorNum;

    //点检异常处理方式
    private Integer ecErrorResult;

    //点检异常处理结论
    private String ecErrorDealContent;

    //点检异常工单id
    private String ecErrorTaskId;

    //点检异常工单code
    private String ecErrorTaskCode;

    //点检异常工单名
    private String ecErrorTaskName;

    /**
     * 停机开始时间
     */
    @TableField("begin_downtime")
    private Date beginDowntime;

    /**
     * 停机结束时间
     */
    @TableField("end_downtime")
    private Date endDowntime;

    //工单耗时
    private String maintTimeCost;

    //停机耗时
    private String downTimeCost;

    //区域
    private String areaType;

    //工序
    private String processType;

    //来源维保计划细则id
    private String sourcePlanEquipmentId;

    //业务类型
    private String bussinessType;

    //是否已经安全确认结束
    private Integer secureConfirmed;

    /**
     * app是否缓存
     */
    @TableField("offline_cache")
    private Boolean offlineCache;

    /**
     * 故障影响
     */
    @TableField(value = "fault_influences", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] faultInfluences;

    /**
     * 一级保养设备停机是否关闭工单
     */
    @TableField("equipment_stop_closed")
    private Boolean equipmentStopClosed;

    /**
     * 创建日期周
     */
    @TableField("create_time_week")
    private Integer createTimeWeek;

    /**
     * 当前节点处理人
     */
    @TableField(value = "uids", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] uids;

    /**
     * 超期后处理方式
     */
    @TableField("overdue_handling_method")
    private Integer overdueHandlingMethod;

    /**
     * 是否超期
     */
    @TableField("is_over_time")
    private Boolean isOverTime;

    private Integer errorFlag;

    private Integer readyDelete;

//    private Integer lotoFlag;

    private String hangUpReason;

    private Boolean defectFlag;

    private String sourceTaskId;

    private String sourceTaskCode;

    @TableField("damage_reason")
    private String damageReason;

    /**
     * 是否挂起
     */
    @TableField("has_hang_up")
    private Boolean hasHangUp;

    @TableField(typeHandler = StringArrayTypeHandler.class)
    @ApiModelProperty(value = "验收维护人员集合")
    private String[] acceptMaintainerIds;

    @TableField(typeHandler = StringArrayTypeHandler.class)
    @ApiModelProperty(value = "验收维护班组集合")
    private String[] acceptTeamIds;

    private String returnReason;

    private String reopenReason;

    private Integer reopenCount;
}
