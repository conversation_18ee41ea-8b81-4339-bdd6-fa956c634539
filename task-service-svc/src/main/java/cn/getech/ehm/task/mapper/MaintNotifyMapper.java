package cn.getech.ehm.task.mapper;

import cn.getech.ehm.task.dto.task.notify.MaintNotifyDto;
import cn.getech.ehm.task.entity.MaintNotify;
import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 通知 Mapper 接口
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
@Repository
public interface MaintNotifyMapper extends BaseMapper<MaintNotify> {
    /**
     * 关闭巡检任务单通知
     * @param ids
     * @return
     */
    @SqlParser(filter = true)
    Integer closedTaskNotify(@Param("ids") List<String> ids);

    /**
     * 获取维保计划开启的通知信息
     * @param sourceIds
     * @return
     */
    @SqlParser(filter = true)
    List<MaintNotify> getPlanEnableNotifyBySourceId(@Param("sourceIds") List<String> sourceIds);
}
