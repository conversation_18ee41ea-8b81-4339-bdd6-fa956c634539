package cn.getech.ehm.task.dto;

import cn.getech.poros.framework.common.bean.BaseEntity;
import cn.getech.ehm.task.entity.MaintTaskDefect;
import org.mapstruct.Mapper;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <pre>
 * 缺陷记录 参数实体映射
 * </pre>
 *
 * <AUTHOR>
 * @date 2025-03-26
 */
@Mapper(componentModel = "spring",imports = {BaseEntity.class})
public interface  MaintTaskDefectParamMapper{

    /**
     * 新增参数转换为实体
     *
     * @param maintTaskDefectAddParam
     * @return
     */
        MaintTaskDefect addParam2Entity(MaintTaskDefectAddParam maintTaskDefectAddParam);

    /**
     * 编辑参数转换为实体
     * @param maintTaskDefectEditParam
     * @return
     */
        MaintTaskDefect editParam2Entity(MaintTaskDefectEditParam maintTaskDefectEditParam);

    /**
     * 实体转换为Dto
     * @param maintTaskDefect
     * @return
     */
        MaintTaskDefectDto entity2Dto(MaintTaskDefect maintTaskDefect);

    /**
     * 分页实体转DTO
     * @param page
     * @return
     */
    PageResult<MaintTaskDefectDto> pageEntity2Dto(PageResult<MaintTaskDefect> page);


    /**
     * dto集合转entity集合
     * @param rows
     * @return
     */
    List<MaintTaskDefect> dtoList2Entity(List<MaintTaskDefectDto> rows);

}
