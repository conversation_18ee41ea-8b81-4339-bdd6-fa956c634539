package cn.getech.ehm.task.entity;

import cn.getech.ehm.common.mp.handler.IntegerArrayTypeHandler;
import cn.getech.ehm.common.mp.handler.StringArrayTypeHandler;
import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

import java.util.Date;

/**
 * 工单屏蔽
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("maint_task_shield")
public class MaintTaskShield extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 是否开启
     */
    @TableField("enabled")
    private Boolean enabled;

    /**
     * 按时间范围是否开启
     */
    @TableField("time_enabled")
    private Boolean timeEnabled;

    /**
     * 开始时间
     */
    @TableField(value = "begin_time", updateStrategy = FieldStrategy.IGNORED)
    private Date beginTime;

    /**
     * 结束时间
     */
    @TableField(value = "end_time", updateStrategy = FieldStrategy.IGNORED)
    private Date endTime;

    /**
     * 按工作周选择过滤
     */
    @TableField(value = "week_dates", jdbcType = JdbcType.VARCHAR, typeHandler = IntegerArrayTypeHandler.class)
    private Integer[] weekDates;

    /**
     * 按工单范围是否开启
     */
    @TableField("task_enabled")
    private Boolean taskEnabled;

    /**
     * 工单状态
     */
    @TableField(value = "task_status", jdbcType = JdbcType.VARCHAR, typeHandler = IntegerArrayTypeHandler.class)
    private Integer[] taskStatus;

    /**
     * 按故障影响是否开启
     */
    @TableField("fault_enabled")
    private Boolean faultEnabled;

    /**
     * 故障影响
     */
    @TableField(value = "fault_influences", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] faultInfluences;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;
}
