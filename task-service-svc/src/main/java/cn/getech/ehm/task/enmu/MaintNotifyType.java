package cn.getech.ehm.task.enmu;


import cn.getech.ehm.common.dto.EnumListDto;
import java.util.ArrayList;
import java.util.List;

/**
 * 通知枚举
 * <AUTHOR>
 * @date 2020/3/3
 */
public enum MaintNotifyType {
    ADVENT(1, "临期通知", "将临期"),
    OVERDUE(2, "超期", "已超期"),
    REPAIR_RECEIVE(3, "接单通知", "接单通知");


    MaintNotifyType(int value, String name, String notifyTitle) {
        this.value = value;
        this.name = name;
        this.notifyTitle = notifyTitle;
    }

    private int value;

    private String name;

    //通知标题
    private String notifyTitle;

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNotifyTitle() { return notifyTitle; }

    public void setNotifyTitle(String notifyTitle) { this.notifyTitle = notifyTitle; }

    public static List<EnumListDto> getList(){
        List<EnumListDto> enumListDtos = new ArrayList<>();
        for(MaintNotifyType maintNotifyType : MaintNotifyType.values()){
            EnumListDto enumListDto = new EnumListDto();
            enumListDto.setValue(maintNotifyType.value);
            enumListDto.setName(maintNotifyType.name);
            enumListDtos.add(enumListDto);
        }
        return enumListDtos;
    }

    public static String getNameByValue(Integer value) {
        if(null != value) {
            for (MaintNotifyType notifyType : MaintNotifyType.values()) {
                if (notifyType.getValue() == value) {
                    return notifyType.getName();
                }
            }
        }
        return null;
    }

    public static MaintNotifyType getEnumByValue(Integer value) {
        if(null != value) {
            for (MaintNotifyType notifyType : MaintNotifyType.values()) {
                if (notifyType.getValue() == value) {
                    return notifyType;
                }
            }
        }
        return null;
    }
}

