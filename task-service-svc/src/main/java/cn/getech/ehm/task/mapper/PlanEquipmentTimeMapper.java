package cn.getech.ehm.task.mapper;

import cn.getech.ehm.task.dto.plan.PlanEquipmentDto;
import cn.getech.ehm.task.dto.plan.SynPlanEquipmentDto;
import cn.getech.ehm.task.dto.plan.SynPlanEquipmentTimeDto;
import cn.getech.ehm.task.entity.PlanEquipment;
import cn.getech.ehm.task.entity.PlanEquipmentTime;
import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 维保对象 Mapper 接口
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
@Repository
public interface PlanEquipmentTimeMapper extends BaseMapper<PlanEquipmentTime> {

    /**
     * 根据计划ids获取维保对象集合
     * @param planIds
     * @return
     */
    @SqlParser(filter = true)
    List<SynPlanEquipmentTimeDto> getListByPlanIds(@Param("planIds") List<String> planIds);

}
