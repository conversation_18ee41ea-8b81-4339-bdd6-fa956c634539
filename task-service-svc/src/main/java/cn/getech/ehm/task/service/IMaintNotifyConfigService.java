package cn.getech.ehm.task.service;

import cn.getech.ehm.task.entity.MaintNotifyConfig;
import cn.getech.poros.framework.common.service.IBaseService;
import cn.getech.ehm.task.dto.MaintNotifyConfigQueryParam;
import cn.getech.ehm.task.dto.MaintNotifyConfigAddParam;
import cn.getech.ehm.task.dto.MaintNotifyConfigEditParam;
import cn.getech.ehm.task.dto.MaintNotifyConfigDto;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <pre>
 *  服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2025-02-10
 */
public interface IMaintNotifyConfigService extends IBaseService<MaintNotifyConfig> {

        /**
         * 分页查询，返回Dto
         *
         * @param maintNotifyConfigQueryParam
         * @return
         */
        PageResult<MaintNotifyConfigDto> pageDto(MaintNotifyConfigQueryParam maintNotifyConfigQueryParam);

        /**
         * 保存
         * @param maintNotifyConfigAddParam
         * @return
         */
        boolean saveByParam(MaintNotifyConfigAddParam maintNotifyConfigAddParam);

        /**
         * 根据id查询，转dto
         * @param id
         * @return
         */
        MaintNotifyConfigDto getDtoById(Long id);

        /**
         * 批量保存
         * @param rows
         */
        boolean saveDtoBatch(List<MaintNotifyConfigDto> rows);

        /**
         * 更新
         * @param maintNotifyConfigEditParam
         */
        boolean updateByParam(MaintNotifyConfigEditParam maintNotifyConfigEditParam);
}