package cn.getech.ehm.task.service;

import cn.getech.ehm.task.dto.ManualRepairAddDto;
import cn.getech.ehm.task.dto.repair.*;
import cn.getech.ehm.task.dto.screen.ManualRepairGroupCustomerDto;
import cn.getech.ehm.task.dto.screen.TimeQueryParam;
import cn.getech.ehm.task.entity.ManualRepair;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.IBaseService;
import java.util.List;

/**
 * 人工报修 服务类
 *
 * <AUTHOR>
 * @since 2020-08-06
 */
public interface IManualRepairService extends IBaseService<ManualRepair> {

        /**
         * 分页查询人工报修，返回Dto
         *
         * @param manualRepairQueryParam
         * @return
         */
        PageResult<ManualRepairListDto> manualPageDto(ManualRepairQueryParam manualRepairQueryParam);

        /**
         * 查询导出数据
         *
         * @param queryParam
         * @return
         */
        List<ManualRepairExcelDto> manualExcelDto(ManualRepairQueryParam queryParam);

        /**
         * 维保计划分页查询人工报修，返回Dto
         *
         * @param queryParam
         * @return
         */
        PageResult<ManualRepairDetailDto> detailList(DetailQueryParam queryParam);

        /**
         * 分页查询app人工报修，返回Dto
         *
         * @param repairAppQueryParam
         * @return
         */
        PageResult<ManualRepairAppDto> appManualPageDto(RepairAppQueryParam repairAppQueryParam);

        /**
         * 保存人工报修
         * @param manualRepairAddParam
         * @return
         */
        Boolean saveManualRepair(ManualRepairAddParam manualRepairAddParam);

        /**
         * 修改人工报修
         * @param manualRepairEditParam
         * @return
         */
        Boolean editManualRepair(ManualRepairEditParam manualRepairEditParam);

        /**
         * 保存人工报修
         * @param addDto
         * @return
         */
        Boolean saveWarnRepair(ManualRepairAddDto addDto);

        /**
         * 根据id查询人工报修，转dto
         * @param id
         * @return
         */
        ManualRepairDto getManualDtoById(String id);

        /**
         * 工单
         * @param manualRepairEditParam
         * @return
         */
        Boolean updateStatus(ManualRepairEditParam manualRepairEditParam);

        /**
         * 查询大屏诊断排名前五的数据
         * @param timeQueryParam
         * @return
         */
        ManualRepairGroupCustomerDto getManualRepairGroupCustomerDto(TimeQueryParam timeQueryParam);

        /**
         * 获取故障单关联的设备ids
         * @return
         */
        List<String> getUsedInfoIds();

        /**
         * 校验当天设备参数是否有生成故障单
         * @param infoParamId
         * @return
         */
        Boolean checkHaveRepair(String infoParamId);

        /**
         * scada保存人工报修
         */
        Boolean scadaSaveWarnRepair(List<ManualRepairAddDto> addDtos);

}