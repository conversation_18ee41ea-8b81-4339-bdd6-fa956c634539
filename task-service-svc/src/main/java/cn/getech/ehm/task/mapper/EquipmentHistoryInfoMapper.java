package cn.getech.ehm.task.mapper;

import cn.getech.ehm.task.dto.historyInfo.EquipmentHistoryInfoDto;
import cn.getech.ehm.task.dto.historyInfo.EquipmentHistoryInfoQueryParam;
import cn.getech.ehm.task.entity.EquipmentHistoryInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-06
 */
public interface EquipmentHistoryInfoMapper extends BaseMapper<EquipmentHistoryInfo> {

    IPage<EquipmentHistoryInfoDto> pageListByTaskId(Page<EquipmentHistoryInfoDto> page, @Param("param") EquipmentHistoryInfoQueryParam equipmentHistoryInfoQueryParam);

    IPage<EquipmentHistoryInfoDto> pageList(Page<EquipmentHistoryInfoDto> page, @Param("param") EquipmentHistoryInfoQueryParam equipmentHistoryInfoQueryParam);
}
