package cn.getech.ehm.task.dto;

import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

/**
 * <pre>
 * 工单通知配置 分页查询参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-11-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "TaskNotifyConfig查询", description = "工单通知配置查询参数")
public class TaskNotifyConfigQueryParam extends PageParam {

    @ApiModelProperty(value = "")
    private String id;

}
