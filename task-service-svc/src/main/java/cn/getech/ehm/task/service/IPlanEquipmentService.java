package cn.getech.ehm.task.service;

import cn.getech.ehm.task.dto.plan.PlanEquipmentDto;
import cn.getech.ehm.task.dto.plan.PlanEquipmentEditDto;
import cn.getech.ehm.task.dto.plan.SynPlanEquipmentDto;
import cn.getech.ehm.task.entity.PlanEquipment;
import cn.getech.poros.framework.common.service.IBaseService;
import java.util.List;
import java.util.Map;

/**
 * 计划任务 服务类
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
public interface IPlanEquipmentService extends IBaseService<PlanEquipment> {

        /**
         * 批量修改
         * @param rows
         */
        Boolean saveOrUpdateDto(List<PlanEquipmentEditDto> rows, String planId, Boolean add);

        /**
         * 单个保存
         * @param editDto
         * @return
         */
        Boolean saveByParam(PlanEquipmentEditDto editDto);

        /**
         * 根据计划id获取维保对象集合
         * @param planId
         * @return
         */
        List<PlanEquipmentDto> getListByPlanId(String planId);

        /**
         * 获取使用中的设备id集合
         * @param equipmentIds
         * @return
         */
        List<String> getUsedInfoIds(String[] equipmentIds);

        /**
         * 构造计划发单日期
         * @param planId
         * @return
         */
        Boolean createTriggerTime(String planId);

        /**
         * 构造下次计划发单日期
         * @param planId
         * @return
         */
        Boolean createNextTriggerTime(String planId, String equipmentId);

        /**
         * 获取计划单对应维保对象
         * @param planIds
         * @return
         */
        Map<String, SynPlanEquipmentDto> getMapByPlanIds(List<String> planIds);

        /**
         * 获取计划单对应作业标准ids
         * @param planIds
         * @return
         */
        List<String> getStandardIds(String[] planIds);

        /**
         * 获取cbm计划单id
         * @param locationIds
         * @param categoryId
         * @param equipmentId
         * @return
         */
        List<String> getCbmPlanIds(String[] locationIds, String categoryId, String equipmentId, String categoryParamId);
}