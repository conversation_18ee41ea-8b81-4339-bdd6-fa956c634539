package cn.getech.ehm.task.dto.activiti;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Map;

@Data
@ApiModel(value = "PartTaskAuditParam", description = "审核参数")
public class PartTaskAuditParam extends ApiParam {

    @ApiModelProperty(value = "当前操作任务id", required = true)
    protected String activityId;

    @ApiModelProperty(value = "审批结果 1 同意 0 驳回", required = true)
    @NotNull(message = "审批结果不能为空")
    private Integer result;

    @ApiModelProperty(value = "流程实例ID", required = true)
    @NotNull(message = "流程实例ID不能为空")
    private String processInstanceId;

    @ApiModelProperty("处理意见")
    protected String comment;

    @ApiModelProperty("全局变量")
    protected Map<String, Object> variables;

    @ApiModelProperty("业务主键id")
    private String serviceId;

}
