package cn.getech.ehm.task.controller;


import io.swagger.annotations.*;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import org.springframework.beans.factory.annotation.Autowired;
import cn.getech.poros.framework.common.annotation.Permission;
import cn.getech.poros.framework.common.api.PageResult;
import org.apache.commons.collections4.CollectionUtils;
import cn.getech.ehm.task.dto.MaintNotifyConfigQueryParam;
import cn.getech.ehm.task.dto.MaintNotifyConfigAddParam;
import cn.getech.ehm.task.dto.MaintNotifyConfigEditParam;
import cn.getech.ehm.task.dto.MaintNotifyConfigDto;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.utils.ExcelUtils;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import org.springframework.web.multipart.MultipartFile;
import javax.validation.constraints.NotEmpty;
import cn.getech.ehm.task.service.IMaintNotifyConfigService;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 工单通知配置控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-10
 */
@RestController
@RequestMapping("/maintNotifyConfig")
@Api(tags = "工单通知配置服务接口")
public class MaintNotifyConfigController {

    @Autowired
    private IMaintNotifyConfigService maintNotifyConfigService;

    /**
     * 分页获取工单通知配置列表
     */
    @ApiOperation("分页获取工单通知配置列表")
    @GetMapping("/list")
    //@Permission("maint:notify:config:list")
    public RestResponse<PageResult<MaintNotifyConfigDto>> pageList(@Valid MaintNotifyConfigQueryParam maintNotifyConfigQueryParam){
        return RestResponse.ok(maintNotifyConfigService.pageDto(maintNotifyConfigQueryParam));
    }

    /**
     * 新增工单通知配置
     */
    @ApiOperation("新增工单通知配置")
    @AuditLog(title = "工单通知配置",desc = "新增工单通知配置",businessType = BusinessType.INSERT)
    @PostMapping
    //@Permission("maint:notify:config:update")
    public RestResponse<Boolean> add(@RequestBody @Valid MaintNotifyConfigAddParam maintNotifyConfigAddParam) {
        return RestResponse.ok(maintNotifyConfigService.saveByParam(maintNotifyConfigAddParam));
    }

    /**
     * 修改工单通知配置
     */
    @ApiOperation(value="修改工单通知配置")
    @AuditLog(title = "工单通知配置",desc = "修改工单通知配置",businessType = BusinessType.UPDATE)
    @PutMapping
    //@Permission("maint:notify:config:update")
    public RestResponse<Boolean> update(@RequestBody @Valid MaintNotifyConfigEditParam maintNotifyConfigEditParam) {
        return RestResponse.ok(maintNotifyConfigService.updateByParam(maintNotifyConfigEditParam));
    }

    /**
     * 根据id删除工单通知配置
     */
    @ApiOperation(value="根据id删除工单通知配置")
    @AuditLog(title = "工单通知配置",desc = "工单通知配置",businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    //@Permission("maint:notify:config:delete")
    public RestResponse<Boolean> delete(@PathVariable("ids") @NotEmpty Long[] ids) {
        return RestResponse.ok(maintNotifyConfigService.removeByIds(ids));
    }

    /**
     * 根据id获取工单通知配置
     */
    @ApiOperation(value = "根据id获取工单通知配置")
    @GetMapping(value = "/{id}")
    //@Permission("maint:notify:config:list")
    public RestResponse<MaintNotifyConfigDto> get(@PathVariable  Long id) {
        return RestResponse.ok(maintNotifyConfigService.getDtoById(id));
    }

    /**
     * 导出工单通知配置列表
     */
    @ApiOperation(value = "导出工单通知配置列表")
    @AuditLog(title = "工单通知配置",desc = "导出工单通知配置列表",businessType = BusinessType.EXPORT)
    @GetMapping("/export")
   // @Permission("maint:notify:config:export")
    public void excelExport(@Valid MaintNotifyConfigQueryParam maintNotifyConfigQueryParam, HttpServletResponse response){
        PageResult<MaintNotifyConfigDto> pageResult  = maintNotifyConfigService.pageDto(maintNotifyConfigQueryParam);
        ExcelUtils<MaintNotifyConfigDto> util = new ExcelUtils<>(MaintNotifyConfigDto.class);

        util.exportExcel(pageResult.getRecords(), "工单通知配置",response);
    }

    /**
     * Excel导入工单通知配置
     */
    @ApiOperation(value = "Excel导入工单通知配置")
    @AuditLog(title = "工单通知配置",desc = "Excel导入工单通知配置",businessType = BusinessType.INSERT)
    @PostMapping("/import")
    //@Permission("maint:notify:config:import")
    public RestResponse<Boolean> excelImport(@RequestParam("file") MultipartFile file){
        ExcelUtils<MaintNotifyConfigDto> util = new ExcelUtils<>(MaintNotifyConfigDto.class);
        List<MaintNotifyConfigDto> rows = util.importExcel(file);
        if (CollectionUtils.isEmpty(rows)){
            return RestResponse.failed();
        }
        return RestResponse.ok(maintNotifyConfigService.saveDtoBatch(rows));
    }

}
