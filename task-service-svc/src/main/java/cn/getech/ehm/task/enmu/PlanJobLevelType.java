package cn.getech.ehm.task.enmu;


import cn.getech.ehm.common.dto.EnumListDto;

import java.util.ArrayList;
import java.util.List;

/**
 * 维保计划等级
 */
public enum PlanJobLevelType {
    ONE(1, "一级保养"),
    TWO(2, "二级保养"),
    THREE(3, "三级保养"),
    FOUR(4, "巡检");


    PlanJobLevelType(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public static List<EnumListDto> getList(){
        List<EnumListDto> enumListDtos = new ArrayList<>();
        for(PlanJobLevelType frequencyType : PlanJobLevelType.values()){
            EnumListDto enumListDto = new EnumListDto();
            enumListDto.setValue(frequencyType.value);
            enumListDto.setName(frequencyType.name);
            enumListDtos.add(enumListDto);
        }
        return enumListDtos;
    }

    public static String getNameByValue(int value){
        switch (value){
            case 1 : return PlanJobLevelType.ONE.name;
            case 2 : return PlanJobLevelType.TWO.name;
            case 3 : return PlanJobLevelType.THREE.name;
            case 4 : return PlanJobLevelType.FOUR.name;
        }
        return null;
    }

    private int value;

    private String name;

    public int getValue() { return value; }

    public void setValue(int value) { this.value = value; }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
