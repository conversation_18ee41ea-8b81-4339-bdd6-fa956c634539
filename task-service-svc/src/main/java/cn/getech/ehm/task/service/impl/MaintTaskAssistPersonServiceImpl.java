package cn.getech.ehm.task.service.impl;

import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.task.dto.task.assist.MaintTaskAssisEditParam;
import cn.getech.ehm.task.dto.task.assist.MaintTaskAssistAddParam;
import cn.getech.ehm.task.dto.task.assist.MaintTaskAssistListDto;
import cn.getech.ehm.task.dto.task.info.MaintTaskDto;
import cn.getech.ehm.task.entity.MaintTaskAssistPerson;
import cn.getech.ehm.task.enums.TaskStatusType;
import cn.getech.ehm.task.handler.CommonGetHandler;
import cn.getech.ehm.task.mapper.MaintTaskAssistPersonMapper;
import cn.getech.ehm.task.service.IMaintTaskAssistPersonService;
import cn.getech.ehm.task.service.IMaintTaskService;
import cn.getech.ehm.task.util.DateUtils;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.ResultCode;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 工单辅助人员
 *
 * <AUTHOR>
 * @since 2022-06-08
 */
@Slf4j
@Service
public class MaintTaskAssistPersonServiceImpl extends BaseServiceImpl<MaintTaskAssistPersonMapper, MaintTaskAssistPerson> implements IMaintTaskAssistPersonService {

    @Autowired
    private MaintTaskAssistPersonMapper assistPersonMapper;

    @Autowired
    private IMaintTaskService maintTaskService;
    @Autowired
    CommonGetHandler commonGetHandler;

    @Override
    public Boolean addTaskAssistPerson(MaintTaskAssistAddParam addParam) {
        MaintTaskDto maintTaskDto = checkAddParam(addParam);
        MaintTaskAssistPerson assistPerson = CopyDataUtil.copyObject(addParam, MaintTaskAssistPerson.class);
        switch (assistPerson.getTimeMode()) {
            case 1:
                if (maintTaskDto.getStatus() != null && maintTaskDto.getStatus() >= TaskStatusType.PROCESSING.getValue()
                    && maintTaskDto.getStatus() < TaskStatusType.CLOSED.getValue()) {
                    assistPerson.setStartTime(maintTaskDto.getStartTime());
                } else {
                    assistPerson.setStartTime(null);
                }
                assistPerson.setEndTime(null);
                break;
            case 2:
                if (assistPerson.getStartTime() != null && assistPerson.getEndTime() != null) {
                    double workHours = DateUtils.getDateMistake(assistPerson.getStartTime(), assistPerson.getEndTime());
                    assistPerson.setWorkHours(BigDecimal.valueOf(workHours));
                }
                break;
            case 3:
                assistPerson.setWorkHours(new BigDecimal(0));
                break;
        }
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, assistPerson);
        return this.save(assistPerson);
    }

    @Override
    public Boolean updateTaskAssistPerson(MaintTaskAssisEditParam editParam) {
        if (StringUtils.isBlank(editParam.getId())) {
            throw new GlobalServiceException(GlobalResultMessage.of("辅助人员id不能为空"));
        }
        MaintTaskAssistPerson assistPerson = (MaintTaskAssistPerson) this.getById(editParam.getId());
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, assistPerson);
        assistPerson = CopyDataUtil.copyObject(editParam, MaintTaskAssistPerson.class);
        buildAssisPersonByTimeMode(editParam, assistPerson);
        return assistPersonMapper.updateById(assistPerson) > 0;
    }

    private void buildAssisPersonByTimeMode(MaintTaskAssisEditParam editParam, MaintTaskAssistPerson assistPerson) {
        if (editParam.getTimeMode() != null) {
            switch (assistPerson.getTimeMode()) {
                case 1:
                    MaintTaskDto maintTaskDto = maintTaskService.getDtoById(editParam.getTaskId());
                    if (maintTaskDto != null && maintTaskDto.getStatus() != null
                        && maintTaskDto.getStatus() >= TaskStatusType.PROCESSING.getValue()
                        && maintTaskDto.getStatus() < TaskStatusType.CLOSED.getValue()) {
                        assistPerson.setStartTime(maintTaskDto.getStartTime());
                    } else {
                        assistPerson.setStartTime(null);
                    }
                    assistPerson.setEndTime(null);
                    assistPerson.setWorkHours(null);
                    break;
                case 2:
                    if (assistPerson.getStartTime() != null && assistPerson.getEndTime() != null) {
                        double workHours = DateUtils.getDateMistake(assistPerson.getStartTime(), assistPerson.getEndTime());
                        assistPerson.setWorkHours(BigDecimal.valueOf(workHours));
                    }
                    break;
                case 3:
                    assistPerson.setStartTime(null);
                    assistPerson.setEndTime(null);
                    assistPerson.setWorkHours(new BigDecimal(0));
                    break;
            }
        }
    }

    @Override
    public Boolean deleteTaskAssistPerson(String id) {
        return assistPersonMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean deleteList(List<String> ids) {
        return assistPersonMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public List<MaintTaskAssistListDto> getAssistPersonList(String taskId) {
        if (StringUtils.isBlank(taskId)) {
            return new ArrayList<>();
        }
        List<MaintTaskAssistListDto> result = new ArrayList<>();
        LambdaQueryWrapper<MaintTaskAssistPerson> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MaintTaskAssistPerson::getTaskId, taskId);
        List<MaintTaskAssistPerson> assistPersonList = assistPersonMapper.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(assistPersonList)) {
            result = CopyDataUtil.copyList(assistPersonList, MaintTaskAssistListDto.class);
        }
        return result;
    }

    @Override
    public List<MaintTaskAssistListDto> getAssistNameList(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        List<MaintTaskAssistListDto> result = new ArrayList<>();
        LambdaQueryWrapper<MaintTaskAssistPerson> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MaintTaskAssistPerson::getTaskId, ids);
        List<MaintTaskAssistPerson> assistPersonList = assistPersonMapper.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(assistPersonList)) {
            result = CopyDataUtil.copyList(assistPersonList, MaintTaskAssistListDto.class);
        }
        return result;
    }

    @Override
    public List<MaintTaskAssistPerson> getAssistPersonListByTaskId(String taskId, Integer timeMode) {
        LambdaQueryWrapper<MaintTaskAssistPerson> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(taskId), MaintTaskAssistPerson::getTaskId, taskId);
        wrapper.eq(timeMode != null, MaintTaskAssistPerson::getTimeMode, timeMode);
        return assistPersonMapper.selectList(wrapper);
    }

    @Override
    public Boolean saveOrUpdateAssistPersons(List<MaintTaskAssisEditParam> editParamList) {
        List<MaintTaskAssistPerson> assistPersonList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(editParamList)) {
            editParamList.forEach(ed -> {
                if (StringUtils.isNotBlank(ed.getId())) {
                    MaintTaskAssistPerson assistPerson = (MaintTaskAssistPerson) this.getById(ed.getId());
                    if (null == assistPerson) {
                        return;
                    }
                    assistPerson = CopyDataUtil.copyObject(ed, MaintTaskAssistPerson.class);
                    buildAssisPersonByTimeMode(ed, assistPerson);
                    assistPersonList.add(assistPerson);
                } else {
                    MaintTaskAssistPerson assistPerson = CopyDataUtil.copyObject(ed, MaintTaskAssistPerson.class);
                    Assert.notNull(ResultCode.PARAM_VALID_ERROR, assistPerson);
                    buildAssisPersonByTimeMode(ed, assistPerson);
                    assistPersonList.add(assistPerson);
                }
            });
        }
        boolean result = false;
        if (CollectionUtils.isNotEmpty(assistPersonList)) {
            result = saveOrUpdateBatch(assistPersonList);
        }
        return result;
    }

    private MaintTaskDto checkAddParam(MaintTaskAssistAddParam addParam) {
        if (StringUtils.isBlank(addParam.getTaskId())) {
            throw new GlobalServiceException(GlobalResultMessage.of("工单id不能为空"));
        }
        MaintTaskDto maintTaskDto = maintTaskService.getDtoById(addParam.getTaskId());
        if (maintTaskDto == null) {
            throw new GlobalServiceException(GlobalResultMessage.of("工单不能为空"));
        }
        if (StringUtils.isBlank(addParam.getUserName())) {
            throw new GlobalServiceException(GlobalResultMessage.of("辅助人员姓名不能为空"));
        }
        if (addParam.getTimeMode() == null) {
            throw new GlobalServiceException(GlobalResultMessage.of("工时计算方式不能为空"));
        }
        if (addParam.getTimeMode() == 2 && (addParam.getStartTime() == null || addParam.getEndTime() == null)) {
            throw new GlobalServiceException(GlobalResultMessage.of("人工输入参与时间不能为空"));
        }
        return maintTaskDto;
    }

    @Override
    public Boolean offlineCacheSubmit(List<MaintTaskAssistListDto>  assistPeopleList, String taskId, Date startTime, Date endTime){
        if(CollectionUtils.isNotEmpty(assistPeopleList)) {
            List<String> userNames = assistPeopleList.stream().filter(dto -> StringUtils.isNotBlank(dto.getUserId())).map(MaintTaskAssistListDto::getUserName).collect(Collectors.toList());
            Map<String, String> allUserNameMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(userNames)) {
                allUserNameMap = commonGetHandler.getAllUserNameMap();
            }
            List<MaintTaskAssistPerson> assistPersonList = new ArrayList<>(assistPeopleList.size());
            for (MaintTaskAssistListDto dto : assistPeopleList) {
                MaintTaskAssistPerson assistPerson = CopyDataUtil.copyObject(dto, MaintTaskAssistPerson.class);
                assistPerson.setTaskId(taskId);
                if (assistPerson.getTimeMode() != null && assistPerson.getTimeMode() == 1) {
                    assistPerson.setStartTime(startTime);
                    assistPerson.setEndTime(endTime);
                }else if(assistPerson.getTimeMode() != null && assistPerson.getTimeMode() == 3) {
                    assistPerson.setStartTime(null);
                    assistPerson.setEndTime(null);
                }
                assistPerson.setWorkHours(new BigDecimal(0));
                if (assistPerson.getStartTime() != null && assistPerson.getEndTime() != null) {
                    double workHours = DateUtils.getDateMistake(assistPerson.getStartTime(), assistPerson.getEndTime());
                    assistPerson.setWorkHours(BigDecimal.valueOf(workHours));
                }
                if(StringUtils.isNotBlank(dto.getUserName()) && StringUtils.isBlank(dto.getUserId())){
                    assistPerson.setUserId(allUserNameMap.get(dto.getUserName()));
                }
                assistPersonList.add(assistPerson);
            }
            return saveOrUpdateBatch(assistPersonList);
        }
        return true;
    }
}
