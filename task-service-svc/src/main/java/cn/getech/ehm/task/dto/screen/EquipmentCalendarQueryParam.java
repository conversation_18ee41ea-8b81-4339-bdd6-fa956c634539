package cn.getech.ehm.task.dto.screen;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2022-07-08
 */
@Data
@ApiModel("设备视角工作日历查询参数")
public class EquipmentCalendarQueryParam {
    @ApiModelProperty("设备名")
    private String equipmentName;

    @ApiModelProperty("月份")
    @JsonFormat(pattern = "yyyy-MM", timezone = "GMT+8")
    @NotNull
    public Date searchDate;

    @ApiModelProperty(value = "维护设备位置ids")
    private List<String> equipmentLocationIds;
}
