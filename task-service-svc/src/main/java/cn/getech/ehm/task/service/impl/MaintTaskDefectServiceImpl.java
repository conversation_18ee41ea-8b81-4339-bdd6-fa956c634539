package cn.getech.ehm.task.service.impl;

import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.task.dto.defect.DefectInfoAddParam;
import cn.getech.ehm.task.entity.MaintTaskDefect;
import cn.getech.ehm.task.mapper.MaintTaskDefectMapper;
import cn.getech.ehm.task.service.IDefectInfoService;
import cn.getech.ehm.task.service.IMaintTaskDefectService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.ehm.task.dto.MaintTaskDefectQueryParam;
import cn.getech.ehm.task.dto.MaintTaskDefectAddParam;
import cn.getech.ehm.task.dto.MaintTaskDefectEditParam;
import cn.getech.ehm.task.dto.MaintTaskDefectParamMapper;
import cn.getech.ehm.task.dto.MaintTaskDefectDto;
import cn.getech.poros.framework.common.bean.BaseEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.ResultCode;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import java.util.List;
import cn.hutool.core.util.ArrayUtil;



/**
 * <pre>
 * 缺陷记录 服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@Slf4j
@Service
public class MaintTaskDefectServiceImpl extends BaseServiceImpl<MaintTaskDefectMapper, MaintTaskDefect> implements IMaintTaskDefectService {

    @Autowired
    private MaintTaskDefectParamMapper maintTaskDefectParamMapper;
    @Autowired
    private IDefectInfoService defectInfoService;

    @Override
    public PageResult<MaintTaskDefectDto> pageDto(MaintTaskDefectQueryParam maintTaskDefectQueryParam) {
        Wrapper<MaintTaskDefect> wrapper = getPageSearchWrapper(maintTaskDefectQueryParam);
        PageResult<MaintTaskDefectDto> result = maintTaskDefectParamMapper.pageEntity2Dto(page(maintTaskDefectQueryParam, wrapper));

        return Optional.ofNullable(result).orElse(new PageResult<>());
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveByParam(MaintTaskDefectAddParam maintTaskDefectAddParam) {
        MaintTaskDefect maintTaskDefect = maintTaskDefectParamMapper.addParam2Entity(maintTaskDefectAddParam);
        maintTaskDefect.setId(maintTaskDefect.getSourceTaskId());
        maintTaskDefect.setDefectName("");
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,maintTaskDefect);
        return saveOrUpdate(maintTaskDefect);
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean updateByParam(MaintTaskDefectEditParam maintTaskDefectEditParam) {
        MaintTaskDefect maintTaskDefect = maintTaskDefectParamMapper.editParam2Entity(maintTaskDefectEditParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,maintTaskDefect);
        return updateById(maintTaskDefect);
    }


    @Override
    public MaintTaskDefectDto getDtoById(String id) {
        return maintTaskDefectParamMapper.entity2Dto((MaintTaskDefect) this.getById(id));
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveDtoBatch(List<MaintTaskDefectDto> rows) {
        return saveBatch(maintTaskDefectParamMapper.dtoList2Entity(rows));
    }

    private Wrapper<MaintTaskDefect> getPageSearchWrapper(MaintTaskDefectQueryParam maintTaskDefectQueryParam) {
        LambdaQueryWrapper<MaintTaskDefect> wrapper = Wrappers.<MaintTaskDefect>lambdaQuery();
                            wrapper.like(StringUtils.isNotBlank(maintTaskDefectQueryParam.getId()),
                                MaintTaskDefect::getId, maintTaskDefectQueryParam.getId());
        if(BaseEntity.class.isAssignableFrom(MaintTaskDefect.class)){
            wrapper.orderByDesc(MaintTaskDefect::getUpdateTime,MaintTaskDefect::getCreateTime);
        }
        return wrapper;
    }

    public void dealTaskDefect(String taskId){
        MaintTaskDefectDto dtoById = this.getDtoById(taskId);
        DefectInfoAddParam defectInfoAddParam = CopyDataUtil.copyObject(dtoById, DefectInfoAddParam.class);
        String s = defectInfoService.saveByParam(defectInfoAddParam);
    }
}
