package cn.getech.ehm.task.dto.repair;

import cn.getech.ehm.base.dto.FaultKnowledgeResDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import java.util.List;

/**
 * 维保报修列表
 */
@Data
@EqualsAndHashCode()
@ApiModel(value = "ManualRepairDetailDto", description = "维保报修列表")
public class ManualRepairDetailDto {

    @ApiModelProperty(value = "报修单id")
    private String id;

    @ApiModelProperty(value = "设备id")
    private String equipmentId;

    @ApiModelProperty(value = "设备名称")
    private String equipmentName;

    @ApiModelProperty(value = "设备编码")
    private String equipmentCode;

    @ApiModelProperty(value = "专业")
    private String major;

    @ApiModelProperty(value = "紧急程度")
    private String urgency;

    @ApiModelProperty(value = "故障现象ids")
    private String[] faultPhenomenonIds;

    @ApiModelProperty(value = "故障现象Dtos")
    private List<FaultKnowledgeResDto> faultPhenomenonDtos;

    @ApiModelProperty(value = "故障现象扩展")
    private String faultPhenomenonRemark;

    @ApiModelProperty(value = "报修日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "维护设备类型id")
    private String categoryId;

    @ApiModelProperty(value = "维护设备类型名称")
    private String categoryName;

    @ApiModelProperty(value = "维护设备位置id")
    private String locationId;

    @ApiModelProperty(value = "维护设备位置名称")
    private String locationName;

    @ApiModelProperty(value = "报修人/创建人")
    private String createUserName;

    //截止日期
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deadlineDate;

}
