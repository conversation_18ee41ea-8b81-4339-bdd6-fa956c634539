package cn.getech.ehm.task.dto.activiti;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel(value = "AuditActivitiServiceResult", description = "审核流程后业务参数")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuditActivitiServiceResult{
    public String processUser;

    public String[] processUserArray;

    public String taskId;

    public String taskName;
}
