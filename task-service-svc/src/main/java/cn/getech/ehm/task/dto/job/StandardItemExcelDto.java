package cn.getech.ehm.task.dto.job;

import cn.getech.ehm.common.util.excel.FormExcel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;

/**
 * 作业项目
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "StandardItemExcelDto", description = "作业标准dto")
public class StandardItemExcelDto {

    @ApiModelProperty(value = "大类")
    @FormExcel(name="大类",cellType = FormExcel.ColumnType.STRING, required = true)
    private String largeCategory;

    @ApiModelProperty(value = "小类")
    @FormExcel(name="小类",cellType = FormExcel.ColumnType.STRING, required = true)
    private String subCategory;

    @ApiModelProperty(value = "作业类型等级")
    @FormExcel(name="作业类型等级",cellType = FormExcel.ColumnType.STRING, required = true)
    private String jobTypeLevel;

    @ApiModelProperty(value = "作业内容")
    @FormExcel(name="作业内容",cellType = FormExcel.ColumnType.STRING, required = true)
    private String content;

    @ApiModelProperty(value = "标准工时(人*min)")
    @FormExcel(name="标准工时(人*min)",cellType = FormExcel.ColumnType.NUMERIC, required = true)
    private BigDecimal standardTime;

    @ApiModelProperty(value = "作业时间")
    @FormExcel(name="作业时间",cellType = FormExcel.ColumnType.NUMERIC, required = true)
    private BigDecimal workingTime;

    @ApiModelProperty(value = "投入人员")
    @FormExcel(name="投入人员",cellType = FormExcel.ColumnType.NUMERIC, required = true)
    private Integer inputPerson;

    @ApiModelProperty(value = "作业方法")
    @FormExcel(name="作业方法",cellType = FormExcel.ColumnType.STRING, required = true)
    private String method;

    @ApiModelProperty(value = "作业方法详情")
    private String methodDetail;

    @ApiModelProperty(value = "结果类型")
    @FormExcel(name="结果类型",cellType = FormExcel.ColumnType.STRING, required = true)
    private String jobItemResultType;

    @ApiModelProperty(value = "基准目标")
    @FormExcel(name="基准目标",cellType = FormExcel.ColumnType.STRING, required = true)
    private String benchmark;

    @ApiModelProperty(value = "目标值")
    @FormExcel(name="目标值",cellType = FormExcel.ColumnType.NUMERIC)
    private BigDecimal targetValue;

    @ApiModelProperty(value = "区间最大值")
    @FormExcel(name="区间最大值",cellType = FormExcel.ColumnType.NUMERIC)
    private BigDecimal targetMax;

    @ApiModelProperty(value = "区间最小值")
    @FormExcel(name="区间最小值",cellType = FormExcel.ColumnType.NUMERIC)
    private BigDecimal targetMin;

    @ApiModelProperty(value = "单位")
    @FormExcel(name="单位",cellType = FormExcel.ColumnType.STRING)
    private String unit;

    @ApiModelProperty(value = "工具")
    //@FormExcel(name="工具",cellType = FormExcel.ColumnType.STRING)
    private String tool;

    @ApiModelProperty(value = "特殊要求")
    @FormExcel(name="特殊要求",cellType = FormExcel.ColumnType.STRING)
    private String specialRequirements;

}