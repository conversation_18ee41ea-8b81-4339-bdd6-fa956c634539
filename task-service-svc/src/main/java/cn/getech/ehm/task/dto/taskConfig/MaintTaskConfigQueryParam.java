package cn.getech.ehm.task.dto.taskConfig;

import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

/**
 * <pre>
 * 工单配置信息 分页查询参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-04-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MaintTaskConfig查询", description = "工单配置信息查询参数")
public class MaintTaskConfigQueryParam extends PageParam {

    @ApiModelProperty(value = "")
    private String id;

    @ApiModelProperty("工单类型")
    private Integer taskType;

    @ApiModelProperty("配置类型")
    private Integer configType;

    @ApiModelProperty("配置内容")
    private String configContent;

}
