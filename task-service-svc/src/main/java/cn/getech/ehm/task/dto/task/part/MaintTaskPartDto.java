package cn.getech.ehm.task.dto.task.part;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;


/**
 * 维护工单主表 返回数据模型
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "MaintTaskPartDto", description = "工单备件返回数据模型")
public class MaintTaskPartDto {

    @ApiModelProperty(value = "工单id")
    private String taskId;

    @ApiModelProperty(value = "是否有权限修改")
    private Boolean haveAuth;

    @ApiModelProperty(value = "备件集合")
    List<TaskPartDto> parts;
}