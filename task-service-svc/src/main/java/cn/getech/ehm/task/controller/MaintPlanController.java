package cn.getech.ehm.task.controller;

import cn.getech.ehm.common.dto.EnumListDto;
import cn.getech.ehm.task.dto.ParameterCategoryDto;
import cn.getech.ehm.task.enmu.*;
import cn.getech.ehm.task.enums.CbmFrequencyType;
import cn.getech.ehm.task.dto.plan.*;
import cn.getech.ehm.task.schedule.MaintPlanSchedule;
import cn.getech.ehm.task.service.*;
import io.swagger.annotations.*;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import org.springframework.beans.factory.annotation.Autowired;
import cn.getech.poros.framework.common.api.PageResult;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import cn.getech.poros.framework.common.api.RestResponse;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import org.springframework.web.bind.annotation.RestController;

/**
 * 维护计划控制器
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
@RestController
@RequestMapping("/maintPlan")
@Api(tags = "维护计划服务接口")
public class MaintPlanController {

    @Autowired
    private IMaintPlanService maintPlanService;
    @Autowired
    private MaintPlanSchedule maintPlanSchedule;

    /**
     * 分页获取维护计划列表
     */
    @ApiOperation("分页获取维护计划列表")
    @PostMapping("/list")
    //@Permission("maint:plan:list")
    public RestResponse<PageResult<MaintPlanListDto>> pageList(@RequestBody MaintPlanQueryParam maintPlanQueryParam) {
        return RestResponse.ok(maintPlanService.pageDto(maintPlanQueryParam));
    }

    /**
     * 新增维护计划
     */
    @ApiOperation("新增维护计划")
    @AuditLog(title = "维护计划", desc = "新增维护计划", businessType = BusinessType.INSERT)
    @PostMapping
    //@Permission("maint:plan:update")
    public RestResponse<String> add(@RequestBody @Valid MaintPlanAddParam maintPlanAddParam) {
        return RestResponse.ok(maintPlanService.saveByParam(maintPlanAddParam));
    }

    /**
     * 修改维护计划
     */
    @ApiOperation(value = "修改维护计划")
    @AuditLog(title = "维护计划", desc = "修改维护计划", businessType = BusinessType.UPDATE)
    @PutMapping
    //@Permission("maint:plan:update")
    public RestResponse<Boolean> update(@RequestBody @Valid MaintPlanEditParam maintPlanEditParam) {
        return RestResponse.ok(maintPlanService.updateByParam(maintPlanEditParam));
    }

    /**
     * 根据id删除维护计划
     */
    @ApiOperation(value = "根据id删除维护计划")
    @AuditLog(title = "维护计划", desc = "维护计划", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    //@Permission("maint:plan:delete")
    public RestResponse<Boolean> delete(@PathVariable("ids") @NotEmpty String[] ids) {
        return RestResponse.ok(maintPlanService.removeByIds(ids));
    }

    /**
     * 根据id获取维护计划
     */
    @ApiOperation(value = "根据id获取维护计划")
    @GetMapping(value = "/{id}")
    public RestResponse<MaintPlanDto> get(@PathVariable String id) {
        return RestResponse.ok(maintPlanService.getDtoById(id));
    }

    @ApiOperation(value = "维护计划流程审核提交")
    @PostMapping(value = "/submitProcessTask")
    public RestResponse<Boolean> submitProcessTask(@RequestBody PlanAuditParam taskAuditParam) {
        return RestResponse.ok(maintPlanService.submitProcessTask(taskAuditParam));
    }

    /**
     * 释放工单
     */
    @ApiOperation(value = "释放工单")
    @AuditLog(title = "维护计划", desc = "释放工单", businessType = BusinessType.UPDATE)
    @GetMapping(value = "/release/{id}")
    //@Permission("maint:plan:update")
    public RestResponse<Boolean> release(@PathVariable String id) {
        return RestResponse.ok(maintPlanService.release(id));
    }

    /**
     * 根据字段查询数据库中存在的条数
     *
     * @param tableName
     * @param columnName
     * @param value
     * @return
     */
    @GetMapping(value = "/getCount")
    RestResponse<Integer> getCount(@RequestParam("tableName") String tableName,
                                   @RequestParam("columnName") String columnName,
                                   @RequestParam("value") String value) {
        return RestResponse.ok(maintPlanService.getCount(tableName, columnName, value));
    }

    /**
     * 根据流程实例ID获取计划表单
     */
    @ApiOperation(value = "根据流程实例ID获取计划表单")
    @GetMapping("/getProcessInstanceId")
    RestResponse<MaintPlanDto> getMaintPlanDtoByProcessInstanceId(
            @RequestParam("processInstanceId") String processInstanceId) {

        return RestResponse.ok(maintPlanService.getMaintPlanDtoByProcessInstanceId(processInstanceId));
    }

    @ApiOperation("获取CBM触发频率")
    @GetMapping("/getCbmFrequency")
    public RestResponse<List<EnumListDto>> getCbmFrequency() {
        return RestResponse.ok(CbmFrequencyType.getList());
    }

    @ApiOperation("生成cbm单逻辑")
    @PostMapping("/createCbmOrder")
    RestResponse<Boolean> createCbmOrder(@RequestBody ParameterCategoryDto dto){
        return RestResponse.ok(maintPlanService.createCbmOrder(dto));
    }

    /**
     * 更新使用状态
     */
    @ApiOperation(value = "更新使用状态")
    @AuditLog(title = "更新使用状态", desc = "更新使用状态", businessType = BusinessType.UPDATE)
    @PostMapping("/markEnabled")
    public RestResponse<Boolean> markEnabled(@RequestBody PlanEnableEditParam param) {
        return RestResponse.ok(maintPlanService.markEnabled(param));
    }

    @ApiOperation("批量清除该计划下的工单及工作流")
    @GetMapping("/markDeletedBatch")
    public RestResponse markDeletedBatch(String id) {
        maintPlanService.markDeletedBatch(id);
        return RestResponse.ok();
    }

    @ApiOperation("工作台-维保计划预警")
    @GetMapping("/workbench/statistics/maintPlan")
    public RestResponse<String> getStatisticsOfPlanEndCount(@RequestParam("type") String type) {
        //1 1周内临近2今日到期3过期
        return RestResponse.ok(maintPlanService.getStatisticsOfPlanEndCount(type));
    }

    @ApiOperation("获取维保等级")
    @GetMapping("/getPlanJobLevel")
    public RestResponse<List<EnumListDto>> getPlanJobLevel() {
        return RestResponse.ok(PlanJobLevelType.getList());
    }

    @ApiOperation("获取通知方式")
    @GetMapping("/getNotifyMethodType")
    public RestResponse<List<EnumListDto>> getNotifyMethod() {
        return RestResponse.ok(NotifyMethodType.getList());
    }

    @ApiOperation("获取通知对象")
    @GetMapping("/getNotifyObjectType")
    public RestResponse<List<EnumListDto>> getNotifyObject() {
        return RestResponse.ok(NotifyObjectType.getList());
    }

    @ApiOperation("获取超期后处理方式")
    @GetMapping("/getOverdueHandlingType")
    public RestResponse<List<EnumListDto>> getOverdueHandling() {
        return RestResponse.ok(OverdueHandlingType.getList());
    }

    @ApiOperation("获取设备停机后处理方式")
    @GetMapping("/getStopHandingType")
    public RestResponse<List<EnumListDto>> getStopHanding() {
        return RestResponse.ok(StopHandlingType.getList());
    }

    @ApiOperation("获取通知设备管理人员枚举")
    @GetMapping("/getEquipmentManagerType")
    public RestResponse<List<EnumListDto>> getEquipmentManagerType() {
        return RestResponse.ok(NotifyEquipmentManagerType.getList());
    }

    @ApiOperation(value = "触发维保计划定时器生产工单(用作测试)")
    @GetMapping(value = "/releaseTask")
    public RestResponse<Boolean> releaseTask() {
        maintPlanSchedule.releaseTask();
        return RestResponse.ok();
    }
}
