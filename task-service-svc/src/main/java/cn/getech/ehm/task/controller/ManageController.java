package cn.getech.ehm.task.controller;

import cn.getech.ehm.common.context.UserContextHolder;
import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.equipment.client.EquipmentClient;
import cn.getech.ehm.equipment.dto.CategoryInfoDto;
import cn.getech.ehm.task.dto.activiti.ActivitiManageDto;
import cn.getech.ehm.task.entity.JobStandard;
import cn.getech.ehm.task.entity.MaintPlan;
import cn.getech.ehm.task.entity.MaintTask;
import cn.getech.ehm.task.entity.PlanEquipmentTime;
import cn.getech.ehm.task.enums.TaskStatusType;
import cn.getech.ehm.task.schedule.MaintPlanSchedule;
import cn.getech.ehm.task.service.IJobStandardService;
import cn.getech.ehm.task.service.IMaintPlanService;
import cn.getech.ehm.task.service.IMaintTaskService;
import cn.getech.ehm.task.service.IPlanEquipmentTimeService;
import cn.getech.ehm.task.service.impl.ManageServiceImpl;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ArrayUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/manage")
@Slf4j
@Api(tags = "管理接口", value = "管理接口")
public class ManageController {
    @Autowired
    MaintPlanSchedule maintPlanSchedule;
    @Autowired
    ManageServiceImpl manageService;
    @Autowired
    IMaintTaskService maintTaskService;
    @Autowired
    IPlanEquipmentTimeService planEquipmentTimeService;
    @Autowired
    IMaintPlanService maintPlanService;
    @Autowired
    EquipmentClient equipmentClient;
    @Autowired
    IJobStandardService jobStandardService;
    @Autowired
    RedisTemplate redisTemplate;

    @GetMapping("/releaseTask")
    @ApiOperation("立即触发工单释放")
    public RestResponse releaseTask() {
        maintPlanSchedule.realDeal();
        return RestResponse.ok();
    }

    @PostMapping("/delete")
    @ApiOperation("根据规则删除工单")
    public RestResponse deleteTask(@RequestBody ActivitiManageDto request) {
        manageService.deleteTask(request);
        return RestResponse.ok();
    }

    @GetMapping("/set/init/code")
    @ApiOperation("设置起始编码")
    public RestResponse setInitCode(String prex, Long num) {
        manageService.setInitCode(prex, num);
        return RestResponse.ok();
    }

    @GetMapping("/test")
    @ApiOperation("生成编码")
    public void testCode() {
        String test20241226 = maintTaskService.getMaxCode("TEST20241226");
        log.info("code:{}", test20241226);
    }

    @GetMapping("/dealCodeWrong")
    @ApiOperation("处理工单号重复")
    public void dealCodeWrong() {
        Date now = new Date();
        List<MaintTask> list = maintTaskService.list(new QueryWrapper<MaintTask>().lambda()
                .select(MaintTask::getId, MaintTask::getCode)
                .ge(MaintTask::getCreateTime, DateUtil.beginOfDay(now))
                .le(MaintTask::getCreateTime, DateUtil.endOfDay(now)));
        Map<String, List<MaintTask>> collect = list.stream().collect(Collectors.groupingBy(item -> item.getCode()));
        List<MaintTask> updateList = Lists.newArrayList();
        for (String key : collect.keySet()) {
            List<MaintTask> value = collect.get(key);
            int i = 0;
            if (value.size() > 1) {
                log.info("code:{} 重复数量：{}", key, value.size());
                for (MaintTask item : value) {
                    item.setCode(key + "-fix-" + i);
                    updateList.add(item);
                    i++;
                    updateList.add(item);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            log.info("updateSize:{}", updateList.size());
            maintTaskService.updateBatchById(updateList);
        }
    }

    @PostMapping("/mark/delete")
    @ApiOperation("表示错误和待删除")
    public void dealErrorTaskAndDelete(@RequestBody ActivitiManageDto request) {
        UserContextHolder.defaultContext();
        UserBaseInfo userBaseInfo = UserContextHolder.getContext().getUserBaseInfo();
        userBaseInfo.setTenantId("geek");
        userBaseInfo.setUid("admin");
        List<MaintTask> list = maintTaskService.list(new QueryWrapper<MaintTask>().lambda()
                .select(MaintTask::getId, MaintTask::getCode, MaintTask::getEquipmentId, MaintTask::getSourceId, MaintTask::getStatus, MaintTask::getErrorFlag)
                .ge(MaintTask::getCreateTime, request.getBeginDate())
                .lt(MaintTask::getCreateTime, request.getEndDate())
                .orderByAsc(MaintTask::getCreateTime));
        List<String> planId = list.stream().map(item -> item.getSourceId()).collect(Collectors.toList());
        List<MaintPlan> list2 = maintPlanService.list(new QueryWrapper<MaintPlan>().lambda().in(MaintPlan::getId, planId));
        Map<String, MaintPlan> planMap = list2.stream().collect(Collectors.toMap(item -> item.getId(), item -> item, (v1, v2) -> v1));
        List<PlanEquipmentTime> list1 = planEquipmentTimeService.list(new QueryWrapper<PlanEquipmentTime>().lambda().in(PlanEquipmentTime::getPlanId, planId));
        Map<String, List<PlanEquipmentTime>> timeByPlanIdMap = list1.stream().collect(Collectors.groupingBy(item -> item.getPlanId()));
        Map<String, Map<String, List<MaintTask>>> collect = list.stream().collect(Collectors.groupingBy(item -> item.getEquipmentId(), Collectors.groupingBy(item -> item.getSourceId())));
        List<MaintTask> finalList = Lists.newArrayList();
        for (String equipmentId : collect.keySet()) {
            Map<String, List<MaintTask>> value = collect.get(equipmentId);
            for (String sourceId : value.keySet()) {
                List<MaintTask> taskList = value.get(sourceId);
                if (taskList.size() > 1) {
                    log.info("重复工单：设备id:{} 工单类型:{} 重复数量：{}", equipmentId, sourceId, taskList.size());
                    List<PlanEquipmentTime> planEquipmentTimes = timeByPlanIdMap.get(sourceId);
                    MaintPlan maintPlan = planMap.get(sourceId);
                    //实际为小时
                    Integer advanceDay = maintPlan.getAdvanceDay();
                    if (CollectionUtils.isNotEmpty(planEquipmentTimes) && planEquipmentTimes.size() > 1) {
                        log.info("一天多班次，特殊处理");
                        int startIndex = 10; // "1655"开始的位置
                        int endIndex = startIndex + 4; // "1655"结束的位置
                        Map<String, List<MaintTask>> tempByTime = taskList.stream().collect(Collectors.groupingBy(item -> item.getCode().substring(startIndex, endIndex)));
                        List<MaintTask> tempList = Lists.newArrayList();
                        for (PlanEquipmentTime planEquipmentTime : planEquipmentTimes) {
                            Date planTime = planEquipmentTime.getPlanTime();
                            planTime = DateUtil.offset(planTime, DateField.HOUR_OF_DAY, -advanceDay);
                            String format = DateUtil.format(planTime, "HHmm");
                            List<MaintTask> maintTasks = tempByTime.get(format);
                            if (CollectionUtils.isNotEmpty(maintTasks) && maintTasks.size() > 1) {
                                List<MaintTask> maintTasks1 = this.realDeal(maintTasks);
                                tempList.addAll(maintTasks1);
                            }
                        }
                        taskList = tempList;
                    } else {
                        taskList = this.realDeal(taskList);
                    }
                    finalList.addAll(taskList);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(finalList)) {
            log.info("待更新数量：{}", finalList.size());
            maintTaskService.updateBatchById(finalList);
        }
    }

    public List<MaintTask> realDeal(List<MaintTask> taskList) {
        List<MaintTask> collect1 = taskList.stream().filter(item -> item.getStatus() == TaskStatusType.RECEIVING.getValue()).collect(Collectors.toList());
        taskList.stream().forEach(item -> {
            item.setErrorFlag(1);
            item.setReadyDelete(1);
        });
        if (collect1.size() == taskList.size()) {
            log.info("全部未处理，仅保留一条");
            taskList.get(0).setReadyDelete(0);
        } else {
            log.info("有处理记录，保留处理记录得那一条");
            List<MaintTask> collect2 = taskList.stream().filter(item -> finishStatusList.contains(item.getStatus())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect2)) {
                collect2.get(0).setReadyDelete(0);
            } else {
                taskList.get(0).setReadyDelete(0);
            }
        }
        return taskList;
    }

    @PostMapping("/delete/flag")
    @ApiOperation("根据规则删除工单")
    public RestResponse deleteTaskByFlag() {
        manageService.deleteTaskByFalg();
        return RestResponse.ok();
    }

    @PostMapping("/mark/status/error")
    @ApiOperation("根据规则查找工单状态和流程状态不一致的工单")
    public RestResponse markStatusError(@RequestBody ActivitiManageDto request) {
        manageService.markStatusError(request);
        return RestResponse.ok();
    }

    @GetMapping("/mark/maintplan/tag")
    @ApiOperation("给维保计划增加一级设备类型标签")
    public RestResponse maintPlanTag() {
        List<MaintPlan> list = maintPlanService.list();
        List<String> categoryId = list.stream().filter(item -> StringUtils.isNotBlank(item.getInfoCategoryId())).map(item -> item.getInfoCategoryId()).collect(Collectors.toList());
        RestResponse<List<CategoryInfoDto>> allCategory = equipmentClient.getAllCategory();
        if (!allCategory.isOk()) {
            log.error("连接设备服务,获取设备信息失败");
            throw new GlobalServiceException(GlobalResultMessage.of(""));
        }
        List<CategoryInfoDto> data = allCategory.getData();
        Map<String, String> categoryMap = data.stream().collect(Collectors.toMap(item -> item.getId(), item -> item.getLayerCode(), (v1, v2) -> v1));
        List<MaintPlan> toUpdateList = Lists.newArrayList();
        for (MaintPlan plan : list) {
            if (StringUtils.isNotBlank(plan.getInfoCategoryId())) {
                String s = categoryMap.get(plan.getInfoCategoryId());
                if (StringUtils.isNotBlank(s)) {
                    String[] split = s.split("/");
                    plan.setEquipType(split[0]);
                    toUpdateList.add(plan);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(toUpdateList)) {
            maintPlanService.updateBatchById(toUpdateList);
        }
        return RestResponse.ok();
    }


    @GetMapping("/mark/jobstandard/tag")
    @ApiOperation("给作业标准增加一级设备类型标签")
    public RestResponse jobstandardTag() {
        List<JobStandard> list = jobStandardService.list();
        List<String> categoryId = list.stream().filter(item -> ArrayUtils.isNotEmpty(item.getInfoCategoryIds()))
                .map(item -> Arrays.asList(item.getInfoCategoryIds())).flatMap(item -> item.stream()).collect(Collectors.toList());
        RestResponse<List<CategoryInfoDto>> allCategory = equipmentClient.getAllCategory();
        if (!allCategory.isOk()) {
            log.error("连接设备服务,获取设备信息失败");
            throw new GlobalServiceException(GlobalResultMessage.of(""));
        }
        List<CategoryInfoDto> data = allCategory.getData();
        Map<String, String> categoryMap = data.stream().collect(Collectors.toMap(item -> item.getId(), item -> item.getLayerCode(), (v1, v2) -> v1));
        List<JobStandard> toUpdateList = Lists.newArrayList();
        for (JobStandard jobStandard : list) {
            if (ArrayUtils.isNotEmpty(jobStandard.getInfoCategoryIds())) {
                String[] infoCategoryIds = jobStandard.getInfoCategoryIds();
                String s = categoryMap.get(infoCategoryIds[0]);
                if (StringUtils.isNotBlank(s)) {
                    String[] split = s.split("/");
                    jobStandard.setEquipType(split[0]);
                    toUpdateList.add(jobStandard);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(toUpdateList)) {
            jobStandardService.updateBatchById(toUpdateList);
        }
        return RestResponse.ok();
    }

    @GetMapping("/dealRedisKey")
    @ApiOperation("处理redis中的工单编号key")
    public RestResponse dealRedisKey(){
        Set<String> keys = redisTemplate.keys("EHM:CODE:*");
        for (String key : keys){
            redisTemplate.expire(key, 24, TimeUnit.HOURS);
        }
        return RestResponse.ok();
    }

    public static List<Integer> finishStatusList = Arrays.asList(TaskStatusType.CHECK_ACCEPT.getValue(),
            TaskStatusType.CLOSED.getValue());

}
