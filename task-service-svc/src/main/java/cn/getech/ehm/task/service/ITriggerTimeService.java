package cn.getech.ehm.task.service;

import cn.getech.ehm.task.dto.plan.*;
import cn.getech.ehm.task.entity.PlanTriggerTime;
import cn.getech.poros.framework.common.service.IBaseService;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 计划发单日期 服务类
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
public interface ITriggerTimeService extends IBaseService<PlanTriggerTime> {

        /**
         * 保存
         * @return 主键ID
         */
        Boolean saveByParam(List<TriggerTimeDto> triggerTimeDtos);

        /**
         * 根据计划单ids删除所有时间
         * @param planIds
         * @return
         */
        Boolean deleteByPlanIds(List<String> planIds);

        public Boolean deleteByPlanIdsNow(List<String> planIds);

        /**
         * 获取所有可开单日期合集
         * @return
         */
        List<TriggerTimeDto> canCreateTaskDtos();

        /**
         * 更新状态为已开单
         * @param triggerTimeIds
         * @return
         */
        Boolean changeStatus(List<String> triggerTimeIds, Integer status);

        /**
         * 获取下一次工单日期(距离今天最近)
         * @param planIds
         * @return
         */
        Map<String, Date> getNextPlanDateMap(List<String> planIds);

        /**
         * 获取可开单日期
         * @param beginTime
         * @param endTime
         * @return
         */
        List<PlanTriggerTime> getNormalListByTime(Date beginTime, Date endTime);

        /**
         * 以上次工单完成日期开单,开单时候如果设备暂停,日期延后一天
         * @param updateDtos
         * @return
         */
        Boolean stopUpdateTime(List<TriggerTimeDto> updateDtos);
}