package cn.getech.ehm.task.util;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 时间工具类
 *
 * <AUTHOR>
 * @since 2022-06-08
 */
public class DateUtils {
    public static double getDateMistake(Date startTime, Date endTime) {
        long times = endTime.getTime() - startTime.getTime();
        double hours = (double) times / (1000 * 60 * 60);
        BigDecimal result = BigDecimal.valueOf(hours);
        return result.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
    }
}
