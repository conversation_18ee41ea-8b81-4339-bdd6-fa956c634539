package cn.getech.ehm.task.service;

import cn.getech.ehm.task.dto.task.ticket.TaskTicketItemDto;
import cn.getech.ehm.task.entity.MaintTaskTicket;
import cn.getech.poros.framework.common.service.IBaseService;

import java.util.List;

/**
 * 工单作业票
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
public interface IMaintTaskTicketService extends IBaseService<MaintTaskTicket> {

    /**
     * 获取工单作业票列表
     * @param taskId
     * @return
     */
    List<TaskTicketItemDto> getListByTaskId(String taskId);

    /**
     * 更新作业票
     * @param taskId
     * @return
     */
    Boolean saveOrUpdateBatch(List<TaskTicketItemDto> itemDtos, String taskId);
}