package cn.getech.ehm.task.dto.task.statistics;

import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * 维护工单主表 返回数据模型
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "TaskStatisticsResultDto", description = "TaskStatisticsResultDto")
public class TaskStatisticsResultDto {

    private String title;

    private String valueOne;

    private String valueOneRemark;

    private String valueTwo;

    private String valueTwoRemark;

    private String valueThree;

    private String valueThreeRemark;

    private String valueFour;

    private String valueFourRemark;

    private Integer sortNum;

    private Integer jobLevel;
}