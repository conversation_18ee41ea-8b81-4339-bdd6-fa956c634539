package cn.getech.ehm.task.service;

import cn.getech.ehm.task.dto.CommonConfigDto;
import cn.getech.ehm.task.dto.ticket.JobTicketDto;
import cn.getech.ehm.task.dto.ticket.JobTicketEditDto;
import cn.getech.ehm.task.entity.JobTicket;
import cn.getech.poros.framework.common.service.IBaseService;

/**
 * 作业票service
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
public interface IJobTicketService extends IBaseService<JobTicket> {

    /**
     * 新增作业票
     * @param addDto
     * @return
     */
    String saveByParam(JobTicketDto addDto);

    /**
     * 修改作业票
     * @param editDto
     * @return
     */
    Boolean updateByParam(JobTicketEditDto editDto);

    /**
     * 获取作业票
     * @return
     */
    JobTicketDto getDetialDto();

    /**
     * 获取主表信息
     * @return
     */
    JobTicketDto getDto();

    /**
     * 是否开启故障单/缺陷单派单人工审批
     * @return
     */
    Boolean getRepairTaskAudit();

    /**
     * 工单通用配置
     * @return
     */
    CommonConfigDto commonConfig();

    /**
     * 获取当前租户作业票id
     * @return
     */
    String getTicketId();
}