package cn.getech.ehm.task.dto.task.history;

import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

/**
 * <pre>
 * 工单历史 分页查询参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2025-02-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MaintTaskHistory查询", description = "工单历史查询参数")
public class MaintTaskHistoryQueryParam extends PageParam {

    @ApiModelProperty(value = "")
    private String id;

    @ApiModelProperty(value = "流程id")
    private String processInstanceId;

//    @ApiModelProperty(value = "环节id")
//    private String activityId;
//
//    @ApiModelProperty(value = "环节名")
//    private String activityName;
//
//    @ApiModelProperty(value = "任务id")
//    private String taskId;
//
//    @ApiModelProperty(value = "候选人/接单人")
//    private String assigneeName;
//
//    @ApiModelProperty(value = "候选人/接单人")
//    private String assigneeUid;
//
//    @ApiModelProperty(value = "备注")
//    private String comment;
//
//    @ApiModelProperty(value = "开始时间")
//    private Date startTime;
//
//    @ApiModelProperty(value = "结束时间")
//    private Date endTime;
//
//    @ApiModelProperty(value = "处理状态")
//    private String operator;
//
//    @ApiModelProperty(value = "已读状态")
//    private String readStatus;
//
//    @ApiModelProperty(value = "任务处理中/任务已完成")
//    private String taskStatus;

    @ApiModelProperty(value = "工单id")
    private String maintTaskId;
}
