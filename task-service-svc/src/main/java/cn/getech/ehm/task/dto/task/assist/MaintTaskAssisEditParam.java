package cn.getech.ehm.task.dto.task.assist;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

/**
 * 工单辅助人员修改体
 *
 * <AUTHOR>
 * @since 2022-06-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MaintTaskAssisEditParam", description = "工单辅助人员修改体")
public class MaintTaskAssisEditParam extends MaintTaskAssistDto {
    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "工单id")
    @NotEmpty
    private String taskId;
}
