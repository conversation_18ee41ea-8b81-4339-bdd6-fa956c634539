package cn.getech.ehm.task.dto.analyst;

import cn.getech.poros.framework.common.param.ApiParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <pre>
 * 报修工单统计 编辑参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MaintTaskAnalyst编辑", description = "报修工单统计编辑参数")
public class MaintTaskAnalystEditParam extends ApiParam {

    @ApiModelProperty(value = "")
    private Long deleted;

    @ApiModelProperty(value = "")
    private String tenantId;

    @ApiModelProperty(value = "工单id")
    private String taskId;

    @ApiModelProperty(value = "工单编号")
    private String taskCode;

    @ApiModelProperty(value = "工单状态")
    private Integer taskStatus;

    @ApiModelProperty(value = "设备id")
    private String equipId;

    @ApiModelProperty(value = "设备编号")
    private String equipCode;

    @ApiModelProperty(value = "设备名")
    private String equipName;

    @ApiModelProperty(value = "设备位置id")
    private String equipLocationId;

    @ApiModelProperty(value = "设备位置")
    private String equipLocationName;

    @ApiModelProperty(value = "设备类型id")
    private String equipCategoryId;

    @ApiModelProperty(value = "设备类型")
    private String equipCategotyName;

    @ApiModelProperty(value = "报修人")
    private String reportUid;

    @ApiModelProperty(value = "报修人")
    private String reportName;

    @ApiModelProperty(value = "处理人")
    private String handlerUid;

    @ApiModelProperty(value = "报修人")
    private String handlerName;

    @ApiModelProperty(value = "是否改派")
    private Long transferFlag;

    @ApiModelProperty(value = "工单创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date taskCreateTime;

    @ApiModelProperty(value = "工单接单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date taskReceiveTime;

    @ApiModelProperty(value = "工单关闭时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date taskCloseTime;

    @ApiModelProperty(value = "工单实际耗时")
    private Long taskWorkingTimeStr;

    @ApiModelProperty(value = "工单检修耗时")
    private Long taskMaintTimeStr;

    @ApiModelProperty(value = "接单时长")
    private Long receiveStr;

    @ApiModelProperty(value = "挂起审批发起时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date handupSubmitTime;

    @ApiModelProperty(value = "挂起审批审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date handupAuditTime;

    @ApiModelProperty(value = "挂起审批耗时")
    private Long handupCostStr;

    @ApiModelProperty(value = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date handupSubmitTime2;

    @ApiModelProperty(value = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date handupAuditTime2;

    @ApiModelProperty(value = "")
    private Long handupCostStr2;

    @ApiModelProperty(value = "挂起总时长")
    private Long handupCostTotal;

    @ApiModelProperty(value = "运行转缺陷发起时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date runDefectSubmitTime;

    @ApiModelProperty(value = "运行转缺陷审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date runDefectAuditTime;

    @ApiModelProperty(value = "运行转缺陷耗时")
    private Long runDefectCostStr;

    @ApiModelProperty(value = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date runDefectSubmitTime2;

    @ApiModelProperty(value = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date runDefectAuditTime2;

    @ApiModelProperty(value = "")
    private Long runDefectCostStr2;

    @ApiModelProperty(value = "验收转缺陷发起时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date checkDefectSubmitTime;

    @ApiModelProperty(value = "验收转缺陷审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date checkDefectAuditTime;

    @ApiModelProperty(value = "验收转缺陷耗时")
    private Long checkDefectCostStr;

    @ApiModelProperty(value = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date checkDefectSubmitTime2;

    @ApiModelProperty(value = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date checkDefectAuditTime2;

    @ApiModelProperty(value = "")
    private Long checkDefectCostStr2;

    @ApiModelProperty(value = "验收发起时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date checkSubmitTime;

    @ApiModelProperty(value = "验收审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date checkAuditTime;

    @ApiModelProperty(value = "验收耗时")
    private Long checkCostStr;

    @ApiModelProperty(value = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date checkSubmitTime2;

    @ApiModelProperty(value = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date checkAuditTime2;

    @ApiModelProperty(value = "")
    private Long checkCostStr2;

}
