package cn.getech.ehm.task.dto.task.notify;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * 巡检任务单通知dto
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "MaintTaskNotifyDto", description = "工单通知dto")
public class MaintTaskNotifyDto {

    @ApiModelProperty(value = "工单id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "编号")
    private String code;

    @ApiModelProperty(value = "设备id")
    private String equipmentId;

    @ApiModelProperty(value = "设备名称")
    private String equipmentName;

    @ApiModelProperty(value = "设备编码")
    private String equipmentCode;

    @ApiModelProperty(value = "计划巡检日期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date planMaintTime;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "状态名称")
    private String statusName;

    @ApiModelProperty(value = "截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date taskDeadlineDate;

    @ApiModelProperty(value = "人员和班组组合人员ids")
    private String allStaffIds;

    @ApiModelProperty(value = "人员和班组组合人员ids")
    private String handler;

    @ApiModelProperty(value = "租户id")
    private String tenantId;

    @ApiModelProperty(value = "通知表id")
    private String notifyId;

    @ApiModelProperty(value = "类型1临期通知2超期")
    private Integer notifyType;

    @ApiModelProperty(value = "通知方式")
    private Integer[] notifyMethods;

    @ApiModelProperty(value = "通知对象")
    private Integer[] notifyObjects;

    @ApiModelProperty(value = "自定义通知用户UIDS")
    private String[] customUids;

    @ApiModelProperty(value = "自定义通知角色(id)")
    private String[] customRoles;

    @ApiModelProperty(value = "设备管理人员")
    private Integer[] equipmentManager;

    @ApiModelProperty(value = "指定维护人员")
    private String[] maintainerIds;

    @ApiModelProperty(value = "指定维护班组")
    private String[] teamIds;
}
