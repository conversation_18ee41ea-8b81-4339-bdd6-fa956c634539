package cn.getech.ehm.task.controller;


import io.swagger.annotations.*;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import org.springframework.beans.factory.annotation.Autowired;
import cn.getech.poros.framework.common.annotation.Permission;
import cn.getech.poros.framework.common.api.PageResult;
import org.apache.commons.collections4.CollectionUtils;
import cn.getech.ehm.task.dto.TaskNotifyConfigQueryParam;
import cn.getech.ehm.task.dto.TaskNotifyConfigAddParam;
import cn.getech.ehm.task.dto.TaskNotifyConfigEditParam;
import cn.getech.ehm.task.dto.TaskNotifyConfigDto;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.utils.ExcelUtils;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotEmpty;

import cn.getech.ehm.task.service.ITaskNotifyConfigService;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 工单配置控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-01
 */
@RestController
@RequestMapping("/taskNotifyConfig")
@Api(tags = "工单通知配置服务接口")
public class TaskNotifyConfigController {

    @Autowired
    private ITaskNotifyConfigService taskNotifyConfigService;

    /**
     * 分页获取工单配置列表
     */
//    @ApiOperation("分页获取工单配置列表")
//    @GetMapping("/list")
//    //@Permission("task:notify:config:list")
//    public RestResponse<PageResult<TaskNotifyConfigDto>> pageList(@Valid TaskNotifyConfigQueryParam taskNotifyConfigQueryParam){
//        return RestResponse.ok(taskNotifyConfigService.pageDto(taskNotifyConfigQueryParam));
//    }

    /**
     * 新增工单配置
     */
//    @ApiOperation("新增工单配置")
//    @AuditLog(title = "工单配置",desc = "新增工单配置",businessType = BusinessType.INSERT)
//    @PostMapping
//    //@Permission("task:notify:config:update")
//    public RestResponse<Boolean> add(@RequestBody @Valid TaskNotifyConfigAddParam taskNotifyConfigAddParam) {
//        return RestResponse.ok(taskNotifyConfigService.saveByParam(taskNotifyConfigAddParam));
//    }
    @ApiOperation("获取配置")
    @GetMapping("/get/default")
    public RestResponse getDefault() {
        return RestResponse.ok(taskNotifyConfigService.getDefault());
    }

    /**
     * 修改工单配置
     */
    @ApiOperation(value = "修改工单配置")
    @AuditLog(title = "工单配置", desc = "修改工单配置", businessType = BusinessType.UPDATE)
    @PutMapping
    //@Permission("task:notify:config:update")
    public RestResponse<Boolean> update(@RequestBody @Valid TaskNotifyConfigEditParam taskNotifyConfigEditParam) {
        return RestResponse.ok(taskNotifyConfigService.updateByParam(taskNotifyConfigEditParam));
    }

    /**
     * 根据id删除工单配置
     */
//    @ApiOperation(value="根据id删除工单配置")
//    @AuditLog(title = "工单配置",desc = "工单配置",businessType = BusinessType.DELETE)
//    @DeleteMapping("/{ids}")
//    //@Permission("task:notify:config:delete")
//    public RestResponse<Boolean> delete(@PathVariable("ids") @NotEmpty Long[] ids) {
//        return RestResponse.ok(taskNotifyConfigService.removeByIds(ids));
//    }

//    /**
//     * 根据id获取工单配置
//     */
//    @ApiOperation(value = "根据id获取工单配置")
//    @GetMapping(value = "/{id}")
//    //@Permission("task:notify:config:list")
//    public RestResponse<TaskNotifyConfigDto> get(@PathVariable  Long id) {
//        return RestResponse.ok(taskNotifyConfigService.getDtoById(id));
//    }
//
//    /**
//     * 导出工单配置列表
//     */
//    @ApiOperation(value = "导出工单配置列表")
//    @AuditLog(title = "工单配置",desc = "导出工单配置列表",businessType = BusinessType.EXPORT)
//    @GetMapping("/export")
//   // @Permission("task:notify:config:export")
//    public void excelExport(@Valid TaskNotifyConfigQueryParam taskNotifyConfigQueryParam, HttpServletResponse response){
//        PageResult<TaskNotifyConfigDto> pageResult  = taskNotifyConfigService.pageDto(taskNotifyConfigQueryParam);
//        ExcelUtils<TaskNotifyConfigDto> util = new ExcelUtils<>(TaskNotifyConfigDto.class);
//
//        util.exportExcel(pageResult.getRecords(), "工单配置",response);
//    }
//
//    /**
//     * Excel导入工单配置
//     */
//    @ApiOperation(value = "Excel导入工单配置")
//    @AuditLog(title = "工单配置",desc = "Excel导入工单配置",businessType = BusinessType.INSERT)
//    @PostMapping("/import")
//    //@Permission("task:notify:config:import")
//    public RestResponse<Boolean> excelImport(@RequestParam("file") MultipartFile file){
//        ExcelUtils<TaskNotifyConfigDto> util = new ExcelUtils<>(TaskNotifyConfigDto.class);
//        List<TaskNotifyConfigDto> rows = util.importExcel(file);
//        if (CollectionUtils.isEmpty(rows)){
//            return RestResponse.failed();
//        }
//        return RestResponse.ok(taskNotifyConfigService.saveDtoBatch(rows));
//    }

}
