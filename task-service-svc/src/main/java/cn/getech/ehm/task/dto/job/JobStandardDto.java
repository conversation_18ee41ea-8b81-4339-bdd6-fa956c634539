package cn.getech.ehm.task.dto.job;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * 作业标准
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "JobStandardDto", description = "作业标准dto")
public class JobStandardDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "设备类型id集合")
    private String[] infoCategoryIds;

    @ApiModelProperty(value = "设备类型名称集合")
    private String infoCategoryNames;

    @ApiModelProperty(value = "设备位置id集合")
    private String[] infoLocationIds;

    @ApiModelProperty(value = "设备位置名称集合")
    private String infoLocationNames;

    @ApiModelProperty(value = "专业类别集合")
    private String[] majors;

    @ApiModelProperty(value = "作业类别")
    private String jobType;

    @ApiModelProperty(value = "是否发布")
    private Boolean published;

    @ApiModelProperty(value = "是否停机")
    private Boolean stopped;

    @ApiModelProperty(value = "描述")
    private String remark;

    @ApiModelProperty(value = "作业项目集合")
    private List<JobStandardItemDto> itemDtos;

    @ApiModelProperty(value = "作业项目统计")
    private ItemCountDto countDto;

    @ApiModelProperty(value = "设备类型")
    private String equipType;
}