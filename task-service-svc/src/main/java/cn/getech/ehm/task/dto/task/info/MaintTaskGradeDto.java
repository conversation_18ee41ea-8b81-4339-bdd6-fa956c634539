package cn.getech.ehm.task.dto.task.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 工单验收
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "MaintTaskGradeDto", description = "工单验收")
public class MaintTaskGradeDto {

    @ApiModelProperty(value = "工单id")
    private String taskId;

    @ApiModelProperty(value = "评分")
    private Integer grade;

    @ApiModelProperty(value = "评价")
    private String comment;

    @ApiModelProperty(value = "处理情况")
    private String handleInformation;

    @ApiModelProperty(value = "结果描述")
    private String handleResult;

    @ApiModelProperty(value = "验收结果0未通过1通过")
    private Integer checkAcceptResult;

}