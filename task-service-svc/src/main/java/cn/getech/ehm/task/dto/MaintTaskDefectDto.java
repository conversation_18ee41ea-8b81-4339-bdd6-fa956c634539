package cn.getech.ehm.task.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import cn.getech.poros.framework.common.annotation.Excel;
import java.util.Date;


/**
 * <pre>
 * 缺陷记录 返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2025-03-26
 */
@Data
@ApiModel(value = "MaintTaskDefectDto", description = "缺陷记录返回数据模型")
public class MaintTaskDefectDto{

    @ApiModelProperty(value = "deleted")
    @Excel(name="deleted",cellType = Excel.ColumnType.STRING )
    private Integer deleted;

    @ApiModelProperty(value = "tenantId")
    @Excel(name="tenantId",cellType = Excel.ColumnType.STRING )
    private String tenantId;

    @ApiModelProperty(value = "设备id")
    @Excel(name="设备id",cellType = Excel.ColumnType.STRING )
    private String equipmentId;

    @ApiModelProperty(value = "缺陷名称")
    @Excel(name="缺陷名称",cellType = Excel.ColumnType.STRING )
    private String defectName;

    @ApiModelProperty(value = "缺陷内容")
    @Excel(name="缺陷内容",cellType = Excel.ColumnType.STRING )
    private String defectContent;

    @ApiModelProperty(value = "影响描述")
    @Excel(name="影响描述",cellType = Excel.ColumnType.STRING )
    private String affectContent;

    @ApiModelProperty(value = "缺陷种类")
    @Excel(name="缺陷种类",cellType = Excel.ColumnType.STRING )
    private Integer defectType;

    @ApiModelProperty(value = "专业类别")
    @Excel(name="专业类别",cellType = Excel.ColumnType.STRING )
    private Integer majorType;

    @ApiModelProperty(value = "现场图片/视频")
    @Excel(name="现场图片/视频",cellType = Excel.ColumnType.STRING )
    private String[] liveMediaIds;

    @ApiModelProperty(value = "缺陷状态")
    @Excel(name="缺陷状态",cellType = Excel.ColumnType.STRING )
    private Integer defectStatus;

    @ApiModelProperty(value = "验收状态")
    @Excel(name="验收状态",cellType = Excel.ColumnType.STRING )
    private Integer checkStatus;

    @ApiModelProperty(value = "截止日期")
    @Excel(name="截止日期",cellType = Excel.ColumnType.STRING ,dateFormat="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "处理人ids")
    @Excel(name="处理人ids",cellType = Excel.ColumnType.STRING )
    private String dealPersonIds;

    @ApiModelProperty(value = "建议处理方案")
    @Excel(name="建议处理方案",cellType = Excel.ColumnType.STRING )
    private String suggestDealContent;

    @ApiModelProperty(value = "工单id")
    @Excel(name="工单id",cellType = Excel.ColumnType.STRING )
    private String maintTaskId;

    @ApiModelProperty(value = "工单code")
    @Excel(name="工单code",cellType = Excel.ColumnType.STRING )
    private String maintTaskCode;

    @ApiModelProperty(value = "验收说明")
    @Excel(name="验收说明",cellType = Excel.ColumnType.STRING )
    private String checkExplain;

    @ApiModelProperty(value = "实际处理方案")
    @Excel(name="实际处理方案",cellType = Excel.ColumnType.STRING )
    private String realDealContent;

    @ApiModelProperty(value = "关闭原因")
    @Excel(name="关闭原因",cellType = Excel.ColumnType.STRING )
    private String closeReason;

    @ApiModelProperty(value = "处理时间")
    @Excel(name="处理时间",cellType = Excel.ColumnType.STRING ,dateFormat="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date dealDate;

    @ApiModelProperty(value = "关闭时间")
    @Excel(name="关闭时间",cellType = Excel.ColumnType.STRING ,dateFormat="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date closeDate;

    @ApiModelProperty(value = "来源工单id")
    @Excel(name="来源工单id",cellType = Excel.ColumnType.STRING )
    private String sourceTaskId;

    @ApiModelProperty(value = "缺陷原因")
    @Excel(name="缺陷原因",cellType = Excel.ColumnType.STRING )
    private String defectReason;

    @ApiModelProperty(value = "来源工单编码")
    @Excel(name="来源工单编码",cellType = Excel.ColumnType.STRING )
    private String sourceTaskCode;

    @ApiModelProperty(value = "id")
    @Excel(name="id",cellType = Excel.ColumnType.STRING )
    private String id;

    @ApiModelProperty(value = "createBy")
    @Excel(name="createBy",cellType = Excel.ColumnType.STRING )
    private String createBy;

    @ApiModelProperty(value = "updateBy")
    @Excel(name="updateBy",cellType = Excel.ColumnType.STRING )
    private String updateBy;

    @ApiModelProperty(value = "createTime")
    @Excel(name="createTime",cellType = Excel.ColumnType.STRING ,dateFormat="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "updateTime")
    @Excel(name="updateTime",cellType = Excel.ColumnType.STRING ,dateFormat="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value = "remark")
    @Excel(name="remark",cellType = Excel.ColumnType.STRING )
    private String remark;

    @ApiModelProperty(value = "驳回原因")
    private String rejectReason;

}