package cn.getech.ehm.task.handler;

import cn.getech.ehm.common.context.UserContextHolder;
import cn.getech.ehm.system.client.NotifyClient;
import cn.getech.ehm.system.dto.notify.NotifyParam;
import cn.getech.ehm.system.dto.notify.NotifyType;
import cn.getech.ehm.system.dto.notify.SystemNotify;
import cn.getech.ehm.task.entity.MaintTask;
import cn.getech.ehm.task.entity.MaintTaskConfig;
import cn.getech.ehm.task.entity.MaintTaskHistory;
import cn.getech.ehm.task.enums.TaskOperationType;
import cn.getech.ehm.task.enums.TaskStatusType;
import cn.getech.ehm.task.enums.TaskType;
import cn.getech.ehm.task.service.IMaintTaskConfigService;
import cn.getech.ehm.task.service.IMaintTaskService;
import cn.getech.ehm.task.service.ITaskNotifyConfigService;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.permission.client.PorosSecStaffClient;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.EnumSet;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ActivitiTaskNotifyHandler {

    @Autowired
    NotifyClient notifyClient;
    @Autowired
    ITaskNotifyConfigService taskNotifyConfigService;
    @Autowired
    PorosSecStaffClient porosSecStaffClient;

    @Autowired
    private IMaintTaskConfigService maintTaskConfigService;

    @Value("${aliyun.sms.default-sign:EHM}")
    private String defaultSign;

    @Value("${aliyun.sms.templates.task:SMS_215338506}")
    private String taskTemplate;

    @Value("${oa.order.jumpUrl:/maint/order/handle/?id=%s&type=edit}")
    private String todoTaskJumpUrl;

    @Value("${oa.baseJumpUrl:http://szporos.csot.tcl.com/web-admin}")
    private String baseJumpUrl;

    public static String REDIS_KEY = "cn.getech.ehm.maint.task.notify";

    @Resource
    @Lazy
    IMaintTaskService maintTaskService;

    @Autowired
    ActivitiHandler activitiHandler;

    @Async("sendNotify")
    public void send(MaintTask dtoById, MaintTaskHistory maintTaskHistory, UserBaseInfo userBaseInfo) {
        log.debug("发送通知，taskId：{}", dtoById.getId());
        if (StringUtils.isEmpty(maintTaskHistory.getAssigneeUid())) {
            return;
        }
        UserContextHolder.switchContext(userBaseInfo);
        Date now = new Date();
        String title = "您有新的工单处于[" + TaskStatusType.getNameByValue(maintTaskHistory.getNewStatus()) + "]状态，请及时处理";
        String content = title;
        if (dtoById != null) {
            content = "工单名：" + dtoById.getName() + " 编号：" + dtoById.getCode();
        }
        String url = baseJumpUrl + String.format(todoTaskJumpUrl, dtoById.getId());
        NotifyParam notifyParam = new NotifyParam();
        EnumSet<NotifyType> notifyTypes = EnumSet.of(NotifyType.SYSTEM);
        notifyParam.setNotifyTypes(notifyTypes);
        if (dtoById != null && dtoById.getType() == TaskType.BREAKDOWN.getValue()) {
            notifyParam = this.generalContent(notifyParam, title, content, url, Arrays.asList(maintTaskHistory.getAssigneeUid().split(",")));
        } else {
            notifyParam = this.generalContentNoConfig(notifyParam, title, content, url, Arrays.asList(maintTaskHistory.getAssigneeUid().split(",")));
        }
        log.debug("发送通知，参数：{}", JSON.toJSONString(notifyParam));
        RestResponse<String> stringRestResponse = notifyClient.sendNotify(notifyParam);
        log.debug("发送通知，返回结果：{}", JSON.toJSONString(stringRestResponse));
    }

    @Async("sendNotify")
    public void send(String taskId, MaintTaskHistory maintTaskHistory, UserBaseInfo userBaseInfo) {
        log.debug("发送通知，taskId：{}", taskId);
        if (StringUtils.isEmpty(maintTaskHistory.getAssigneeUid())) {
            return;
        }
        UserContextHolder.switchContext(userBaseInfo);
        Date now = new Date();
        MaintTask dtoById = (MaintTask) maintTaskService.getById(taskId);
        String title = "您有新的工单处于[" + TaskStatusType.getNameByValue(maintTaskHistory.getNewStatus()) + "]状态，请及时处理";
        String content = title;
        if (dtoById != null) {
            content = "工单名：" + dtoById.getName() + " 编号：" + dtoById.getCode();
        }
        String url = baseJumpUrl + String.format(todoTaskJumpUrl, taskId);
        NotifyParam notifyParam = new NotifyParam();
        EnumSet<NotifyType> notifyTypes = EnumSet.of(NotifyType.SYSTEM);
        notifyParam.setNotifyTypes(notifyTypes);
        if (dtoById != null && dtoById.getType() == TaskType.BREAKDOWN.getValue()) {
            notifyParam = this.generalContent(notifyParam, title, content, url, Arrays.asList(maintTaskHistory.getAssigneeUid().split(",")));
        } else {
            notifyParam = this.generalContentNoConfig(notifyParam, title, content, url, Arrays.asList(maintTaskHistory.getAssigneeUid().split(",")));
        }
        log.debug("发送通知，参数：{}", JSON.toJSONString(notifyParam));
        RestResponse<String> stringRestResponse = notifyClient.sendNotify(notifyParam);
        log.debug("发送通知，返回结果：{}", JSON.toJSONString(stringRestResponse));
    }

    @Async("sendNotify")
    public void send(MaintTask maintTask, MaintTaskHistory maintTaskHistory, UserBaseInfo userBaseInfo, Long duration) {
        log.debug("发送通知，taskId：{}", maintTask.getId());
        if (StringUtils.isEmpty(maintTaskHistory.getAssigneeUid())) {
            return;
        }
        UserContextHolder.switchContext(userBaseInfo);
        Date now = new Date();
        String title = "您有新的工单处于[" + TaskStatusType.getNameByValue(maintTaskHistory.getNewStatus()) + "]状态，请及时处理";
        String content = title;
        if (maintTask != null) {
            content = "工单名：" + maintTask.getName() + " 编号：" + maintTask.getCode();
        }
        String url = baseJumpUrl + String.format(todoTaskJumpUrl, maintTask.getId());
        NotifyParam notifyParam = new NotifyParam();
        EnumSet<NotifyType> notifyTypes = EnumSet.of(NotifyType.SYSTEM);
        notifyParam.setNotifyTypes(notifyTypes);
        notifyParam = this.generalContent(notifyParam, title, content, url, Arrays.asList(maintTaskHistory.getAssigneeUid().split(",")), duration);
        log.debug("发送通知，参数：{}", JSON.toJSONString(notifyParam));
        RestResponse<String> stringRestResponse = notifyClient.sendNotify(notifyParam);
        log.debug("发送通知，返回结果：{}", JSON.toJSONString(stringRestResponse));
    }

    private NotifyParam generalContentNoConfig(NotifyParam notifyParam, String title, String content, String url, List<String> uids) {
        return this.generalContent(notifyParam, title, content, url, uids, 0L);
    }

    private NotifyParam generalContent(NotifyParam notifyParam, String title, String content, String url, List<String> uids) {
        MaintTaskConfig durationConfig = maintTaskConfigService.getConfig("" + TaskType.BREAKDOWN.getValue(), "" + TaskOperationType.NOTIFY_DURATION_RULE.getValue());
        String durationValue = durationConfig != null ? durationConfig.getConfigContent() : "0";
        return this.generalContent(notifyParam, title, content, url, uids, Long.valueOf(durationValue));
    }

    private NotifyParam generalContent(NotifyParam notifyParam, String title, String content, String url, List<String> uids, Long duration) {
//        RestResponse<List<PorosSecStaffDto>> userByUids = porosSecStaffClient.findUserByUids(uids);
//        if (userByUids.isSuccess()) {
//            List<PorosSecStaffDto> porosSecStaffDtos = userByUids.getData();
//            List<String> emails = new ArrayList<>();
//            List<String> mobiles = new ArrayList<>();
//            porosSecStaffDtos.stream().forEach(porosSecStaffDto -> {
//                emails.add(porosSecStaffDto.getEmail());
//                mobiles.add(porosSecStaffDto.getMobile());
//            });
//
//            List<String> _emails = emails.stream().distinct().collect(Collectors.toList());
//            List<String> _mobiles = mobiles.stream().distinct().collect(Collectors.toList());
////            if (notifyParam.getNotifyTypes().contains(NotifyType.SMS)) {
////                Map<String, String> smsParams = new HashMap<>(3);
////                smsParams.put("name", "");
////                smsParams.put("statusValue", "");
////                SMSNotify smsNotify = SMSNotify.builder().sign(defaultSign).template(taskTemplate).params(smsParams).mobiles(_mobiles.toArray(new String[_mobiles.size()])).build();
////                notifyParam.setSmsNotify(smsNotify);
////            }
////            if (notifyParam.getNotifyTypes().contains(NotifyType.EMAIL)) {
////                EmailNotify emailNotify = EmailNotify.builder().title(title).content(content + "<br/>" + url).emails(_emails.toArray(new String[_emails.size()])).build();
////                notifyParam.setEmailNotify(emailNotify);
////            }
////            if (notifyParam.getNotifyTypes().contains(NotifyType.APP)) {
////                PushNotify pushNotify = PushNotify.builder().title(title).content(content).uids(_uids.toArray(new String[_uids.size()])).build();
////                notifyParam.setPushNotify(pushNotify);
////            }
//        }
        List<String> _uids = uids.stream().distinct().collect(Collectors.toList());
        if (notifyParam.getNotifyTypes().contains(NotifyType.SYSTEM)) {
            SystemNotify systemNotify = SystemNotify.builder()
                    .uids(_uids.toArray(new String[_uids.size()])).title(title).content(content).type("notice").duration(duration).build();
            notifyParam.setSystemNotify(systemNotify);
        }
        return notifyParam;
    }

    @Async("sendNotify")
    public void send(MaintTask maintTask, MaintTaskHistory maintTaskHistory, UserBaseInfo userBaseInfo, Long duration, List<String> targetUids) {
        log.debug("发送通知，taskId：{}", maintTask.getId());
        if (StringUtils.isEmpty(maintTaskHistory.getAssigneeUid())) {
            return;
        }
        UserContextHolder.switchContext(userBaseInfo);
        Date now = new Date();
        String title = "您有新的工单处于[" + TaskStatusType.getNameByValue(maintTaskHistory.getNewStatus()) + "]状态，请及时处理";
        String content = title;
        if (maintTask != null) {
            content = "工单名：" + maintTask.getName() + " 编号：" + maintTask.getCode();
        }
        NotifyParam notifyParam = new NotifyParam();
        EnumSet<NotifyType> notifyTypes = EnumSet.of(NotifyType.SYSTEM);
        notifyParam.setNotifyTypes(notifyTypes);
        notifyParam = this.generalContent(notifyParam, title, content, null, targetUids, duration);
        log.debug("发送通知，参数：{}", JSON.toJSONString(notifyParam));
        RestResponse<String> stringRestResponse = notifyClient.sendNotify(notifyParam);
        log.debug("发送通知，返回结果：{}", JSON.toJSONString(stringRestResponse));
    }
}
