package cn.getech.ehm.task.dto.defect;

import cn.getech.poros.framework.common.param.ApiParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <pre>
 * 缺陷信息 新增参数
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-02-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DefectInfo新增", description = "缺陷信息新增参数")
public class DefectInfoAddParam extends ApiParam {

    @ApiModelProperty(value = "设备id",required = true)
    @NotBlank(message = "必须选择设备")
    private String equipmentId;
    @ApiModelProperty(value = "缺陷名称",required = true)
    //@NotBlank(message = "缺陷名称必填")
    private String defectName;
    @ApiModelProperty(value = "缺陷内容",required = true)
    private String defectContent;
    @ApiModelProperty(value = "影响描述")
    private String affectContent;
    @ApiModelProperty(value = "缺陷种类",required = true)
    private Integer defectType;
    @ApiModelProperty(value = "专业类别",required = true)
    private Integer majorType;
    @ApiModelProperty(value = "现场图片视频")
    private String[] liveMediaIds;

    @ApiModelProperty(value = "来源工单id")
    private String sourceTaskId;

    private String sourceTaskCode;

    @ApiModelProperty(value = "缺陷原因")
    private String defectReason;

    @ApiModelProperty(value = "截止日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date endTime;
}