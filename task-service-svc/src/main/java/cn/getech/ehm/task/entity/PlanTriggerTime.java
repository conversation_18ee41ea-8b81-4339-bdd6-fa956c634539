package cn.getech.ehm.task.entity;

import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 计划发单日期
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("plan_trigger_time")
public class PlanTriggerTime extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 计划id
     */
    @TableField("plan_id")
    private String planId;

    /**
     * 维保计划时间id
     */
    @TableField("plan_equipment_time_id")
    private String planEquipmentTimeId;

    /**
     * 设备id
     */
    @TableField("equipment_id")
    private String equipmentId;

    /**
     * 发单日期
     */
    @TableField("trigger_time")
    private Date triggerTime;

    /**
     * 计划维护日期
     */
    @TableField("plan_maint_time")
    private Date planMaintTime;

    /**
     * 状态0未出单1已生成工单 2节假日不开单
     */
    @TableField("status")
    private Integer status;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

}
