package cn.getech.ehm.task.dto.task.assist;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 工单辅助人员新增体
 *
 * <AUTHOR>
 * @since 2022-06-08
 */
@Data
@ApiModel(value = "MaintTaskAssistAddDto", description = "工单辅助人员新增体")
public class MaintTaskAssistListDto {
    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "工单id")
    private String taskId;

    @ApiModelProperty(value = "辅助人员id")
    private String userId;

    @ApiModelProperty(value = "辅助人员名称")
    private String userName;

    @ApiModelProperty(value = "参与说明")
    private String description;

    @ApiModelProperty(value = "计时方式")
    private Integer timeMode;

    @ApiModelProperty(value = "参与开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "参与结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "工时/h")
    private BigDecimal workHours;
}
