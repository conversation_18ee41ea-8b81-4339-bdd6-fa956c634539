package cn.getech.ehm.task.entity;

import cn.getech.ehm.common.mp.handler.IntegerArrayTypeHandler;
import cn.getech.ehm.common.mp.handler.StringArrayTypeHandler;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;
import java.util.Date;

/**
 * 维护计划
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("maint_plan")
public class MaintPlan extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 计划名称
     */
    @TableField("name")
    private String name;

    /**
     * 维保类型/作业类型
     */
    @TableField("job_type")
    private String jobType;

    /**
     * 维保等级(一级保养/二级保养/三级保养)
     */
    @TableField("job_level")
    private Integer jobLevel;

    /**
     * 报修单id
     */
    @TableField("repair_id")
    private String repairId;

    /**
     * 紧急程度/优先程度
     */
    @TableField("urgency")
    private String urgency;

    /**
     * 专业
     */
    @TableField("major")
    private String major;

    /**
     * 触发逻辑1以上次工单完成日期开始2定时触发
     */
    @TableField("trigger_type")
    private Integer triggerType;

    /**
     * 间隔值
     */
    @TableField("inter_val")
    private Integer interVal;

    /**
     * 提前释放天数
     */
    @TableField("advance_day")
    private Integer advanceDay;

    /**
     * 周期 0 单次 1 天 2 周 3 月
     */
    @TableField("period")
    private String period;

    /**
     * 周期为周/月时，日期数组
     */
    @TableField(value = "exec_val", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] execVal;

    /**
     * 周期为月时，数组(1-12月份)
     */
    @TableField(value = "month_exec_val", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] monthExecVal;

    /**
     * 截止日期
     */
    @TableField("expiry_time")
    private Date expiryTime;

    /**
     * 发布状态 0 待发布 1 已发布 2 发布审批中 3 发布驳回
     */
    @TableField("status")
    private Integer status;

    /**
     * 是否故障单自动转换0false1true
     */
    @TableField("repair_auto")
    private Boolean repairAuto;

    /**
     * 流程实例ID
     */
    @TableField("process_instance_id")
    private String processInstanceId;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 是否删除(0false1true)
     */
    @TableField("deleted")
    private Integer deleted;

    /**
     * 截止天数
     */
    @TableField("deadline_days")
    private Integer deadlineDays;
    
    private Integer enabled;

    /**
     * cbm触发频率
     */
    @TableField("cbm_frequency")
    private Integer cbmFrequency;

    /**
     * 设备类型id
     */
    @TableField(value = "info_category_id")
    private String infoCategoryId;

    /**
     * 设备位置id
     */
    @TableField(value = "info_location_id")
    private String infoLocationId;

    /**
     * 设备id集合
     */
    @TableField(value = "equipment_ids", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] equipmentIds;

    /**
     * 作业标准id
     */
    @TableField("standard_id")
    private String standardId;

    /**
     * 说明
     */
    @TableField("description")
    private String description;

    /**
     * 节假日
     */
    @TableField(value = "festival_type", jdbcType = JdbcType.VARCHAR, typeHandler = IntegerArrayTypeHandler.class)
    private Integer[] festivalType;

    /**
     * 超期后处理方式
     */
    @TableField("overdue_handling_method")
    private Integer overdueHandlingMethod;

    /**
     * 设备停机后处理方式
     */
    @TableField("stop_handling_method")
    private Integer stopHandlingMethod;

    private String equipType;

}
