package cn.getech.ehm.task.dto;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <pre>
 * 节点时限记录 编辑参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2023-03-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ActOvertimeInfo编辑", description = "节点时限记录编辑参数")
public class ActOvertimeInfoEditParam extends ApiParam {

    @ApiModelProperty(value = "")
    private String id;

    @ApiModelProperty(value = "")
    private String remark;

}
