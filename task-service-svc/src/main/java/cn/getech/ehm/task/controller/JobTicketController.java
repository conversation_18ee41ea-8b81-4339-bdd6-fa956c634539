package cn.getech.ehm.task.controller;

import cn.getech.ehm.task.dto.ticket.JobTicketDto;
import cn.getech.ehm.task.dto.ticket.JobTicketEditDto;
import cn.getech.ehm.task.dto.ticket.JobTicketItemDto;
import cn.getech.ehm.task.service.IJobTicketItemService;
import cn.getech.ehm.task.service.IJobTicketService;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import cn.hutool.core.date.DateUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;

/**
 * 作业票接口
 *
 * <AUTHOR>
 * @since 2020-08-06
 */
@RestController
@RequestMapping("/jobTicket")
@Api(tags = "作业票接口")
public class JobTicketController {

    @Autowired
    private IJobTicketService jobTicketService;
    @Autowired
    private IJobTicketItemService ticketItemService;

    /**
     * 新增作业票
     */
    @ApiOperation("新增作业票")
    @AuditLog(title = "作业票", desc = "新增作业票", businessType = BusinessType.INSERT)
    @PostMapping("addTicket")
    public RestResponse<String> add(@RequestBody @Valid JobTicketDto addDto) {
        return RestResponse.ok(jobTicketService.saveByParam(addDto));
    }

    /**
     * 修改作业票
     */
    @ApiOperation("修改作业票")
    @AuditLog(title = "作业票", desc = "修改作业票", businessType = BusinessType.UPDATE)
    @PostMapping("editTicket")
    public RestResponse<Boolean> edit(@RequestBody @Valid JobTicketEditDto editDto) {
        return RestResponse.ok(jobTicketService.updateByParam(editDto));
    }

    /**
     * 获取作业票
     */
    @ApiOperation(value = "获取作业票")
    @GetMapping
    public RestResponse<JobTicketDto> get() {
        return RestResponse.ok(jobTicketService.getDetialDto());
    }

    /**
     * 新增作业票内容
     */
    @ApiOperation("新增作业票内容")
    @AuditLog(title = "作业票", desc = "新增作业票内容", businessType = BusinessType.INSERT)
    @PostMapping("addTicketItem")
    public RestResponse<String> addItem(@RequestBody @Valid JobTicketItemDto addDto) {
        return RestResponse.ok(ticketItemService.saveByParam(addDto));
    }

    /**
     * 修改作业票内容
     */
    @ApiOperation("修改作业票内容")
    @AuditLog(title = "作业票", desc = "修改作业票内容", businessType = BusinessType.UPDATE)
    @PostMapping("editTicketItem")
    public RestResponse<Boolean> editTicketItem(@RequestBody @Valid JobTicketItemDto editDto) {
        return RestResponse.ok(ticketItemService.updateByParam(editDto));
    }

    /**
     * 根据id删除作业票内容
     */
    @ApiOperation(value="根据id删除作业票内容")
    @AuditLog(title = "作业票内容",desc = "作业票内容",businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public RestResponse<Boolean> delete(@PathVariable("id") @NotEmpty String id) {
        return RestResponse.ok(ticketItemService.deleteById(id));
    }
}
