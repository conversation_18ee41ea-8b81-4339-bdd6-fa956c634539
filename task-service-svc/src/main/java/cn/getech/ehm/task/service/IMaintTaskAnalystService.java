package cn.getech.ehm.task.service;

import cn.getech.ehm.task.dto.analyst.MaintTaskAnalystAddParam;
import cn.getech.ehm.task.dto.analyst.MaintTaskAnalystDto;
import cn.getech.ehm.task.dto.analyst.MaintTaskAnalystEditParam;
import cn.getech.ehm.task.dto.analyst.MaintTaskAnalystQueryParam;
import cn.getech.ehm.task.entity.MaintTaskAnalyst;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.IBaseService;

import java.util.List;

/**
 * <pre>
 * 报修工单统计 服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
public interface IMaintTaskAnalystService extends IBaseService<MaintTaskAnalyst> {

    /**
     * 分页查询，返回Dto
     *
     * @param maintTaskAnalystQueryParam
     * @return
     */
    PageResult<MaintTaskAnalystDto> pageDto(MaintTaskAnalystQueryParam maintTaskAnalystQueryParam);

    /**
     * 保存
     * @param maintTaskAnalystAddParam
     * @return
     */
    boolean saveByParam(MaintTaskAnalystAddParam maintTaskAnalystAddParam);

    /**
     * 根据id查询，转dto
     * @param id
     * @return
     */
    MaintTaskAnalystDto getDtoById(String id);

    /**
     * 批量保存
     * @param rows
     */
    boolean saveDtoBatch(List<MaintTaskAnalystDto> rows);

    /**
     * 更新
     * @param maintTaskAnalystEditParam
     */
    boolean updateByParam(MaintTaskAnalystEditParam maintTaskAnalystEditParam);

    public List<MaintTaskAnalystDto> listDto(MaintTaskAnalystQueryParam maintTaskAnalystQueryParam);
}