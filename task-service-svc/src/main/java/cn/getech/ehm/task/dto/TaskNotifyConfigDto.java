package cn.getech.ehm.task.dto;

import cn.getech.ehm.common.mp.handler.StringArrayTypeHandler;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import cn.getech.poros.framework.common.annotation.Excel;
import java.util.Date;


/**
 * <pre>
 * 工单通知配置 返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-11-01
 */
@Data
@ApiModel(value = "TaskNotifyConfigDto", description = "工单通知配置返回数据模型")
public class TaskNotifyConfigDto{

    @ApiModelProperty(value = "")
    @Excel(name="",cellType = Excel.ColumnType.STRING)
    private String id;

    @ApiModelProperty(value = "createBy")
    @Excel(name="createBy",cellType = Excel.ColumnType.STRING )
    private String createBy;

    @ApiModelProperty(value = "updateBy")
    @Excel(name="updateBy",cellType = Excel.ColumnType.STRING )
    private String updateBy;

    @ApiModelProperty(value = "createTime")
    @Excel(name="createTime",cellType = Excel.ColumnType.STRING ,dateFormat="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "updateTime")
    @Excel(name="updateTime",cellType = Excel.ColumnType.STRING ,dateFormat="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(value = "remark")
    @Excel(name="remark",cellType = Excel.ColumnType.STRING )
    private String remark;

    @TableField(value = "task_type",typeHandler = StringArrayTypeHandler.class)
    @ApiModelProperty(value = "工单类型")
    private String[] taskType;

    /**
     * 工单状态类型
     */
    @TableField(value = "task_status_type",typeHandler = StringArrayTypeHandler.class)
    @ApiModelProperty(value = "工单状态类型")
    private String[] taskStatusType;

    /**
     * 提醒方式
     */
    @TableField(value = "notify_type",typeHandler = StringArrayTypeHandler.class)
    @ApiModelProperty(value = "提醒方式")
    private String[] notifyType;

    /**
     * 开始时间
     */
    @TableField("begin_time")
    @ApiModelProperty(value = "开始时间")
    private Date beginTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    @ApiModelProperty(value = "结束时间")
    private Date endTime;

}