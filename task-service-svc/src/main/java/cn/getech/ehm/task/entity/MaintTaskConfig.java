package cn.getech.ehm.task.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import cn.getech.poros.framework.common.annotation.Excel;

/**
 * <p>
 * 工单配置信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("maint_task_config")
public class MaintTaskConfig extends BaseEntity {

    private static final long serialVersionUID=1L;

    @TableField("deleted")
    private Integer deleted;

    @TableField("tenant_id")
    private String tenantId;

    @TableField("create_user_name")
    private String createUserName;

    /**
     * 工单类型
     */
    @TableField("task_type")
    private Integer taskType;

    /**
     * 配置类型
     */
    @TableField("config_type")
    private Integer configType;

    /**
     * 配置内容
     */
    @TableField("config_content")
    private String configContent;

    /**
     * 开关
     */
    @TableField("is_alert")
    private Boolean isAlert;


}
