package cn.getech.ehm.task.service.impl;

import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.equipment.client.EquipmentClient;
import cn.getech.ehm.equipment.dto.EquipmentInfoSearchDto;
import cn.getech.ehm.task.dto.analyst.*;
import cn.getech.ehm.task.entity.MaintTaskAnalyst;
import cn.getech.ehm.task.enums.TaskStatusType;
import cn.getech.ehm.task.mapper.MaintTaskAnalystMapper;
import cn.getech.ehm.task.service.IMaintTaskAnalystService;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.api.ResultCode;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;


/**
 * <pre>
 * 报修工单统计 服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
@Slf4j
@Service
public class MaintTaskAnalystServiceImpl extends BaseServiceImpl<MaintTaskAnalystMapper, MaintTaskAnalyst> implements IMaintTaskAnalystService {

    @Autowired
    private MaintTaskAnalystParamMapper maintTaskAnalystParamMapper;
    @Autowired
    private EquipmentClient equipmentClient;

    @Override
    public PageResult<MaintTaskAnalystDto> pageDto(MaintTaskAnalystQueryParam maintTaskAnalystQueryParam) {
        Wrapper<MaintTaskAnalyst> wrapper = getPageSearchWrapper(maintTaskAnalystQueryParam);
        PageResult<MaintTaskAnalystDto> result = maintTaskAnalystParamMapper.pageEntity2Dto(page(maintTaskAnalystQueryParam, wrapper));

        return Optional.ofNullable(result).orElse(new PageResult<>());
    }

    @Override
    public List<MaintTaskAnalystDto> listDto(MaintTaskAnalystQueryParam maintTaskAnalystQueryParam) {
        Wrapper<MaintTaskAnalyst> wrapper = getPageSearchWrapper(maintTaskAnalystQueryParam);
        List<MaintTaskAnalyst> list = list(wrapper);
        List<MaintTaskAnalystDto> result = CopyDataUtil.copyList(list, MaintTaskAnalystDto.class);
        for (MaintTaskAnalystDto dto : result) {
            dto.setTaskStatusStr(TaskStatusType.getNameByValue(dto.getTaskStatus()));
        }
        return result;
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveByParam(MaintTaskAnalystAddParam maintTaskAnalystAddParam) {
        MaintTaskAnalyst maintTaskAnalyst = maintTaskAnalystParamMapper.addParam2Entity(maintTaskAnalystAddParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, maintTaskAnalyst);
        return save(maintTaskAnalyst);
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean updateByParam(MaintTaskAnalystEditParam maintTaskAnalystEditParam) {
        MaintTaskAnalyst maintTaskAnalyst = maintTaskAnalystParamMapper.editParam2Entity(maintTaskAnalystEditParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, maintTaskAnalyst);
        return updateById(maintTaskAnalyst);
    }


    @Override
    public MaintTaskAnalystDto getDtoById(String id) {
        return maintTaskAnalystParamMapper.entity2Dto((MaintTaskAnalyst) this.getById(id));
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveDtoBatch(List<MaintTaskAnalystDto> rows) {
        return saveBatch(maintTaskAnalystParamMapper.dtoList2Entity(rows));
    }

    private Wrapper<MaintTaskAnalyst> getPageSearchWrapper(MaintTaskAnalystQueryParam maintTaskAnalystQueryParam) {
        MaintTaskAnalyst maintTaskAnalyst = CopyDataUtil.copyObject(maintTaskAnalystQueryParam, MaintTaskAnalyst.class);
        LambdaQueryWrapper<MaintTaskAnalyst> wrapper = Wrappers.<MaintTaskAnalyst>lambdaQuery(maintTaskAnalyst);
        wrapper = this.buildEquipmentSearchWrapper(maintTaskAnalystQueryParam, wrapper);
        wrapper.ge(maintTaskAnalystQueryParam.getTaskCreateTimeBeginParam() != null, MaintTaskAnalyst::getTaskCreateTime, maintTaskAnalystQueryParam.getTaskCreateTimeBeginParam())
                .le(maintTaskAnalystQueryParam.getTaskCreateTimeEndParam() != null, MaintTaskAnalyst::getTaskCreateTime, maintTaskAnalystQueryParam.getTaskCreateTimeEndParam());
        wrapper.orderByDesc(MaintTaskAnalyst::getTaskCreateTime);
        return wrapper;
    }

    private LambdaQueryWrapper<MaintTaskAnalyst> buildEquipmentSearchWrapper(MaintTaskAnalystQueryParam queryParam, LambdaQueryWrapper<MaintTaskAnalyst> wrapper) {
        if (CollectionUtils.isEmpty(queryParam.getCategoryIds()) && CollectionUtils.isEmpty(queryParam.getLocationIds())) {
            return wrapper;
        }
        List<String> equipmentIds = Lists.newArrayList();
        EquipmentInfoSearchDto param = new EquipmentInfoSearchDto();
        param.setCategoryIds(queryParam.getCategoryIds());
        param.setLocationIds(queryParam.getLocationIds());
        RestResponse<List<String>> listRestResponse = equipmentClient.getEquipmentIdsByParam(param);
        if (!listRestResponse.isOk()) {
            log.error("远程调用equipment-service查询设备失败");
            equipmentIds = null;
        } else {
            List<String> searchEquipmentIds = listRestResponse.getData();
            if (CollectionUtils.isEmpty(searchEquipmentIds)) {
                //远程未查询到设备
                equipmentIds = null;
            } else {
                if (CollectionUtils.isNotEmpty(equipmentIds)) {
                    equipmentIds.retainAll(searchEquipmentIds);
                } else {
                    equipmentIds = searchEquipmentIds;
                }
            }
        }
        if (!CollectionUtils.isNotEmpty(equipmentIds)) {
            wrapper.eq(MaintTaskAnalyst::getEquipId, StaticValue.MINUS_ONE);
        } else {
            wrapper.in(MaintTaskAnalyst::getEquipId, equipmentIds);
        }
        return wrapper;
    }
}
