package cn.getech.ehm.task.controller;

import cn.getech.ehm.common.dto.EnumListDto;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.common.util.excel.FormExcelUtils;
import cn.getech.ehm.task.dto.MaintTaskDtoForeign;
import cn.getech.ehm.task.dto.TaskStatisticsDto;
import cn.getech.ehm.task.dto.TaskStatisticsReqDto;
import cn.getech.ehm.task.dto.repair.ManualRepairAddParam;
import cn.getech.ehm.task.dto.task.assist.MaintTaskAssisEditParam;
import cn.getech.ehm.task.dto.task.assist.MaintTaskAssistAddParam;
import cn.getech.ehm.task.dto.task.info.*;
import cn.getech.ehm.task.dto.task.item.MaintTaskItemDto;
import cn.getech.ehm.task.dto.task.item.TaskItemEditParam;
import cn.getech.ehm.task.dto.task.part.MaintTaskPartDto;
import cn.getech.ehm.task.dto.task.part.TaskPartEditParam;
import cn.getech.ehm.task.dto.task.process.TaskSubmitParam;
import cn.getech.ehm.task.dto.task.repair.MaintTaskFaultDto;
import cn.getech.ehm.task.dto.task.repair.TaskFaultEditParam;
import cn.getech.ehm.task.dto.task.ticket.TaskTicketDto;
import cn.getech.ehm.task.entity.MaintTask;
import cn.getech.ehm.task.enums.TaskSourceType;
import cn.getech.ehm.task.enums.TaskStatusType;
import cn.getech.ehm.task.schedule.MaintTaskSchedule;
import cn.getech.ehm.task.schedule.RepairTaskNotifySchedule;
import cn.getech.ehm.task.service.*;
import cn.getech.poros.framework.common.annotation.CreateByAndUpdateBy;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import cn.getech.ehm.task.dto.task.assist.MaintTaskAssistListDto;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 工单主表控制器
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
@RestController
@RequestMapping("/maintTask")
@Api(tags = "工单主表服务接口")
@Slf4j
public class MaintTaskController {
    @Autowired
    private IMaintTaskService maintTaskService;
    @Autowired
    private IMaintTaskRepairService maintTaskDetailService;
    @Autowired
    private IMaintTaskAssistPersonService maintTaskAssistPersonService;
    @Autowired
    private IJobTicketService jobTicketService;
    @Autowired
    private MaintTaskSchedule maintTaskSchedule;
    @Autowired
    private RepairTaskNotifySchedule repairTaskNotifySchedule;
    /**
     * 分页列表
     */
    @ApiOperation("分页列表")
    @PostMapping("/pageList")
    //@Permission("maint:task:list")
    public RestResponse<PageResult<MaintTaskPageDto>> pageList(@RequestBody @Valid MaintTaskQueryParam maintTaskQueryParam) {
        return RestResponse.ok(maintTaskService.pageDto(maintTaskQueryParam));
    }

    @ApiOperation(value = "导出工单列表")
    @AuditLog(title = "工单列表", desc = "导出工单列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    // @Permission("defect:info:export")
    public void excelExport(@RequestBody @Valid MaintTaskQueryParam maintTaskQueryParam, HttpServletResponse response) {
        if(maintTaskQueryParam.getSourceType() == TaskSourceType.BREAKDOWN.getValue()) {
            FormExcelUtils<MaintTaskFaultExcelDto> util = new FormExcelUtils<>(MaintTaskFaultExcelDto.class);
            util.exportExcel(CopyDataUtil.copyList(maintTaskService.exportList(maintTaskQueryParam), MaintTaskFaultExcelDto.class), "工单列表", response);
        }else if(maintTaskQueryParam.getSourceType() == TaskSourceType.PLAN.getValue()) {
            FormExcelUtils<MaintTaskPlanExcelDto> util = new FormExcelUtils<>(MaintTaskPlanExcelDto.class);
            util.exportExcel(CopyDataUtil.copyList(maintTaskService.exportList(maintTaskQueryParam), MaintTaskPlanExcelDto.class), "工单列表", response);
        }else if(maintTaskQueryParam.getSourceType() == TaskSourceType.DEFECT.getValue()) {
            FormExcelUtils<MaintTaskDefectExcelDto> util = new FormExcelUtils<>(MaintTaskDefectExcelDto.class);
            util.exportExcel(CopyDataUtil.copyList(maintTaskService.exportList(maintTaskQueryParam), MaintTaskDefectExcelDto.class), "工单列表", response);
        }
    }

    @ApiOperation(value = "导出工单列表-数量")
    @AuditLog(title = "工单列表", desc = "导出工单列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export/shownum")
    // @Permission("defect:info:export")
    public RestResponse<Integer> excelExportShowNum(@RequestBody @Valid MaintTaskQueryParam maintTaskQueryParam) {
        return RestResponse.ok(maintTaskService.exportListCount(maintTaskQueryParam));
    }

    /**
     * 获取工单状态
     */
    @ApiOperation("获取工单状态")
    @GetMapping("/getTaskStatus")
    public RestResponse<List<EnumListDto>> getTaskStatus() {

        return RestResponse.ok(TaskStatusType.getList(jobTicketService.commonConfig()));
    }

    /**
     * 获取工单来源
     */
    @ApiOperation("获取工单来源")
    @GetMapping("/getTaskSourceType")
    public RestResponse<Map<Integer, String>> getTaskSourceType() {
        return RestResponse.ok(TaskSourceType.getList());
    }

    /**
     * 根据id获取工单主表
     */
    @ApiOperation(value = "根据id获取工单主表")
    @GetMapping(value = "/getTaskById/{id}")
    //@Permission("maint:task:list")
    @CreateByAndUpdateBy(value = true)
    public RestResponse<MaintTaskDto> getTaskById(@PathVariable String id) {
        return RestResponse.ok(maintTaskService.getDtoById(id));
    }

    @ApiOperation(value = "根据id获取工单主表-feign")
    @GetMapping(value = "/getTaskById/foreign/{id}")
    public RestResponse<MaintTaskDtoForeign> getTaskByIdForeign(@PathVariable String id) {
        return RestResponse.ok(maintTaskService.getDtoByIdForeign(id));
    }

    @ApiOperation(value = "根据ids获取工单主表-feign")
    @PostMapping(value = "/getTaskById/foreign/batch")
    public RestResponse<List<MaintTaskDtoForeign>> getTaskListByIdForeign(@RequestBody List<String> ids) {
        List<MaintTask> list = maintTaskService.list(new QueryWrapper<MaintTask>().lambda().in(MaintTask::getId, ids));
        List<MaintTaskDtoForeign> maintTaskDtoForeigns = CopyDataUtil.copyList(list, MaintTaskDtoForeign.class);
        return RestResponse.ok(maintTaskDtoForeigns);
    }

    /**
     * 修改工单主表信息
     */
    @ApiOperation(value = "修改工单主表信息")
    @AuditLog(title = "工单处理", desc = "修改工单主表信息", businessType = BusinessType.UPDATE)
    @PostMapping("/updateTask")
    //@Permission("maint:task:update")
    public RestResponse<Boolean> updateTask(@RequestBody TaskEditParam editParam) {
        return RestResponse.ok(maintTaskService.updateTask(editParam));
    }

    /**
     * 根据id获取维保任务
     */
    @ApiOperation(value = "根据id获取维保任务")
    @GetMapping(value = "/getTaskItemById")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", dataType = "string", paramType = "query")})
    public RestResponse<MaintTaskItemDto> getTaskItemById(@RequestParam("id") String id,@RequestParam(value = "onlyError",defaultValue = "false") Boolean onlyError) {
        return RestResponse.ok(maintTaskService.getTaskItemById(id,onlyError));
    }

    /**
     * 修改维保任务
     */
    @ApiOperation(value = "修改维保任务")
    @AuditLog(title = "工单处理", desc = "修改维保任务", businessType = BusinessType.UPDATE)
    @PostMapping("/updateTaskItem")
    //@Permission("maint:task:update")
    public RestResponse<Boolean> updateTaskItem(@RequestBody @Valid TaskItemEditParam editParam) {
        return RestResponse.ok(maintTaskService.updateTaskItem(editParam));
    }

    /**
     * 根据id获取故障维修
     */
    @ApiOperation(value = "根据id获取故障维修")
    @GetMapping(value = "/getFaultById")
    //@Permission("maint:task:list")
    public RestResponse<MaintTaskFaultDto> getFaultById(@RequestParam("id") String id) {
        return RestResponse.ok(maintTaskService.getFaultDtoById(id));
    }

    /**
     * 修改故障维修
     */
    @ApiOperation(value = "修改故障维修")
    @AuditLog(title = "工单处理", desc = "修改故障维修", businessType = BusinessType.UPDATE)
    @PostMapping("/updateTaskFault")
    //@Permission("maint:task:update")
    public RestResponse<Boolean> updateTaskFault(@RequestBody @Valid TaskFaultEditParam editParam) {
        return RestResponse.ok(maintTaskService.updateTaskFault(editParam));
    }

    /**
     * 根据id获取备件
     */
    @ApiOperation(value = "根据id获取备件")
    @GetMapping(value = "/getPartById")
    //@Permission("maint:task:list")
    public RestResponse<MaintTaskPartDto> getPartById(@RequestParam("id") String id) {
        return RestResponse.ok(maintTaskService.getPartById(id));
    }

    /**
     * 工单管理修改备件
     */
    @ApiOperation(value = "修改备件")
    @AuditLog(title = "修改备件", desc = "工单管理修改备件", businessType = BusinessType.UPDATE)
    @PostMapping("/updateTaskPart")
    //@Permission("maint:task:update")
    public RestResponse<Boolean> updateTaskPart(@RequestBody @Valid TaskPartEditParam editParam) {
        return RestResponse.ok(maintTaskService.updateTaskPart(editParam));
    }

    /**
     * 新增工单辅助人员信息
     */
    @ApiOperation(value = "新增工单辅助人员信息")
    @AuditLog(title = "新增工单辅助人员信息", desc = "新增工单辅助人员信息", businessType = BusinessType.INSERT)
    @PostMapping("/addTaskAssistPerson")
    public RestResponse<Boolean> addTaskAssistPerson(@RequestBody @Valid MaintTaskAssistAddParam addParam) {
        return RestResponse.ok(maintTaskAssistPersonService.addTaskAssistPerson(addParam));
    }

    /**
     * 修改工单辅助人员信息
     */
    @ApiOperation(value = "修改工单辅助人员信息")
    @AuditLog(title = "修改工单辅助人员信息", desc = "修改工单辅助人员信息", businessType = BusinessType.UPDATE)
    @PostMapping("/updateTaskAssistPerson")
    public RestResponse<Boolean> updateTaskAssistPerson(@RequestBody @Valid MaintTaskAssisEditParam editParam) {
        return RestResponse.ok(maintTaskAssistPersonService.updateTaskAssistPerson(editParam));
    }

    /**
     * 删除工单辅助人员信息
     */
    @ApiOperation(value = "删除工单辅助人员信息")
    @AuditLog(title = "删除工单辅助人员信息", desc = "删除工单辅助人员信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/deleteTaskAssistPerson")
    public RestResponse<Boolean> deleteTaskAssistPerson(@RequestParam("id") String id) {
        return RestResponse.ok(maintTaskAssistPersonService.deleteTaskAssistPerson(id));
    }

    /**
     * 查询工单辅助人员信息列表
     */
    @ApiOperation(value = "查询工单辅助人员信息列表")
    @AuditLog(title = "查询工单辅助人员信息列表", desc = "查询工单辅助人员信息列表", businessType = BusinessType.QUERY)
    @GetMapping("/getAssistPersonList")
    public RestResponse<List<MaintTaskAssistListDto>> getAssistPersonList(@RequestParam("taskId") String taskId) {
        return RestResponse.ok(maintTaskAssistPersonService.getAssistPersonList(taskId));
    }

    /**
     * 批量更新工单辅助人员信息列表
     */
    @ApiOperation(value = "批量更新工单辅助人员信息列表")
    @AuditLog(title = "批量更新工单辅助人员信息列表", desc = "批量更新工单辅助人员信息列表", businessType = BusinessType.UPDATE)
    @PostMapping("/saveOrUpdateAssistPersons")
    public RestResponse<Boolean> saveOrUpdateAssistPersons(@RequestBody @Valid List<MaintTaskAssisEditParam> editParamList) {
        return RestResponse.ok(maintTaskAssistPersonService.saveOrUpdateAssistPersons(editParamList));
    }

    /**
     * 根据id获取作业票
     */
    @ApiOperation(value = "根据id获取作业票")
    @GetMapping(value = "/getTaskTicketById")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", dataType = "string", paramType = "query")})
    public RestResponse<TaskTicketDto> getTaskTicketById(@RequestParam("id") String id) {
        return RestResponse.ok(maintTaskService.getTaskTicketById(id));
    }

    /**
     * 修改作业票
     */
    @ApiOperation(value = "修改作业票")
    @AuditLog(title = "工单处理", desc = "修改作业票", businessType = BusinessType.UPDATE)
    @PostMapping("/updateTaskTicket")
    //@Permission("maint:task:update")
    public RestResponse<Boolean> updateTaskTicket(@RequestBody @Valid TaskTicketDto editParam) {
        return RestResponse.ok(maintTaskService.updateTaskTicket(editParam));
    }

    /**
     * 根据id获取评分
     */
    @ApiOperation(value = "根据id获取验收评分")
    @GetMapping(value = "/getGradeById")
    //@Permission("maint:task:list")
    public RestResponse<MaintTaskGradeDto> getGradeById(@RequestParam("id") String id) {
        return RestResponse.ok(maintTaskService.getGradeById(id));
    }

    /**
     * 工单管理修改评分
     */
    @ApiOperation(value = "工单管理修改验收评分")
    @AuditLog(title = "工单管理修改评分", desc = "工单管理修改评分", businessType = BusinessType.UPDATE)
    @PostMapping("/updateTaskGrade")
    //@Permission("maint:task:update")
    public RestResponse<Boolean> updateTaskGrade(@RequestBody MaintTaskGradeDto editParam) {
        return RestResponse.ok(maintTaskService.updateTaskGrade(editParam));
    }

    /**
     * 根据流程实例ID获取维护工单
     *
     * @param processInstanceId
     * @return
     */
    @ApiOperation(value = "根据流程实例获取维护单信息")
    @GetMapping(value = "/getByProcessInstanceId")
    //@Permission("maint:task:list")
    public RestResponse<MaintTaskDto> getByProcessInstanceId(@RequestParam String processInstanceId) {
        return RestResponse.ok(maintTaskService.getByProcessInstanceId(processInstanceId));
    }

    @ApiOperation(value = "维护工单流程提交")
    @PostMapping(value = "/submitProcessTask")
    //@Permission("maint:task:list")
    public RestResponse<Boolean> submitReceiving(@RequestBody TaskSubmitParam taskSubmitParam) {
        boolean b = maintTaskService.submitProcessTask(taskSubmitParam);
        //UserBaseInfo currentUser = PorosContextHolder.getCurrentUser();
        //taskNotifyHandler.dealSendNotify(taskSubmitParam.getTaskId(),currentUser.getUid(),currentUser.getTenantId());
        return RestResponse.ok(b);
    }

    @ApiOperation(value = "批量关闭工单")
    @PostMapping(value = "/closeTask/batch")
    public RestResponse<Boolean> closeTaskBatch(@RequestBody List<TaskSubmitParam> taskSubmitParams) {
        return RestResponse.ok(maintTaskService.closeTaskBatch(taskSubmitParams));
    }

    @ApiOperation(value = "批量操作工单")
    @PostMapping(value = "/batchEdit")
    public RestResponse<String> batchEdit(@RequestBody TaskBatchEditParam param) {
        return RestResponse.ok(maintTaskService.batchEdit(param));
    }

    @ApiOperation(value = "批量改派工单")
    @PostMapping(value = "/batchTransform")
    public RestResponse<Boolean> transformBatch(@RequestBody List<TaskBatchTransformParam> paramList) {
        this.maintTaskService.transformBatch(paramList);
        return RestResponse.ok();
    }

    @ApiOperation(value = "转缺陷")
    @PostMapping(value = "/submitDefect")
    //@Permission("maint:task:list")
    public RestResponse<Boolean> submitDefect(@RequestBody TaskDefectAddParam defectAddParam) {
        return RestResponse.ok(maintTaskService.submitDefect(defectAddParam));
    }


    /**
     * 获取统计需要工单信息
     *
     * @param taskStatisticsReqDto
     * @return
     */
    @ApiOperation(value = "获取统计需要工单信息")
    @PostMapping(value = "/getTaskStatistics")
    public RestResponse<List<TaskStatisticsDto>> getTaskStatistics(@RequestBody TaskStatisticsReqDto taskStatisticsReqDto) {
        return RestResponse.ok(maintTaskService.getTaskStatistics(taskStatisticsReqDto));
    }

    /**
     * 校验备件是否被维护工单使用
     *
     * @param partIds
     * @return
     */
    @ApiOperation(value = "校验备件是否被维护工单使用")
    @PostMapping("/checkPartUsed")
    public RestResponse<List<String>> checkPartUsed(@RequestBody String[] partIds) {
        return RestResponse.ok(maintTaskService.checkPartUsed(partIds));
    }

    /**
     * 校验备件是否被维护计划、维护工单使用
     *
     * @param equipmentIds
     * @return
     */
    @ApiOperation(value = "校验设备是否被维护计划、维护工单使用")
    @PostMapping("/checkEquipmentUsed")
    public RestResponse<List<String>> checkEquipmentUsed(@RequestBody String[] equipmentIds) {
        return RestResponse.ok(maintTaskService.checkEquipmentUsed(equipmentIds));
    }

    /**
     * 获取流程任务id
     */
    @ApiOperation(value = "获取流程任务id")
    @GetMapping(value = "/getTaskId/{id}")
    //@Permission("maint:task:list")
    public RestResponse<String> getTaskIdByProcessInstanceId(@PathVariable("id") String id) {
        return RestResponse.ok(maintTaskService.getProcessTaskId(id, true));
    }

    /**
     * 获取工单关联的设备ids
     */
    @ApiOperation(value = "获取工单关联的设备ids")
    @GetMapping(value = "/getUsedInfoIds")
    //@Permission("maint:task:list")
    public RestResponse<List<String>> getUsedInfoIds() {
        return RestResponse.ok(maintTaskService.getUsedInfoIds());
    }


    @ApiOperation("工作台-工单统计")
    @GetMapping("/workbench/statistics/maintTask")
    public RestResponse<String> getWorkbenchStatisticsOfMaintTask(String param) {
        String[] split = param.split(",");
        return RestResponse.ok(maintTaskService.getWorkbenchStatisticsOfMaintTask(split[0], split[1], split[2]));
    }

    @ApiOperation("工作台-工单超时统计")
    @GetMapping("/workbench/statistics/maintTask/overtime")
    public RestResponse<String> getWorkbenchStatisticsOfMaintTaskOverTime(String param) {
        String[] split = param.split(",");
        return RestResponse.ok(maintTaskService.getWorkbenchStatisticsOfMaintTaskOfOverTime(split[0]));
    }

    @ApiOperation("更新工单完工附件")
    @PostMapping("/update/Task/Attachment")
    public RestResponse updateMaintTaskOfFile(@RequestBody MaintTaskFileParam param) {
        return RestResponse.ok(maintTaskService.updateMaintTaskOfFile(param));
    }

//    @ApiOperation("工作日历")
//    @PostMapping("/workCalendar")
//    public RestResponse getWorkCalendar() {
//        return RestResponse.ok();
//    }

    @ApiOperation("ec单处理-新增故障工单")
    @PostMapping
    public RestResponse<MaintTaskDto> add(@RequestBody @Valid ManualRepairAddParam manualRepairAddParam) {
        return RestResponse.ok(maintTaskService.saveManualRepair(manualRepairAddParam));
    }

    @ApiOperation("更新EC单异常情况")
    @PostMapping("/update/ec/error")
    public RestResponse<Boolean> updateEcError(@RequestBody MaintTaskEcErrorEditParam param) {
        return RestResponse.ok(maintTaskService.updateEcError(param));
    }

    @ApiOperation("分页获取报警关联工单列表")
    @PostMapping("/getListByWarnId")
    public RestResponse<PageResult<WarnTaskPageDto>> getListByWarnId(@RequestBody @Valid WarnQueryParam queryParam) {
        return RestResponse.ok(maintTaskService.getListByWarnId(queryParam));
    }

    @ApiOperation("清除工单及工作流")
    @GetMapping("/markDeletedBatch")
    public RestResponse markDeletedBatch(String id) {
        maintTaskService.markDeletedBatch(id);
        return RestResponse.ok();
    }

    /**
     * 获取设备关联故障树
     * 类型(1故障现象2故障原因3处理措施)
     */
    @GetMapping(value = "/getUsedFaultTree")
    @ApiOperation("取设备关联故障树")
    RestResponse<Map<String, Integer>> getUsedFaultTree(@RequestParam("equipmentId") String equipmentId, @RequestParam("faultTreeIds") List<String> faultTreeIds, @RequestParam("type") Integer type){
        return RestResponse.ok(maintTaskDetailService.getUsedFaultTree(equipmentId, faultTreeIds, type));
    }

    @ApiOperation("工单设备变更")
    @PostMapping("/editEquipmentInfo")
    public RestResponse<Boolean> editEquipmentInfo(@RequestBody TaskEquipmentInfoEditParam param) {
        return RestResponse.ok(maintTaskService.editEquipmentInfo(param));
    }

//    @ApiOperation("工单设备确认变更")
//    @PostMapping("/confirmEditEquipment")
//    public RestResponse<Boolean> updateTask(@RequestBody TaskEquipmentInfoEditParam param) {
//        return RestResponse.ok(maintTaskService.updateTask(param));
//    }

    @ApiOperation("同步更新工作流当前操作人")
    @PostMapping("/updateTaskUids")
    public RestResponse<Boolean> updateTaskUids() {
        return RestResponse.ok(maintTaskService.updateTaskUids());
    }

    @ApiOperation(value = "触发工单临期通知(用作测试)")
    @GetMapping(value = "/releaseTask")
    public RestResponse<Boolean> releaseTask() {
        maintTaskSchedule.releaseTask();
        return RestResponse.ok();
    }

    @ApiOperation(value = "触发故障工单通知(用作测试)")
    @GetMapping(value = "/releaseRepairTask")
    public RestResponse<Boolean> releaseRepairTask() {
        repairTaskNotifySchedule.releaseTask();
        return RestResponse.ok();
    }

    @ApiOperation("重新打开工单")
    @PostMapping("/reopen")
    public RestResponse reopenTask(@RequestBody MaintTaskReopenDto request){
        maintTaskService.reopenTask(request);
        return RestResponse.ok();
    }
}
