package cn.getech.ehm.task.entity;

import cn.getech.poros.framework.common.bean.BaseEntity;
import cn.getech.ehm.common.mp.handler.StringArrayTypeHandler;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;
import java.math.BigDecimal;

/**
 * 作业项目
 *
 * <AUTHOR>
 * @since 2020-08-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("job_standard_item")
public class JobStandardItem extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 作业标准id
     */
    @TableField("job_standard_id")
    private String jobStandardId;

    /**
     * 大类
     */
    @TableField(value = "large_category")
    private String largeCategory;

    /**
     * 小类
     */
    @TableField(value = "sub_category")
    private String subCategory;

    /**
     * 作业类型等级
     */
    @TableField(value = "job_type_level", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] jobTypeLevel;

    /**
     * 作业内容
     */
    @TableField("content")
    private String content;

    /**
     * 标准工时(人*min)
     */
    @TableField("standard_time")
    private BigDecimal standardTime;

    /**
     * 作业时间
     */
    @TableField("working_time")
    private BigDecimal workingTime;

    /**
     * 投入人员
     */
    @TableField("input_person")
    private Integer inputPerson;

    /**
     * 作业方法
     */
    @TableField("method")
    private String method;

    /**
     * 作业方法详情
     */
    @TableField("method_detail")
    private String methodDetail;

    /**
     * 结果类型
     */
    @TableField("job_item_result_type")
    private String jobItemResultType;

    /**
     * 基准目标
     */
    @TableField("benchmark")
    private String benchmark;

    /**
     * 目标值
     */
    @TableField("target_value")
    private BigDecimal targetValue;

    /**
     * 区间最大值
     */
    @TableField("target_max")
    private BigDecimal targetMax;

    /**
     * 区间最小值
     */
    @TableField("target_min")
    private BigDecimal targetMin;

    /**
     * 单位
     */
    @TableField("unit")
    private String unit;

    /**
     * 工具
     */
    @TableField("tool")
    private String tool;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 上传参数监控0否1指标2波形
     */
    @TableField("iot_push")
    private Integer iotPush;

    /**
     * 特殊要求
     */
    @TableField("special_requirements")
    private String specialRequirements;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    @TableField(typeHandler = StringArrayTypeHandler.class)
    private String[] fileIds;
}
