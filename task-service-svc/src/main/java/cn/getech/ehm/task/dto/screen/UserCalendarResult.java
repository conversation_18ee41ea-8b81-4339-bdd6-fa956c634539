package cn.getech.ehm.task.dto.screen;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UserCalendarResult {
/*    @JsonFormat(pattern = "MM-dd", timezone = "GMT+8")
    public Date date;*/

    @ApiModelProperty("接单人uid")
    public String handler;

    @ApiModelProperty("接单人名称")
    public String handlerName;

    @ApiModelProperty("当日接单完成数量")
    public Long takeCompleteCount;

    @ApiModelProperty("当日接单数量")
    public Long takeCount;
}
