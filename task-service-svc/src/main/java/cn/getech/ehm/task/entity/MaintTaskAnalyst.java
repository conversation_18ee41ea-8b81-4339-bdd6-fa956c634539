package cn.getech.ehm.task.entity;

import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <p>
 * 报修工单统计
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("maint_task_analyst")
public class MaintTaskAnalyst extends BaseEntity {

    private static final long serialVersionUID=1L;

    @TableField("deleted")
    private Long deleted;

    @TableField("tenant_id")
    private String tenantId;

    /**
     * 工单id
     */
    @TableField("task_id")
    private String taskId;

    /**
     * 工单编号
     */
    @TableField("task_code")
    private String taskCode;

    /**
     * 工单状态
     */
    @TableField("task_status")
    private Integer taskStatus;

    /**
     * 设备id
     */
    @TableField("equip_id")
    private String equipId;

    /**
     * 设备编号
     */
    @TableField("equip_code")
    private String equipCode;

    private String equipName;

    /**
     * 设备位置id
     */
    @TableField("equip_location_id")
    private String equipLocationId;

    /**
     * 设备位置
     */
    @TableField("equip_location_name")
    private String equipLocationName;

    /**
     * 设备类型id
     */
    @TableField("equip_category_id")
    private String equipCategoryId;

    /**
     * 设备类型
     */
    @TableField("equip_categoty_name")
    private String equipCategotyName;

    /**
     * 报修人
     */
    @TableField("report_uid")
    private String reportUid;

    /**
     * 报修人
     */
    @TableField("report_name")
    private String reportName;

    /**
     * 处理人
     */
    @TableField("handler_uid")
    private String handlerUid;

    /**
     * 处理人
     */
    @TableField("handler_name")
    private String handlerName;

    private String acceptUid;

    private String acceptName;

    /**
     * 是否改派
     */
    @TableField("transfer_flag")
    private Integer transferFlag;

    /**
     * 工单创建时间
     */
    @TableField("task_create_time")
    private Date taskCreateTime;

    /**
     * 工单接单时间
     */
    @TableField("task_receive_time")
    private Date taskReceiveTime;

    /**
     * 工单关闭时间
     */
    @TableField("task_close_time")
    private Date taskCloseTime;

    /**
     * 工单实际耗时
     */
    @TableField("task_working_time_str")
    private Long taskWorkingTimeStr;

    /**
     * 工单检修耗时
     */
    @TableField("task_maint_time_str")
    private Long taskMaintTimeStr;

    /**
     * 接单时长
     */
    @TableField("receive_str")
    private Long receiveStr;

    /**
     * 挂起审批发起时间
     */
    @TableField("handup_submit_time")
    private Date handupSubmitTime;

    /**
     * 挂起审批审批时间
     */
    @TableField("handup_audit_time")
    private Date handupAuditTime;

    /**
     * 挂起审批耗时
     */
    @TableField("handup_cost_str")
    private Long handupCostStr;

    @TableField("handup_submit_time2")
    private Date handupSubmitTime2;

    @TableField("handup_audit_time2")
    private Date handupAuditTime2;

    @TableField("handup_cost_str2")
    private Long handupCostStr2;

    /**
     * 挂起总时长
     */
    @TableField("handup_cost_total")
    private Long handupCostTotal;

    /**
     * 运行转缺陷发起时间
     */
    @TableField("run_defect_submit_time")
    private Date runDefectSubmitTime;

    /**
     * 运行转缺陷审批时间
     */
    @TableField("run_defect_audit_time")
    private Date runDefectAuditTime;

    /**
     * 运行转缺陷耗时
     */
    @TableField("run_defect_cost_str")
    private Long runDefectCostStr;

    @TableField("run_defect_submit_time2")
    private Date runDefectSubmitTime2;

    @TableField("run_defect_audit_time2")
    private Date runDefectAuditTime2;

    @TableField("run_defect_cost_str2")
    private Long runDefectCostStr2;

    /**
     * 验收转缺陷发起时间
     */
    @TableField("check_defect_submit_time")
    private Date checkDefectSubmitTime;

    /**
     * 验收转缺陷审批时间
     */
    @TableField("check_defect_audit_time")
    private Date checkDefectAuditTime;

    /**
     * 验收转缺陷耗时
     */
    @TableField("check_defect_cost_str")
    private Long checkDefectCostStr;

    @TableField("check_defect_submit_time2")
    private Date checkDefectSubmitTime2;

    @TableField("check_defect_audit_time2")
    private Date checkDefectAuditTime2;

    @TableField("check_defect_cost_str2")
    private Long checkDefectCostStr2;

    /**
     * 验收发起时间
     */
    @TableField("check_submit_time")
    private Date checkSubmitTime;

    /**
     * 验收审批时间
     */
    @TableField("check_audit_time")
    private Date checkAuditTime;

    /**
     * 验收耗时
     */
    @TableField("check_cost_str")
    private Long checkCostStr;

    @TableField("check_submit_time2")
    private Date checkSubmitTime2;

    @TableField("check_audit_time2")
    private Date checkAuditTime2;

    @TableField("check_cost_str2")
    private Long checkCostStr2;

    private Long lotoCostStr;

    private Date beginMaintTime;


}
