package cn.getech.ehm.task.dto.task.item;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 工单维保作业
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "MaintTaskItemRelDto", description = "工单维保作业")
public class MaintTaskItemDto {

    @ApiModelProperty(value = "工单id")
    private String taskId;

    @ApiModelProperty(value = "工单作业项统计")
    ItemTotalCountDto countDto;

    @ApiModelProperty(value = "工单作业项详情")
    List<TaskItemDetailDto> itemDetailDtos;

    @ApiModelProperty(value = "实际停机开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginDowntime;

    @ApiModelProperty(value = "实际停机结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDowntime;

    @ApiModelProperty("停机耗时")
    private String downTimeCost;

    @ApiModelProperty(value = "是否停机(0未1是)")
    private Integer stopped;
}