package cn.getech.ehm.task.handler;

import cn.getech.ehm.common.context.UserContextHolder;
import cn.getech.ehm.system.client.NotifyClient;
import cn.getech.ehm.system.dto.notify.*;
import cn.getech.ehm.task.dto.activiti.AuditActivitiServiceResult;
import cn.getech.ehm.task.dto.task.info.MaintTaskDto;
import cn.getech.ehm.task.entity.TaskNotifyConfig;
import cn.getech.ehm.task.service.IMaintTaskService;
import cn.getech.ehm.task.service.ITaskNotifyConfigService;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.permission.client.PorosSecStaffClient;
import cn.getech.poros.permission.dto.PorosSecStaffDto;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TaskNotifyHandler {

    @Autowired
    NotifyClient notifyClient;
    @Autowired
    ITaskNotifyConfigService taskNotifyConfigService;
    @Autowired
    PorosSecStaffClient porosSecStaffClient;

    @Value("${aliyun.sms.default-sign:EHM}")
    private String defaultSign;

    @Value("${aliyun.sms.templates.task:SMS_215338506}")
    private String taskTemplate;

    @Value("${oa.order.jumpUrl:/maint/order/handle/?id=%s&type=edit}")
    private String todoTaskJumpUrl;

    @Value("${oa.baseJumpUrl:http://szporos.csot.tcl.com/web-admin}")
    private String baseJumpUrl;

    public static String REDIS_KEY = "cn.getech.ehm.maint.task.notify";

    @Resource
    @Lazy
    IMaintTaskService maintTaskService;

    @Autowired
    ActivitiHandler activitiHandler;


    public void dealSendNotify(String taskId, String uid, String tenantId) {
        try {
            log.info("触发工单推送通知任务，参数：{},{},{}", taskId, uid, tenantId);
            UserBaseInfo userBaseInfo = new UserBaseInfo();
            userBaseInfo.setUid(uid);
            userBaseInfo.setTenantId(tenantId);
            UserContextHolder.switchContext(userBaseInfo);
            MaintTaskDto dtoById = maintTaskService.getDtoById(taskId);
            this.send(taskId, uid, tenantId, dtoById);
        }catch (Exception e){
            log.error("-----------推送失败");
        }
    }

    @Async
    public void send(String taskId, String uid, String tenantId, MaintTaskDto dtoById) {
        UserBaseInfo userBaseInfo = new UserBaseInfo();
        userBaseInfo.setUid(uid);
        userBaseInfo.setTenantId(tenantId);
        UserContextHolder.switchContext(userBaseInfo);
        List<TaskNotifyConfig> list = taskNotifyConfigService.list();
        TaskNotifyConfig config = null;
        if (CollectionUtils.isNotEmpty(list)) {
            config = list.get(0);
        } else {
            log.info("获取不到配置");
            return;
        }
        log.info("获取到的配置信息：{}", JSON.toJSONString(config));
        if (ObjectUtils.isNull(config)) {
            log.info("无配置，跳过发送");
            return;
        }
        String[] taskType = config.getTaskType();
        String[] notifyType = config.getNotifyType();
        String[] taskStatusType = config.getTaskStatusType();
        Date beginTime = config.getBeginTime();
        Date endTime = config.getEndTime();
        Date now = new Date();
        if (beginTime != null && endTime != null) {
            Calendar beginCalendar = Calendar.getInstance();
            beginCalendar.setTime(now);
            beginCalendar.set(Calendar.HOUR_OF_DAY, DateUtil.hour(beginTime, true));
            beginCalendar.set(Calendar.MINUTE, DateUtil.minute(beginTime));
            beginCalendar.set(Calendar.SECOND, DateUtil.second(beginTime));
            Calendar endCalendar = Calendar.getInstance();
            endCalendar.setTime(now);
            endCalendar.set(Calendar.HOUR_OF_DAY, DateUtil.hour(endTime, true));
            endCalendar.set(Calendar.MINUTE, DateUtil.minute(endTime));
            endCalendar.set(Calendar.SECOND, DateUtil.second(endTime));
            Date resultBeginTime = beginCalendar.getTime();
            Date resultEndTime = endCalendar.getTime();
            if (!resultBeginTime.before(now) || !resultEndTime.after(now)) {
                log.info("不在目标时间段");
                return;
            }
        }

        //MaintTaskDto dtoById = maintTaskService.getDtoById(taskId);
        if (ObjectUtils.isEmpty(dtoById)) {
            log.info("工单信息获取失败");
            return;
        }
        if (!Arrays.asList(taskType).contains(dtoById.getType().toString())) {
            log.info("目标工单类型不推送");
            return;
        }
        if (!Arrays.asList(taskStatusType).contains(dtoById.getStatus().toString())) {
            log.info("目标工单状态不推送");
            return;
        }
        TaskNotifyDto taskNotifyDto = new TaskNotifyDto();
        taskNotifyDto.setTaskId(taskId);
        taskNotifyDto.setNotifyType(notifyType);
        taskNotifyDto.setTaskStatus(dtoById.getStatusName());
        taskNotifyDto.setTaskCode(dtoById.getCode());
        taskNotifyDto.setTaskName(dtoById.getName());
        taskNotifyDto.setTaskType(dtoById.getTypeName());
        taskNotifyDto.setEquipmentId(dtoById.getEquipmentCode());
        taskNotifyDto.setEquipmentName(dtoById.getEquipmentName());
        AuditActivitiServiceResult processTaskUserId = activitiHandler.getProcessTaskUserId(dtoById.getProcessInstanceId());
        String[] processUserArray = processTaskUserId.getProcessUserArray();
        String title = dtoById.getName() + "提醒";
        String content = JSON.toJSONString(taskNotifyDto);
        String url = baseJumpUrl + String.format(todoTaskJumpUrl, taskId);
        NotifyParam notifyParam = new NotifyParam();
        EnumSet<NotifyType> notifyTypes = EnumSet.of(NotifyType.SYSTEM);
        notifyParam.setNotifyTypes(notifyTypes);
        notifyParam = this.generalContent(notifyParam, title, content, url, Arrays.asList(processUserArray));
        log.debug("发送通知，参数：{}", JSON.toJSONString(notifyParam));
        RestResponse<String> stringRestResponse = notifyClient.sendNotify(notifyParam);
        log.debug("发送通知，返回结果：{}", JSON.toJSONString(stringRestResponse));
    }

    private NotifyParam generalContent(NotifyParam notifyParam, String title, String content, String url, List<String> uids) {
        RestResponse<List<PorosSecStaffDto>> userByUids = porosSecStaffClient.findUserByUids(uids);
        if (userByUids.isSuccess()) {
            List<PorosSecStaffDto> porosSecStaffDtos = userByUids.getData();
            List<String> emails = new ArrayList<>();
            List<String> mobiles = new ArrayList<>();
            porosSecStaffDtos.stream().forEach(porosSecStaffDto -> {
                emails.add(porosSecStaffDto.getEmail());
                mobiles.add(porosSecStaffDto.getMobile());
            });
            List<String> _uids = uids.stream().distinct().collect(Collectors.toList());
            List<String> _emails = emails.stream().distinct().collect(Collectors.toList());
            List<String> _mobiles = mobiles.stream().distinct().collect(Collectors.toList());
//            if (notifyParam.getNotifyTypes().contains(NotifyType.SMS)) {
//                Map<String, String> smsParams = new HashMap<>(3);
//                smsParams.put("name", "");
//                smsParams.put("statusValue", "");
//                SMSNotify smsNotify = SMSNotify.builder().sign(defaultSign).template(taskTemplate).params(smsParams).mobiles(_mobiles.toArray(new String[_mobiles.size()])).build();
//                notifyParam.setSmsNotify(smsNotify);
//            }
//            if (notifyParam.getNotifyTypes().contains(NotifyType.EMAIL)) {
//                EmailNotify emailNotify = EmailNotify.builder().title(title).content(content + "<br/>" + url).emails(_emails.toArray(new String[_emails.size()])).build();
//                notifyParam.setEmailNotify(emailNotify);
//            }
//            if (notifyParam.getNotifyTypes().contains(NotifyType.APP)) {
//                PushNotify pushNotify = PushNotify.builder().title(title).content(content).uids(_uids.toArray(new String[_uids.size()])).build();
//                notifyParam.setPushNotify(pushNotify);
//            }
            if (notifyParam.getNotifyTypes().contains(NotifyType.SYSTEM)) {
                SystemNotify systemNotify = SystemNotify.builder().uids(_uids.toArray(new String[_uids.size()])).title(title).content(content).type("warning").topicName("task").build();
                notifyParam.setSystemNotify(systemNotify);
            }
        }
        return notifyParam;
    }
}
