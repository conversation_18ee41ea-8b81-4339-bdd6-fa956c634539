package cn.getech.ehm.task.dto.job;

import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 作业项目查询参数
 * <AUTHOR>
 * @date 2020-08-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ItemQueryParam", description = "作业项目查询参数")
public class ItemQueryParam extends PageParam {

    @ApiModelProperty(value = "作业标准id")
    private String standardId;

}
