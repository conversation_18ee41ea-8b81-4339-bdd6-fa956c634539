package cn.getech.ehm.task.service.impl;

import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.task.dto.CommonConfigDto;
import cn.getech.ehm.task.dto.ticket.JobTicketDto;
import cn.getech.ehm.task.dto.ticket.JobTicketEditDto;
import cn.getech.ehm.task.dto.ticket.JobTicketItemDto;
import cn.getech.ehm.task.entity.JobTicket;
import cn.getech.ehm.task.mapper.JobTicketMapper;
import cn.getech.ehm.task.service.IJobTicketItemService;
import cn.getech.ehm.task.service.IJobTicketService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

/**
 * 作业票imp
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
@Slf4j
@Service
public class JobTicketServiceImpl extends BaseServiceImpl<JobTicketMapper, JobTicket> implements IJobTicketService {

    @Autowired
    private JobTicketMapper jobTicketMapper;
    @Autowired
    private IJobTicketItemService ticketItemService;

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public String saveByParam(JobTicketDto addDto) {
        JobTicket jobTicket = CopyDataUtil.copyObject(addDto, JobTicket.class);
        save(jobTicket);
        if(CollectionUtils.isNotEmpty(addDto.getItemDtos())){
            ticketItemService.saveBatchByParams(addDto.getItemDtos(), jobTicket.getId());
        }
        return jobTicket.getId();
    }

    @Override
    public Boolean updateByParam(JobTicketEditDto editDto) {
        JobTicket jobTicket = CopyDataUtil.copyObject(editDto, JobTicket.class);
        return saveOrUpdate(jobTicket);
    }

    @Override
    public JobTicketDto getDetialDto() {
        JobTicketDto jobTicketDto = new JobTicketDto();
        JobTicket jobTicket = jobTicketMapper.selectOne(null);
        if(null != jobTicket){
            jobTicketDto = CopyDataUtil.copyObject(jobTicket, JobTicketDto.class);
            List<JobTicketItemDto> itemDtos = ticketItemService.getListByTicketId(jobTicket.getId());
            jobTicketDto.setItemDtos(itemDtos);
        }else{
            jobTicketDto.setId(UUID.randomUUID().toString().replace("-",""));
            jobTicketDto.setConfirm(false);
            jobTicketDto.setShowInformation(false);
            jobTicketDto.setShowTicketType(false);
            jobTicketDto.setRepairTaskAudit(false);
        }
        return jobTicketDto;
    }
    @Override
    public JobTicketDto getDto() {
        return  CopyDataUtil.copyObject(jobTicketMapper.selectOne(null), JobTicketDto.class);
    }

    @Override
    public Boolean getRepairTaskAudit(){
        JobTicket jobTicket = jobTicketMapper.selectOne(null);
        if(null != jobTicket){
            return jobTicket.getRepairTaskAudit();
        }
        return false;
    }

    @Override
    public CommonConfigDto commonConfig(){
        CommonConfigDto commonConfigDto = new CommonConfigDto();
        JobTicket jobTicket = jobTicketMapper.selectOne(null);
        if(null != jobTicket){
            commonConfigDto.setConfirm(jobTicket.getConfirm());
            commonConfigDto.setRepairAudit(jobTicket.getRepairTaskAudit());
        }
        return commonConfigDto;
    }

    @Override
    public String getTicketId(){
        JobTicket jobTicket = jobTicketMapper.selectOne(null);
        if(null != jobTicket){
            return jobTicket.getId();
        }
        return null;
    }
}
