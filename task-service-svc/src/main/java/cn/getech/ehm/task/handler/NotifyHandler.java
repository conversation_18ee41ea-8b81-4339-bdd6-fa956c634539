package cn.getech.ehm.task.handler;

import cn.getech.ehm.common.context.UserContextHolder;
import cn.getech.ehm.system.client.NotifyClient;
import cn.getech.ehm.system.dto.notify.*;
import cn.getech.ehm.task.entity.MaintTask;
import cn.getech.ehm.task.entity.MaintTaskConfig;
import cn.getech.ehm.task.enums.*;
import cn.getech.ehm.task.redis.RedisConsumer;
import cn.getech.ehm.task.redis.RedisDelayQueue;
import cn.getech.ehm.task.service.IDefectInfoService;
import cn.getech.ehm.task.service.IMaintTaskConfigService;
import cn.getech.ehm.task.service.IMaintTaskService;
import cn.getech.poros.bpm.client.TaskServiceClient;
import cn.getech.poros.bpm.param.task.TaskCompleteParam;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.framework.common.utils.DateUtils;
import cn.getech.poros.permission.client.PorosSecStaffClient;
import cn.getech.poros.permission.dto.PorosSecStaffDto;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RefreshScope
public class NotifyHandler implements RedisConsumer {
    @Autowired
    NotifyClient notifyClient;
    @Autowired
    IMaintTaskConfigService maintTaskConfigService;
    @Autowired
    PorosSecStaffClient porosSecStaffClient;

    @Value("${aliyun.sms.default-sign:EHM}")
    private String defaultSign;

    @Value("${aliyun.sms.templates.task:SMS_215338506}")
    private String taskTemplate;

    @Value("${spring.redis.testaccept:false}")
    private Boolean testAccept;

    public static String REDIS_KEY = "cn.getech.ehm.maint.task.accept";

    @Resource
    RedisDelayQueue redisDelayQueue;

    @Resource
    IMaintTaskService maintTaskService;

    @Resource
    private IDefectInfoService defectInfoService;

    @Resource
    private TaskServiceClient taskServiceClient;


    @SneakyThrows
    public Boolean pushAcceptTask(String taskId, String tenantId) {
        MaintTask dtoById = (MaintTask) maintTaskService.getById(taskId);
        if (dtoById == null || dtoById.getStatus() != TaskStatusType.CHECK_ACCEPT.getValue()) {
            return false;
        }
        Map<String, Object> data = new HashMap<>();
        long time = System.currentTimeMillis() + 10 * 1000;
        data.put("expireTime", DateUtil.format(new Date(time), "yyyy-MM-dd HH:mm:ss"));
        data.put("acceptStartTime", DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        data.put("taskId", taskId);
        data.put("tenantId", tenantId);
        return redisDelayQueue.push(REDIS_KEY, data, time);
    }

    private boolean startSendByTaskId(String taskId, String tenantId, String acceptStartTime) {
        log.info("开始处理工单超时验收任务，任务ID：{}, tenantId:{}, acceptStartTime:{}", taskId, tenantId, acceptStartTime);
        UserBaseInfo userBaseInfo = new UserBaseInfo();
        userBaseInfo.setUid("admin");
        userBaseInfo.setTenantId(tenantId);
        UserContextHolder.switchContext(userBaseInfo);
        MaintTask dtoById = (MaintTask) maintTaskService.getById(taskId);
        if (dtoById == null) {
            return true;
        }
        if ((dtoById.getType() == TaskType.PLAN.getValue() || dtoById.getType() == TaskType.EC.getValue())
            && dtoById.getEcErrorNum() > 0) {
            return true;
        }
        String configType = "";
        if (dtoById.getStatus() != null && dtoById.getStatus() == TaskStatusType.CHECK_ACCEPT.getValue()) {
            configType = "" + TaskOperationType.OVERTIME_CHECK_RULE.getValue();
        }
        if (StringUtils.isNotBlank(configType)) {
            return this.handleToAcceptTask(new String[]{dtoById.getType().toString(), configType}, acceptStartTime, dtoById);
        } else {
            log.info("不在检查范围内，跳过发送：{}", JSON.toJSONString(dtoById));
        }
        return true;
    }

    private boolean handleToAcceptTask(String[] sceneCode, String acceptStartTime, MaintTask maintTask) {
        log.info("触发工单超时验收任务，参数：{}, 任务开始时间：{}", sceneCode, acceptStartTime);
        UserBaseInfo userBaseInfo = new UserBaseInfo();
        userBaseInfo.setUid(maintTask.getCreateBy());
        userBaseInfo.setTenantId(maintTask.getTenantId());
        UserContextHolder.switchContext(userBaseInfo);
        MaintTaskConfig config = maintTaskConfigService.getConfig(sceneCode[0], sceneCode[1]);
        log.info("获取到的配置信息：{}", JSON.toJSONString(config));
        if (config == null || !config.getIsAlert()) {
            return true;
        }
        String activityId = maintTaskService.getProcessTaskId(maintTask.getProcessInstanceId(), true);
        if (StringUtils.isBlank(activityId)) {
            log.error("activityId is null.");
            return true;
        }
        Date startTime = DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, acceptStartTime);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startTime);
        if (testAccept) {
            calendar.add(Calendar.MINUTE, Integer.parseInt(config.getConfigContent()));
        } else {
            calendar.add(Calendar.HOUR_OF_DAY, Integer.parseInt(config.getConfigContent()));
        }
        Date endTime = calendar.getTime();
        if (System.currentTimeMillis() > endTime.getTime()) {
            TaskCompleteParam taskCompleteParam = new TaskCompleteParam();
            taskCompleteParam.setTaskId(activityId);
            taskCompleteParam.setComment("自动验收");
            RestResponse<Object> completeTask = taskServiceClient.completeTask(taskCompleteParam);
            log.info("工作流返回消息->" + JSONObject.toJSON(completeTask));
            if (completeTask.isOk()) {
                maintTask.setStatus(TaskStatusType.CLOSED.getValue());
                maintTask.setGrade(5);
                maintTask.setCheckAcceptResult(1);
                maintTask.setHandleResult("自动验收");
                maintTask.setComment("自动验收");
                maintTaskService.updateById(maintTask);
                if (maintTask.getSourceType() == TaskSourceType.DEFECT.getValue()) {
                    //缺陷生成的工单，需要同步状态
                    defectInfoService.updateDefectStatusById(maintTask.getSourceId(), DefectStatusEnum.DONE.getValue());
                }
                return true;
            }
        }
        return false;
    }

    private EnumSet<NotifyType> generalNotifyTypes(String[] alertMethod) {
        if (alertMethod == null || alertMethod.length == 0) {
            return EnumSet.noneOf(NotifyType.class);
        }
        EnumSet<NotifyType> result = EnumSet.noneOf(NotifyType.class);
        String alertMethodStr = String.join(",", alertMethod);
        for (NotifyType temp : EnumSet.allOf(NotifyType.class)) {
            if (alertMethodStr.contains(temp.getCode().toString())) {
                result.add(temp);
            }
        }
        return result;
    }

    private NotifyParam generalContent(NotifyParam notifyParam, String title, String content, String url, List<String> uids) {
        RestResponse<List<PorosSecStaffDto>> userByUids = porosSecStaffClient.findUserByUids(uids);
        if (userByUids.isSuccess()) {
            List<PorosSecStaffDto> porosSecStaffDtos = userByUids.getData();
            List<String> emails = new ArrayList<>();
            List<String> mobiles = new ArrayList<>();
            porosSecStaffDtos.stream().forEach(porosSecStaffDto -> {
                emails.add(porosSecStaffDto.getEmail());
                mobiles.add(porosSecStaffDto.getMobile());
            });
            List<String> _uids = uids.stream().distinct().collect(Collectors.toList());
            List<String> _emails = emails.stream().distinct().collect(Collectors.toList());
            List<String> _mobiles = mobiles.stream().distinct().collect(Collectors.toList());
            if (notifyParam.getNotifyTypes().contains(NotifyType.SMS)) {
                Map<String, String> smsParams = new HashMap<>(3);
                smsParams.put("name", "");
                smsParams.put("statusValue", "");
                SMSNotify smsNotify = SMSNotify.builder().sign(defaultSign).template(taskTemplate).params(smsParams).mobiles(_mobiles.toArray(new String[_mobiles.size()])).build();
                notifyParam.setSmsNotify(smsNotify);
            }
            if (notifyParam.getNotifyTypes().contains(NotifyType.EMAIL)) {
                EmailNotify emailNotify = EmailNotify.builder().title(title).content(content + "<br/>" + url).emails(_emails.toArray(new String[_emails.size()])).build();
                notifyParam.setEmailNotify(emailNotify);
            }
            if (notifyParam.getNotifyTypes().contains(NotifyType.APP)) {
                PushNotify pushNotify = PushNotify.builder().title(title).content(content).uids(_uids.toArray(new String[_uids.size()])).build();
                notifyParam.setPushNotify(pushNotify);
            }
            if (notifyParam.getNotifyTypes().contains(NotifyType.SYSTEM)) {
                SystemNotify systemNotify = SystemNotify.builder().uids(_uids.toArray(new String[_uids.size()])).title(title).content(content).type("warning").build();
                notifyParam.setSystemNotify(systemNotify);
            }
        }
        return notifyParam;
    }

    @Override
    public String getKey() {
        return REDIS_KEY;
    }

    @Override
    public void Consumer(Object data, ZSetOperations<String, Object> zSet) {
        try {
            log.info("消息推送被消费：【key=" + getKey() + "】===>" + data);
            Map<String, Object> map = (Map<String, Object>) data;
            String taskId = map.get("taskId").toString();
            String tenantId = map.get("tenantId").toString();
            String acceptStartTime = map.get("acceptStartTime").toString();
            boolean result = this.startSendByTaskId(taskId, tenantId, acceptStartTime);
            if (result) {
                zSet.remove(getKey(), data);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}
