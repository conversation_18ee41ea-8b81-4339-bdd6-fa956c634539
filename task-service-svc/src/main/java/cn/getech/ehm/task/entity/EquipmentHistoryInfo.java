package cn.getech.ehm.task.entity;

import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("equipment_history_info")
public class EquipmentHistoryInfo extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 出库单号
     */
    @TableField("task_id")
    private String taskId;

    /**
     * 变更前设备id
     */
    @TableField("old_equipment_id")
    private String oldEquipmentId;

    /**
     * 变更后设备id
     */
    @TableField("new_equipment_id")
    private String newEquipmentId;

    /**
     * 是否变更(0false1true)
     */
    @TableField("changed")
    private Integer changed;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 是否删除(0false1true)
     */
    @TableField("deleted")
    private Integer deleted;

    @TableField("communicationer_id")
    private String communicationerId;

    @TableField("communicationer_name")
    private String communicationerName;

    @TableField("creater")
    private String creater;

}
