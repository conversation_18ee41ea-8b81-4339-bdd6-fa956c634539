package cn.getech.ehm.task.schedule;

import cn.getech.ehm.common.context.UserContextHolder;
import cn.getech.ehm.equipment.dto.EquipmentSummaryDto;
import cn.getech.ehm.system.client.NotifyClient;
import cn.getech.ehm.system.dto.notify.DingNotify;
import cn.getech.ehm.system.dto.notify.NotifyParam;
import cn.getech.ehm.system.dto.notify.NotifyType;
import cn.getech.ehm.task.dto.task.notify.MaintTaskNotifyDto;
import cn.getech.ehm.task.enmu.NotifyEquipmentManagerType;
import cn.getech.ehm.task.enmu.NotifyObjectType;
import cn.getech.ehm.task.enums.TaskStatusType;
import cn.getech.ehm.task.handler.CommonGetHandler;
import cn.getech.ehm.task.service.IMaintNotifyService;
import cn.getech.ehm.task.service.IMaintTaskService;
import cn.getech.poros.permission.dto.PorosSecStaffDto;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 工单临期/超期地并使其
 * <AUTHOR>
 * @since 2020-08-05
 */
@Slf4j
@Component
public class MaintTaskSchedule {
    @Autowired
    private IMaintTaskService maintTaskService;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private IMaintNotifyService maintNotifyService;
    @Autowired
    private CommonGetHandler commonGetHandler;
    @Autowired
    private NotifyClient notifyClient;
    private final static String PLAN_RELEASE_TASK_LOCK="EHM:MAINT_TASK:RELEASE_LOCK";
    @Value("${task.notify_url:https://admin.premaint.com/web-admin/maint/order/handle/?id=}")
    private String notifyUrl;

    @Value("${task.overtime.autoClose:false}")
    public Boolean enabled;

    /**
     * 每小时触发一次扫描
     */
    @Scheduled(cron = "0 0 0/1 * * ?")
    public void releaseTask() {
        UserContextHolder.defaultContext();
        if (!enabled){
            log.info("超期关闭工单开关未打开");
        }
        log.info("定时执行巡检单临期/超期校验任务开始");
        if (!redisTemplate.opsForValue().setIfAbsent(PLAN_RELEASE_TASK_LOCK,"1",30, TimeUnit.SECONDS)){
            log.info("锁竞争失败，跳过释放");
            return;
        }
        // 获取需要通知的工单
        Date date = new Date();
        //fix:临期通知已经迁移到custom服务-这里注释
//        List<MaintTaskNotifyDto> adventTasks = maintTaskService.getNotifyTask(MaintNotifyType.ADVENT.getValue(), date);
        List<MaintTaskNotifyDto> overdueTaskIds = maintTaskService.getOverdueTaskIds();

//        List<String> notifiedIds = new ArrayList<>();
//        if(CollectionUtils.isNotEmpty(adventTasks)){
//            Map<String, EquipmentSummaryDto> equipmentMap = Maps.newHashMap();
//            List<String> equipmentIds = adventTasks.stream().map(MaintTaskNotifyDto::getEquipmentId).distinct().collect(Collectors.toList());
//            if(CollectionUtils.isNotEmpty(equipmentIds)){
//                EquipmentInfoSearchDto searchDto = new EquipmentInfoSearchDto();
//                searchDto.setEquipmentIds(equipmentIds);
//                equipmentMap = commonGetHandler.getEquipmentSummaryMap(searchDto);
//            }
//            List<Integer> closeStatus = Arrays.asList(TaskStatusType.CHECK_ACCEPT.getValue(), TaskStatusType.CLOSED.getValue(),TaskStatusType.EXCEPTION_CLOSED.getValue());
//            List<MaintTaskNotifyDto> notifyAdventTasks = new ArrayList<>();
//            for(MaintTaskNotifyDto adventTask : adventTasks){
//                //剔除已关闭的工单
//                if(!closeStatus.contains(adventTask.getStatus())) {
//                    notifyAdventTasks.add(adventTask);
//                }
//                notifiedIds.add(adventTask.getNotifyId());
//            }
//            if(CollectionUtils.isNotEmpty(notifyAdventTasks)) {
//                this.buildUidTaskList(notifyAdventTasks, equipmentMap);
//            }
//        }
//        if(CollectionUtils.isNotEmpty(notifiedIds)){
//            //处理完关掉巡检任务通知
//            maintNotifyService.closedTaskNotify(notifiedIds);
//        }

        //超期异常关闭
        if(CollectionUtils.isNotEmpty(overdueTaskIds)){
            maintTaskService.synExceptionClose(overdueTaskIds.stream().map(MaintTaskNotifyDto::getId).distinct().collect(Collectors.toList()));
        }

        log.info("定时执行巡检单临期/超期校验任务结束");
    }

    //聚合需要通知的每个人对应的工单集合
    private void buildUidTaskList(List<MaintTaskNotifyDto> adventTasks, Map<String, EquipmentSummaryDto> equipmentMap){
        Map<String, List<MaintTaskNotifyDto>> uidTaskNumMap = Maps.newHashMap();
        List<String> allUids = new ArrayList<>();
        for(MaintTaskNotifyDto adventTask : adventTasks){
            EquipmentSummaryDto equipmentSummaryDto = equipmentMap.get(adventTask.getEquipmentId());
            /*if(null != equipmentSummaryDto){
                adventTask.setEquipmentName(equipmentSummaryDto.getEquipmentName());
                adventTask.setEquipmentCode(equipmentSummaryDto.getEquipmentCode());
            }
            adventTask.setStatusName(TaskStatusType.getNameByValue(adventTask.getStatus()));*/
            List<Integer> notifyObjects = Arrays.asList(adventTask.getNotifyObjects());
            if(CollectionUtils.isNotEmpty(notifyObjects)){
                List<String> maintainerIds = new ArrayList<>();
                List<String> teamIds = new ArrayList<>();
                List<String> uids = new ArrayList<>();
                if(notifyObjects.contains(NotifyObjectType.HANDLER.getValue())){
                    if(adventTask.getStatus() == TaskStatusType.RECEIVING.getValue() && StringUtils.isNotBlank(adventTask.getAllStaffIds())){
                        maintainerIds.addAll(Arrays.asList(adventTask.getAllStaffIds().split(",")));
                    }else if(StringUtils.isNotBlank(adventTask.getHandler())){
                        uids.add(adventTask.getHandler());
                    }
                }
                if(notifyObjects.contains(NotifyObjectType.EQUIPMENT_MANAGER.getValue()) && null != equipmentSummaryDto
                        && null != adventTask.getEquipmentManager() && adventTask.getEquipmentManager().length > 0){
                    List<Integer> equipmentManager = Arrays.asList(adventTask.getEquipmentManager());
                    if(equipmentManager.contains(NotifyEquipmentManagerType.MANAGE_PRINCIPAL.getValue()) && StringUtils.isNotBlank(equipmentSummaryDto.getManagePrincipal())){
                        uids.add(equipmentSummaryDto.getManagePrincipal());
                    }
                    if(equipmentManager.contains(NotifyEquipmentManagerType.USER_PRINCIPAL.getValue()) && StringUtils.isNotBlank(equipmentSummaryDto.getUsePrincipal())){
                        uids.add(equipmentSummaryDto.getUsePrincipal());
                    }
                    if(equipmentManager.contains(NotifyEquipmentManagerType.TEAM.getValue()) && null != equipmentSummaryDto.getTeamIds() && equipmentSummaryDto.getTeamIds().length > 0){
                        teamIds.addAll(Arrays.asList(equipmentSummaryDto.getTeamIds()));
                    }
                    if(equipmentManager.contains(NotifyEquipmentManagerType.MAINTAINER.getValue()) && null != equipmentSummaryDto.getMaintainerIds() && equipmentSummaryDto.getMaintainerIds().length > 0){
                        maintainerIds.addAll(Arrays.asList(equipmentSummaryDto.getMaintainerIds()));
                    }
                }
                if(notifyObjects.contains(NotifyObjectType.MAINT_PERSON.getValue())){
                    if(null != adventTask.getMaintainerIds() && adventTask.getMaintainerIds().length > 0){
                        maintainerIds.addAll(Arrays.asList(adventTask.getMaintainerIds()));
                    }
                    if(null != adventTask.getTeamIds() && adventTask.getTeamIds().length > 0){
                        teamIds.addAll(Arrays.asList(adventTask.getTeamIds()));
                    }
                }
                /*if(notifyObjects.contains(NotifyObjectType.CUSTOM.getValue())){
                    if(null != adventTask.getCustomUids() && adventTask.getCustomUids().length > 0){
                        uids.addAll(Arrays.asList(adventTask.getCustomUids()));
                    }
                    if(null != adventTask.getCustomRoles() && adventTask.getCustomRoles().length > 0){
                        for(String roleId : adventTask.getCustomRoles()){
                            List<String> roleUids = commonGetHandler.getUserByRoleId(roleId).stream().map(PorosSecStaffDto::getUid).distinct().collect(Collectors.toList());
                            uids.addAll(roleUids);
                        }
                    }
                }*/
                if(CollectionUtils.isNotEmpty(teamIds)){
                    List<String> personIds = commonGetHandler.getMaintainerIdByTeamIds(teamIds);
                    if(CollectionUtils.isNotEmpty(personIds)){
                        maintainerIds.addAll(personIds);
                    }
                }
                if(CollectionUtils.isNotEmpty(maintainerIds)){
                    List<String> maintainerUids = commonGetHandler.getUidsByMaintainerIds(maintainerIds);
                    if(CollectionUtils.isNotEmpty(maintainerUids)){
                        uids.addAll(maintainerUids);
                    }
                }
                if(CollectionUtils.isNotEmpty(uids)){
                    uids = uids.stream().distinct().collect(Collectors.toList());
                    for(String uid : uids){
                        List<MaintTaskNotifyDto> taskList = uidTaskNumMap.get(uid);
                        if(CollectionUtils.isEmpty(taskList)){
                            taskList = new ArrayList<>();
                        }
                        taskList.add(adventTask);
                        uidTaskNumMap.put(uid, taskList);
                    }
                    allUids.addAll(uids);
                }
            }
        }
        //推送
        this.sendNotify(uidTaskNumMap, allUids);
    }

    private void sendNotify(Map<String, List<MaintTaskNotifyDto>> uidTaskNumMap, List<String> allUids){
        if(CollectionUtils.isEmpty(allUids) || uidTaskNumMap.isEmpty()){
            log.debug("不存在需要推送的uid或工单");
        }
        allUids = allUids.stream().distinct().collect(Collectors.toList());
        log.debug("---------------构造uid对应手机号推送");
        Map<String, String> mobileTaskMap = Maps.newHashMap();
        Map<String, String> uidMobileMap = commonGetHandler.getStaffMap(allUids).stream()
                .filter(dto -> StringUtils.isNotBlank(dto.getMobile())).collect(Collectors.toMap(PorosSecStaffDto::getUid, PorosSecStaffDto::getMobile));
        if(!uidMobileMap.isEmpty()){
            for(Map.Entry<String, List<MaintTaskNotifyDto>> entry : uidTaskNumMap.entrySet()){
                List<MaintTaskNotifyDto> taskList = entry.getValue();
                String taskCodeName = taskList.stream().map(dto -> dto.getName() + StringPool.SLASH + dto.getCode()).collect(Collectors.joining(StringPool.COMMA));
                String mobile = uidMobileMap.get(entry.getKey());
                if(StringUtils.isNotBlank(mobile)){
                    mobileTaskMap.put(mobile, "您有" + taskList.size() + "个工单任务即将到期,请及时处理。工单名称/编号为" + taskCodeName);
                }
            }
            log.debug("-------------------开始推送钉钉");
            if(!mobileTaskMap.isEmpty()){
                EnumSet<NotifyType> notifyTypes = EnumSet.noneOf(NotifyType.class);
                NotifyParam notifyParam = NotifyParam.builder().build();
                DingNotify dingNotify = DingNotify.builder().params(mobileTaskMap).build();
                notifyParam.setDingNotify(dingNotify);
                notifyTypes.add(NotifyType.DING);
                notifyParam.setNotifyTypes(notifyTypes);
                notifyClient.sendNotify(notifyParam);
            }
        }

    }
}

