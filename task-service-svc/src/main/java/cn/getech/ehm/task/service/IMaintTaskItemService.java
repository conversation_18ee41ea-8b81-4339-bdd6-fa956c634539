package cn.getech.ehm.task.service;

import cn.getech.ehm.task.dto.task.item.MaintTaskItemDto;
import cn.getech.ehm.task.dto.task.item.TaskItemDetailDto;
import cn.getech.ehm.task.entity.MaintTaskItem;
import cn.getech.poros.framework.common.service.IBaseService;

import java.util.Date;
import java.util.List;

/**
 * <pre>
 * 维护工单、任务关联表 服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
public interface IMaintTaskItemService extends IBaseService<MaintTaskItem> {

    /**
     * 修改任务项
     * @return
     */
    Boolean saveOrUpdateList(List<TaskItemDetailDto> itemDetailDtos, String taskId);


    /**
     * 根据工单id获取详细数据
     * @param taskId
     * @return
     */
    MaintTaskItemDto getByTaskId(String taskId, String standardId, Boolean onlyError);

    /**
     * 推送参数监控
     * @param taskId
     * @return
     */
    Boolean pushToIot(String taskId);

    public List<TaskItemDetailDto> getByTaskIds(List<String> taskIds, Boolean onlyError);

    /**
     * 离线缓存
     */
    List<TaskItemDetailDto> offlineCache(String taskId, String standardId);

}