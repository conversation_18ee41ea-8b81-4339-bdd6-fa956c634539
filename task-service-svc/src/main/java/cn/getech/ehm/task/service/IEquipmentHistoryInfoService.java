package cn.getech.ehm.task.service;

import cn.getech.ehm.task.entity.EquipmentHistoryInfo;
import cn.getech.poros.framework.common.service.IBaseService;
import cn.getech.ehm.task.dto.historyInfo.EquipmentHistoryInfoQueryParam;
import cn.getech.ehm.task.dto.historyInfo.EquipmentHistoryInfoAddParam;
import cn.getech.ehm.task.dto.historyInfo.EquipmentHistoryInfoEditParam;
import cn.getech.ehm.task.dto.historyInfo.EquipmentHistoryInfoDto;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <pre>
 *  服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2022-12-06
 */
public interface IEquipmentHistoryInfoService extends IBaseService<EquipmentHistoryInfo> {

        /**
         * 分页查询，返回Dto
         *
         * @param equipmentHistoryInfoQueryParam
         * @return
         */
        PageResult<EquipmentHistoryInfoDto> pageDto(EquipmentHistoryInfoQueryParam equipmentHistoryInfoQueryParam);

        /**
         * 保存
         * @param equipmentHistoryInfoAddParam
         * @return
         */
        boolean saveByParam(EquipmentHistoryInfoAddParam equipmentHistoryInfoAddParam);

        /**
         * 根据id查询，转dto
         * @param id
         * @return
         */
        EquipmentHistoryInfoDto getDtoById(Long id);

        /**
         * 批量保存
         * @param rows
         */
        boolean saveDtoBatch(List<EquipmentHistoryInfoDto> rows);

        /**
         * 更新
         * @param equipmentHistoryInfoEditParam
         */
        boolean updateByParam(EquipmentHistoryInfoEditParam equipmentHistoryInfoEditParam);

        PageResult<EquipmentHistoryInfoDto> listRecord(EquipmentHistoryInfoQueryParam equipmentHistoryInfoQueryParam);

        PageResult<EquipmentHistoryInfoDto> list(EquipmentHistoryInfoQueryParam equipmentHistoryInfoQueryParam);
}