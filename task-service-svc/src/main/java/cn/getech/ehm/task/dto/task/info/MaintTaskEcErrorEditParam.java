package cn.getech.ehm.task.dto.task.info;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 维护工单主表 返回数据模型
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "MaintTaskEcErrorEditParam", description = "MaintTaskEcErrorEditParam")
public class MaintTaskEcErrorEditParam {

    @ApiModelProperty(value = "工单id")
    private String id;

//    @ApiModelProperty("点检单忽略数量")
//    private Integer ecErrorNum;

    @ApiModelProperty("点检异常处理方式")
    private Integer ecErrorResult;

    @ApiModelProperty("点检单处理结果")
    private String ecErrorDealContent;

    @ApiModelProperty("ec错误关联工单id")
    private String ecErrorTaskId;

    @ApiModelProperty("ec错误关联工单号")
    private String ecErrorTaskCode;

    @ApiModelProperty("ec错误关联工单名称")
    private String ecErrorTaskName;
}