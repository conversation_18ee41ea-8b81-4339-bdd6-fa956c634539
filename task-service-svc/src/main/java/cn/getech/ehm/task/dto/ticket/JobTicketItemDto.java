package cn.getech.ehm.task.dto.ticket;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 作业票内容
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "JobTicketItemDto", description = "作业票内容")
public class JobTicketItemDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "作业票id")
    private String ticketId;

    @ApiModelProperty(value = "安全确认内容")
    private String content;

    @ApiModelProperty(value = "内容类型1风险提示2安全措施")
    private Integer contentType;

    @ApiModelProperty(value = "作业票类型")
    private String[] ticketTypes;

    @ApiModelProperty(value = "作业票类型值")
    private String ticketTypeNames;
}