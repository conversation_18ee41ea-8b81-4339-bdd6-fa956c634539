package cn.getech.ehm.task.service;

import cn.getech.ehm.task.dto.task.repair.TaskFaultEditParam;
import cn.getech.ehm.task.dto.task.repair.MaintTaskFaultDto;
import cn.getech.ehm.task.entity.MaintTaskRepair;
import cn.getech.poros.framework.common.service.IBaseService;
import java.util.List;
import java.util.Map;

/**
 * <pre>
 * 维护工单详情表 服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
public interface IMaintTaskRepairService extends IBaseService<MaintTaskRepair> {
    /**
     * 根据工单id获取明细信息
     * @param taskId
     * @return
     */
    MaintTaskFaultDto getFaultDtoById(String taskId);

    /**
     * 修改任务项
     * @param editParam
     * @return
     */
    Boolean updateFault(TaskFaultEditParam editParam);

    /**
     * 获取设备关联故障树出现次数
     * 类型(1故障现象2故障原因3处理措施)
     */
    Map<String, Integer> getUsedFaultTree(String equipmentId, List<String> faultTreeIds, Integer type);

    /**
     * 获取故障明细
     * @param taskIds
     * @return
     */
    Map<String, MaintTaskFaultDto> getFaultDtoByTaskIds(List<String> taskIds);
}