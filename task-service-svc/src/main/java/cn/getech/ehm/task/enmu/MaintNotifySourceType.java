package cn.getech.ehm.task.enmu;

import cn.getech.ehm.common.dto.EnumListDto;

import java.util.ArrayList;
import java.util.List;

/**
 * 通知来源枚举
 * <AUTHOR>
 * @date 2020/3/3
 */
public enum MaintNotifySourceType {
    PLAN(1, "维保计划"),
    TASK(2, "维保工单");


    MaintNotifySourceType(int value, String name) {
        this.value = value;
        this.name = name;
    }

    private int value;

    private String name;

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static List<EnumListDto> getList(){
        List<EnumListDto> enumListDtos = new ArrayList<>();
        for(MaintNotifySourceType maintNotifyType : MaintNotifySourceType.values()){
            EnumListDto enumListDto = new EnumListDto();
            enumListDto.setValue(maintNotifyType.value);
            enumListDto.setName(maintNotifyType.name);
            enumListDtos.add(enumListDto);
        }
        return enumListDtos;
    }
}
