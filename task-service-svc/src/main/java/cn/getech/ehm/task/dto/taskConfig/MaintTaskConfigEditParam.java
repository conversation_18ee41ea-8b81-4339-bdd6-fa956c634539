package cn.getech.ehm.task.dto.taskConfig;

import cn.getech.poros.framework.common.param.ApiParam;
import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <pre>
 * 工单配置信息 编辑参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-04-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MaintTaskConfig编辑", description = "工单配置信息编辑参数")
public class MaintTaskConfigEditParam extends ApiParam {

    @ApiModelProperty(value = "")
    private String id;

    @ApiModelProperty(value = "")
    private String updateBy;

    @ApiModelProperty(value = "")
    private Date updateTime;

    @ApiModelProperty(value = "")
    private String remark;

    @ApiModelProperty("工单类型")
    private Integer taskType;

    @ApiModelProperty("配置类型")
    private Integer configType;

    @ApiModelProperty("配置内容")
    private String configContent;

    @ApiModelProperty("开关")
    private Boolean isAlert;

}
