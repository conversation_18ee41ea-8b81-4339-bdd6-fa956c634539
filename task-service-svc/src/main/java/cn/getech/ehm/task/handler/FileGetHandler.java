package cn.getech.ehm.task.handler;

import cn.getech.ehm.base.client.BaseServiceClient;
import cn.getech.ehm.base.dto.AttachmentClientDto;
import cn.getech.poros.framework.common.api.RestResponse;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FileGetHandler {
    @Autowired
    private BaseServiceClient baseServiceClient;

    public Map<String, AttachmentClientDto> getAttachmentMap(List<String> picIdList) {
        Map<String, AttachmentClientDto> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(picIdList)) {
            List<String> allPicIds = new ArrayList<>();
            for (String picIdStr : picIdList) {
                String[] strs = picIdStr.split(StringPool.COMMA);
                allPicIds.addAll(Arrays.asList(strs));
            }
            allPicIds = allPicIds.stream().distinct().collect(Collectors.toList());
            RestResponse<Map<String, AttachmentClientDto>> restResponse = baseServiceClient.getAttachmentMap(allPicIds.toArray(new String[allPicIds.size()]));
            if (restResponse.isOk()) {
                map = restResponse.getData();
            } else {
                log.error("获取附件失败");
            }
        }
        return map;
    }

    public List<AttachmentClientDto> getAttachmentList(List<String> picIdList) {
        Map<String, AttachmentClientDto> map = new HashMap<>();
        List<AttachmentClientDto> result = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(picIdList)) {
            List<String> allPicIds = new ArrayList<>();
            for (String picIdStr : picIdList) {
                String[] strs = picIdStr.split(StringPool.COMMA);
                allPicIds.addAll(Arrays.asList(strs));
            }
            allPicIds = allPicIds.stream().distinct().collect(Collectors.toList());
            RestResponse<Map<String, AttachmentClientDto>> restResponse = baseServiceClient.getAttachmentMap(allPicIds.toArray(new String[allPicIds.size()]));
            if (restResponse.isOk()) {
                map = restResponse.getData();
                for (String key:map.keySet()){
                    result.add(map.get(key));
                }
            } else {
                log.error("获取附件失败");
            }
        }
        return result;
    }

    public List<AttachmentClientDto> buildPic(String[] picStrs, Map<String, AttachmentClientDto> map) {
        List<AttachmentClientDto> picUrls = new ArrayList<>(picStrs.length);
        for (String picId : picStrs) {
            AttachmentClientDto attachmentClientDto = map.get(picId);
            if (null != attachmentClientDto) {
                picUrls.add(attachmentClientDto);
            }
        }
        return picUrls;
    }
}
