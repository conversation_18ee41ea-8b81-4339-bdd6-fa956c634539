package cn.getech.ehm.task.dto.analyst;

import cn.getech.ehm.task.entity.MaintTaskAnalyst;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.bean.BaseEntity;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <pre>
 * 报修工单统计 参数实体映射
 * </pre>
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
@Mapper(componentModel = "spring",imports = {BaseEntity.class})
public interface  MaintTaskAnalystParamMapper{

    /**
     * 新增参数转换为实体
     *
     * @param maintTaskAnalystAddParam
     * @return
     */
        MaintTaskAnalyst addParam2Entity(MaintTaskAnalystAddParam maintTaskAnalystAddParam);

    /**
     * 编辑参数转换为实体
     * @param maintTaskAnalystEditParam
     * @return
     */
        MaintTaskAnalyst editParam2Entity(MaintTaskAnalystEditParam maintTaskAnalystEditParam);

    /**
     * 实体转换为Dto
     * @param maintTaskAnalyst
     * @return
     */
        MaintTaskAnalystDto entity2Dto(MaintTaskAnalyst maintTaskAnalyst);

    /**
     * 分页实体转DTO
     * @param page
     * @return
     */
    PageResult<MaintTaskAnalystDto> pageEntity2Dto(PageResult<MaintTaskAnalyst> page);


    /**
     * dto集合转entity集合
     * @param rows
     * @return
     */
    List<MaintTaskAnalyst> dtoList2Entity(List<MaintTaskAnalystDto> rows);

}
