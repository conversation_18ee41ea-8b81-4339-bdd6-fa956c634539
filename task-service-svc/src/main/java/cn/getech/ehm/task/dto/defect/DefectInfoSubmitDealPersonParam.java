package cn.getech.ehm.task.dto.defect;

import cn.getech.poros.framework.common.param.ApiParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <pre>
 * 缺陷信息 编辑参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-02-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DefectInfoSubmitDealPersonParam", description = "缺陷信息编辑参数")
public class DefectInfoSubmitDealPersonParam extends ApiParam {


    @ApiModelProperty(value = "截止日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @NotNull
    private Date endTime;

    @ApiModelProperty(value = "处理人ids")
    @NotEmpty
    private String[] dealPersonIds;

    @ApiModelProperty(value = "建议处理方案")
    private String suggestDealContent;

    @ApiModelProperty(value = "")
    private String id;

}
