package cn.getech.ehm.task.controller;

import cn.getech.ehm.task.dto.MonthNode;
import cn.getech.ehm.task.dto.TypeMonthNode;
import cn.getech.ehm.task.dto.NameNode;
import cn.getech.ehm.task.dto.task.info.MaintTaskReportQueryParam;
import cn.getech.ehm.task.dto.task.statistics.TaskStatisticsSearchDto;
import cn.getech.ehm.task.service.IMaintTaskService;
import cn.getech.poros.framework.common.api.RestResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 工单统计控制器
 *
 * <AUTHOR>
 * @since 2021-01-13
 */
@RestController
@RequestMapping("/taskPartRel")
@Api(tags = "工单统计接口")
@Slf4j
public class MaintTaskReportController {

    @Autowired
    private IMaintTaskService maintTaskService;

    /**
     * 工单类型统计
     */
    @ApiOperation("工单类型统计-饼图")
    @PostMapping("/typeReport")
    public RestResponse<List<NameNode>> typeReport(@Valid @RequestBody MaintTaskReportQueryParam maintTaskReportQueryParam){
            return RestResponse.ok(maintTaskService.typeReport(maintTaskReportQueryParam));
    }

    /**
     * 工单类型月度统计
     */
    @ApiOperation("工单类型月度统计-柱状图")
    @PostMapping("/typeMonthlyReport")
    public RestResponse<List<TypeMonthNode>> typeMonthlyReport(@Valid @RequestBody MaintTaskReportQueryParam maintTaskReportQueryParam){
        return RestResponse.ok(maintTaskService.typeMonthlyReport(maintTaskReportQueryParam));
    }

    /**
     * 工单状态统计
     */
    @ApiOperation("工单状态统计-饼图")
    @PostMapping("/statusReport")
    public RestResponse<List<NameNode>> statusReport(@Valid @RequestBody MaintTaskReportQueryParam maintTaskReportQueryParam){
        return RestResponse.ok(maintTaskService.statusReport(maintTaskReportQueryParam));
    }

    /**
     * 工单工时月度统计
     */
    @ApiOperation("工单工时月度统计-折线图")
    @GetMapping("/maintTimeMonthlyReport")
    public RestResponse<List<MonthNode>> maintTimeMonthlyReport(@Valid MaintTaskReportQueryParam maintTaskReportQueryParam){
        return RestResponse.ok(maintTaskService.maintTimeMonthlyReport(maintTaskReportQueryParam));
    }
}
