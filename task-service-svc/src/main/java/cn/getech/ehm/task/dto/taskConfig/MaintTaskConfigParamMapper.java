package cn.getech.ehm.task.dto.taskConfig;

import cn.getech.poros.framework.common.bean.BaseEntity;
import cn.getech.ehm.task.entity.MaintTaskConfig;
import org.mapstruct.Mapper;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <pre>
 * 工单配置信息 参数实体映射
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-04-21
 */
@Mapper(componentModel = "spring",imports = {BaseEntity.class})
public interface  MaintTaskConfigParamMapper{

    /**
     * 新增参数转换为实体
     *
     * @param maintTaskConfigAddParam
     * @return
     */
    MaintTaskConfig addParam2Entity(MaintTaskConfigAddParam maintTaskConfigAddParam);

    /**
     * 编辑参数转换为实体
     * @param maintTaskConfigEditParam
     * @return
     */
    MaintTaskConfig editParam2Entity(MaintTaskConfigEditParam maintTaskConfigEditParam);

    /**
     * 实体转换为Dto
     * @param maintTaskConfig
     * @return
     */
    MaintTaskConfigDto entity2Dto(MaintTaskConfig maintTaskConfig);

    /**
     * 分页实体转DTO
     * @param page
     * @return
     */
    PageResult<MaintTaskConfigDto> pageEntity2Dto(PageResult<MaintTaskConfig> page);


    /**
     * dto集合转entity集合
     * @param rows
     * @return
     */
    List<MaintTaskConfig> dtoList2Entity(List<MaintTaskConfigDto> rows);

}
