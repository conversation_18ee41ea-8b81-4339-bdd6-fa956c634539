package cn.getech.ehm.task.dto.task.performance;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * 个人绩效查询参数对象
 *
 * <AUTHOR>
 * @date 2021-01-18
 */
@Data
@ApiModel(value = "PersonalPerformanceTopQueryParam", description = "个人绩效查询参数对象")
public class PersonalPerformanceTopQueryParam {

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "开始时间")
    Date beginTime;

    @ApiModelProperty(value = "班组id")
    List<String> teamIds;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "结束时间")
    Date endTime;

    @ApiModelProperty(value = "需要查询的前N")
    Integer topN;
}
