package cn.getech.ehm.task.dto.task.info;

import cn.getech.poros.framework.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * MaintTaskOvertimeExcelDto
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "MaintTaskErrorExcelDto", description = "MaintTaskErrorExcelDto")
public class MaintTaskErrorExcelDto {

    @ApiModelProperty(value = "序号")
    @Excel(name = "序号", cellType = Excel.ColumnType.STRING)
    private String taskNo;

    @ApiModelProperty(value = "关闭时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @Excel(name = "关闭时间", cellType = Excel.ColumnType.STRING)
    private String updateTimeStr;

    @ApiModelProperty(value = "工单名称/编号")
    @Excel(name = "工单名称/编号", cellType = Excel.ColumnType.STRING)
    private String nameStr;

    @ApiModelProperty(value = "工单类型")
    @Excel(name = "工单类型", cellType = Excel.ColumnType.STRING)
    private String typeName;

    @ApiModelProperty("处理人")
    @Excel(name = "处理人", cellType = Excel.ColumnType.STRING)
    private String handlerName;

    @ApiModelProperty("关闭原因")
    @Excel(name = "关闭原因", cellType = Excel.ColumnType.STRING)
    private String closeReason;

    @ApiModelProperty(value = "设备名称")
    @Excel(name = "设备名称", cellType = Excel.ColumnType.STRING)
    private String equipmentName;

    @ApiModelProperty(value = "设备编码")
    @Excel(name = "设备编码", cellType = Excel.ColumnType.STRING)
    private String equipmentCode;

    @ApiModelProperty(value = "设备类型")
    @Excel(name = "设备类型", cellType = Excel.ColumnType.STRING)
    private String equipmentCategoryName;

}