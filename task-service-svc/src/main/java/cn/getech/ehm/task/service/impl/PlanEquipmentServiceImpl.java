package cn.getech.ehm.task.service.impl;

import cn.getech.ehm.base.client.BaseServiceClient;
import cn.getech.ehm.base.dto.FaultKnowledgeResDto;
import cn.getech.ehm.base.dto.FaultKnowledgeSearchDto;
import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.equipment.client.EquipmentClient;
import cn.getech.ehm.equipment.dto.EquipmentListDto;
import cn.getech.ehm.task.dto.plan.*;
import cn.getech.ehm.task.entity.*;
import cn.getech.ehm.task.enums.TaskJobType;
import cn.getech.ehm.task.mapper.PlanEquipmentMapper;
import cn.getech.ehm.task.service.IJobStandardService;
import cn.getech.ehm.task.service.IMaintPlanService;
import cn.getech.ehm.task.service.IPlanEquipmentService;
import cn.getech.ehm.task.service.ITriggerTimeService;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.poros.framework.common.utils.StringPool;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 维保对象 服务实现类
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
@Slf4j
@Service
public class PlanEquipmentServiceImpl extends BaseServiceImpl<PlanEquipmentMapper, PlanEquipment> implements IPlanEquipmentService {

    @Autowired
    private PlanEquipmentMapper planEquipmentMapper;
    @Autowired
    private EquipmentClient equipmentClient;
    @Autowired
    private BaseServiceClient baseServiceClient;
    @Autowired
    private IMaintPlanService maintPlanService;
    @Autowired
    private ITriggerTimeService triggerTimeService;
    @Autowired
    private IJobStandardService jobStandardService;

    @Override
    public Boolean saveOrUpdateDto(List<PlanEquipmentEditDto> rows, String planId, Boolean add) {
        this.deleteByPlanId(planId);
        if(CollectionUtils.isNotEmpty(rows)) {
            List<PlanEquipment> planEquipments = new ArrayList<>(rows.size());
            List<String> standardIds = new ArrayList<>(rows.size());
            for (PlanEquipmentEditDto row : rows) {
                PlanEquipment planEquipment = CopyDataUtil.copyObject(row, PlanEquipment.class);
                if(add){
                    //复制新增设置id为空
                    planEquipment.setId(null);
                }
                planEquipment.setPlanId(planId);
                planEquipments.add(planEquipment);
                standardIds.add(row.getStandardId());
            }
            standardIds = standardIds.stream().distinct().collect(Collectors.toList());
            jobStandardService.updateStatus(standardIds, StaticValue.ONE);
            return saveOrUpdateBatch(planEquipments);
        }
        return true;
    }

    @Override
    public Boolean saveByParam(PlanEquipmentEditDto editDto){
        PlanEquipment planEquipment = CopyDataUtil.copyObject(editDto, PlanEquipment.class);
        return save(planEquipment);
    }

    @Override
    public List<PlanEquipmentDto> getListByPlanId(String planId) {
        //目前只有单个维保对象，但是按照多个来设计
        List<PlanEquipmentDto> planEquipmentDtos = planEquipmentMapper.getListByPlanId(planId);
        if(CollectionUtils.isNotEmpty(planEquipmentDtos)){
            List<String> infoCategoryIds = new ArrayList<>();
            List<String> infoLocationIds = new ArrayList<>();
            List<String> equipmentIds = new ArrayList<>();
            List<String> maintainerIds = new ArrayList<>();
            List<String> teamIds = new ArrayList<>();
            List<String> phenomenonIds = new ArrayList<>();
            for(PlanEquipmentDto planEquipmentDto : planEquipmentDtos) {
                infoCategoryIds.add(planEquipmentDto.getInfoCategoryId());
                infoLocationIds.add(planEquipmentDto.getInfoLocationId());
                if(null != planEquipmentDto.getEquipmentIds() && planEquipmentDto.getEquipmentIds().length > 0){
                    equipmentIds.addAll(Arrays.asList(planEquipmentDto.getEquipmentIds()));
                }
                if(null != planEquipmentDto.getMaintainerIds() && planEquipmentDto.getMaintainerIds().length > 0){
                    maintainerIds.addAll(Arrays.asList(planEquipmentDto.getMaintainerIds()));
                }
                if(null != planEquipmentDto.getTeamIds() && planEquipmentDto.getTeamIds().length > 0){
                    teamIds.addAll(Arrays.asList(planEquipmentDto.getTeamIds()));
                }
                if(null != planEquipmentDto.getFaultPhenomenonIds() && planEquipmentDto.getFaultPhenomenonIds().length > 0){
                    phenomenonIds.addAll(Arrays.asList(planEquipmentDto.getFaultPhenomenonIds()));
                }
            }
            infoCategoryIds = infoCategoryIds.stream().distinct().collect(Collectors.toList());
            infoLocationIds = infoLocationIds.stream().distinct().collect(Collectors.toList());
            equipmentIds = equipmentIds.stream().distinct().collect(Collectors.toList());
            maintainerIds = maintainerIds.stream().distinct().collect(Collectors.toList());
            teamIds = teamIds.stream().distinct().collect(Collectors.toList());
            Map<String, String> categoryNameMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(infoCategoryIds)) {
                RestResponse<Map<String, String>> categoryRes = equipmentClient.getCategoryMapByIds(infoCategoryIds.toArray(new String[infoCategoryIds.size()]));
                if(categoryRes.isOk()){
                    categoryNameMap = categoryRes.getData();
                }else{
                    log.error("获取设备类型失败");
                }
            }
            Map<String, String> locationNameMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(infoLocationIds)) {
                RestResponse<Map<String, String>> locationRes = equipmentClient.getLocationMapByIds(infoLocationIds.toArray(new String[infoLocationIds.size()]));
                if(locationRes.isOk()){
                    locationNameMap = locationRes.getData();
                }else{
                    log.error("获取设备位置失败");
                }
            }
            Map<String, EquipmentListDto> equipmentMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(equipmentIds)) {
                RestResponse<Map<String, EquipmentListDto>> equipmentRes = equipmentClient.getListByIds(equipmentIds.toArray(new String[equipmentIds.size()]));
                if (equipmentRes.isOk()) {
                    equipmentMap = equipmentRes.getData();

                } else {
                    log.error("获取设备信息失败");
                }
            }
            Map<String, String> personNameMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(maintainerIds)) {
                RestResponse<Map<String, String>> personRes = baseServiceClient.getPersonMapByIds(maintainerIds.toArray(new String[maintainerIds.size()]));
                if(personRes.isOk()){
                    personNameMap = personRes.getData();
                }else{
                    log.error("获取指定维护人员失败");
                }
            }
            Map<String, String> teamNameMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(teamIds)) {
                RestResponse<Map<String, String>> teamRes = baseServiceClient.getTeamMapByIds(teamIds.toArray(new String[maintainerIds.size()]));
                if(teamRes.isOk()){
                    teamNameMap = teamRes.getData();
                }else{
                    log.error("获取指定维护班组失败");
                }
            }
            Map<String, FaultKnowledgeResDto> faultPhenomenonMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(phenomenonIds)){
                FaultKnowledgeSearchDto searchDto = new FaultKnowledgeSearchDto();
                searchDto.setType(StaticValue.ONE);
                searchDto.setIds(phenomenonIds);
                RestResponse<Map<String, FaultKnowledgeResDto>> restResponse = baseServiceClient.faultKnowledgeMap(searchDto);
                if(restResponse.isOk()){
                    faultPhenomenonMap = restResponse.getData();
                }else{
                    log.error("获取故障现象失败");
                }
            }
            for(PlanEquipmentDto planEquipmentDto : planEquipmentDtos) {
                planEquipmentDto.setInfoCategoryName(categoryNameMap.get(planEquipmentDto.getInfoCategoryId()));
                planEquipmentDto.setInfoLocationName(locationNameMap.get(planEquipmentDto.getInfoLocationId()));
                if(null != planEquipmentDto.getEquipmentIds() && planEquipmentDto.getEquipmentIds().length > 0){
                    buildEquipmentNames(planEquipmentDto.getEquipmentIds(), equipmentMap, planEquipmentDto);
                }
                if(null != planEquipmentDto.getMaintainerIds() && planEquipmentDto.getMaintainerIds().length > 0){
                    buildPersonNames(planEquipmentDto.getMaintainerIds(), personNameMap, planEquipmentDto);
                }
                if(null != planEquipmentDto.getTeamIds() && planEquipmentDto.getTeamIds().length > 0){
                    buildTeamNames(planEquipmentDto.getTeamIds(), teamNameMap, planEquipmentDto);
                }
                if(null != planEquipmentDto.getFaultPhenomenonIds() && planEquipmentDto.getFaultPhenomenonIds().length > 0){
                    List<FaultKnowledgeResDto> faultPhenomenonDtos = new ArrayList<>();
                    for(String faultPhenomenonId : planEquipmentDto.getFaultPhenomenonIds()){
                        FaultKnowledgeResDto faultKnowledgeResDto = faultPhenomenonMap.get(faultPhenomenonId);
                        if(null != faultKnowledgeResDto){
                            faultPhenomenonDtos.add(faultKnowledgeResDto);
                        }
                    }
                    planEquipmentDto.setFaultPhenomenonDtos(faultPhenomenonDtos);
                }
            }
        }
        return planEquipmentDtos;
    }

    /**
     * 构造设备类型名称集合
     * @param equipmentIds
     * @return
     */
    private void buildEquipmentNames(String[] equipmentIds, Map<String, EquipmentListDto> equipmentMap, PlanEquipmentDto planEquipmentDto){
        List<String> names = new ArrayList<>();
        List<PlanEquipmentDetailDto> detailDtos = new ArrayList<>();
        for(String equipmentId : equipmentIds){
            EquipmentListDto equipmentListDto = equipmentMap.get(equipmentId);
            if(null != equipmentListDto){
                names.add(equipmentListDto.getEquipmentName() + StringPool.SLASH + equipmentListDto.getEquipmentCode());
                PlanEquipmentDetailDto detailDto = new PlanEquipmentDetailDto();
                detailDto.setId(equipmentListDto.getEquipmentId());
                detailDto.setName(equipmentListDto.getEquipmentName());
                detailDto.setCode(equipmentListDto.getEquipmentCode());
                detailDtos.add(detailDto);
            }
        }
        if(CollectionUtils.isNotEmpty(names)){
            //拼接找到的名称
            planEquipmentDto.setEquipmentNames(StringUtils.join(names.toArray(), StringPool.COMMA));
            planEquipmentDto.setEquipmentDtos(detailDtos);
        }
    }

    /**
     * 构造维护人员名称集合
     * @param maintainerIds
     * @return
     */
    private void buildPersonNames(String[] maintainerIds, Map<String, String> personNameMap, PlanEquipmentDto planEquipmentDto){
        List<String> names = new ArrayList<>();
        List<PlanEquipmentDetailDto> detailDtos = new ArrayList<>();
        for(String maintainerId : maintainerIds){
            String name = personNameMap.get(maintainerId);
            if(StringUtils.isNotBlank(name)){
                names.add(name);
                PlanEquipmentDetailDto detailDto = new PlanEquipmentDetailDto();
                detailDto.setId(maintainerId);
                detailDto.setName(name);
                detailDtos.add(detailDto);
            }
        }
        if(CollectionUtils.isNotEmpty(names)){
            //拼接找到的名称
            planEquipmentDto.setMaintainerNames(StringUtils.join(names.toArray(), StringPool.COMMA));
            planEquipmentDto.setMaintainerDtos(detailDtos);
        }
    }

    /**
     * 构造维护班组名称集合
     * @param teamIds
     * @return
     */
    private void buildTeamNames(String[] teamIds, Map<String, String> teamNameMap, PlanEquipmentDto planEquipmentDto){
        List<String> names = new ArrayList<>();
        List<PlanEquipmentDetailDto> detailDtos = new ArrayList<>();
        for(String teamId : teamIds){
            String name = teamNameMap.get(teamId);
            if(StringUtils.isNotBlank(name)){
                names.add(name);
                PlanEquipmentDetailDto detailDto = new PlanEquipmentDetailDto();
                detailDto.setId(teamId);
                detailDto.setName(name);
                detailDtos.add(detailDto);
            }
        }
        if(CollectionUtils.isNotEmpty(names)){
            //拼接找到的名称
            planEquipmentDto.setTeamNames(StringUtils.join(names.toArray(), StringPool.COMMA));
            planEquipmentDto.setTeamDtos(detailDtos);
        }
    }

    private Boolean deleteByPlanId(String planId){
        LambdaQueryWrapper<PlanEquipment> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(PlanEquipment::getPlanId, planId);
        return planEquipmentMapper.delete(wrapper) > 0;
    }

    @Override
    public List<String> getUsedInfoIds(String[] equipmentIds){
        return new ArrayList<>();
    }

    @Override
    public Map<String, SynPlanEquipmentDto> getMapByPlanIds(List<String> planIds){
        Map<String, SynPlanEquipmentDto> map = new HashMap<>();
        List<SynPlanEquipmentDto> synPlanEquipmentDtos = planEquipmentMapper.getListByPlanIds(planIds);
        if(CollectionUtils.isNotEmpty(synPlanEquipmentDtos)) {
            for(SynPlanEquipmentDto synPlanEquipmentDto : synPlanEquipmentDtos){
                map.put(synPlanEquipmentDto.getPlanId(), synPlanEquipmentDto);
            }
        }return map;
    }

    @Override
    public Boolean createTriggerTime(String planId){
        //编辑后再次审核通过，需要清理未生成的计划单
        List<String> planIds = new ArrayList<>();
        planIds.add(planId);
        triggerTimeService.deleteByPlanIds(planIds);

        List<TriggerTimeDto> triggerTimeDtos = new ArrayList<>();
        Map<String, SynPlanEquipmentDto> planEquipmentMap = this.getMapByPlanIds(planIds);
        //获取对象计划单信息以及维保计划信息
        SynMaintPlanDto synMaintPlanDto = maintPlanService.getSynById(planId);
        if(null != synMaintPlanDto) {
            if(synMaintPlanDto.getJobType().equals(TaskJobType.CBM.getValue())){
                //CBM不进行计划释放
                return true;
            }
            SynPlanEquipmentDto planEquipmentDto = planEquipmentMap.get(planId);
            //去除BM
            if (synMaintPlanDto.getTriggerType() == StaticValue.ONE) {
                //按上次工单日期开始，只需要计算一次开单日期
                buildLastTime(planEquipmentDto, synMaintPlanDto, triggerTimeDtos);
            } else if (synMaintPlanDto.getTriggerType() == StaticValue.TWO) {
                if (synMaintPlanDto.getPeriod().equals("0")) {
                    buildSingleTime(planEquipmentDto, synMaintPlanDto, triggerTimeDtos, null);
                } else{
                    //日、周、月
                    buildTime(planEquipmentDto, synMaintPlanDto, triggerTimeDtos);
                }
            }
            if (CollectionUtils.isNotEmpty(triggerTimeDtos)) {
                triggerTimeService.saveByParam(triggerTimeDtos);
            }
        }
        return true;
    }

    @Override
    public Boolean createNextTriggerTime(String planId, String equipmentId){
        List<String> planIds = new ArrayList<>();
        planIds.add(planId);

        List<TriggerTimeDto> triggerTimeDtos = new ArrayList<>();
        Map<String, SynPlanEquipmentDto> planEquipmentMap = this.getMapByPlanIds(planIds);
        SynMaintPlanDto synMaintPlanDto = maintPlanService.getSynById(planId);
        //按上次工单日期开始，完成后计算下一次
        if(null != synMaintPlanDto && synMaintPlanDto.getTriggerType() == StaticValue.ONE) {
            SynPlanEquipmentDto planEquipmentDto = planEquipmentMap.get(planId);

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());
            calendar.add(Calendar.DATE, synMaintPlanDto.getInterVal());
            planEquipmentDto.setPlanMaintTime(calendar.getTime());
            buildSingleTime(planEquipmentDto,synMaintPlanDto,triggerTimeDtos,equipmentId);
            if (CollectionUtils.isNotEmpty(triggerTimeDtos)) {
                triggerTimeService.saveByParam(triggerTimeDtos);
                PlanEquipment planEquipment = new PlanEquipment();
                planEquipment.setId(planEquipmentDto.getId());
                planEquipment.setPlanMaintTime(planEquipmentDto.getPlanMaintTime());
                planEquipmentMapper.updateById(planEquipment);
            }
        }
        return true;
    }

    private static Calendar initCalendar(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar;
    }

    /**
     * 已上次工单完成日期(需要按照设备定开单日期)
     */
    private void buildLastTime(SynPlanEquipmentDto planEquipmentDto, SynMaintPlanDto synMaintPlanDto, List<TriggerTimeDto> triggerTimeDtos ){
        List<String> equipmentIds = new ArrayList<>();
        if(null == planEquipmentDto.getEquipmentIds() || planEquipmentDto.getEquipmentIds().length <= 0){
            //没有设备id，为根据类型或位置取全部设备
            List<String> peEquipmentIds = maintPlanService.getEquipmentIds(planEquipmentDto.getInfoLocationId(), planEquipmentDto.getInfoCategoryId());
            if(CollectionUtils.isNotEmpty(peEquipmentIds)){
                equipmentIds.addAll(peEquipmentIds);
            }
        }else{
            equipmentIds.addAll(Arrays.asList(planEquipmentDto.getEquipmentIds()));
        }
        if(CollectionUtils.isNotEmpty(equipmentIds)){
            for(String equipmentId : equipmentIds){
                buildSingleTime(planEquipmentDto,synMaintPlanDto,triggerTimeDtos,equipmentId);
            }
        }

    }

    /**
     * 单次
     */
    private void buildSingleTime(SynPlanEquipmentDto planEquipmentDto, SynMaintPlanDto synMaintPlanDto, List<TriggerTimeDto> triggerTimeDtos,String equipmentId ){
        if(null != synMaintPlanDto.getExpiryTime()){
            //将截止日期设置为最大小时分钟数
            Calendar expireCalendar = initCalendar(synMaintPlanDto.getExpiryTime());
            expireCalendar.set(Calendar.HOUR_OF_DAY, expireCalendar.getActualMaximum(Calendar.HOUR_OF_DAY));
            expireCalendar.set(Calendar.MINUTE, expireCalendar.getActualMaximum(Calendar.MINUTE));
            expireCalendar.set(Calendar.SECOND, expireCalendar.getActualMaximum(Calendar.SECOND));
            Calendar planCalendar = initCalendar(planEquipmentDto.getPlanMaintTime());
            if(planCalendar.after(expireCalendar)){
                //计划日期大于截止日期，直接停止
                return;
            }
        }
        TriggerTimeDto triggerTimeDto = new TriggerTimeDto();
        triggerTimeDto.setPlanId(synMaintPlanDto.getId());
        //triggerTimeDto.setPlanEquipmentId(planEquipmentDto.getId());
        triggerTimeDto.setEquipmentId(equipmentId);
        triggerTimeDto.setPlanMaintTime(planEquipmentDto.getPlanMaintTime());
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(planEquipmentDto.getPlanMaintTime());
        calendar.add(Calendar.HOUR, -synMaintPlanDto.getAdvanceDay());
        Date triggerTime = calendar.getTime();
        triggerTimeDto.setTriggerTime(triggerTime);
        triggerTimeDtos.add(triggerTimeDto);
    }

    /**
     * 每日
     * 每周 execVal中为1-7
     * 每月 execVal中为1-31，last;last为每月最后一天
     */
    private void buildTime(SynPlanEquipmentDto planEquipmentDto, SynMaintPlanDto synMaintPlanDto, List<TriggerTimeDto> triggerTimeDtos){

        //将截止日期设置为最大小时分钟数
        Calendar expireCalendar = initCalendar(synMaintPlanDto.getExpiryTime());
        expireCalendar.set(Calendar.HOUR_OF_DAY, expireCalendar.getActualMaximum(Calendar.HOUR_OF_DAY));
        expireCalendar.set(Calendar.MINUTE, expireCalendar.getActualMaximum(Calendar.MINUTE));
        expireCalendar.set(Calendar.SECOND, expireCalendar.getActualMaximum(Calendar.SECOND));
        Calendar planCalendar = initCalendar(planEquipmentDto.getPlanMaintTime());

        List<String> weeks = new ArrayList<>();
        List<String> monthDays = new ArrayList<>();
        List<String> months = new ArrayList<>();
        Boolean haveLast = false;
        if(synMaintPlanDto.getPeriod().equals("2")) {
            weeks = this.transformWeek(synMaintPlanDto.getExecVal());
        }else if(synMaintPlanDto.getPeriod().equals("3")){
            if(null != synMaintPlanDto.getExecVal() && synMaintPlanDto.getExecVal().length > 0) {
                monthDays = Arrays.asList(synMaintPlanDto.getExecVal());
            }
            //是否选择最后一天，需要根据每月时间自动计算
            haveLast = monthDays.contains("last");

            //设置了月份
            if(null != synMaintPlanDto.getMonthExecVal() && synMaintPlanDto.getMonthExecVal().length > 0){
                months = Arrays.asList(synMaintPlanDto.getMonthExecVal());
            }
        }

        //首次开单日期最早为明天,所以大于今晚23:59:59
        Calendar nowCalendar = initCalendar(new Date());
        /*nowCalendar.set(Calendar.HOUR_OF_DAY, expireCalendar.getActualMaximum(Calendar.HOUR_OF_DAY));
        nowCalendar.set(Calendar.MINUTE, expireCalendar.getActualMaximum(Calendar.MINUTE));
        nowCalendar.set(Calendar.SECOND, expireCalendar.getActualMaximum(Calendar.SECOND));*/
        //标记当前这一周是否正常生成记录
        Boolean weekFlag = false;
        //计划维护时间->截止日期，一直出单;跳过小于今天的单
        while(planCalendar.before(expireCalendar)) {
            //出单日期和计划日期差距为间隔天数
            Calendar triggerCalendar = initCalendar(planCalendar.getTime());
            triggerCalendar.add(Calendar.HOUR, -synMaintPlanDto.getAdvanceDay());
            Date triggerTime = triggerCalendar.getTime();

            Date planDate = planCalendar.getTime();
            if(synMaintPlanDto.getPeriod().equals("2")){
                int week = planCalendar.get(Calendar.DAY_OF_WEEK);
                if(!weeks.contains(String.valueOf(week))){
                    if(weekFlag && week == 7){
                        //周日，增加周期
                        planCalendar.add(Calendar.DATE, (synMaintPlanDto.getInterVal() - 1) * 7 + 1);
                    }else{
                        planCalendar.add(Calendar.DATE, 1);
                    }
                    continue;
                }
            }else if(synMaintPlanDto.getPeriod().equals("3")){
                int day = planCalendar.get(Calendar.DAY_OF_MONTH);
                int month = planCalendar.get(Calendar.MONTH) + 1;
                int planDateLast = -1;
                if(haveLast){
                    //获取计划日期所属月份最大日期
                    planDateLast = planCalendar.getActualMaximum(Calendar.DAY_OF_MONTH);
                }
                //当前日期不在所选月份中
                if(!months.contains(String.valueOf(month))){
                    planCalendar.add(Calendar.DATE, 1);
                    continue;
                }
                //所选日期不在集合内，且集合内设置last时不为当月最后一天
                if(!monthDays.contains(String.valueOf(day)) && planDateLast != day){
                    planCalendar.add(Calendar.DATE, 1);
                    continue;
                }
            }

            //计划日期在明天凌晨之后，比审批通过时间早的日期不再开单
            if(planCalendar.after(nowCalendar)) {
                TriggerTimeDto triggerTimeDto = new TriggerTimeDto();
                triggerTimeDto.setPlanId(synMaintPlanDto.getId());
                //triggerTimeDto.setPlanEquipmentId(planEquipmentDto.getId());
                triggerTimeDto.setTriggerTime(triggerTime);
                triggerTimeDto.setPlanMaintTime(planDate);
                triggerTimeDtos.add(triggerTimeDto);
                weekFlag = true;
            }
            //按照计划开单日期、间隔天数，计算后续开单时间
            if(synMaintPlanDto.getPeriod().equals("1") && synMaintPlanDto.getInterVal() > 1){
                //每日触发且间隔天数大于1
                planCalendar.add(Calendar.DATE, synMaintPlanDto.getInterVal());
            }else if(synMaintPlanDto.getPeriod().equals("2") && synMaintPlanDto.getInterVal() > 1){
                //每周触发且间隔天数大于1
                int week = planCalendar.get(Calendar.DAY_OF_WEEK);
                if(weekFlag && week == 7){
                    //周日，增加周期
                    planCalendar.add(Calendar.DATE, (synMaintPlanDto.getInterVal() - 1) * 7 + 1);
                }else{
                    planCalendar.add(Calendar.DATE, 1);
                }
            }else{
                planCalendar.add(Calendar.DATE, 1);
            }
        }
    }

    /**
     * 周转换，国际化中周日起始
     * @param weeks
     * @return
     */
    private List<String> transformWeek(String[] weeks){
        List<String> list = new ArrayList<>(weeks.length);
        for(String week : weeks){
            if(week.equals("7")){
                //周日标记转换
                list.add("1");
            }else{
                int numWeek = Integer.valueOf(week) + 1;
                list.add(String.valueOf(numWeek));
            }
        }
        return list;
    }

    @Override
    public List<String> getStandardIds(String[] planIds){
        LambdaQueryWrapper<PlanEquipment> wrapper = Wrappers.lambdaQuery();
        wrapper.in(PlanEquipment::getPlanId, planIds);
        wrapper.select(PlanEquipment::getStandardId);
        return planEquipmentMapper.selectList(wrapper).stream().map(PlanEquipment::getStandardId).distinct().collect(Collectors.toList());
    }

    @Override
    public List<String> getCbmPlanIds(String[] locationIds, String categoryId, String equipmentId, String categoryParamId){
        return planEquipmentMapper.getCbmPlanIds(locationIds, categoryId, equipmentId, TaskJobType.CBM.getValue(), categoryParamId);
    }
}
