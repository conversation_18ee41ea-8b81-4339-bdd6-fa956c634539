package cn.getech.ehm.task.dto;

import cn.getech.poros.framework.common.param.ApiParam;
import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

/**
 * <pre>
 *  编辑参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2025-02-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MaintNotifyConfig编辑", description = "编辑参数")
public class MaintNotifyConfigEditParam extends ApiParam {

    @ApiModelProperty(value = "")
    private String id;

    @ApiModelProperty(value = "")
    private String updateBy;

    @ApiModelProperty(value = "")
    private Date updateTime;

    @ApiModelProperty(value = "")
    private String remark;

}
