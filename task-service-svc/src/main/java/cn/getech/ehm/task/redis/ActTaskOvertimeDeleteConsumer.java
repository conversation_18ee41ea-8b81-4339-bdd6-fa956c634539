package cn.getech.ehm.task.redis;

import cn.getech.ehm.task.entity.ActOvertimeInfo;
import cn.getech.ehm.task.handler.ActivitiHandler;
import cn.getech.ehm.task.service.IActOvertimeInfoService;
import cn.getech.poros.bpm.callback.ProcessConstant;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;

@Component
@Slf4j
public class ActTaskOvertimeDeleteConsumer implements RedisConsumer {
    @Autowired
    RedisTemplate redisTemplate;

    @Autowired
    @Lazy
    IActOvertimeInfoService actOvertimeInfoService;
    @Autowired
    ActivitiHandler activitiHandler;

    @Value("${flow.maint.code:E0004}")
    private String maintFlowCode;

    @Override
    public String getKey() {
        return ProcessConstant.OVERTIME_NOTIFY_DELETE + maintFlowCode;
    }

    @Override
    public void Consumer(Object data, ZSetOperations<String, Object> zSet) {
        try {
            log.info("工单超时记录删除被消费：【key=" + getKey() + "】===>" + data);
            JSONObject temp = JSON.parseObject(data.toString());
            String taskId = temp.getString("taskId");
            String uid = temp.getString("uid");
            String tenantId = temp.getString("tenantId");
            activitiHandler.dealContext(uid, tenantId);
            actOvertimeInfoService.update(new UpdateWrapper<ActOvertimeInfo>().lambda().set(ActOvertimeInfo::getTaskRealTime,new Date()).eq(ActOvertimeInfo::getTaskId, taskId));
            //actOvertimeInfoService.remove(new QueryWrapper<ActOvertimeInfo>().lambda().eq(ActOvertimeInfo::getTaskId, taskId));
            zSet.remove(getKey(), data);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
