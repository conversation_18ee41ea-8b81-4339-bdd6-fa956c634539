package cn.getech.ehm.task.dto.task.info;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;


/**
 * app维护工单主表 返回数据模型
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "MaintTaskGradeAppDto", description = "app工单评分返回数据模型")
public class MaintTaskGradeAppDto extends MaintTaskGradeDto {

    @ApiModelProperty(value = "维护设备id")
    private String equipmentId;

    @ApiModelProperty(value = "维护设备名称")
    private String equipmentName;

    @ApiModelProperty(value = "维护设备类型")
    private String equipmentCategory;

    @ApiModelProperty(value = "维护设备位置")
    private String equipmentLocation;

    @ApiModelProperty(value = "工单类型(1故障单2维保单)")
    private Integer type;

    @ApiModelProperty(value = "工单编号")
    private String code;

    @ApiModelProperty(value = "工单名称")
    private String name;

    @ApiModelProperty(value = "计划维护时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date planMaintTime;

    @ApiModelProperty(value = "优先程度(0高1中2低)")
    private Integer priority;

    @ApiModelProperty(value = "维护人员id集合")
    private String[] staffIds;

    @ApiModelProperty(value = "维护人员名称集合")
    private String staffNames;

    @ApiModelProperty(value = "维护班组id集合")
    private String[] teamIds;

    @ApiModelProperty(value = "维护班组名称集合")
    private String teamNames;

    @ApiModelProperty(value = "流程实例id")
    private String processInstanceId;

    @ApiModelProperty(value = "任务id")
    private String taskId;


}