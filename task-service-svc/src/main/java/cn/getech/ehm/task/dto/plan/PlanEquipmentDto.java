package cn.getech.ehm.task.dto.plan;

import cn.getech.ehm.base.dto.FaultKnowledgeResDto;
import cn.getech.ehm.common.mp.handler.StringArrayTypeHandler;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.util.List;


/**
 * 维保对象dto
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "PlanEquipmentDto", description = "维保对象dto")
public class PlanEquipmentDto{

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "计划id")
    private String planId;

    @ApiModelProperty(value = "设备类型id")
    private String infoCategoryId;

    @ApiModelProperty(value = "设备类型名称")
    private String infoCategoryName;

    @ApiModelProperty(value = "设备位置id")
    private String infoLocationId;

    @ApiModelProperty(value = "设备位置名称")
    private String infoLocationName;

    @ApiModelProperty(value = "设备id集合")
    private String[] equipmentIds;

    @ApiModelProperty(value = "设备名称集合")
    private String equipmentNames;

    @ApiModelProperty(value = "设备id集合")
    private List<PlanEquipmentDetailDto> equipmentDtos;

    @ApiModelProperty(value = "作业标准id")
    private String standardId;

    @ApiModelProperty(value = "作业标准名称")
    private String standardName;

    @ApiModelProperty(value = "维护人员id集合")
    private String[] maintainerIds;

    @ApiModelProperty(value = "维护人员名称集合")
    private String maintainerNames;

    @ApiModelProperty(value = "维护人员集合")
    private List<PlanEquipmentDetailDto> maintainerDtos;

    @ApiModelProperty(value = "维护班组id集合")
    private String[] teamIds;

    @ApiModelProperty(value = "维护班组名称集合")
    private String teamNames;

    @ApiModelProperty(value = "维护班组集合")
    private List<PlanEquipmentDetailDto> teamDtos;

    @ApiModelProperty(value = "人员指派策略 0 根据设备 1 人工配置 2 自由扫码")
    private Integer personStrategy;

    @ApiModelProperty(value = "计划维护日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date planMaintTime;

    @ApiModelProperty(value = "故障现象ids")
    private String[] faultPhenomenonIds;

    @ApiModelProperty(value = "故障现象Dtos")
    private List<FaultKnowledgeResDto> faultPhenomenonDtos;

    @ApiModelProperty(value = "故障现象扩展")
    private String faultPhenomenonRemark;

    @ApiModelProperty(value = "说明")
    private String remark;

    @TableField(typeHandler = StringArrayTypeHandler.class)
    @ApiModelProperty(value = "验收维护人员集合")
    private String[] acceptMaintainerIds;

    @TableField(typeHandler = StringArrayTypeHandler.class)
    @ApiModelProperty(value = "验收维护班组集合")
    private String[] acceptTeamIds;

}