package cn.getech.ehm.task.dto.screen;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EquipmentCalendarResult {
/*    @JsonFormat(pattern = "MM-dd", timezone = "GMT+8")
    public Date date;*/

    @ApiModelProperty("设备id")
    public String equipmentId;

    @ApiModelProperty("设备名称")
    public String equipmentName;

    @ApiModelProperty("当日接单完成数量")
    public Long takeCompleteCount;

    @ApiModelProperty("当日接单数量")
    public Long takeCount;
}
