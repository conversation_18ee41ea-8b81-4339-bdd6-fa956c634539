package cn.getech.ehm.task.dto.defect;

import cn.getech.poros.framework.common.param.ApiParam;
import cn.getech.poros.framework.common.param.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

/**
 * <pre>
 * 缺陷信息 编辑参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-02-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DefectInfo编辑", description = "缺陷信息编辑参数")
public class DefectInfoEditParam extends ApiParam {

    @ApiModelProperty(value = "设备id", required = true)
    private String equipmentId;
    @ApiModelProperty(value = "缺陷名称", required = true)
    private String defectName;
    @ApiModelProperty(value = "缺陷内容", required = true)
    private String defectContent;
    @ApiModelProperty(value = "影响描述")
    private String affectContent;
    @ApiModelProperty(value = "缺陷种类", required = true)
    private Integer defectType;
    @ApiModelProperty(value = "专业类别", required = true)
    private Integer majorType;
    @ApiModelProperty(value = "现场图片视频")
    private String[] liveMediaIds;
    @ApiModelProperty(value = "实际处理方案")
    private String realDealContent;
    @ApiModelProperty(value = "")
    private String id;

    @ApiModelProperty(value = "工单id")
    private String taskId;

    @ApiModelProperty(value = "实际停机开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginDowntime;

    @ApiModelProperty(value = "实际停机结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDowntime;

    @ApiModelProperty(value = "缺陷原因")
    private String defectReason;

    @ApiModelProperty(value = "截止日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date endTime;
}
