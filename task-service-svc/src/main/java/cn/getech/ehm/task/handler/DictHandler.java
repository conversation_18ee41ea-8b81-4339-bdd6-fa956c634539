package cn.getech.ehm.task.handler;

import cn.getech.ehm.base.client.BaseServiceClient;
import cn.getech.ehm.base.dto.DictionaryItemDto;
import cn.getech.poros.framework.common.api.RestResponse;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Slf4j
@Service
public class DictHandler {
    @Autowired
    private BaseServiceClient baseServiceClient;


    //获取字典信息并输出错误log
    public Map<String, DictionaryItemDto> getDictMap(String dictType) {
        RestResponse<Map<String, DictionaryItemDto>> baseServiceClientItemMapByCode = baseServiceClient.getItemMapByCode(dictType);
        if (!baseServiceClientItemMapByCode.isOk()) {
            log.info("连接base-service获取{}字典表失败", dictType);
            return Maps.newHashMap();
        } else {
            return baseServiceClientItemMapByCode.getData();
        }
    }

    //获取字典信息并输出错误log
    public Map<String, Map<String, DictionaryItemDto>> getDictMap(String[] dictType) {
        Map<String, Map<String, DictionaryItemDto>> result = Maps.newHashMap();
        for (String temp : dictType) {
            RestResponse<Map<String, DictionaryItemDto>> baseServiceClientItemMapByCode = baseServiceClient.getItemMapByCode(temp);
            if (!baseServiceClientItemMapByCode.isOk()) {
                log.info("连接base-service获取{}字典表失败", dictType);
                result.put(temp, Maps.newHashMap());
            } else {
                result.put(temp, baseServiceClientItemMapByCode.getData());
            }
        }
        return result;
    }

    //获取字典翻译，无翻译则使用系统enum
    public String getDictValue(Map<String, DictionaryItemDto> dictInfo, Integer dictOriginalValue, String dictType) {
        DictionaryItemDto dictInfoOrDefault = dictInfo.getOrDefault(dictOriginalValue.toString(), null);
        if (dictInfoOrDefault != null) {
            return dictInfoOrDefault.getName();
        }
        return "";
    }

    public Map<String, String> getDictValue(Map<String, Map<String, DictionaryItemDto>> dictMap, Object[] dictOriginalValue, String[] dictType) {
        Map result = Maps.newHashMap();
        int i = 0;
        for (String temp : dictType) {
            Map<String, DictionaryItemDto> dictInfo = dictMap.get(temp);
            if (ObjectUtils.isNull(dictOriginalValue[i])) {
                result.put(temp, "");
                i++;
                continue;
            }
            if (ObjectUtils.isNull(dictInfo)) {
                result.put(temp, "" + dictOriginalValue[i].toString());
                i++;
                continue;
            }
            DictionaryItemDto dictInfoOrDefault = dictInfo.getOrDefault(dictOriginalValue[i].toString(), null);
            if (dictInfoOrDefault != null) {
                result.put(temp, dictInfoOrDefault.getName());
            } else {
                result.put(temp, "" + dictOriginalValue[i].toString());
            }
            i++;
        }
        return result;
    }


    //获取enum翻译
//    private String getDefaultEnum(Integer dictOriginalValue, String dictType) {
//        if (dictType.equals(defectStatusDict)) {
//            return DefectStatusEnum.getNameByValue(dictOriginalValue);
//        }
//        return "" + dictOriginalValue;
//    }
}
