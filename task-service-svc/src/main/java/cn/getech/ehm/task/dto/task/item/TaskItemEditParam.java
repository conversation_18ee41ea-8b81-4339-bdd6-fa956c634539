package cn.getech.ehm.task.dto.task.item;

import cn.getech.poros.framework.common.param.ApiParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;

/**
 * 维护工单任务项编辑参数
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "TaskItemEditParam", description = "维护工单任务项编辑参数")
public class TaskItemEditParam extends ApiParam {
    @ApiModelProperty(value = "工单id", required = true)
    @NotBlank(message = "工单id不能为空")
    private String taskId;

    @ApiModelProperty(value = "工单作业项详情")
    List<TaskItemDetailDto> itemDetailDtos;

    @ApiModelProperty(value = "实际停机开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginDowntime;

    @ApiModelProperty(value = "实际停机结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDowntime;
}
