package cn.getech.ehm.task.service;

import cn.getech.ehm.task.dto.ActOvertimeInfoAddParam;
import cn.getech.ehm.task.dto.ActOvertimeInfoDto;
import cn.getech.ehm.task.dto.ActOvertimeInfoEditParam;
import cn.getech.ehm.task.dto.ActOvertimeInfoQueryParam;
import cn.getech.ehm.task.entity.ActOvertimeInfo;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.IBaseService;

import java.util.Date;
import java.util.List;

/**
 * <pre>
 * 节点时限记录 服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2023-03-15
 */
public interface IActOvertimeInfoService extends IBaseService<ActOvertimeInfo> {

    /**
     * 分页查询，返回Dto
     *
     * @param actOvertimeInfoQueryParam
     * @return
     */
    PageResult<ActOvertimeInfoDto> pageDto(ActOvertimeInfoQueryParam actOvertimeInfoQueryParam);

    /**
     * 保存
     *
     * @param actOvertimeInfoAddParam
     * @return
     */
    boolean saveByParam(ActOvertimeInfoAddParam actOvertimeInfoAddParam);

    /**
     * 根据id查询，转dto
     *
     * @param id
     * @return
     */
    ActOvertimeInfoDto getDtoById(Long id);

    /**
     * 批量保存
     *
     * @param rows
     */
    boolean saveDtoBatch(List<ActOvertimeInfoDto> rows);

    /**
     * 更新
     *
     * @param actOvertimeInfoEditParam
     */
    boolean updateByParam(ActOvertimeInfoEditParam actOvertimeInfoEditParam);
}