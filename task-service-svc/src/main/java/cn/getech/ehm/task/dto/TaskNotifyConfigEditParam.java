package cn.getech.ehm.task.dto;

import cn.getech.ehm.common.mp.handler.StringArrayTypeHandler;
import cn.getech.poros.framework.common.param.ApiParam;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

/**
 * <pre>
 * 工单通知配置 编辑参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-11-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "TaskNotifyConfig编辑", description = "工单通知配置编辑参数")
public class TaskNotifyConfigEditParam extends ApiParam {

    @ApiModelProperty(value = "")
    private String id;

    @ApiModelProperty(value = "")
    private String updateBy;

    @ApiModelProperty(value = "")
    private Date updateTime;

    @ApiModelProperty(value = "")
    private String remark;

    /**
     * 工单类型
     */
    @TableField(value = "task_type",typeHandler = StringArrayTypeHandler.class)
    @ApiModelProperty(value = "工单类型")
    private String[] taskType;

    /**
     * 工单状态类型
     */
    @TableField(value = "task_status_type",typeHandler = StringArrayTypeHandler.class)
    @ApiModelProperty(value = "工单状态类型")
    private String[] taskStatusType;

    /**
     * 提醒方式
     */
    @TableField(value = "notify_type",typeHandler = StringArrayTypeHandler.class)
    @ApiModelProperty(value = "提醒方式")
    private String[] notifyType;

    /**
     * 开始时间
     */
    @TableField("begin_time")
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

}
