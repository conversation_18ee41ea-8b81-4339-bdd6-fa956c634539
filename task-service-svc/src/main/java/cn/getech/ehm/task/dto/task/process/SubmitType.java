package cn.getech.ehm.task.dto.task.process;

import lombok.Getter;
import lombok.Setter;

/**
 * 提交流程任务类型
 * <AUTHOR>
 * @date 2020-09-01 20:17:42
 **/
public enum SubmitType {

    /**
     * 指派
     */
    DISPATCH(0),
    /**
     * 接单
     */
    RECEIVING(1),
    /**
     * 转派
     */
    TRANSFORM(2),
    /**
     * 开始安全确认
     */
    BEGIN_CONFIRM(3),
    /**
     * 确认提交审核
     */
    CONFIRM(4),
    /**
     * 安全确认
     */
    CONFIRMED(5),
    /**
     * 安全确认驳回
     */
    REJECT_CONFIRMED(6),
    /**
     * 开始执行
     */
    PROCESSING(7),
    /**
     * 挂起
     */
    HANG_UP(8),
    /**
     * 执行完成
     */
    COMPLETE(9),
    /**
     * 验收
     */
    CHECK_ACCEPT(10),
    /**
     * 异常关闭
     */
    EXCEPTION_CLOSED(11),
    /**
     * 转缺陷
     */
    TURN_DEFECT(12),
    /**
     * 派单审批通过
     */
    REPAIR_AUDIT_PASS(13),

    /**
     * 一级保养设备停机关闭
     */
    EQUIPMENT_STOP_CLOSED(14),
    //转为待提交
    TO_SUBMIT(15),
    //转为待接单
    TO_WAIT_RECEIVING(16),
    //转为已接单执行中
    TO_RECEIVED(17),
    //发起挂起审批
    HANG_UP_AUDIT(18),
    //挂起审批通过
    HANG_UP_AUDIT_PASS(19),
    //挂起审批驳回
    HANG_UP_AUDIT_REJECT(20),
    //删除
    TO_DELETE(50),
    //执行中转缺陷待审批
    PROCESS_DEFECT_AUDIT(21),
    //验收转缺陷待审批
    ACCEPT_DEFECT_AUDIT(22),
    //缺陷审批通过
    DEFECT_AUDIT_PASS(23),
    //缺陷审批驳回
    DEFECT_AUDIT_REJECT(24),
    ;

    @Getter
    @Setter
    private int code;

    SubmitType(int code){
        this.code = code;
    }

    public static SubmitType valueOf(int code){
        for (SubmitType submitType:SubmitType.values()){
            if(submitType.getCode() == code){
                return submitType;
            }
        }
        return null;
    }

}
