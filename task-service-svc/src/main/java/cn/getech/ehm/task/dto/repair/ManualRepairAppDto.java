package cn.getech.ehm.task.dto.repair;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

/**
 * 故障报修 返回数据模型
 *
 * <AUTHOR>
 * @date 2020-08-06
 */
@Data
@ApiModel(value = "ManualRepairAppDto", description = "故障报修返回数据模型")
public class ManualRepairAppDto {

    @ApiModelProperty(value = "报修单id")
    private String id;

    @ApiModelProperty(value = "设备id")
    private String equipmentId;

    @ApiModelProperty(value = "设备名称")
    private String equipmentName;

    @ApiModelProperty(value = "设备编码")
    private String equipmentCode;

    @ApiModelProperty(value = "维护设备类型")
    private String equipmentCategory;

    @ApiModelProperty(value = "维护设备位置")
    private String equipmentLocation;

    @ApiModelProperty(value = "维护设备责任人")
    private String equipmentPrincipal;

    @ApiModelProperty(value = "维护设备状态")
    private Integer equipmentRunningStatus;

    @ApiModelProperty(value = "故障现象ids")
    private String faultPhenomenonIds;

    @ApiModelProperty(value = "故障现象扩展")
    private String faultPhenomenonRemark;

    @ApiModelProperty(value = "紧急程度")
    private String urgency;

    @ApiModelProperty(value = "工单id")
    private String taskId;

    @ApiModelProperty(value = "工单编号")
    private String taskCode;

    @ApiModelProperty(value = "工单名称")
    private String taskName;

    @ApiModelProperty(value = "状态(0待派单1待接单2待执行3开始确认4安全确认中5已确认6执行中7执行中已挂起8待验收9正常关闭10异常关闭)")
    private Integer taskStatus;

    @ApiModelProperty(value = "状态(0未维修1维修中2已维修)")
    private Integer status;

    @ApiModelProperty(value = "附件id集合")
    private String mediaIds;

    @ApiModelProperty(value = "修复/完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date repairTime;

    @ApiModelProperty(value = "报修人")
    private String createBy;

    @ApiModelProperty(value = "报修人名称")
    private String createUserName;

    @ApiModelProperty(value = "报修时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "图片id")
    private String picId;

    @ApiModelProperty(value = "图片url")
    private String picUrl;

    @ApiModelProperty(value = "评分")
    private Integer grade;

    //截止日期
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deadlineDate;

}