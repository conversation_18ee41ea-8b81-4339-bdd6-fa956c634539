package cn.getech.ehm.task.dto.task.history;

import cn.getech.poros.framework.common.bean.BaseEntity;
import cn.getech.ehm.task.entity.MaintTaskHistory;
import org.mapstruct.Mapper;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <pre>
 * 工单历史 参数实体映射
 * </pre>
 *
 * <AUTHOR>
 * @date 2025-02-05
 */
@Mapper(componentModel = "spring",imports = {BaseEntity.class})
public interface  MaintTaskHistoryParamMapper{

    /**
     * 新增参数转换为实体
     *
     * @param maintTaskHistoryAddParam
     * @return
     */
    MaintTaskHistory addParam2Entity(MaintTaskHistoryAddParam maintTaskHistoryAddParam);

    /**
     * 编辑参数转换为实体
     * @param maintTaskHistoryEditParam
     * @return
     */
    MaintTaskHistory editParam2Entity(MaintTaskHistoryEditParam maintTaskHistoryEditParam);

    /**
     * 实体转换为Dto
     * @param maintTaskHistory
     * @return
     */
    MaintTaskHistoryDto entity2Dto(MaintTaskHistory maintTaskHistory);

    /**
     * 分页实体转DTO
     * @param page
     * @return
     */
    PageResult<MaintTaskHistoryDto> pageEntity2Dto(PageResult<MaintTaskHistory> page);


    /**
     * dto集合转entity集合
     * @param rows
     * @return
     */
    List<MaintTaskHistory> dtoList2Entity(List<MaintTaskHistoryDto> rows);

}
