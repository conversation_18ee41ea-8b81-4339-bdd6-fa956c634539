package cn.getech.ehm.task.mapper;

import cn.getech.ehm.iot.dto.parameter.TaskIotPushDto;
import cn.getech.ehm.task.entity.MaintTaskItem;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 维护工单、任务关联表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
@Repository
public interface MaintTaskItemMapper extends BaseMapper<MaintTaskItem> {
    /**
     * 获取可推送参数监控的数据
     * @param taskId
     * @return
     */
    List<TaskIotPushDto> getCanIotPushData(@Param("taskId") String taskId, @Param("iotPushTypes") Integer[] iotPushTypes);
}
