package cn.getech.ehm.task.dto.task.performance;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 任务信息返回数据模型
 *
 * <AUTHOR>
 * @date 2021-1-18
 */
@Data
@ApiModel(value = "TaskInfoStatisticsDto", description = "任务信息返回数据模型")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TaskInfoStatisticsDto{

    @ApiModelProperty(value = "任务数")
    private Integer taskCount = 0;


    @ApiModelProperty(value = "工时")
    private Long timeCount = 0L;
}
