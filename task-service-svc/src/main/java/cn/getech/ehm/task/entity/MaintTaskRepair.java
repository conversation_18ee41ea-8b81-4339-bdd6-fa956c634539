package cn.getech.ehm.task.entity;

import cn.getech.ehm.common.mp.handler.StringArrayTypeHandler;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

/**
 * 维护工单故障详情表
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("maint_task_repair")
public class MaintTaskRepair extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 工单主表id
     */
    @TableField("task_id")
    private String taskId;

    /**
     * 附件id集合
     */
    @TableField(value = "media_ids", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] mediaIds;

    /**
     * 故障现象ids
     */
    @TableField(value = "fault_phenomenon_ids", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] faultPhenomenonIds;

    /**
     * 故障现象扩展
     */
    @TableField("fault_phenomenon_remark")
    private String faultPhenomenonRemark;

    /**
     * 故障原因ids
     */
    @TableField(value = "fault_reason_ids", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] faultReasonIds;

    /**
     * 故障原因扩展
     */
    @TableField("fault_reason_remark")
    private String faultReasonRemark;

    /**
     * 处理措施ids
     */
    @TableField(value = "fault_measures_ids", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] faultMeasuresIds;

    /**
     * 处理措施扩展
     */
    @TableField("fault_measures_remark")
    private String faultMeasuresRemark;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] faultStructureIds;

    @ApiModelProperty(value = "LOTO标志1是2否")
    private Integer lotoFlag;

    private String lotoContent;

    /**
     * 0保养失效 1来料异常 2提产 3设计缺陷
     */
    @TableField("damage_reason")
    private String damageReason;

    private String[] tmpFileIds;

    private String[] lotoFileIds;

}
