package cn.getech.ehm.task.service;

import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.common.util.ExcelUtil;
import cn.getech.ehm.equipment.dto.EquipmentListDto;
import cn.getech.ehm.task.dto.TaskStatisticsDto;
import cn.getech.ehm.task.dto.TaskStatisticsReqDto;
import cn.getech.ehm.task.dto.*;
import cn.getech.ehm.task.dto.repair.ManualRepairAddParam;
import cn.getech.ehm.task.dto.screen.WorkCalendarAppResult;
import cn.getech.ehm.task.dto.screen.WorkCalendarQueryParam;
import cn.getech.ehm.task.dto.screen.WorkCalendarResult;
import cn.getech.ehm.task.dto.screen.*;
import cn.getech.ehm.task.dto.task.notify.MaintTaskNotifyDto;
import cn.getech.ehm.task.dto.task.repair.*;
import cn.getech.ehm.task.dto.task.info.*;
import cn.getech.ehm.task.dto.task.item.MaintTaskItemDto;
import cn.getech.ehm.task.dto.task.item.TaskItemEditParam;
import cn.getech.ehm.task.dto.task.part.MaintTaskPartDto;
import cn.getech.ehm.task.dto.task.part.TaskPartEditParam;
import cn.getech.ehm.task.dto.task.performance.PersonalPerformanceDto;
import cn.getech.ehm.task.dto.task.performance.PersonalPerformanceQueryParam;
import cn.getech.ehm.task.dto.task.performance.PersonalPerformanceTopQueryParam;
import cn.getech.ehm.task.dto.task.process.TaskSubmitParam;
import cn.getech.ehm.task.dto.task.ticket.TaskTicketDto;
import cn.getech.ehm.task.entity.MaintTask;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.framework.common.service.IBaseService;
import cn.getech.poros.framework.common.utils.ExcelUtils;
import cn.hutool.core.date.DateUtil;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 维护工单主表 服务类
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
public interface IMaintTaskService extends IBaseService<MaintTask> {

    /**
     * 分页查询，返回Dto
     *
     * @param maintTaskQueryParam
     * @return
     */
    PageResult<MaintTaskPageDto> pageDto(MaintTaskQueryParam maintTaskQueryParam);

    /**
     * 获取工单各个状态数量
     *
     * @param ourTask
     * @return
     */
    List<TaskCountDto> getCount(Boolean ourTask);

    List<TaskCountDto> getCount(Boolean ourTask,String type);

    /**
     * 工作台任务总览
     * @return
     */
    List<TaskCountDto> getWorkbenchCount();

    /**
     * App分页查询，返回Dto
     *
     * @param maintTaskQueryAppParam
     * @return
     */
    PageResult<MaintTaskAppPageDto> appPageDto(MaintTaskQueryAppParam maintTaskQueryAppParam);

    /**
     * 根据计划生成工单
     *
     * @param maintTaskPlanAddDto
     * @return
     */
    MaintTaskDto savePlanTask(MaintTaskPlanAddDto maintTaskPlanAddDto);

    /**
     * 根据报修生成工单
     *
     * @param maintTaskRepairAddDto
     * @return
     */
    MaintTaskDto saveRepairTask(MaintTaskRepairAddDto maintTaskRepairAddDto);

    /**
     * 新增缺陷工单
     *
     * @param defectAddDto
     * @return
     */
    String saveDefectTask(MaintTaskDefectAddDto defectAddDto);

    /**
     * 生成工单编号
     *
     * @return
     */
    String buildCode(Integer sourceType);

    /**
     * 获取最大编码
     *
     * @param prex
     * @return
     */
    String getMaxCode(String prex);

    /**
     * 根据id查询，转dto
     *
     * @param id
     * @return
     */
    MaintTaskDto getDtoById(String id);

    /**
     * 修改工单信息
     *
     * @param editParam
     * @return
     */
    Boolean updateTask(TaskEditParam editParam);

    /**
     * 获取设备名称
     *
     * @param equipmentId
     * @return
     */
    EquipmentListDto getEquipment(String equipmentId);

    /**
     * 根据id查询任务项
     *
     * @param id
     * @return
     */
    MaintTaskItemDto getTaskItemById(String id,Boolean onlyError);

    /**
     * 获取工作流id
     *
     * @param processInstanceId
     * @param scan
     * @return
     */
    String getProcessTaskId(String processInstanceId, Boolean scan);

    /**
     * 更改工单处理任务项
     *
     * @param editParam
     * @return
     */
    Boolean updateTaskItem(TaskItemEditParam editParam);

    /**
     * 根据id查询故障
     *
     * @param id
     * @return
     */
    MaintTaskFaultDto getFaultDtoById(String id);

    /**
     * 更改工单处理故障维修
     *
     * @param editParam
     * @return
     */
    Boolean updateTaskFault(TaskFaultEditParam editParam);

    /**
     * 根据id查询备件
     *
     * @param id
     * @return
     */
    MaintTaskPartDto getPartById(String id);

    /**
     * 更改工单备件
     *
     * @param editParam
     * @return
     */
    Boolean updateTaskPart(TaskPartEditParam editParam);

    /**
     * 根据id查询工作票
     *
     * @param id
     * @return
     */
    TaskTicketDto getTaskTicketById(String id);

    /**
     * 更改工作票
     *
     * @param editParam
     * @return
     */
    Boolean updateTaskTicket(TaskTicketDto editParam);

    /**
     * 根据id查询评分
     *
     * @param id
     * @return
     */
    MaintTaskGradeDto getGradeById(String id);

    /**
     * 更改工单评分
     *
     * @param editParam
     * @return
     */
    Boolean updateTaskGrade(MaintTaskGradeDto editParam);

    /**
     * 获取今日工单数量
     *
     * @return
     */
    Integer getTodayTaskQty();

    /**
     * 获取超期工单数量
     *
     * @return
     */
    Integer getOverdueTaskQty();

    /**
     * 获取今日已完成工单数量
     *
     * @return
     */
    Integer getTodayCompletedTaskQty();

    /**
     * 获取工单完成动态（返回最近5条）
     *
     * @return
     */
    List<TaskCompleteDynamicDto> getTaskCompleteDynamic();

    /**
     * 获取工单总览列表
     *
     * @param status 工单状态
     * @return
     */
    List<MaintTaskDto> getTaskOverview(Integer status);

    /**
     * 获取已评价工单统计
     *
     * @return
     */
    List<Map<Integer, Integer>> getTaskEvaluated();

    /**
     * @return
     */
    List<MaintTaskDto> getTaskTodoList();

    /**
     * 根据流程实例ID获取维护工单
     *
     * @param processInstanceId
     * @return
     */
    MaintTaskDto getByProcessInstanceId(String processInstanceId);

    /**
     * 维护工单流程任务提交
     *
     * @param taskSubmitParam
     * @return
     */
    boolean submitProcessTask(TaskSubmitParam taskSubmitParam);

    /**
     * 获取统计需要工单信息
     *
     * @param reqDto
     * @return
     */
    List<TaskStatisticsDto> getTaskStatistics(TaskStatisticsReqDto reqDto);

    /**
     * 设备故障统计
     *
     * @return
     */
    List<FaultStatisticsDto> faultEquipmentStatistics(FaultStatisticsParam param);

    /**
     * 所有故障设备信息统计
     *
     * @return
     */
    List<FaultStatisticsDto> allFaultEquipmentStatistics(FaultStatisticsParam param);

    /**
     * 工单类型统计-饼图
     *
     * @param maintTaskReportQueryParam
     * @return
     */
    List<NameNode> typeReport(MaintTaskReportQueryParam maintTaskReportQueryParam);

    /**
     * 工单类型月度统计-柱状图
     *
     * @param maintTaskReportQueryParam
     * @return
     */
    List<TypeMonthNode> typeMonthlyReport(MaintTaskReportQueryParam maintTaskReportQueryParam);

    /**
     * 工单状态统计-饼图
     *
     * @param maintTaskReportQueryParam
     * @return
     */
    List<NameNode> statusReport(MaintTaskReportQueryParam maintTaskReportQueryParam);

    /**
     * 工单工时月度统计-拆线图
     *
     * @param maintTaskReportQueryParam
     * @return
     */
    List<MonthNode> maintTimeMonthlyReport(MaintTaskReportQueryParam maintTaskReportQueryParam);

    /**
     * 绩效统计 查询人员绩效分和工时
     *
     * @param personPerformanceQueryParam
     * @return
     */
    PageResult<PersonalPerformanceDto> personalPerformancePage(PersonalPerformanceQueryParam personPerformanceQueryParam);

    /**
     * 查找人员绩效TopN个
     *
     * @param param
     * @return
     */
    List<PersonalPerformanceDto> personPerformanceTop(PersonalPerformanceTopQueryParam param);

    /**
     * 首页获取设备维护列表
     *
     * @param maintTaskQueryAppParam
     * @return
     */
    PageResult<MaintTaskAppPageDto> maintTaskFirstPage(MaintTaskQueryAppParam maintTaskQueryAppParam);

    /**
     * 校验备件是否被维护计划。维护工单相关使用
     *
     * @param partIds
     * @return
     */
    List<String> checkPartUsed(String[] partIds);

    /**
     * 校验设备是否被维护计划。维护工单相关使用
     *
     * @param equipmentIds
     * @return
     */
    List<String> checkEquipmentUsed(String[] equipmentIds);

    /**
     * 获取工单关联的设备ids
     *
     * @return
     */
    List<String> getUsedInfoIds();

    /**
     * 获取最新完成工单日期
     *
     * @param planIds
     * @return
     */
    Map<String, Date> getLastPlanDateMap(List<String> planIds);

    /**
     * 获取工单数量
     *
     * @param planIds
     * @return
     */
    Map<String, Integer> getTaskCountMap(List<String> planIds);

    /**
     * 工单转缺陷关闭
     *
     * @param defectAddParam
     * @return
     */
    Boolean submitDefect(TaskDefectAddParam defectAddParam);

    public String getWorkbenchStatisticsOfMaintTask(String type, String status, String isTody);

    public String getWorkbenchStatisticsOfMaintTaskOfOverTime(String overTimeType);

    LinkedHashMap<String, WorkCalendarResult> getWorkCalendar(WorkCalendarQueryParam param);

    public List<WorkCalendarAppResult> getWorkCalendarOfApp(WorkCalendarQueryParam param);

    PageResult<MaintTaskDto> getTodoOfTask(MaintTaskQueryParam param);

    public List<MaintTaskExcelDto> exportList(MaintTaskQueryParam maintTaskQueryParam);

    public Integer exportListCount(MaintTaskQueryParam maintTaskQueryParam);

    public Boolean updateMaintTaskOfFile(MaintTaskFileParam param);

    public Boolean updateEcError(MaintTaskEcErrorEditParam param);

    public MaintTaskDto saveManualRepair(ManualRepairAddParam manualRepairAddParam);

    LinkedHashMap<String, List<UserCalendarResult>> getUserCalcuelate(UserCalendarQueryParam param);

    LinkedHashMap<String, List<EquipmentCalendarResult>> getEquipmentCalcuelate(EquipmentCalendarQueryParam param);

    /**
     * 获取报警关联工单
     * @param queryParam
     * @return
     */
    PageResult<WarnTaskPageDto> getListByWarnId(WarnQueryParam queryParam);

    public void markDeletedBatch(String id);

    public MaintTaskDtoForeign getDtoByIdForeign(String id);

    Boolean closeTaskBatch(List<TaskSubmitParam> taskSubmitParams);

    /**
     * 批量操作
     * @param param
     * @return
     */
    String batchEdit(TaskBatchEditParam param);

    /**
     * 工作台-今日任务
     */
    TodayTaskCountDto todayTask(Boolean ourTask);

    public List<MaintTaskPageDto> listDto(MaintTaskQueryParam maintTaskQueryParam);

//    public PageResult<MaintTaskPageDto> getOverTimeTaskList(MaintTaskQueryParam queryParam);

    public void exportOvertimeTask(MaintTaskQueryParam maintTaskQueryParam, HttpServletResponse response);
    public void exportEcErrorTask(MaintTaskQueryParam maintTaskQueryParam, HttpServletResponse response);
    public void exportErrorTask(MaintTaskQueryParam maintTaskQueryParam, HttpServletResponse response);

    Boolean editEquipmentInfo(TaskEquipmentInfoEditParam param);

    Boolean updateTask(TaskEquipmentInfoEditParam param);

    /**
     * 离线缓存
     * @return
     */
    MaintTaskCacheDto offlineCache(String id);

    /**
     * 离线缓存提交
     */
    Boolean offlineCacheSubmit(MaintTaskCacheEditDto dto);

    /**
     * 校验状态是否变更
     */
    Boolean offlineCacheSubmitCheck(String id);

    /**
     * 更新任务人员
     */
    Boolean updateTaskUids();

    /**
     * 获取临期/超期巡检单
     * @return
     */
    List<MaintTaskNotifyDto> getNotifyTask(Integer notifyType, Date date);

    /**
     * 定时器超期异常关闭
     * @return
     */
    Boolean synExceptionClose(List<String> ids);

    /**
     * 获取超期任务单id
     * @return
     */
    List<MaintTaskNotifyDto> getOverdueTaskIds();

    public MaintTaskDto savePlanTask(MaintTaskPlanAddDto addDto, UserBaseInfo userBaseInfo);

    void transformBatch(List<TaskBatchTransformParam> paramList);

    public void reopenTask(MaintTaskReopenDto request);
}
