package cn.getech.ehm.task.dto.analyst;

import cn.getech.poros.framework.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * <pre>
 * 报修工单统计 返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
@Data
@ApiModel(value = "MaintTaskAnalystDto", description = "报修工单统计返回数据模型")
public class MaintTaskAnalystDto {

    @ApiModelProperty(value = "deleted")
    //@Excel(name="deleted",cellType = Excel.ColumnType.STRING )
    private Long deleted;

    @ApiModelProperty(value = "tenantId")
    //@Excel(name="tenantId",cellType = Excel.ColumnType.STRING )
    private String tenantId;

    @ApiModelProperty(value = "工单id")
    //@Excel(name="工单id",cellType = Excel.ColumnType.STRING )
    private String taskId;

    @ApiModelProperty(value = "工单编号")
    @Excel(name = "工单编号", cellType = Excel.ColumnType.STRING)
    private String taskCode;

    @ApiModelProperty(value = "工单状态")
    //@Excel(name = "工单状态", cellType = Excel.ColumnType.STRING)
    private Integer taskStatus;

    @ApiModelProperty(value = "工单状态")
    @Excel(name = "工单状态", cellType = Excel.ColumnType.STRING)
    private String taskStatusStr;

    @ApiModelProperty(value = "设备id")
    //@Excel(name = "设备id", cellType = Excel.ColumnType.STRING)
    private String equipId;

    @ApiModelProperty(value = "设备位置")
    @Excel(name = "设备位置", cellType = Excel.ColumnType.STRING)
    private String equipLocationName;

    @ApiModelProperty(value = "设备编号")
    @Excel(name = "设备编号", cellType = Excel.ColumnType.STRING)
    private String equipCode;

    @ApiModelProperty(value = "设备名")
    @Excel(name="设备名称",cellType = Excel.ColumnType.STRING )
    private String equipName;

    @ApiModelProperty(value = "设备位置id")
    //@Excel(name="设备位置id",cellType = Excel.ColumnType.STRING )
    private String equipLocationId;



    @ApiModelProperty(value = "设备类型id")
    //@Excel(name="设备类型id",cellType = Excel.ColumnType.STRING )
    private String equipCategoryId;

    @ApiModelProperty(value = "设备类型")
    @Excel(name = "设备类型", cellType = Excel.ColumnType.STRING)
    private String equipCategotyName;

    @ApiModelProperty(value = "报修人")
    //@Excel(name="报修人",cellType = Excel.ColumnType.STRING )
    private String reportUid;

    @ApiModelProperty(value = "报修人")
    @Excel(name = "报修人", cellType = Excel.ColumnType.STRING)
    private String reportName;

    @ApiModelProperty(value = "处理人")
    //@Excel(name="处理人",cellType = Excel.ColumnType.STRING )
    private String handlerUid;

    @ApiModelProperty(value = "处理人")
    @Excel(name = "处理人", cellType = Excel.ColumnType.STRING)
    private String handlerName;

    @ApiModelProperty(value = "验收人")
    //@Excel(name="验收人",cellType = Excel.ColumnType.STRING )
    private String acceptUid;

    @ApiModelProperty(value = "验收人")
    @Excel(name = "验收人", cellType = Excel.ColumnType.STRING)
    private String acceptName;

    @ApiModelProperty(value = "是否改派")
    @Excel(name = "是否改派", cellType = Excel.ColumnType.STRING, readConverterExp = "0=无,1=有")
    private Integer transferFlag;

    @ApiModelProperty(value = "工单创建时间")
    @Excel(name = "创建时间", cellType = Excel.ColumnType.STRING, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date taskCreateTime;

    @ApiModelProperty(value = "工单接单时间")
    @Excel(name = "接单时间", cellType = Excel.ColumnType.STRING, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date taskReceiveTime;

    @ApiModelProperty(value = "开始工作时间")
    @Excel(name = "开始工作时间", cellType = Excel.ColumnType.STRING, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginMaintTime;

    @ApiModelProperty(value = "接单时长")
    @Excel(name = "接单时长", cellType = Excel.ColumnType.STRING)
    private Long receiveStr;

    @ApiModelProperty(value = "特种作业时长")
    @Excel(name = "特种作业时长", cellType = Excel.ColumnType.STRING)
    private Long lotoCostStr;





    @ApiModelProperty(value = "第一次挂起审批中时间")
    @Excel(name = "第一次挂起审批中时间", cellType = Excel.ColumnType.STRING, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date handupSubmitTime;

    @ApiModelProperty(value = "第一次挂起审批操作时间")
    @Excel(name = "第一次挂起审批操作时间", cellType = Excel.ColumnType.STRING, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date handupAuditTime;

    @ApiModelProperty(value = "第一次挂起审批时长")
    @Excel(name = "第一次挂起审批时长", cellType = Excel.ColumnType.STRING)
    private Long handupCostStr;

    @ApiModelProperty(value = "第二次挂起审批中时间")
    @Excel(name = "第二次挂起审批中时间", cellType = Excel.ColumnType.STRING, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date handupSubmitTime2;

    @ApiModelProperty(value = "第二次挂起审批操作时间")
    @Excel(name = "第二次挂起审批操作时间", cellType = Excel.ColumnType.STRING, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date handupAuditTime2;

    @ApiModelProperty(value = "第二次挂起审批时长")
    @Excel(name = "第二次挂起审批时长", cellType = Excel.ColumnType.STRING)
    private Long handupCostStr2;

    @ApiModelProperty(value = "挂起总时长")
    //@Excel(name = "挂起总时长", cellType = Excel.ColumnType.STRING)
    private Long handupCostTotal;

    @ApiModelProperty(value = "运行转缺陷待审批时间")
    @Excel(name = "第一次执行中转缺陷待审批时间", cellType = Excel.ColumnType.STRING, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date runDefectSubmitTime;

    @ApiModelProperty(value = "运行转缺陷审批操作时间")
    @Excel(name = "第一次执行中转缺陷审批操作时间", cellType = Excel.ColumnType.STRING, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date runDefectAuditTime;

    @ApiModelProperty(value = "运行转缺陷审批时长")
    @Excel(name = "第一次执行中转缺陷审批时长", cellType = Excel.ColumnType.STRING)
    private Long runDefectCostStr;

    @ApiModelProperty(value = "第二次运行转缺陷待审批时间")
    @Excel(name = "第二次执行中转缺陷待审批时间", cellType = Excel.ColumnType.STRING, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date runDefectSubmitTime2;

    @ApiModelProperty(value = "第二次运行转缺陷审批操作时间")
    @Excel(name = "第二次执行中转缺陷审批操作时间", cellType = Excel.ColumnType.STRING, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date runDefectAuditTime2;

    @ApiModelProperty(value = "第二次运行转缺陷审批时长")
    @Excel(name = "第二次执行中转缺陷审批时长", cellType = Excel.ColumnType.STRING)
    private Long runDefectCostStr2;

    @ApiModelProperty(value = "第一次验收转缺陷待审批时间")
    @Excel(name = "第一次验收转缺陷待审批时间", cellType = Excel.ColumnType.STRING, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date checkDefectSubmitTime;

    @ApiModelProperty(value = "第一次验收转缺陷审批操作时间")
    @Excel(name = "第一次验收转缺陷审批操作时间", cellType = Excel.ColumnType.STRING, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date checkDefectAuditTime;

    @ApiModelProperty(value = "第一次验收转缺陷审批时长")
    @Excel(name = "第一次验收转缺陷审批时长", cellType = Excel.ColumnType.STRING)
    private Long checkDefectCostStr;

    @ApiModelProperty(value = "第二次验收转缺陷待审批时间")
    @Excel(name = "第二次验收转缺陷待审批时间", cellType = Excel.ColumnType.STRING, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date checkDefectSubmitTime2;

    @ApiModelProperty(value = "第二次验收转缺陷审批操作时间")
    @Excel(name = "第二次验收转缺陷审批操作时间", cellType = Excel.ColumnType.STRING, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date checkDefectAuditTime2;

    @ApiModelProperty(value = "第二次验收转缺陷审批时长")
    @Excel(name = "第二次验收转缺陷审批时长", cellType = Excel.ColumnType.STRING)
    private Long checkDefectCostStr2;

    @ApiModelProperty(value = "验收发起时间")
    @Excel(name = "第一次结束工作时间", cellType = Excel.ColumnType.STRING, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date checkSubmitTime;

    @ApiModelProperty(value = "验收操作时间")
    @Excel(name = "第一次验收操作时间", cellType = Excel.ColumnType.STRING, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date checkAuditTime;

    @ApiModelProperty(value = "验收时长")
    @Excel(name = "第一次验收时长", cellType = Excel.ColumnType.STRING)
    private Long checkCostStr;

    @ApiModelProperty(value = "checkSubmitTime2")
    @Excel(name = "第二次结束工作时间", cellType = Excel.ColumnType.STRING, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date checkSubmitTime2;

    @ApiModelProperty(value = "checkAuditTime2")
    @Excel(name = "第二次验收操作时间", cellType = Excel.ColumnType.STRING, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date checkAuditTime2;

    @ApiModelProperty(value = "checkCostStr2")
    @Excel(name = "第二次验收时长", cellType = Excel.ColumnType.STRING)
    private Long checkCostStr2;

    @ApiModelProperty(value = "id")
    //@Excel(name = "id", cellType = Excel.ColumnType.STRING)
    private String id;

    @ApiModelProperty(value = "createBy")
    //@Excel(name = "createBy", cellType = Excel.ColumnType.STRING)
    private String createBy;

    @ApiModelProperty(value = "updateBy")
    //@Excel(name = "updateBy", cellType = Excel.ColumnType.STRING)
    private String updateBy;

    @ApiModelProperty(value = "createTime")
    //@Excel(name = "createTime", cellType = Excel.ColumnType.STRING, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "updateTime")
    //@Excel(name = "updateTime", cellType = Excel.ColumnType.STRING, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value = "remark")
    //@Excel(name = "remark", cellType = Excel.ColumnType.STRING)
    private String remark;

    @ApiModelProperty(value = "工单检修耗时")
    @Excel(name = "检修时长", cellType = Excel.ColumnType.STRING)
    private Long taskMaintTimeStr;

    @ApiModelProperty(value = "工单关闭时间")
    @Excel(name = "工单关闭时间", cellType = Excel.ColumnType.STRING, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date taskCloseTime;

    @ApiModelProperty(value = "工单实际耗时")
    @Excel(name = "工单时长", cellType = Excel.ColumnType.STRING)
    private Long taskWorkingTimeStr;
}