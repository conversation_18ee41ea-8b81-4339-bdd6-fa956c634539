package cn.getech.ehm.task.dto.screen;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
@ApiModel("工作日历查询参数")
public class WorkCalendarQueryParam {
    public String equipmentName;

    public String equipmentCode;

    public String equipmentLocation;

    public String equipmentType;

    public String equipmentManager;

    @JsonFormat(pattern = "yyyy-MM", timezone = "GMT+8")
    @NotNull
    public Date searchDate;

    @ApiModelProperty("区域")
    private String areaType;

    @ApiModelProperty("工序")
    private List<String> processType;

    @ApiModelProperty(value = "维护设备位置ids")
    private List<String> equipmentLocationIds;
}
