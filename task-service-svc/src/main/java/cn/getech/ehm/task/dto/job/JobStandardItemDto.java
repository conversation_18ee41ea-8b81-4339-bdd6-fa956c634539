package cn.getech.ehm.task.dto.job;

import cn.getech.ehm.base.dto.AttachmentClientDto;
import cn.getech.ehm.common.mp.handler.StringArrayTypeHandler;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

/**
 * 作业项目
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "JobStandardItemDto", description = "作业项目导入")
public class JobStandardItemDto {

    @ApiModelProperty(value = "作业项id")
    private String id;

    @ApiModelProperty(value = "作业标准id")
    private String jobStandardId;

    @ApiModelProperty(value = "大类")
    private String largeCategory;

    @ApiModelProperty(value = "小类")
    private String subCategory;

    @ApiModelProperty(value = "作业类型等级")
    private String[] jobTypeLevel;

    @ApiModelProperty(value = "作业内容")
    private String content;

    @ApiModelProperty(value = "标准工时(人*min)")
    private BigDecimal standardTime;

    @ApiModelProperty(value = "作业时间")
    private BigDecimal workingTime;

    @ApiModelProperty(value = "投入人员")
    private Integer inputPerson;

    @ApiModelProperty(value = "作业方法")
    private String method;

    @ApiModelProperty(value = "作业方法详情")
    private String methodDetail;

    @ApiModelProperty(value = "结果类型")
    private String jobItemResultType;

    @ApiModelProperty(value = "基准目标")
    private String benchmark;

    @ApiModelProperty(value = "目标值")
    private BigDecimal targetValue;

    @ApiModelProperty(value = "区间最大值")
    private BigDecimal targetMax;

    @ApiModelProperty(value = "区间最小值")
    private BigDecimal targetMin;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "工具")
    private String tool;

    @ApiModelProperty(value = "特殊要求")
    private String specialRequirements;

    @TableField(typeHandler = StringArrayTypeHandler.class)
    private String[] fileIds;

    private List<AttachmentClientDto> fileInfoList;

    @ApiModelProperty(value = "上传参数监控0否1指标2波形")
    private Integer iotPush = 0;

}