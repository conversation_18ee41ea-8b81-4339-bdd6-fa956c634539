package cn.getech.ehm.task.config;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @date 2020-08-10 10:53:47
 **/
@EnableAsync
@Configuration
public class ExecutorConfig {

    @Bean
    public ExecutorService executorService(){
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat("executorService-%d").build();
        ExecutorService executorService = new ThreadPoolExecutor(20, 50, 0L, TimeUnit.MILLISECONDS,
                new ArrayBlockingQueue<Runnable>(1024), threadFactory, new ThreadPoolExecutor.AbortPolicy());
        return executorService ;
    }

    @Bean
    public ExecutorService schedulerExecutorService(){
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat("schedulerExecutorService-%d").build();
        ExecutorService executorService = new ThreadPoolExecutor(10, 10, 0L, TimeUnit.MILLISECONDS,
                new ArrayBlockingQueue<Runnable>(50000), threadFactory, new ThreadPoolExecutor.AbortPolicy());
        return executorService ;
    }

    @Bean
    public ExecutorService activitiExecutorService(){
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat("activitiExecutorService-%d").build();
        ExecutorService executorService = new ThreadPoolExecutor(10, 10, 0L, TimeUnit.MILLISECONDS,
                new ArrayBlockingQueue<Runnable>(50000), threadFactory, new ThreadPoolExecutor.DiscardOldestPolicy());
        return executorService ;
    }

    @Bean("sendNotify")
    public Executor autoClose() {
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat("sendNotify-runner-thread-%d").build();
        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(8, 8,
                10L, TimeUnit.SECONDS,
                new LinkedBlockingQueue<Runnable>(10000), Executors.defaultThreadFactory(),
                new ThreadPoolExecutor.CallerRunsPolicy());
        threadPoolExecutor.setThreadFactory(threadFactory);
        return threadPoolExecutor;
    }

    @Bean("closeOrder")
    public Executor closeOrder() {
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat("cloaseOrder-runner-thread-%d").build();
        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(4, 8,
                10L, TimeUnit.SECONDS,
                new LinkedBlockingQueue<Runnable>(50000), Executors.defaultThreadFactory(),
                new ThreadPoolExecutor.CallerRunsPolicy());
        threadPoolExecutor.setThreadFactory(threadFactory);
        return threadPoolExecutor;
    }



}
