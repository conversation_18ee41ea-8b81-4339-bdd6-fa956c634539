package cn.getech.ehm.task.dto.repair;

import cn.getech.poros.framework.common.param.ApiParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import java.util.List;

/**
 * 人工报修 更新参数
 *
 * <AUTHOR>
 * @date 2020-08-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ManualRepairEditParam", description = "人工报修更新参数")
public class ManualRepairEditParam extends ApiParam {

    @ApiModelProperty(value = "报修单id")
    private String id;

    @ApiModelProperty(value = "故障类别")
    private String faultType;

    @ApiModelProperty(value = "专业")
    private String major;

    @ApiModelProperty(value = "紧急程度")
    private String urgency;

    @ApiModelProperty(value = "影响程度")
    private String influence;

    @ApiModelProperty(value = "故障现象ids")
    private List<String> faultPhenomenonIds;

    @ApiModelProperty(value = "故障现象扩展")
    private String faultPhenomenonRemark;

    @ApiModelProperty(value = "故障原因ids")
    private List<String> faultReasonIds;

    @ApiModelProperty(value = "故障原因扩展")
    private String faultReasonRemark;

    @ApiModelProperty(value = "处理措施ids")
    private List<String> faultMeasuresIds;

    @ApiModelProperty(value = "处理措施扩展")
    private String faultMeasuresRemark;

    @ApiModelProperty(value = "状态(0未维修1维修中2已维修)")
    private Integer status;

    @ApiModelProperty(value = "解决方案")
    private String measures;

    @ApiModelProperty(value = "修复时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date repairTime;

    //截止日期
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deadlineDate;
}