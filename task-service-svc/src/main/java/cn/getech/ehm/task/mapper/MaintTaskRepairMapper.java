package cn.getech.ehm.task.mapper;

import cn.getech.ehm.task.dto.task.repair.MaintTaskFaultDto;
import cn.getech.ehm.task.entity.MaintTaskRepair;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 维护工单详情表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
@Repository
public interface MaintTaskRepairMapper extends BaseMapper<MaintTaskRepair> {
    /**
     * 根据工单id获取明细信息
     * @param taskId
     * @return
     */
    MaintTaskFaultDto getFaultDtoById(@Param("taskId") String taskId);

    /**
     * 获取设备关联故障树
     * 类型(1故障现象2故障原因3处理措施)
     */
    List<MaintTaskRepair> getUsedFaultTree(@Param("equipmentId") String equipmentId, @Param("faultTreeIds") List<String> faultTreeIds, @Param("type") Integer type, @Param("statusList") Integer[] statusList);
}
