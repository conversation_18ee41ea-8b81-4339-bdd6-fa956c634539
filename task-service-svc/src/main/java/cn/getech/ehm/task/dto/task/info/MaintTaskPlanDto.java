package cn.getech.ehm.task.dto.task.info;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

/**
 * 计划工单列表
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "MaintTaskPlanDto", description = "计划工单列表")
public class MaintTaskPlanDto{

    @ApiModelProperty(value = "工单id")
    private String id;

    @ApiModelProperty(value = "工单编号")
    private String code;

    @ApiModelProperty(value = "工单名称")
    private String name;

    @ApiModelProperty(value = "计划维护时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date planMaintTime;

    @ApiModelProperty(value = "实际维护时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date actualMaintTime;

    @ApiModelProperty(value = "维护人员id集合")
    private String[] staffIds;

    @ApiModelProperty(value = "维护班组id集合")
    private String[] teamIds;

    @ApiModelProperty(value = "维护人员")
    private String staffs;

    @ApiModelProperty(value = "人员和班组组合人员ids(可做权限)")
    private String[] allStaffIds;

}
