package cn.getech.ehm.task.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import cn.getech.poros.framework.common.annotation.Excel;
import java.util.Date;


/**
 * <pre>
 *  返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2025-02-10
 */
@Data
@ApiModel(value = "MaintNotifyConfigDto", description = "返回数据模型")
public class MaintNotifyConfigDto{

    @ApiModelProperty(value = "")
    @Excel(name="",cellType = Excel.ColumnType.STRING)
    private String id;

    @ApiModelProperty(value = "createBy")
    @Excel(name="createBy",cellType = Excel.ColumnType.STRING )
    private String createBy;

    @ApiModelProperty(value = "updateBy")
    @Excel(name="updateBy",cellType = Excel.ColumnType.STRING )
    private String updateBy;

    @ApiModelProperty(value = "createTime")
    @Excel(name="createTime",cellType = Excel.ColumnType.STRING ,dateFormat="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "updateTime")
    @Excel(name="updateTime",cellType = Excel.ColumnType.STRING ,dateFormat="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(value = "remark")
    @Excel(name="remark",cellType = Excel.ColumnType.STRING )
    private String remark;

}