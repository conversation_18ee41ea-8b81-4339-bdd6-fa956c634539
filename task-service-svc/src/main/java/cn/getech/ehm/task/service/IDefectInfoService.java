package cn.getech.ehm.task.service;

import cn.getech.ehm.task.dto.defect.*;
import cn.getech.ehm.task.dto.task.info.DefectCountDto;
import cn.getech.ehm.task.entity.DefectInfo;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.IBaseService;

import java.util.List;
import java.util.Map;

/**
 * <pre>
 * 缺陷信息 服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2022-02-21
 */
public interface IDefectInfoService extends IBaseService<DefectInfo> {

    /**
     * 分页查询，返回Dto
     *
     * @param defectInfoQueryParam
     * @return
     */
    PageResult<DefectInfoDto> pageDto(DefectInfoQueryParam defectInfoQueryParam, Integer searchType);

    /**
     * 保存
     *
     * @param defectInfoAddParam
     * @return
     */
    String saveByParam(DefectInfoAddParam defectInfoAddParam);

    /**
     * 根据id查询，转dto
     *
     * @param id
     * @return
     */
    DefectInfoDto getDtoById(String id);

    /**
     * 批量保存
     *
     * @param rows
     */
    boolean saveDtoBatch(List<DefectInfoDto> rows);

    /**
     * 更新
     *
     * @param defectInfoEditParam
     */
    boolean updateByParam(DefectInfoEditParam defectInfoEditParam);

    public Boolean updateDefectStatusById(String defectId, Integer defectStatus);

    public Boolean updateMaintTaskIdCodeById(String defectId, String maintTaskId, String maintTaskCode);

    public Boolean submitCheck(DefectInfoSubmitCheckParam param);

    public void closeDefect(String defectId, String reason);

    public Boolean submitDone(DefectInfoSubmitDoneParam param);

    public DefectInfo submitDealPerson(DefectInfoSubmitDealPersonParam param);

    public Boolean submitDealPersonAndMaint(DefectInfoSubmitDealPersonAndMaintParam param);

    public String generateMaintCode();

    public List<DefectCountDto> getCountOfApp();

    String getCountOfStatistics(String param);

    public String getCountOfStatisticsOverTime(String param);

    Boolean updateRealDealContent(DefectInfoEditParam defectInfoEditParam);

    /**
     * 工单对应名称
     * @param taskIds
     * @return
     */
    Map<String, String> getNameByTaskId(List<String> taskIds);

    public String saveByParamAndCreateMaint(DefectInfoAddMaintParam defectInfoAddParam);
}