package cn.getech.ehm.task.service;

import cn.getech.ehm.task.entity.MaintTaskConfig;
import cn.getech.poros.framework.common.service.IBaseService;
import cn.getech.ehm.task.dto.taskConfig.MaintTaskConfigQueryParam;
import cn.getech.ehm.task.dto.taskConfig.MaintTaskConfigAddParam;
import cn.getech.ehm.task.dto.taskConfig.MaintTaskConfigEditParam;
import cn.getech.ehm.task.dto.taskConfig.MaintTaskConfigDto;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <pre>
 * 工单配置信息 服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2022-04-21
 */
public interface IMaintTaskConfigService extends IBaseService<MaintTaskConfig> {

        /**
         * 分页查询，返回Dto
         *
         * @param maintTaskConfigQueryParam
         * @return
         */
        PageResult<MaintTaskConfigDto> pageDto(MaintTaskConfigQueryParam maintTaskConfigQueryParam);

        /**
         * 保存
         * @param maintTaskConfigAddParam
         * @return
         */
        boolean saveByParam(MaintTaskConfigAddParam maintTaskConfigAddParam);

        /**
         * 根据id查询，转dto
         * @param id
         * @return
         */
        MaintTaskConfigDto getDtoById(Long id);

        /**
         * 批量保存
         * @param rows
         */
        boolean saveDtoBatch(List<MaintTaskConfigDto> rows);

        /**
         * 更新
         * @param maintTaskConfigEditParam
         */
        boolean updateByParam(MaintTaskConfigEditParam maintTaskConfigEditParam);

        public Integer getByTaskTypeAndConfigType(Integer taskType, Integer configType);

        public boolean updateManualRepairSendRule(MaintTaskConfigEditParam maintTaskConfigEditParam);

        public String getManualRepairSendRule();

        public PageResult<MaintTaskConfigDto> pageDtoOfSendRule(MaintTaskConfigQueryParam maintTaskConfigQueryParam);

        public MaintTaskConfig getConfig(String taskType, String configType);

        PageResult<MaintTaskConfigDto> pageDtoOfOvertimeCheckRule(MaintTaskConfigQueryParam maintTaskConfigQueryParam);

        public PageResult<MaintTaskConfigDto> listNotifyRule(MaintTaskConfigQueryParam maintTaskConfigQueryParam);
}