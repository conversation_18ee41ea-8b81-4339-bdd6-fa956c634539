package cn.getech.ehm.task.config;

import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RabbitMQConfig {
    public static final String DIRECT_EXCHANGE = "maint_plan_exchange";
    public static final String DIRECT_QUEUE = "maint_plan_queue";
    public static final String DIRECT_ROUTING_KEY = "maint_plan_direct_key";


    // 声明 Direct 交换机
    @Bean
    public DirectExchange directExchange() {
        return new DirectExchange(DIRECT_EXCHANGE);
    }

    // 声明队列
    @Bean
    public Queue demoQueue() {
        return new Queue(DIRECT_QUEUE, true); // 持久化队列
    }

    // 绑定队列到交换机，并指定路由键
    @Bean
    public Binding binding(Queue demoQueue, DirectExchange directExchange) {
        return BindingBuilder.bind(demoQueue)
                .to(directExchange)
                .with(DIRECT_ROUTING_KEY);
    }
}
