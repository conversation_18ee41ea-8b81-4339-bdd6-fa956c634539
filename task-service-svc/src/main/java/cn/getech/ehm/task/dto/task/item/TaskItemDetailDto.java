package cn.getech.ehm.task.dto.task.item;

import cn.getech.ehm.base.dto.AttachmentClientDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

/**
 * 作业项目
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "TaskItemDetailDto", description = "工单作业项详情")
public class TaskItemDetailDto {

    private String taskId;

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "大类")
    private String largeCategory;

    @ApiModelProperty(value = "小类")
    private String subCategory;

    @ApiModelProperty(value = "作业类型等级")
    private String[] jobTypeLevel;

    @ApiModelProperty(value = "作业类型等级名称")
    private String jobTypeLevelNames;

    @ApiModelProperty(value = "作业内容")
    private String content;

    @ApiModelProperty(value = "标准工时(人*min)")
    private BigDecimal standardTime;

    @ApiModelProperty(value = "作业时间")
    private BigDecimal workingTime;

    @ApiModelProperty(value = "投入人员")
    private Integer inputPerson;

    @ApiModelProperty(value = "作业方法")
    private String method;

    @ApiModelProperty(value = "作业方法详情")
    private String methodDetail;

    @ApiModelProperty(value = "结果类型")
    private String jobItemResultType;

    @ApiModelProperty(value = "结果类型名称")
    private String jobItemResultTypeName;

    @ApiModelProperty(value = "基准目标")
    private String benchmark;

    @ApiModelProperty(value = "目标值")
    private BigDecimal targetValue;

    @ApiModelProperty(value = "区间最大值")
    private BigDecimal targetMax;

    @ApiModelProperty(value = "区间最小值")
    private BigDecimal targetMin;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "单位")
    private String unitName;

    @ApiModelProperty(value = "工具")
    private String tool;

    @ApiModelProperty(value = "特殊要求")
    private String specialRequirements;

    @ApiModelProperty(value = "特殊要求名称")
    private String specialRequirementsName;

    @ApiModelProperty(value = "结果")
    private String result;

    @ApiModelProperty(value = "附件集合")
    private String[] mediaIds;

    @ApiModelProperty(value = "附件集合")
    private List<AttachmentClientDto> mediaList;

    @ApiModelProperty(value = "是否异常")
    private Boolean abnormal;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty(value = "模板附件集合")
    private String[] fileIds;

    @ApiModelProperty(value = "模板附件集合")
    private List<AttachmentClientDto> fileInfoList;

    @ApiModelProperty(value = "索引")
    private String index;

    @ApiModelProperty(value = "上传参数监控0否1指标2波形")
    private Integer iotPush;

    @ApiModelProperty(value = "作业项id")
    private String jobStandardItemId;
}