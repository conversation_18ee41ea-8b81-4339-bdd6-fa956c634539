package cn.getech.ehm.task.dto.plan;

import cn.getech.ehm.common.mp.handler.StringArrayTypeHandler;
import cn.getech.ehm.task.dto.task.notify.MaintNotifyDto;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;


/**
 * 维护计划 返回数据模型
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "MaintPlanDto", description = "维护计划返回数据模型")
public class MaintPlanDto{

    @ApiModelProperty(value = "计划id")
    private String id;

    @ApiModelProperty(value = "计划名称")
    private String name;

    @ApiModelProperty(value = "维保类别")
    private String jobType;

    @ApiModelProperty(value = "维保等级")
    private Integer jobLevel;

    @ApiModelProperty(value = "报修单id")
    private String repairId;

    @ApiModelProperty(value = "紧急程度/优先程度")
    private String urgency;

    @ApiModelProperty(value = "专业")
    private String major;

    @ApiModelProperty(value = "触发逻辑1以上次工单完成日期开始2定时触发")
    private Integer triggerType;

    @ApiModelProperty(value = "间隔值")
    private Integer interVal;

    @ApiModelProperty(value = "提前释放天数")
    private Integer advanceDay;

    @ApiModelProperty(value = "周期 0 单次 0 天 2 周 3 月")
    private String period;

    @ApiModelProperty(value = "周期为周/月时，数组(周为1-7月为1-31，last)")
    @TableField(typeHandler = StringArrayTypeHandler.class)
    private String[] execVal;

    @ApiModelProperty(value = "周期为月时，数组(1-12月份)")
    @TableField(typeHandler = StringArrayTypeHandler.class)
    private String[] monthExecVal;

    @ApiModelProperty(value = "截止日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expiryTime;

    @ApiModelProperty(value = "描述")
    private String remark;

    @ApiModelProperty(value = "0 待发布 1 已发布 2 发布审批中 3 发布驳回")
    private Integer status;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value = "流程实例id")
    private String processInstanceId;

    @ApiModelProperty(value = "工作流任务id")
    private String activityId;

    @ApiModelProperty(hidden = true)
    private String tenantId;

    //截止天数
    private Integer deadlineDays;

    @ApiModelProperty(value = "cbm触发频率")
    private Integer cbmFrequency;

    @ApiModelProperty(value = "cbm触发器")
    private List<CbmTriggerMainDto> cbmTriggerDtos;

    @ApiModelProperty("开关")
    private Integer enabled;

    @ApiModelProperty(value = "设备类型id")
    private String infoCategoryId;

    @ApiModelProperty(value = "设备类型名称")
    private String infoCategoryName;

    @ApiModelProperty(value = "设备位置id")
    private String infoLocationId;

    @ApiModelProperty(value = "设备位置名称")
    private String infoLocationName;

    @ApiModelProperty(value = "设备id集合")
    private String[] equipmentIds;

    @ApiModelProperty(value = "设备名称集合")
    private String equipmentNames;

    @ApiModelProperty(value = "设备id集合")
    private List<PlanEquipmentDetailDto> equipmentDtos;

    @ApiModelProperty(value = "作业标准id")
    private String standardId;

    @ApiModelProperty(value = "作业标准名称")
    private String standardName;

    @ApiModelProperty(value = "说明")
    private String description;

    @ApiModelProperty(value = "维保计划时间集合")
    private List<PlanEquipmentTimeDto> timeDtos;

    @ApiModelProperty("节假日")
    private Integer[] festivalType;

    @ApiModelProperty(value = "超期后处理方式")
    private Integer overdueHandlingMethod;

    @ApiModelProperty(value = "设备停机后处理方式")
    private Integer stopHandlingMethod;

    @ApiModelProperty(value = "临期通知")
    private MaintNotifyDto adventNotify;

}