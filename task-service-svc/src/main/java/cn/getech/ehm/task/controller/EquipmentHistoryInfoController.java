package cn.getech.ehm.task.controller;


import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.task.dto.historyInfo.*;
import cn.getech.ehm.task.service.IEquipmentHistoryInfoService;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import cn.getech.poros.framework.common.utils.ExcelUtils;
import cn.hutool.core.date.DateUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <p>
 * 设备变更历史记录控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-06
 */
@RestController
@RequestMapping("/equipmentHistoryInfo")
@Api(tags = "设备变更历史记录服务接口")
public class EquipmentHistoryInfoController {

    @Autowired
    private IEquipmentHistoryInfoService equipmentHistoryInfoService;

    @ApiOperation("分页获取设备待变更列表")
    @PostMapping("/listRecord")
    //@Permission("equipment:history:info:list")
    public RestResponse<PageResult<EquipmentHistoryInfoDto>> listRecord(@RequestBody @Valid EquipmentHistoryInfoQueryParam equipmentHistoryInfoQueryParam) {
        return RestResponse.ok(equipmentHistoryInfoService.listRecord(equipmentHistoryInfoQueryParam));
    }

    @ApiOperation("分页获取设备变更历史列表")
    @PostMapping("/list")
    //@Permission("equipment:history:info:list")
    public RestResponse<PageResult<EquipmentHistoryInfoDto>> list(@RequestBody @Valid EquipmentHistoryInfoQueryParam equipmentHistoryInfoQueryParam) {
        return RestResponse.ok(equipmentHistoryInfoService.list(equipmentHistoryInfoQueryParam));
    }

    /**
     * 新增设备变更历史记录
     */
    @ApiOperation("新增设备变更历史记录")
    @AuditLog(title = "设备变更历史记录", desc = "新增设备变更历史记录", businessType = BusinessType.INSERT)
    @PostMapping
    //@Permission("equipment:history:info:update")
    public RestResponse<Boolean> add(@RequestBody @Valid EquipmentHistoryInfoAddParam equipmentHistoryInfoAddParam) {
        return RestResponse.ok(equipmentHistoryInfoService.saveByParam(equipmentHistoryInfoAddParam));
    }

    /**
     * 修改设备变更历史记录
     */
    @ApiOperation(value = "修改设备变更历史记录")
    @AuditLog(title = "设备变更历史记录", desc = "修改设备变更历史记录", businessType = BusinessType.UPDATE)
    @PutMapping
    //@Permission("equipment:history:info:update")
    public RestResponse<Boolean> update(@RequestBody @Valid EquipmentHistoryInfoEditParam equipmentHistoryInfoEditParam) {
        return RestResponse.ok(equipmentHistoryInfoService.updateByParam(equipmentHistoryInfoEditParam));
    }

    /**
     * 根据id删除设备变更历史记录
     */
    @ApiOperation(value = "根据id删除设备变更历史记录")
    @AuditLog(title = "设备变更历史记录", desc = "设备变更历史记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    //@Permission("equipment:history:info:delete")
    public RestResponse<Boolean> delete(@PathVariable("ids") @NotEmpty Long[] ids) {
        return RestResponse.ok(equipmentHistoryInfoService.removeByIds(ids));
    }

    /**
     * 根据id获取设备变更历史记录
     */
    @ApiOperation(value = "根据id获取设备变更历史记录")
    @GetMapping(value = "/{id}")
    //@Permission("equipment:history:info:list")
    public RestResponse<EquipmentHistoryInfoDto> get(@PathVariable Long id) {
        return RestResponse.ok(equipmentHistoryInfoService.getDtoById(id));
    }

    /**
     * 导出设备变更历史记录列表
     */
    @ApiOperation(value = "导出设备变更历史记录列表")
    @AuditLog(title = "设备变更历史记录", desc = "导出设备变更历史记录列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    // @Permission("equipment:history:info:export")
    public void excelExport(@Valid @RequestBody EquipmentHistoryInfoQueryParam equipmentHistoryInfoQueryParam, HttpServletResponse response) {
        equipmentHistoryInfoQueryParam.setLimit(99999);
        PageResult<EquipmentHistoryInfoDto> pageResult = equipmentHistoryInfoService.list(equipmentHistoryInfoQueryParam);
        ExcelUtils<EquipmentHistoryInfoExcelDto> util = new ExcelUtils<>(EquipmentHistoryInfoExcelDto.class);
        List<EquipmentHistoryInfoExcelDto> result = Lists.newArrayList();
        Integer i = 1;
        for (EquipmentHistoryInfoDto temp : pageResult.getRecords()) {
            EquipmentHistoryInfoExcelDto equipmentHistoryInfoExcelDto = CopyDataUtil.copyObject(temp, EquipmentHistoryInfoExcelDto.class);
            equipmentHistoryInfoExcelDto.setNo("" + i++);
            equipmentHistoryInfoExcelDto.setCreateTime(DateUtil.format(temp.getCreateTime(),"yyyy-MM-dd HH:mm:ss"));
            result.add(equipmentHistoryInfoExcelDto);
        }
        util.exportExcel(result, "设备变更历史记录", response);
    }

    /**
     * Excel导入设备变更历史记录
     */
    @ApiOperation(value = "Excel导入设备变更历史记录")
    @AuditLog(title = "设备变更历史记录", desc = "Excel导入设备变更历史记录", businessType = BusinessType.INSERT)
    @PostMapping("/import")
    //@Permission("equipment:history:info:import")
    public RestResponse<Boolean> excelImport(@RequestParam("file") MultipartFile file) {
        ExcelUtils<EquipmentHistoryInfoDto> util = new ExcelUtils<>(EquipmentHistoryInfoDto.class);
        List<EquipmentHistoryInfoDto> rows = util.importExcel(file);
        if (CollectionUtils.isEmpty(rows)) {
            return RestResponse.failed();
        }
        return RestResponse.ok(equipmentHistoryInfoService.saveDtoBatch(rows));
    }

}
