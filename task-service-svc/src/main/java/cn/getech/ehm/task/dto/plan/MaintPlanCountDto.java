package cn.getech.ehm.task.dto.plan;

import cn.getech.ehm.common.constant.StaticValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

/**
 * 维保日期
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "MaintPlanCountDto", description = "维保统计")
public class MaintPlanCountDto {

    @ApiModelProperty(value = "计划id")
    private String planId;

    @ApiModelProperty(value = "上次维保日期")
    private Date lastMaintTime;

    @ApiModelProperty(value = "下次维保时间")
    private Date nextMaintTime;

    @ApiModelProperty(value = "工单数量")
    private Integer taskCount = StaticValue.ZERO;
}