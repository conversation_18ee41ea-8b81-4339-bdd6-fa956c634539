package cn.getech.ehm.task.util;

import com.spire.doc.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.system.ApplicationHome;

import java.io.*;
import java.util.LinkedList;
import java.util.List;

@Slf4j
public class WordtoPDF {

    public static String getUploadResource(String fileName) throws IOException {

        //若文件已存在，则返回的filePath中含有"EXIST"，则不需再重写文件
        String filePath = createFile("new_" + fileName);

        //文件不存在，则创建流输入默认数据到新文件
        if (!filePath.contains("exist")) {
            File newFontFile = new File(filePath);
            try {
                OutputStream os = new FileOutputStream(newFontFile);
                InputStream is = WordtoPDF.class.getResourceAsStream("/mapper/fonts/" + fileName);
                //返回读取指定资源的输入流
                int bytesRead = 0;
                byte[] buffer = new byte[1024];
                if (is != null) {
                    while ((bytesRead = is.read(buffer, 0, 1024)) != -1) {
                        os.write(buffer, 0, bytesRead);
                    }
                } else {
                    log.error("---------------------------InputStream is null-----------------------------");
                    return null;
                }
            } catch (FileNotFoundException e) {
                log.info(e.getMessage(), e);
            } catch (IOException e) {
                log.info(e.getMessage(), e);
            }
            return filePath;
        }
        return filePath.substring(5);
    }

    public static String createFile(String filename) {

        //create file
        String filePath = new ApplicationHome(WordtoPDF.class).getSource().getParentFile().toString() + "/" + filename;

        File file = new File(filePath);
        if (!file.exists()) {
            try {
                file.createNewFile();
            } catch (IOException e) {
                log.info(e.getMessage(), e);
            }
            return filePath;
        }
        return "exist" + filePath;
    }

    public static File word2PDF(String wordFile, String pdfFile) throws IOException {

        //加载word文档
        Document document = new Document();
        document.setEmbedFontsInFile(true);
        document.loadFromFile(wordFile);

        //初始化PrivateFontPath对象，指定私有字体路径
        PrivateFontPath fontPath = new PrivateFontPath("黑体", getUploadResource("simhei.ttf"));

        //创建ToPdfParameterList对象，用于设置Word转PDF的参数
        ToPdfParameterList toPdfParameterList = new ToPdfParameterList();

        //将私有字体作为Word转PDF的参数之一
        List pathList = new LinkedList<>();
        pathList.add(fontPath);
        toPdfParameterList.setPrivateFontPaths(pathList);

        //将Word文档保存为PDF
        document.saveToFile(pdfFile, toPdfParameterList);

        //保存结果文件
//        document.saveToFile(pdfFile, FileFormat.PDF);
        log.info("WordtoPDF:pdffile=" + pdfFile);
        return new File(pdfFile);
    }

    public static void main(String[] args) {

        //加载word示例文档
        Document document = new Document();
        document.loadFromFile("E:\\java\\wk\\kangjisen\\task-service\\task-service-svc\\src\\main\\resources\\mapper\\template\\RemoteRepairReport_output.docx");
        //保存结果文件
        document.saveToFile("E:\\java\\wk\\kangjisen\\task-service\\task-service-svc\\src\\main\\resources\\mapper\\template\\RemoteRepairReport_output.pdf", FileFormat.PDF);
    }
}
