package cn.getech.ehm.task.enmu;

import cn.getech.ehm.common.dto.EnumListDto;

import java.util.ArrayList;
import java.util.List;

/**
 * 通知对象枚举
 * <AUTHOR>
 * @date 2020/3/3
 */
public enum NotifyObjectType {
    HANDLER(1, "工单处理人员"),
    EQUIPMENT_MANAGER(2, "设备管理责任人"),
    MAINT_PERSON(3,"指定维护人员");
    //CUSTOM(4,"自定义");


    NotifyObjectType(int value, String name) {
        this.value = value;
        this.name = name;
    }

    private int value;

    private String name;

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static List<EnumListDto> getList(){
        List<EnumListDto> enumListDtos = new ArrayList<>();
        for(NotifyObjectType notifyObjectType : NotifyObjectType.values()){
            EnumListDto enumListDto = new EnumListDto();
            enumListDto.setValue(notifyObjectType.value);
            enumListDto.setName(notifyObjectType.name);
            enumListDtos.add(enumListDto);
        }
        return enumListDtos;
    }
}

