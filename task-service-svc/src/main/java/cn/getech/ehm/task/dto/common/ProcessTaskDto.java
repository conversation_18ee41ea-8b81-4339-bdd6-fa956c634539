package cn.getech.ehm.task.dto.common;

import cn.getech.poros.bpm.dto.task.TaskDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 待办DTO
 * <AUTHOR>
 * @date 2020-09-08 17:11:20
 **/
@Getter
@Setter
public class ProcessTaskDto extends TaskDto {

    /**
     * 业务单ID
     */
    @ApiModelProperty(value = "业务单ID")
    private String id;

    /**
     * 业务单号
     */
    @ApiModelProperty(value = "业务单号")
    private String code;

    /**
     * 业务单状态
     */
    @ApiModelProperty(value = "业务单状态")
    private Integer status;

    /**
     * 业务单类型
     */
    @ApiModelProperty(value = "业务单类型: 1故障报修2维保计划5备件调拨6备件盘点7维护计划8资源异动9远程诊断15资产出入库16资产盘点")
    private Integer type;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

}
