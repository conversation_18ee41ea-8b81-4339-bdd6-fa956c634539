package cn.getech.ehm.task.dto.task.info;

import cn.getech.ehm.common.mp.handler.StringArrayTypeHandler;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 维护工单主表 返回数据模型
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "MaintTaskFileParam", description = "MaintTaskFileParam")
public class MaintTaskFileParam {

    @ApiModelProperty(value = "工单id")
    private String id;

    @TableField(typeHandler = StringArrayTypeHandler.class)
    private String[] finishFileIds;
}