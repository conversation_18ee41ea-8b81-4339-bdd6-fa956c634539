package cn.getech.ehm.task.controller;

import cn.getech.ehm.task.dto.FaultStatisticsDto;
import cn.getech.ehm.task.dto.FaultStatisticsParam;
import cn.getech.ehm.task.service.IMaintTaskService;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 故障统计报表控制器
 *
 * <AUTHOR>
 * @since 2021-01-13
 */
@RestController
@RequestMapping("/statistics")
@Api(tags = "web接口：故障统计服务接口")
public class FaultStatisticsController {

    @Autowired
    @Lazy
    private IMaintTaskService maintTaskService;

    /**
     * 所有故障类型统计
     */
    @ApiOperation("所有故障类型统计")
    @AuditLog(title = "所有故障类型统计",desc = "所有故障类型统计",businessType = BusinessType.QUERY)
    @PostMapping("/allEquipmentFaultStatistics")
    public RestResponse<List<FaultStatisticsDto>> allEquipmentFaultStatistics(@RequestBody FaultStatisticsParam param) {
        return RestResponse.ok(maintTaskService.allFaultEquipmentStatistics(param));
    }

    /**
     * 故障设备统计(只统计前几个)
     */
    @ApiOperation("故障设备统计")
    @AuditLog(title = "故障设备统计",desc = "故障设备统计",businessType = BusinessType.QUERY)
    @PostMapping("/equipmentFaultStatistics")
    public RestResponse<List<FaultStatisticsDto>> equipmentFaultStatistics(@RequestBody FaultStatisticsParam param) {
        return RestResponse.ok(maintTaskService.faultEquipmentStatistics(param));
    }

}