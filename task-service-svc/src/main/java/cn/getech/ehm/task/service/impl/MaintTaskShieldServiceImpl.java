package cn.getech.ehm.task.service.impl;

import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.task.dto.shield.MaintTaskShieldDto;
import cn.getech.ehm.task.dto.ticket.JobTicketDto;
import cn.getech.ehm.task.dto.ticket.JobTicketItemDto;
import cn.getech.ehm.task.entity.JobTicket;
import cn.getech.ehm.task.entity.MaintTaskShield;
import cn.getech.ehm.task.mapper.MaintTaskShieldMapper;
import cn.getech.ehm.task.service.IMaintTaskShieldService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;


/**
 * 工单屏蔽
 */
@Slf4j
@Service
public class MaintTaskShieldServiceImpl extends BaseServiceImpl<MaintTaskShieldMapper, MaintTaskShield> implements IMaintTaskShieldService {

    @Autowired
    private MaintTaskShieldMapper maintTaskShieldMapper;

    @Override
    public Boolean updateByParam(MaintTaskShieldDto dto) {
        MaintTaskShield maintTaskShield = CopyDataUtil.copyObject(dto, MaintTaskShield.class);
        return saveOrUpdate(maintTaskShield);
    }

    @Override
    public MaintTaskShieldDto getDto() {
        MaintTaskShield maintTaskShield = maintTaskShieldMapper.selectOne(null);
        if(null == maintTaskShield){
            maintTaskShield = new MaintTaskShield();
            maintTaskShield.setId(UUID.randomUUID().toString().replace("-",""));
            maintTaskShield.setEnabled(false);
            maintTaskShield.setTaskEnabled(false);
            maintTaskShield.setTaskEnabled(false);
            maintTaskShield.setFaultEnabled(false);
            maintTaskShield.setFaultInfluences(new String[]{});
            maintTaskShield.setTaskStatus(new Integer[]{});
            maintTaskShield.setWeekDates(new Integer[]{});
        }
        return CopyDataUtil.copyObject(maintTaskShield, MaintTaskShieldDto.class);
    }
}
