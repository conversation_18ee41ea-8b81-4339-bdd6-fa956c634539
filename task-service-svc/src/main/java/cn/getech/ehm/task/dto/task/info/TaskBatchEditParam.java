package cn.getech.ehm.task.dto.task.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 维护工单处理提交参数
 * <AUTHOR>
 */
@Data
@ApiModel(value = "TaskBatchEditParam", description = "维护工单批量操作提交参数")
public class TaskBatchEditParam {

    @ApiModelProperty(value = "提交类型(枚举)")
    private Integer submitType;

    @ApiModelProperty(value = "工单IDs")
    private List<String> taskIds;

    @ApiModelProperty(value = "评分")
    private Integer grade;

    @ApiModelProperty(value = "评价")
    private String comment;

    @ApiModelProperty(value = "处理情况")
    private String handleInformation;

    @ApiModelProperty(value = "结果描述")
    private String handleResult;

    @ApiModelProperty(value = "验收结果0未通过1通过")
    private Integer checkAcceptResult;

    @ApiModelProperty(value = "备注/审批意见/关闭原因")
    private String remark;
}