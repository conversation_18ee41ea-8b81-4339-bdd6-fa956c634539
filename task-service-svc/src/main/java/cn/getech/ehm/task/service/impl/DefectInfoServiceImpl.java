package cn.getech.ehm.task.service.impl;

import cn.getech.ehm.base.client.BaseServiceClient;
import cn.getech.ehm.base.dto.AttachmentClientDto;
import cn.getech.ehm.base.dto.DictionaryItemDto;
import cn.getech.ehm.base.dto.MaintPersonDto;
import cn.getech.ehm.base.dto.MaintTeamDto;
import cn.getech.ehm.common.context.UserContextHolder;
import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.equipment.client.EquipmentClient;
import cn.getech.ehm.equipment.dto.BuildInfoSearchDto;
import cn.getech.ehm.equipment.dto.EquipmentInfoDto;
import cn.getech.ehm.equipment.dto.EquipmentInfoSearchDto;
import cn.getech.ehm.equipment.dto.EquipmentListDto;
import cn.getech.ehm.task.dto.defect.*;
import cn.getech.ehm.task.dto.task.info.DefectCountDto;
import cn.getech.ehm.task.dto.task.info.MaintTaskDefectAddDto;
import cn.getech.ehm.task.entity.DefectInfo;
import cn.getech.ehm.task.entity.MaintTask;
import cn.getech.ehm.task.entity.PermissionMenu;
import cn.getech.ehm.task.enums.*;
import cn.getech.ehm.task.mapper.DefectInfoMapper;
import cn.getech.ehm.task.service.IDefectInfoService;
import cn.getech.ehm.task.service.IMaintTaskService;
import cn.getech.ehm.task.service.feign.PermissionClient;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.api.ResultCode;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.framework.common.context.PorosContextHolder;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <pre>
 * 缺陷信息 服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2022-02-21
 */
@Slf4j
@Service
public class DefectInfoServiceImpl extends BaseServiceImpl<DefectInfoMapper, DefectInfo> implements IDefectInfoService {

    @Autowired
    EquipmentClient equipmentClient;
    @Autowired
    BaseServiceClient baseServiceClient;
    @Autowired
    private ResourceBundleMessageSource messageSource;
    @Autowired
    @Lazy
    private IMaintTaskService maintTaskService;
    @Autowired
    private PermissionClient permissionClient;
    @Autowired
    private DefectInfoMapper defectInfoMapper;

    private final static String defectStatusDict = "defect_status";
    private final static String defectTypeDict = "defect_type";
    private final static String defectCheckStatusDict = "defect_check_status";
    private final static String defectOverTimeStatusDict = "defect_overtime_status";
    private final static String permissionSysCode = "web-admin";
    private final static String permissionCodeDefectMineApp = "defect_mine_app";
    private final static String permissionCodeDefectTodoApp = "defect_todo_app";
    private final static String permissionCodeDefectListApp = "defect_list_app";

    private final static Integer pageSearchTypeAll = 1;
    private final static Integer pageSearchTypeMINE = 2;
    private final static Integer pageSearchTypeTODO = 3;

    @Override
    public PageResult<DefectInfoDto> pageDto(DefectInfoQueryParam defectInfoQueryParam, Integer searchType) {
        Wrapper<DefectInfo> wrapper = getPageSearchWrapper(defectInfoQueryParam, searchType);
        PageResult<DefectInfo> page = page(defectInfoQueryParam, wrapper);
        PageResult<DefectInfoDto> result = new PageResult<>();
        result.setRecords(CopyDataUtil.copyList(page.getRecords(), DefectInfoDto.class));
        result.setTotal(page.getTotal());
        if (!(result != null && result.getRecords().size() > 0)) {
            return result;
        }
        //获取字典翻译
        Map<String, DictionaryItemDto> defectStatuItemMapByCode = this.checkDict(defectStatusDict);
        Map<String, DictionaryItemDto> defectTypeItemMapByCode = this.checkDict(defectTypeDict);
        Map<String, DictionaryItemDto> defectCheckStatusItemMapByCode = this.checkDict(defectCheckStatusDict);
        Map<String, DictionaryItemDto> defectOvertimeStatusItemMapByCode = this.checkDict(defectOverTimeStatusDict);

        List<String> equipmentIdsList = result.getRecords().stream().map(DefectInfoDto::getEquipmentId).collect(Collectors.toList());
        RestResponse<Map<String, EquipmentListDto>> equipmentListByIds = equipmentClient.getListByIds(equipmentIdsList.toArray(new String[equipmentIdsList.size()]));
        Date now = new Date();
        String uid = PorosContextHolder.getCurrentUser().getUid();
        if (equipmentListByIds.isOk()) {
            List<String> picIdList = Lists.newArrayList();
            for (String key : equipmentListByIds.getData().keySet()) {
                EquipmentListDto equipmentListDto = equipmentListByIds.getData().get(key);
                if (StringUtils.isNotBlank(equipmentListDto.getPicId())) {
                    picIdList.add(equipmentListDto.getPicId());
                }
            }
            Map<String, AttachmentClientDto> attachmentMap = this.getAttachmentMap(picIdList);
            for (DefectInfoDto temp : result.getRecords()) {
                temp = this.getOverTimeStatus(temp);
                EquipmentListDto tempEquipmentListDto = equipmentListByIds.getData().get(temp.getEquipmentId());
                temp.setEquipmentListDto(tempEquipmentListDto);
                if (tempEquipmentListDto != null) {
                    temp.setEquipmentPicUrls(this.buildPicUrlList(attachmentMap, tempEquipmentListDto.getPicId()));
                }
                temp.setDefectStatusName(this.getDictValue(defectStatuItemMapByCode, temp.getDefectStatus(), defectStatusDict));
                temp.setDefectTypeName(this.getDictValue(defectTypeItemMapByCode, temp.getDefectType(), defectTypeDict));
                temp.setCheckStatusName(this.getDictValue(defectCheckStatusItemMapByCode, temp.getCheckStatus(), defectCheckStatusDict));
                temp.setHasOverTimeName(this.getDictValue(defectOvertimeStatusItemMapByCode, temp.getHasOverTime(), defectOverTimeStatusDict));
                if (StringUtils.isNotBlank(temp.getDealPersonIds())) {
                    temp.setDealPersonInfos(this.getMaintainerInfo(temp.getDealPersonIds()));
                }
                if (ArrayUtil.isNotEmpty(temp.getUids()) && Arrays.asList(temp.getUids()).contains(uid)) {
                    temp.setPermissionFlag(true);
                }
            }
        } else {
            log.info("连接equipment-service获取{}失败", "设备信息");
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("defect_get_equipment_error", null, LocaleContextHolder.getLocale())));
        }
        return Optional.ofNullable(result).orElse(new PageResult<>());
    }

    public DefectInfoDto getOverTimeStatus(DefectInfoDto temp) {
        //默认未超时
        temp.setHasOverTime(0);
        if (temp.getEndTime() != null) {
            if (temp.getDealDate() != null && temp.getDealDate().after(temp.getEndTime())) {
                temp.setHasOverTime(1);
                return temp;
            }
            if (temp.getCloseDate() != null && temp.getCloseDate().after(temp.getEndTime())) {
                temp.setHasOverTime(1);
                return temp;
            }
            if (ObjectUtils.isNull(temp.getDealDate()) && ObjectUtils.isNull(temp.getCloseDate()) && temp.getEndTime().before(new Date())) {
                temp.setHasOverTime(1);
                return temp;
            }
        }
        return temp;
    }

    public List<String> buildPicUrlList(Map<String, AttachmentClientDto> attachmentMap, String picIds) {
        if (StringUtils.isNotBlank(picIds)) {
            List<String> picUrlList = Lists.newArrayList();
            String[] picIdArray = picIds.split(",");
            for (String picId : picIdArray) {
                AttachmentClientDto attachmentClientDto = attachmentMap.get(picId);
                if (attachmentClientDto != null) {
                    picUrlList.add(attachmentClientDto.getUrl());
                }
            }
            return picUrlList;
        } else {
            return Lists.newArrayList();
        }
    }

    public List<String> buildPicUrlList(String picIds) {
        if (StringUtils.isNotBlank(picIds)) {
            String[] picIdArray = picIds.split(",");
            Map<String, AttachmentClientDto> attachmentMap = this.getAttachmentMap(Arrays.asList(picIdArray));
            List<String> picUrlList = Lists.newArrayList();
            for (String picId : picIdArray) {
                AttachmentClientDto attachmentClientDto = attachmentMap.get(picId);
                if (attachmentClientDto != null) {
                    picUrlList.add(attachmentClientDto.getUrl());
                }
            }
            return picUrlList;
        } else {
            return Lists.newArrayList();
        }
    }

    private Map<String, AttachmentClientDto> getAttachmentMap(List<String> picIdList) {
        Map<String, AttachmentClientDto> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(picIdList)) {
            List<String> allPicIds = new ArrayList<>();
            for (String picIdStr : picIdList) {
                if (StringUtils.isNotBlank(picIdStr)) {
                    String[] strs = picIdStr.split(StringPool.COMMA);
                    allPicIds.addAll(Arrays.asList(strs));
                }
            }
            allPicIds = allPicIds.stream().distinct().collect(Collectors.toList());
            RestResponse<Map<String, AttachmentClientDto>> restResponse = baseServiceClient.getAttachmentMap(allPicIds.toArray(new String[allPicIds.size()]));
            if (restResponse.isOk()) {
                map = restResponse.getData();
            } else {
                log.error("获取附件失败");
            }
        }
        return map;
    }

    //获取维护人员信息
    private List<MaintPersonDto> getMaintainerInfo(String uids) {
        if (StringUtils.isBlank(uids)) {
            return Lists.newArrayList();
        }
        RestResponse<List<MaintPersonDto>> listByIds = baseServiceClient.getMaintainerListByIds(uids.split(","));
        if (listByIds.isOk()) {
            return listByIds.getData();
        } else {
            log.info("连接base-service获取{}失败", "维护人员列表");
            return Lists.newArrayList();
        }
    }

    //获取字典信息并输出错误log
    private Map<String, DictionaryItemDto> checkDict(String dictType) {
        RestResponse<Map<String, DictionaryItemDto>> baseServiceClientItemMapByCode = baseServiceClient.getItemMapByCode(dictType);
        if (!baseServiceClientItemMapByCode.isOk()) {
            log.info("连接base-service获取{}字典表失败", dictType);
            return Maps.newHashMap();
        } else {
            return baseServiceClientItemMapByCode.getData();
        }
    }

    //获取字典翻译，无翻译则使用系统enum
    private String getDictValue(Map<String, DictionaryItemDto> dictInfo, Integer dictOriginalValue, String dictType) {
        if (ObjectUtils.isEmpty(dictOriginalValue)) {
            return "";
        }
        DictionaryItemDto dictInfoOrDefault = dictInfo.getOrDefault(dictOriginalValue.toString(), null);
        if (dictInfoOrDefault != null) {
            return dictInfoOrDefault.getName();
        } else {
            //log.info("dictType:{}无字典配置，使用默认enum", dictType);
            return this.getDefaultEnum(dictOriginalValue, dictType);
        }
    }

    //获取enum翻译
    private String getDefaultEnum(Integer dictOriginalValue, String dictType) {
        if (dictType.equals(defectStatusDict)) {
            return DefectStatusEnum.getNameByValue(dictOriginalValue);
        }
        if (dictType.equals(defectTypeDict)) {
            return DefectTypeEnum.getNameByValue(dictOriginalValue);
        }
        if (dictType.equals(defectCheckStatusDict)) {
            return DefectCheckStatusEnum.getNameByValue(dictOriginalValue);
        }
        if (dictType.equals(defectOverTimeStatusDict)) {
            return DefectOvertimeStatusEnum.getNameByValue(dictOriginalValue);
        }
        return "" + dictOriginalValue;
    }


    @SuppressWarnings("unchecked")
    @Override
    public String saveByParam(DefectInfoAddParam defectInfoAddParam) {
        DefectInfo defectInfo = CopyDataUtil.copyObject(defectInfoAddParam, DefectInfo.class);
        //默认未处理、未核查
        defectInfo.setDefectStatus(DefectStatusEnum.NODEAL.getValue());
        defectInfo.setCheckStatus(DefectCheckStatusEnum.NOCHECK.getValue());
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, defectInfo);
        RestResponse<EquipmentInfoDto> equipmentInfo = equipmentClient.getEquipmentInfo(defectInfoAddParam.getEquipmentId());
        if (!equipmentInfo.isOk() || ObjectUtils.isEmpty(equipmentInfo.getData())) {
            throw new GlobalServiceException(GlobalResultMessage.of("未查询到设备信息"));
        }
        EquipmentInfoDto data = equipmentInfo.getData();
        String[] teamIds = data.getTeamIds();
        RestResponse<List<MaintTeamDto>> maintTeamListByIds = baseServiceClient.getMaintTeamListByIds(teamIds);
        if (!maintTeamListByIds.isOk() || CollectionUtils.isEmpty(maintTeamListByIds.getData())) {
            throw new GlobalServiceException(GlobalResultMessage.of("未查询到设备关联维保班组信息"));
        }
        List<MaintTeamDto> data1 = maintTeamListByIds.getData();
        List<String> leaderId = data1.stream().map(item -> item.getLeaderId()).collect(Collectors.toList());
        RestResponse<List<MaintPersonDto>> maintainerListByIds = baseServiceClient.getMaintainerListByIds(leaderId.toArray(new String[leaderId.size()]));
        if (!maintainerListByIds.isOk() || CollectionUtils.isEmpty(maintainerListByIds.getData())) {
            throw new GlobalServiceException(GlobalResultMessage.of("未查询到设备关联维保负责人信息"));
        }
        List<MaintPersonDto> data2 = maintainerListByIds.getData();
        List<String> collect = data2.stream().map(item -> item.getUid()).collect(Collectors.toList());
        defectInfo.setUids(collect.toArray(new String[collect.size()]));
        this.save(defectInfo);
        return defectInfo.getId();
    }

    @Override
    public String saveByParamAndCreateMaint(DefectInfoAddMaintParam defectInfoAddParam) {
        DefectInfoAddParam defectInfoAddParam1 = CopyDataUtil.copyObject(defectInfoAddParam, DefectInfoAddParam.class);
        String s = this.saveByParam(defectInfoAddParam1);
        this.submitDealPersonAndMaint(defectInfoAddParam);
        return s;
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean updateByParam(DefectInfoEditParam defectInfoEditParam) {
        DefectInfo defectInfo = CopyDataUtil.copyObject(defectInfoEditParam, DefectInfo.class);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, defectInfo);
        return updateById(defectInfo);
    }


    public DefectInfoDto getDtoById(String id) {
        DefectInfoDto defectInfoDto = CopyDataUtil.copyObject(this.getById(id), DefectInfoDto.class);
        if (ObjectUtils.isNull(defectInfoDto)) {
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("defect_get_no_exist_error", null, LocaleContextHolder.getLocale())));
        }
        if (StringUtils.isNotBlank(defectInfoDto.getDealPersonIds())) {
            defectInfoDto.setDealPersonInfos(this.getMaintainerInfo(defectInfoDto.getDealPersonIds()));
        }
        if (StringUtils.isNotBlank(defectInfoDto.getMaintTaskId())) {
            MaintTask maintTask = (MaintTask) maintTaskService.getById(defectInfoDto.getMaintTaskId());
            if (maintTask != null && maintTask.getBeginDowntime() != null && maintTask.getBeginDowntime() != null) {
                defectInfoDto.setBeginDowntime(maintTask.getBeginDowntime());
                defectInfoDto.setEndDowntime(maintTask.getEndDowntime());
                defectInfoDto.setDownTimeCost("" + DateUtil.between(maintTask.getBeginDowntime(), maintTask.getEndDowntime(), DateUnit.MINUTE));
                defectInfoDto.setStopped(1);
            } else {
                defectInfoDto.setStopped(0);
            }
        }
        //获取字典翻译
        Map<String, DictionaryItemDto> defectStatuItemMapByCode = this.checkDict(defectStatusDict);
        Map<String, DictionaryItemDto> defectTypeItemMapByCode = this.checkDict(defectTypeDict);
        Map<String, DictionaryItemDto> defectCheckStatusItemMapByCode = this.checkDict(defectCheckStatusDict);
        Map<String, DictionaryItemDto> defectOvertimeStatusItemMapByCode = this.checkDict(defectOverTimeStatusDict);
        RestResponse<EquipmentInfoDto> equipmentInfo = equipmentClient.getEquipmentInfo(defectInfoDto.getEquipmentId());
        Date now = new Date();
        //默认未超时
        defectInfoDto.setHasOverTime(0);
        if (defectInfoDto.getEndTime() != null && now.after(defectInfoDto.getEndTime())) {
            defectInfoDto.setHasOverTime(1);
        }
        defectInfoDto.setDefectStatusName(this.getDictValue(defectStatuItemMapByCode, defectInfoDto.getDefectStatus(), defectStatusDict));
        defectInfoDto.setDefectTypeName(this.getDictValue(defectTypeItemMapByCode, defectInfoDto.getDefectType(), defectTypeDict));
        defectInfoDto.setCheckStatusName(this.getDictValue(defectCheckStatusItemMapByCode, defectInfoDto.getCheckStatus(), defectCheckStatusDict));
        defectInfoDto.setHasOverTimeName(this.getDictValue(defectOvertimeStatusItemMapByCode, defectInfoDto.getHasOverTime(), defectOverTimeStatusDict));
        defectInfoDto.setDealPersonInfos(this.getMaintainerInfo(defectInfoDto.getDealPersonIds()));
        UserBaseInfo userBaseInfo = UserContextHolder.getContext().getUserBaseInfo();
        if (userBaseInfo != null && ArrayUtil.isNotEmpty(defectInfoDto.getUids()) && Arrays.asList(defectInfoDto.getUids()).contains(userBaseInfo.getUid())) {
            defectInfoDto.setPermissionFlag(true);
        }
        if (equipmentInfo.isOk()) {
            defectInfoDto.setEquipmentInfoDto(equipmentInfo.getData());
            defectInfoDto.setEquipmentPicUrls(this.buildPicUrlList(equipmentInfo.getData().getPicId()));

        }
        return defectInfoDto;
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveDtoBatch(List<DefectInfoDto> rows) {
        return saveBatch(CopyDataUtil.copyList(rows, DefectInfo.class));
    }

    private Wrapper<DefectInfo> getPageSearchWrapper(DefectInfoQueryParam defectInfoQueryParam, Integer searchType) {
        LambdaQueryWrapper<DefectInfo> wrapper = Wrappers.<DefectInfo>lambdaQuery();
        List<String> equipmentIds = Lists.newArrayList();
        //如果有设备信息相关的查询
        EquipmentInfoSearchDto equipmentInfoSearchDto = new EquipmentInfoSearchDto();
        equipmentInfoSearchDto.setLocationIds(defectInfoQueryParam.getLocationIds());
        equipmentInfoSearchDto.setKeyword(defectInfoQueryParam.getKeyword());
        RestResponse<List<String>> equipmentIdsByParam = equipmentClient.getEquipmentIdsByParam(equipmentInfoSearchDto);

        if (equipmentIdsByParam.isOk()) {
            if (CollectionUtils.isEmpty(equipmentIdsByParam.getData())) {
                //如果根据设备信息查询到设备为空，wrapper返回空信息体
                wrapper.apply("1=0");
            }
            equipmentIds.addAll(equipmentIdsByParam.getData());
        } else {
            log.info("连接equipment-service获取{}失败", "设备ids");
        }
        //如果是查询我的缺陷处理
        StringBuilder sb = new StringBuilder();
        if (searchType == pageSearchTypeTODO) {
            String uid = PorosContextHolder.getCurrentUser().getUid();
            RestResponse<String> maintainerId = baseServiceClient.getIdByUid(uid);
            if (maintainerId.isOk()) {
                if (StringUtils.isNotBlank(maintainerId.getData())) {
                    sb.append(maintainerId.getData());
                } else {
                    //该人员不是维保人员
                    log.info("该人员不是维保人员");
                    wrapper.apply("1=0");
                }
            } else {
                log.info("构造缺陷查询语句时查不到当前用户在维保人员中的登记");
                wrapper.apply("1=0");
            }
        }
        this.buildHasOverTimeWrapper(wrapper, defectInfoQueryParam);
        this.buildCreatePersonWrapper(wrapper, defectInfoQueryParam);
        this.buildDealPersonWrapper(wrapper, defectInfoQueryParam);
        //缺陷内容模糊搜索
        wrapper.like(StringUtils.isNotBlank(defectInfoQueryParam.getDefectContent()),
                        DefectInfo::getDefectContent, defectInfoQueryParam.getDefectContent())
                //缺陷名称的模糊搜索
                .like(StringUtils.isNotBlank(defectInfoQueryParam.getDefectName()), DefectInfo::getDefectName, defectInfoQueryParam.getDefectName())
                //创建时间段筛选
                .ge(ObjectUtils.isNotEmpty(defectInfoQueryParam.getSearchStartDate()), DefectInfo::getCreateTime, defectInfoQueryParam.getSearchStartDate())
                .le(ObjectUtils.isNotEmpty(defectInfoQueryParam.getSearchEndDate()), DefectInfo::getCreateTime, defectInfoQueryParam.getSearchEndDate())
                //处理时间段筛选
                .ge(ObjectUtils.isNotEmpty(defectInfoQueryParam.getDealTimeStartDate()), DefectInfo::getDealDate, defectInfoQueryParam.getDealTimeStartDate())
                .le(ObjectUtils.isNotEmpty(defectInfoQueryParam.getDealTimeEndDate()), DefectInfo::getDealDate, defectInfoQueryParam.getDealTimeEndDate())
                //设备位号，名称
                .in(CollectionUtil.isNotEmpty(equipmentIds), DefectInfo::getEquipmentId, equipmentIds)
                //设备状态
                .eq(StringUtils.isNotBlank(defectInfoQueryParam.getDefectStatus()), DefectInfo::getDefectStatus, defectInfoQueryParam.getDefectStatus())
                .in(ArrayUtil.isNotEmpty(defectInfoQueryParam.getDefectStatusArray()), DefectInfo::getDefectStatus, defectInfoQueryParam.getDefectStatusArray())
                //缺陷种类
                .eq(StringUtils.isNotBlank(defectInfoQueryParam.getDefectType()), DefectInfo::getDefectType, defectInfoQueryParam.getDefectType())
                //验收状态
                .eq(StringUtils.isNotBlank(defectInfoQueryParam.getCheckStatus()), DefectInfo::getCheckStatus, defectInfoQueryParam.getCheckStatus())
                //查询我的申报
                .eq(searchType == pageSearchTypeMINE, DefectInfo::getCreateBy, PorosContextHolder.getCurrentUser().getUid())
                //查询我的缺陷处理
                .like(searchType == pageSearchTypeTODO && StringUtils.isNotBlank(sb.toString()), DefectInfo::getDealPersonIds, sb)
                //根据创建时间倒序
                .orderByDesc(DefectInfo::getUpdateTime, DefectInfo::getCreateTime);
//        if (BaseEntity.class.isAssignableFrom(DefectInfo.class)) {
//            wrapper.orderByDesc(DefectInfo::getUpdateTime, DefectInfo::getCreateTime);
//        }
        return wrapper;
    }

    private Wrapper buildHasOverTimeWrapper(LambdaQueryWrapper<DefectInfo> wrapper, DefectInfoQueryParam defectInfoQueryParam) {
        if (StringUtils.isNotBlank(defectInfoQueryParam.getHasOverTime())) {
            Date now = new Date();
            //已超时
            if (defectInfoQueryParam.getHasOverTime().equals("" + DefectOvertimeStatusEnum.HASOVERTIME.getValue())) {
                wrapper.nested(temp -> temp.nested(item -> item.isNotNull(DefectInfo::getEndTime).isNull(DefectInfo::getDealDate).le(DefectInfo::getEndTime, now)).or().nested(
                        item -> item.isNotNull(DefectInfo::getEndTime).isNotNull(DefectInfo::getDealDate).apply("deal_date > end_time")));
            }
            //未超时
            if (defectInfoQueryParam.getHasOverTime().equals("" + DefectOvertimeStatusEnum.NOOVERTIME.getValue())) {
                wrapper.nested(temp -> temp.nested(item -> item.isNotNull(DefectInfo::getEndTime).isNull(DefectInfo::getDealDate).ge(DefectInfo::getEndTime, now))
                        .or().nested(item -> item.isNotNull(DefectInfo::getEndTime).isNotNull(DefectInfo::getDealDate).apply("deal_date<=end_time"))
                        .or().nested(item -> item.isNull(DefectInfo::getEndTime)));
            }
        }
        return wrapper;
    }

    private Wrapper buildCreatePersonWrapper(LambdaQueryWrapper<DefectInfo> wrapper, DefectInfoQueryParam defectInfoQueryParam) {
        //根据创建人查询
        if (StringUtils.isNotBlank(defectInfoQueryParam.getCreateBy())) {
            RestResponse<Map<String, MaintPersonDto>> listByNames = baseServiceClient.getMaintainerListByNames(Arrays.asList(defectInfoQueryParam.getCreateBy().split(",")));
            if (listByNames.isOk()) {
                List<String> idList = Lists.newArrayList();
                for (String temp : listByNames.getData().keySet()) {
                    MaintPersonDto maintPersonDto = listByNames.getData().get(temp);
                    idList.add(maintPersonDto.getUid());
                }
                if (CollectionUtil.isNotEmpty(idList)) {
                    wrapper.in(DefectInfo::getCreateBy, idList);
                } else {
                    wrapper.apply("1=0");
                    log.info("构造缺陷查询语句-创建人 时查不到当前用户在维保人员中的登记");
                }
            } else {
                log.info("构造缺陷查询语句-创建人 时查不到当前用户在维保人员中的登记");
                wrapper.apply("1=0");
            }
        }
        return wrapper;
    }

    public Wrapper buildDealPersonWrapper(LambdaQueryWrapper<DefectInfo> wrapper, DefectInfoQueryParam defectInfoQueryParam) {
        //根据处理人查询
        if (StringUtils.isNotBlank(defectInfoQueryParam.getDealPerson())) {
            RestResponse<Map<String, MaintPersonDto>> listByNames = baseServiceClient.getMaintainerListByNames(Arrays.asList(defectInfoQueryParam.getCreateBy().split(",")));
            if (listByNames.isOk()) {
                List<String> idList = Lists.newArrayList();
                for (String temp : listByNames.getData().keySet()) {
                    MaintPersonDto maintPersonDto = listByNames.getData().get(temp);
                    idList.add(maintPersonDto.getId());
                }
                if (CollectionUtil.isNotEmpty(idList)) {
                    wrapper.nested(item -> {
                        int i = 0;
                        for (String id : idList) {
                            if (i == idList.size()) {
                                item.like(DefectInfo::getDealPersonIds, id);
                            } else {
                                item.like(DefectInfo::getDealPersonIds, id).or();
                            }
                            i++;
                        }
                    });
                } else {
                    log.info("构造缺陷查询语句-处理人 时查不到当前用户在维保人员中的登记");
                    wrapper.apply("1=0");
                }
            } else {
                log.info("构造缺陷查询语句-处理人 时查不到当前用户在维保人员中的登记");
                wrapper.apply("1=0");
            }
        }
        return wrapper;
    }

    //更新缺陷状态，使用DefectStatusEnum
    public Boolean updateDefectStatusById(String defectId, Integer defectStatus) {
        DefectInfo defectInfo = new DefectInfo();
        defectInfo.setDefectStatus(defectStatus);
        defectInfo.setDealDate(new Date());
        return this.update(defectInfo, new UpdateWrapper<DefectInfo>().lambda().eq(DefectInfo::getId, defectId));
    }

    //更新缺陷单中的工单信息
    public Boolean updateMaintTaskIdCodeById(String defectId, String maintTaskId, String maintTaskCode) {
        DefectInfo defectInfo = new DefectInfo();
        defectInfo.setMaintTaskId(maintTaskId);
        defectInfo.setMaintTaskCode(maintTaskCode);
        return this.update(defectInfo, new UpdateWrapper<DefectInfo>().lambda().eq(DefectInfo::getId, defectId));
    }

    @Override
    public Boolean submitCheck(DefectInfoSubmitCheckParam param) {
        DefectInfoDto defectInfoDto = this.getDtoById(param.getId());
        //缺陷状态为已处理，缺陷核查状态为未核查的允许提交核查状态
        if (defectInfoDto != null &&
                defectInfoDto.getDefectStatus() != null && defectInfoDto.getDefectStatus() == DefectStatusEnum.DONE.getValue() &&
                defectInfoDto.getCheckStatus() != null && defectInfoDto.getCheckStatus() == DefectCheckStatusEnum.NOCHECK.getValue()) {
            DefectInfo defectInfo = CopyDataUtil.copyObject(param, new DefectInfo().getClass());
            if (param.getCheckStatus() == DefectCheckStatusEnum.CHECKED.getValue()) {
                defectInfo.setDefectStatus(DefectStatusEnum.FINISHED.getValue());
            }
            return this.update(defectInfo, new UpdateWrapper<DefectInfo>().lambda().eq(DefectInfo::getId, param.getId()));
        } else {
            throw new GlobalServiceException(GlobalResultMessage.of("已验收"));
        }
    }

    public DefectInfo submitDealPerson(DefectInfoSubmitDealPersonParam param) {
        //缺陷状态为未处理的允许提交处理人
        DefectInfoDto defectInfoDto = this.getDtoById(param.getId());
        if (defectInfoDto != null && defectInfoDto.getDefectStatus() != null && defectInfoDto.getDefectStatus().equals(DefectStatusEnum.NODEAL.getValue())) {
            DefectInfo defectInfo = CopyDataUtil.copyObject(param, new DefectInfo().getClass());
            defectInfo.setDealPersonIds(String.join(",", param.getDealPersonIds()));
            defectInfo.setDefectStatus(DefectStatusEnum.DEALING.getValue());
            this.update(defectInfo, new UpdateWrapper<DefectInfo>().lambda().eq(DefectInfo::getId, param.getId()));
            defectInfo = (DefectInfo) this.getById(defectInfo.getId());
            return defectInfo;
        } else {
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("defect_submit_dealperson_error_submitted", null, LocaleContextHolder.getLocale())));
        }
    }

    @Transactional
    public Boolean submitDealPersonAndMaint(DefectInfoAddMaintParam param) {
        DefectInfoSubmitDealPersonParam defectInfoSubmitDealPersonParam = new DefectInfoSubmitDealPersonParam();
        defectInfoSubmitDealPersonParam.setDealPersonIds(param.getDealPersonIds());
        defectInfoSubmitDealPersonParam.setSuggestDealContent(param.getSuggestDealContent());
        defectInfoSubmitDealPersonParam.setEndTime(param.getEndTime());
        defectInfoSubmitDealPersonParam.setId(param.getDefectInfoId());
        DefectInfo defectInfo = this.submitDealPerson(defectInfoSubmitDealPersonParam);
        MaintTaskDefectAddDto maintTaskDefectAddDto = new MaintTaskDefectAddDto();
        maintTaskDefectAddDto.setEquipmentId(param.getEquipmentId());
        maintTaskDefectAddDto.setSourceId(param.getDefectInfoId());
        maintTaskDefectAddDto.setCode(this.generateMaintCode());
        maintTaskDefectAddDto.setName(param.getMaintName());
        maintTaskDefectAddDto.setMajor(param.getMaintType());
        maintTaskDefectAddDto.setUrgency(param.getMaintUrgency());
        maintTaskDefectAddDto.setStaffIds(param.getStaffIds());
        maintTaskDefectAddDto.setContent(defectInfo.getDefectName());
        maintTaskDefectAddDto.setTaskDeadlineDate(defectInfo.getEndTime());
        maintTaskDefectAddDto.setNeedAutoCalander(param.getNeedAutoCalander());
        String maintId = maintTaskService.saveDefectTask(maintTaskDefectAddDto);
        defectInfo.setMaintTaskId(maintId);
        defectInfo.setMaintTaskCode(maintTaskDefectAddDto.getCode());
        this.updateById(defectInfo);
        return true;
    }

    @Transactional
    public Boolean submitDealPersonAndMaint(DefectInfoSubmitDealPersonAndMaintParam param) {
        DefectInfoSubmitDealPersonParam defectInfoSubmitDealPersonParam = new DefectInfoSubmitDealPersonParam();
        defectInfoSubmitDealPersonParam.setDealPersonIds(param.getDealPersonIds());
        defectInfoSubmitDealPersonParam.setSuggestDealContent(param.getSuggestDealContent());
        defectInfoSubmitDealPersonParam.setEndTime(param.getEndTime());
        defectInfoSubmitDealPersonParam.setId(param.getDefectInfoId());
        DefectInfo defectInfo = this.submitDealPerson(defectInfoSubmitDealPersonParam);
        MaintTaskDefectAddDto maintTaskDefectAddDto = new MaintTaskDefectAddDto();
        maintTaskDefectAddDto.setEquipmentId(param.getEquipmentId());
        maintTaskDefectAddDto.setSourceId(param.getDefectInfoId());
        maintTaskDefectAddDto.setCode(this.generateMaintCode());
        maintTaskDefectAddDto.setName(param.getMaintName());
        maintTaskDefectAddDto.setMajor(param.getMaintType());
        maintTaskDefectAddDto.setUrgency(param.getMaintUrgency());
        maintTaskDefectAddDto.setStaffIds(param.getStaffIds());
        maintTaskDefectAddDto.setContent(defectInfo.getDefectName());
        maintTaskDefectAddDto.setTaskDeadlineDate(defectInfo.getEndTime());
        maintTaskDefectAddDto.setNeedAutoCalander(param.getNeedAutoCalander());
        maintTaskDefectAddDto.setDefectReason(param.getDefectReason());
        maintTaskDefectAddDto.setEndTime(param.getEndTime());
        maintTaskDefectAddDto.setSourceTaskId(defectInfo.getSourceTaskId());
        maintTaskDefectAddDto.setSourceTaskCode(defectInfo.getSourceTaskCode());
//        String maintId = maintTaskService.saveDefectTask(maintTaskDefectAddDto);
//        defectInfo.setMaintTaskId(maintId);
//        defectInfo.setMaintTaskCode(maintTaskDefectAddDto.getCode());
        this.updateById(defectInfo);
        return true;
    }


    public String generateMaintCode() {
        String code = maintTaskService.buildCode(TaskSourceType.DEFECT.getValue());
        return code;
    }

    public void closeDefect(String defectId, String reason) {
        //缺陷状态为未处理的允许关闭缺陷单
        DefectInfo defectInfo = (DefectInfo) this.getById(defectId);
        if (defectInfo != null && defectInfo.getDefectStatus() != null && defectInfo.getDefectStatus() == DefectStatusEnum.NODEAL.getValue()) {
            defectInfo = new DefectInfo();
            defectInfo.setCloseDate(new Date());
            defectInfo.setCloseReason(reason);
            defectInfo.setDefectStatus(DefectStatusEnum.CLOSED.getValue());
            this.update(defectInfo, new UpdateWrapper<DefectInfo>().lambda().eq(DefectInfo::getId, defectId));
        } else {
            throw new GlobalServiceException(GlobalResultMessage.of("该缺陷现在不处于可关闭状态"));
        }

    }

    public Boolean submitDone(DefectInfoSubmitDoneParam param) {
        //缺陷状态为处理中的，允许改为已处理
        DefectInfoDto defectInfoDto = this.getDtoById(param.getId());
        if (defectInfoDto != null && defectInfoDto.getDefectStatus() != null && defectInfoDto.getDefectStatus().equals(DefectStatusEnum.DEALING.getValue())) {
            DefectInfo defectInfo = CopyDataUtil.copyObject(param, new DefectInfo().getClass());
            defectInfo.setCloseDate(new Date());
            defectInfo.setDealDate(new Date());
            defectInfo.setDefectStatus(DefectStatusEnum.DONE.getValue());
            return this.update(defectInfo, new UpdateWrapper<DefectInfo>().lambda().eq(DefectInfo::getId, param.getId()));
        } else {
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("defect_submit_done_error_submitted", null, LocaleContextHolder.getLocale())));
        }
    }

    public List<DefectCountDto> getCountOfApp() {
        RestResponse<PermissionMenu> menuAndCode = permissionClient.getMenuAndCode(permissionSysCode);
        if (menuAndCode.isOk()) {
            List<String> buttonMaks = menuAndCode.getData().getButtonMaks();
            List<DefectCountDto> taskCountDtoList = Lists.newArrayList();
            DefectInfoQueryParam param = new DefectInfoQueryParam();
            if (CollectionUtils.isNotEmpty(buttonMaks) && buttonMaks.contains(permissionCodeDefectMineApp)) {
                param.setDefectStatus("" + DefectStatusEnum.DONE.getValue());
                param.setCheckStatus("" + DefectCheckStatusEnum.NOCHECK.getValue());
                Wrapper<DefectInfo> wrapper = this.getPageSearchWrapper(param, pageSearchTypeMINE);
                int count = this.count(wrapper);
                taskCountDtoList.add(DefectCountDto.builder().iconName("我的申报").iconCount(count).uniqueMark(permissionCodeDefectMineApp).build());
            } else {
                //taskCountDtoList.add(DefectCountDto.builder().iconName("我的申报").iconCount(0).uniqueMark(permissionCodeDefectMineApp).build());
            }
            if (CollectionUtils.isNotEmpty(buttonMaks) && buttonMaks.contains(permissionCodeDefectTodoApp)) {
                param.setDefectStatus("" + DefectStatusEnum.DEALING.getValue());
                Wrapper<DefectInfo> wrapper = this.getPageSearchWrapper(param, pageSearchTypeTODO);
                int count = this.count(wrapper);
                taskCountDtoList.add(DefectCountDto.builder().iconName("缺陷处理").iconCount(count).uniqueMark(permissionCodeDefectTodoApp).build());
            } else {
                //taskCountDtoList.add(DefectCountDto.builder().iconName("缺陷处理").iconCount(0).uniqueMark(permissionCodeDefectTodoApp).build());
            }
            if (CollectionUtils.isNotEmpty(buttonMaks) && CollectionUtils.isNotEmpty(buttonMaks) && buttonMaks.contains(permissionCodeDefectListApp)) {
                param.setDefectStatus("" + DefectStatusEnum.NODEAL.getValue());
                Wrapper<DefectInfo> wrapper = this.getPageSearchWrapper(param, pageSearchTypeAll);
                int count = this.count(wrapper);
                taskCountDtoList.add(DefectCountDto.builder().iconName("缺陷列表").iconCount(count).uniqueMark(permissionCodeDefectListApp).build());
            } else {
                //taskCountDtoList.add(DefectCountDto.builder().iconName("缺陷列表").iconCount(0).uniqueMark(permissionCodeDefectListApp).build());
            }
            return taskCountDtoList;
        } else {
            return Lists.newArrayList();
        }
    }

    public String getCountOfStatistics(String param) {
        Date now = new Date();
        Integer count = this.count(new QueryWrapper<DefectInfo>().lambda().in(DefectInfo::getDefectStatus, param.split(","))
                .ge(DefectInfo::getCreateTime, DateUtil.beginOfDay(now))
                .le(DefectInfo::getCreateTime, DateUtil.endOfDay(now)));
        Integer total = this.count(new QueryWrapper<DefectInfo>().lambda()
                .ge(DefectInfo::getCreateTime, DateUtil.beginOfDay(now))
                .le(DefectInfo::getCreateTime, DateUtil.endOfDay(now)));
        return "" + count + "/" + total;
    }

    public String getCountOfStatisticsOverTime(String param) {
        Date now = new Date();
        LambdaQueryWrapper<DefectInfo> wrapper = Wrappers.lambdaQuery();
        RestResponse<BuildInfoSearchDto> authRes = equipmentClient.getCurrentUserInfoIds();
        if (!authRes.isOk()) {
            log.error("获取设备权限失败");
            return "0";
        } else {
            BuildInfoSearchDto buildInfoSearchDto = authRes.getData();
            if (!buildInfoSearchDto.getFlag() || CollectionUtils.isNotEmpty(buildInfoSearchDto.getEquipmentIds())) {
                wrapper.in(CollectionUtils.isNotEmpty(buildInfoSearchDto.getEquipmentIds()), DefectInfo::getEquipmentId, buildInfoSearchDto.getEquipmentIds());
            } else {
                return "0";
            }
        }
        wrapper.le(DefectInfo::getEndTime, now).in(DefectInfo::getDefectStatus, param.split(","));
        return "" + this.count(wrapper);
    }

    @Override
    public Boolean updateRealDealContent(DefectInfoEditParam defectInfoEditParam) {
        updateStopTime(defectInfoEditParam);
        return this.update(new UpdateWrapper<DefectInfo>().lambda()
                .set(DefectInfo::getRealDealContent, defectInfoEditParam.getRealDealContent())
                .eq(DefectInfo::getId, defectInfoEditParam.getId()));
    }

    private void updateStopTime(DefectInfoEditParam defectInfoEditParam) {
        if (null != defectInfoEditParam.getBeginDowntime() && null != defectInfoEditParam.getEndDowntime()) {
            MaintTask maintTask = (MaintTask) maintTaskService.getById(defectInfoEditParam.getTaskId());
            if (maintTask != null) {
                maintTask.setBeginDowntime(defectInfoEditParam.getBeginDowntime());
                maintTask.setEndDowntime(defectInfoEditParam.getEndDowntime());
                maintTask.setDownTimeCost("" + DateUtil.between(maintTask.getBeginDowntime(), maintTask.getEndDowntime(), DateUnit.MINUTE));
                maintTaskService.updateById(maintTask);
            }
        }
    }

    @Override
    public Map<String, String> getNameByTaskId(List<String> taskIds) {
        Map<String, String> map = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(taskIds)) {
            LambdaQueryWrapper<DefectInfo> wrapper = Wrappers.lambdaQuery();
            wrapper.in(DefectInfo::getMaintTaskId, taskIds);
            wrapper.select(DefectInfo::getDefectName, DefectInfo::getId, DefectInfo::getMaintTaskId);
            map = defectInfoMapper.selectList(wrapper).stream().collect(Collectors.toMap(DefectInfo::getMaintTaskId, DefectInfo::getDefectName, (ov, nv) -> nv));
        }
        return map;
    }
}
