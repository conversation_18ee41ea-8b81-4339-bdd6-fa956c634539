package cn.getech.ehm.task.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 维护工单、备件关联表
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("maint_task_part")
public class MaintTaskPart extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 维护工单id
     */
    @TableField("task_id")
    private String taskId;

    /**
     * 备件id
     */
    @TableField("part_id")
    private String partId;

    /**
     * 计划数量
     */
    @TableField("plan_qty")
    private Integer planQty;

    /**
     * 实际数量
     */
    @TableField("actual_qty")
    private Integer actualQty;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

}
