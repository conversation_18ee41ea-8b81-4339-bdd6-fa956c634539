package cn.getech.ehm.task.dto;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <pre>
 * 节点时限记录 新增参数
 * </pre>
 *
 * <AUTHOR>
 * @date 2023-03-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ActOvertimeInfo新增", description = "节点时限记录新增参数")
public class ActOvertimeInfoAddParam extends ApiParam {

    @ApiModelProperty(value = "")
    private String remark;
}