package cn.getech.ehm.task.schedule;

import cn.getech.ehm.common.context.UserContextHolder;
import cn.getech.ehm.equipment.dto.EquipmentInfoSearchDto;
import cn.getech.ehm.equipment.dto.EquipmentSummaryDto;
import cn.getech.ehm.system.client.NotifyClient;
import cn.getech.ehm.system.dto.notify.DingNotify;
import cn.getech.ehm.system.dto.notify.NotifyParam;
import cn.getech.ehm.system.dto.notify.NotifyType;
import cn.getech.ehm.task.dto.task.notify.MaintTaskNotifyDto;
import cn.getech.ehm.task.enmu.MaintNotifyType;
import cn.getech.ehm.task.enums.TaskStatusType;
import cn.getech.ehm.task.handler.CommonGetHandler;
import cn.getech.ehm.task.service.IMaintNotifyService;
import cn.getech.ehm.task.service.IMaintTaskService;
import cn.getech.poros.permission.dto.PorosSecStaffDto;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 故障单通知推送
 */
@Slf4j
@Component
public class RepairTaskNotifySchedule {
    @Autowired
    private IMaintTaskService maintTaskService;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private IMaintNotifyService maintNotifyService;
    @Autowired
    private CommonGetHandler commonGetHandler;
    @Autowired
    private NotifyClient notifyClient;
    private final static String REPAIR_RECEIVE_TASK_LOCK="EHM:MAINT_TASK:REPAIR_RECEIVE_LOCK";

    /**
     * 每5分钟触发一次扫描
     */
    @Scheduled(cron = "0 0/5 * * * ?")
    public void releaseTask() {
        UserContextHolder.defaultContext();
        log.info("定时执行故障单推送任务开始");
        if (!redisTemplate.opsForValue().setIfAbsent(REPAIR_RECEIVE_TASK_LOCK,"1",30, TimeUnit.SECONDS)){
            log.info("锁竞争失败，跳过释放");
            return;
        }
        // 获取需要通知的工单
        Date date = new Date();
        List<MaintTaskNotifyDto> repairTasks = maintTaskService.getNotifyTask(MaintNotifyType.REPAIR_RECEIVE.getValue(), date);

        List<String> notifiedIds = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(repairTasks)){
            Map<String, EquipmentSummaryDto> equipmentMap = Maps.newHashMap();
            List<String> equipmentIds = repairTasks.stream().map(MaintTaskNotifyDto::getEquipmentId).distinct().collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(equipmentIds)){
                EquipmentInfoSearchDto searchDto = new EquipmentInfoSearchDto();
                searchDto.setEquipmentIds(equipmentIds);
                equipmentMap = commonGetHandler.getEquipmentSummaryMap(searchDto);
            }
            List<MaintTaskNotifyDto> notifyTasks = new ArrayList<>();
            for(MaintTaskNotifyDto repairTask : repairTasks){
                //剔除已接单的工单
                if(repairTask.getStatus() == TaskStatusType.RECEIVING.getValue()) {
                    notifyTasks.add(repairTask);
                }
                notifiedIds.add(repairTask.getNotifyId());
            }
            if(CollectionUtils.isNotEmpty(notifyTasks)) {
                this.buildUidTaskList(notifyTasks, equipmentMap);
            }
        }
        if(CollectionUtils.isNotEmpty(notifiedIds)){
            //处理完关掉通知
            maintNotifyService.closedTaskNotify(notifiedIds);
        }

        log.info("定时执行故障单推送任务结束");
    }

    //聚合需要通知的每个人对应的工单集合
    private void buildUidTaskList(List<MaintTaskNotifyDto> repairTasks, Map<String, EquipmentSummaryDto> equipmentMap){
        Map<String, List<MaintTaskNotifyDto>> uidTaskNumMap = Maps.newHashMap();
        List<String> allUids = new ArrayList<>();
        for(MaintTaskNotifyDto repairTask : repairTasks){
            EquipmentSummaryDto equipmentSummaryDto = equipmentMap.get(repairTask.getEquipmentId());
            if(null != equipmentSummaryDto && null != equipmentSummaryDto.getNotifyIds() && equipmentSummaryDto.getNotifyIds().length > 0){
                List<String> uids = commonGetHandler.getUidsByMaintainerIds(Arrays.asList(equipmentSummaryDto.getNotifyIds()));
                if(CollectionUtils.isNotEmpty(uids)) {
                    for (String uid : uids) {
                        List<MaintTaskNotifyDto> taskList = uidTaskNumMap.get(uid);
                        if (CollectionUtils.isEmpty(taskList)) {
                            taskList = new ArrayList<>();
                        }
                        taskList.add(repairTask);
                        uidTaskNumMap.put(uid, taskList);
                    }
                    allUids.addAll(uids);
                }
            }
        }
        //推送
        this.sendNotify(uidTaskNumMap, allUids);
    }

    private void sendNotify(Map<String, List<MaintTaskNotifyDto>> uidTaskNumMap, List<String> allUids){
        if(CollectionUtils.isEmpty(allUids) || uidTaskNumMap.isEmpty()){
            log.debug("不存在需要推送的uid或工单");
        }
        log.debug("---------------构造uid对应手机号推送");
        Map<String, String> mobileTaskMap = Maps.newHashMap();
        Map<String, String> uidMobileMap = commonGetHandler.getStaffMap(allUids).stream()
                .filter(dto -> StringUtils.isNotBlank(dto.getMobile())).collect(Collectors.toMap(PorosSecStaffDto::getUid, PorosSecStaffDto::getMobile));
        if(!uidMobileMap.isEmpty()){
            for(Map.Entry<String, List<MaintTaskNotifyDto>> entry : uidTaskNumMap.entrySet()){
                List<MaintTaskNotifyDto> taskList = entry.getValue();
                String taskCodeName = taskList.stream().map(dto -> dto.getName() + StringPool.SLASH + dto.getCode()).collect(Collectors.joining(StringPool.COMMA));
                String mobile = uidMobileMap.get(entry.getKey());
                if(StringUtils.isNotBlank(mobile)){
                    mobileTaskMap.put(mobile, "有" + taskList.size() + "个工单接单超时。工单名称/编号为" + taskCodeName);
                }
            }
            log.debug("-------------------开始推送钉钉");
            if(!mobileTaskMap.isEmpty()){
                EnumSet<NotifyType> notifyTypes = EnumSet.noneOf(NotifyType.class);
                NotifyParam notifyParam = NotifyParam.builder().build();
                DingNotify dingNotify = DingNotify.builder().params(mobileTaskMap).build();
                notifyParam.setDingNotify(dingNotify);
                notifyTypes.add(NotifyType.DING);
                notifyParam.setNotifyTypes(notifyTypes);
                notifyClient.sendNotify(notifyParam);
            }
        }

    }
}

