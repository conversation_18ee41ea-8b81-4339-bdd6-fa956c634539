package cn.getech.ehm.task.dto.repair;

import cn.getech.poros.framework.common.param.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 维保报修查询参数
 *
 * <AUTHOR>
 * @date 2020-08-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DetailQueryParam", description = "维保报修查询参数")
public class DetailQueryParam extends PageParam {

    @ApiModelProperty(value = "报修开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date beginCreateTime;

    @ApiModelProperty(value = "报修结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date endCreateTime;

    @ApiModelProperty(value = "报修人/创建人")
    private String createUserName;

    @ApiModelProperty(value = "设备名称")
    private String equipmentName;

    @ApiModelProperty(value = "排序[属性:排序(ASC升序DESC降序)]", example = "createTime:ASC")
    private Map<String, String> sortPros;

    @ApiModelProperty(value = "排序sql",hidden = true)
    private String sortValue;

    @ApiModelProperty(value = "设备id")
    private String equipmentId;

    @ApiModelProperty(value = "设备id集合")
    private List<String> equipmentIds;

}
