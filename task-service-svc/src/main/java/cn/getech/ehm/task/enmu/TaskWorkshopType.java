package cn.getech.ehm.task.enmu;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 工单工作台类型枚举
 * <AUTHOR>
 * @date 2020/3/3
 */
public enum TaskWorkshopType {
    PLAN_ONE_LEVEL_MORNING(5, "一级保养(早班)", 1d, "一级保养(早班)", "rgba(241, 116, 0, 1)"),
    PLAN_ONE_LEVEL_NIGHT(6, "一级保养(晚班)", 1d, "一级保养(晚班)", "rgba(241, 116, 0, 1)"),
    PLAN_TWO_LEVEL(7, "二级保养(当月)", 1d, "二级保养(当月)", "rgba(241, 116, 0, 1)"),
    PLAN_THREE_LEVEL(8, "三级保养(当月)", 1d, "三级保养(当月)", "rgba(241, 116, 0, 1)"),
    BREAKDOWN(1, "故障单", 1d, "故障报修(当月)", "rgba(59, 125, 221, 1)"),
    //PLAN(2, "维保单", 1d, "维保工单", "rgba(241, 116, 0, 1)"),
    DEFECT(3, "缺陷单", 1d, "缺陷工单(当月)", "rgba(229, 89, 106, 1)");
    //EC(4, "EC(点检)单", 0.5, "点检工单", "rgba(26, 177, 141, 1)");


    TaskWorkshopType(int value, String name, Double integral, String label, String color) {
        this.value = value;
        this.name = name;
        this.integral = integral;
        this.label = label;
        this.color = color;
    }

    public boolean isSame(Integer value) {
        return null != value && this.value == value;
    }

    public static String getNameByValue(Integer value) {
        String name = null;
        switch (value) {
            case 1:
                name = TaskWorkshopType.BREAKDOWN.name;
                break;
            /*case 2:
                name = TaskWorkshopType.PLAN.name;
                break;*/
            case 3:
                name = TaskWorkshopType.DEFECT.name;
                break;
            /*case 4:
                name = TaskWorkshopType.EC.name;
                break;*/
            case 5:
                name = TaskWorkshopType.PLAN_ONE_LEVEL_MORNING.name;
                break;
            case 6:
                name = TaskWorkshopType.PLAN_ONE_LEVEL_NIGHT.name;
                break;
            case 7:
                name = TaskWorkshopType.PLAN_TWO_LEVEL.name;
                break;
            case 8:
                name = TaskWorkshopType.PLAN_THREE_LEVEL.name;
                break;
            default:
                break;
        }
        return name;
    }

    public static Double getIntegralByValue(Integer value) {
        switch (value) {
            case 1:
                return TaskWorkshopType.BREAKDOWN.integral;
            /*case 2:
                return TaskWorkshopType.PLAN.integral;*/
            case 3:
                return TaskWorkshopType.DEFECT.integral;
            /*case 4:
                return TaskWorkshopType.EC.integral;*/
            case 5:
                return TaskWorkshopType.PLAN_ONE_LEVEL_MORNING.integral;
            case 6:
                return TaskWorkshopType.PLAN_ONE_LEVEL_NIGHT.integral;
            case 7:
                return TaskWorkshopType.PLAN_TWO_LEVEL.integral;
            case 8:
                return TaskWorkshopType.PLAN_THREE_LEVEL.integral;
        }
        return 0d;
    }

    private int value;

    private String name;

    private Double integral;

    private String label;

    private String color;

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Double getIntegral() { return integral; }

    public void setIntegral(Double integral) { this.integral = integral; }

    public String getLabel() { return label; }

    public void setLabel(String label) { this.label = label; }

    public String getColor() { return color; }

    public void setColor(String color) { this.color = color; }

    /**
     * 根据value返回枚举类型
     */
    public static TaskWorkshopType getByValue(int value) {
        for (TaskWorkshopType taskSourceType : values()) {
            if (taskSourceType.getValue() == value) {
                return taskSourceType;
            }
        }
        return null;
    }

    public static Map<Integer, String> getList() {
        Map<Integer, String> map = new LinkedHashMap<>(5);
        for (TaskWorkshopType taskSourceType : TaskWorkshopType.values()) {
            map.put(taskSourceType.getValue(), taskSourceType.getName());
        }
        return map;
    }
}
