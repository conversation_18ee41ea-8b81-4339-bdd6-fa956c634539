package cn.getech.ehm.task.dto;

import cn.getech.poros.framework.common.bean.BaseEntity;
import cn.getech.ehm.task.entity.TaskNotifyConfig;
import org.mapstruct.Mapper;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <pre>
 * 工单通知配置 参数实体映射
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-11-01
 */
@Mapper(componentModel = "spring",imports = {BaseEntity.class})
public interface  TaskNotifyConfigParamMapper{

    /**
     * 新增参数转换为实体
     *
     * @param taskNotifyConfigAddParam
     * @return
     */
    TaskNotifyConfig addParam2Entity(TaskNotifyConfigAddParam taskNotifyConfigAddParam);

    /**
     * 编辑参数转换为实体
     * @param taskNotifyConfigEditParam
     * @return
     */
    TaskNotifyConfig editParam2Entity(TaskNotifyConfigEditParam taskNotifyConfigEditParam);

    /**
     * 实体转换为Dto
     * @param taskNotifyConfig
     * @return
     */
    TaskNotifyConfigDto entity2Dto(TaskNotifyConfig taskNotifyConfig);

    /**
     * 分页实体转DTO
     * @param page
     * @return
     */
    PageResult<TaskNotifyConfigDto> pageEntity2Dto(PageResult<TaskNotifyConfig> page);


    /**
     * dto集合转entity集合
     * @param rows
     * @return
     */
    List<TaskNotifyConfig> dtoList2Entity(List<TaskNotifyConfigDto> rows);

}
