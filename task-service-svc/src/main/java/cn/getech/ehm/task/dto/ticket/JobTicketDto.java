package cn.getech.ehm.task.dto.ticket;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * 作业票
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "JobTicketDto", description = "作业票dto")
public class JobTicketDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "是否安全确认")
    private Boolean confirm;

    @ApiModelProperty(value = "是否显示工况/风险信息")
    private Boolean showInformation;

    @ApiModelProperty(value = "是否显示作业票类型")
    private Boolean showTicketType;

    @ApiModelProperty(value = "是否开启故障单/缺陷单派单人工审批")
    private Boolean repairTaskAudit;

    @ApiModelProperty(value = "作业票内容集合")
    private List<JobTicketItemDto> itemDtos;
}