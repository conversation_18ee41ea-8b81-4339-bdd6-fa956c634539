package cn.getech.ehm.task.dto.task.info;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 报警关联工单分页dto
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "WarnTaskPageDto", description = "报警关联工单分页dto")
public class WarnTaskPageDto {

    @ApiModelProperty(value = "工单id")
    private String id;

    @ApiModelProperty(value = "工单编号")
    private String code;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "状态名称")
    private String statusName;

    @ApiModelProperty("接单人id")
    private String handler;

    @ApiModelProperty("接单人name")
    private String handlerName;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty("报警单id")
    private String equipmentWarnId;

    @ApiModelProperty(value = "流程实例id")
    private String processInstanceId;

    @ApiModelProperty(value = "工作流任务id")
    private String activityId;
}