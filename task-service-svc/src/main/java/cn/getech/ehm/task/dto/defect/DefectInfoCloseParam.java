package cn.getech.ehm.task.dto.defect;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <pre>
 * 缺陷信息 编辑参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-02-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "缺陷关闭", description = "缺陷关闭")
public class DefectInfoCloseParam extends ApiParam {

    @ApiModelProperty(value = "关闭原因")
    private String reason;
    @ApiModelProperty(value = "defectId")
    private String defectId;


}
