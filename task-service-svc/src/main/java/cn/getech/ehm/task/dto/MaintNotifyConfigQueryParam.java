package cn.getech.ehm.task.dto;

import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

/**
 * <pre>
 *  分页查询参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2025-02-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MaintNotifyConfig查询", description = "查询参数")
public class MaintNotifyConfigQueryParam extends PageParam {

    @ApiModelProperty(value = "")
    private String id;

}
