package cn.getech.ehm.task.dto.plan;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "PlanAuditParam", description = "维护计划审核参数")
public class PlanAuditParam extends ApiParam {
    @ApiModelProperty(value = "计划单id", required = true)
    protected String planId;

    @ApiModelProperty(value = "当前操作任务id")
    protected String activityId;

    @ApiModelProperty(value = "提交类型 0:发布，1:审核通过，2:驳回，3:驳回再提交，-1:作废")
    private int submitType;

    @ApiModelProperty(value = "备注/审批意见")
    private String remark;
}
