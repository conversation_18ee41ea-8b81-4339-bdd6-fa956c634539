package cn.getech.ehm.task.controller;

import cn.getech.ehm.task.dto.task.info.*;
import cn.getech.ehm.task.service.IMaintTaskPartService;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import cn.getech.poros.framework.common.utils.ExcelUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * 备份耗用成本统计控制器
 *
 * <AUTHOR>
 * @since 2021-01-13
 */
@RestController
@RequestMapping("/taskPartRel")
@Api(tags = "备件耗用成本统计接口")
@Slf4j
public class TaskPartRelReportController {

    @Autowired
    private IMaintTaskPartService taskPartRelService;

    /**
     * 分页获取备件耗用成本列表
     */
    @ApiOperation("分页获取备件耗用成本列表")
    @PostMapping("/pageList")
    public RestResponse<PageResult<TaskPartRelReportDto>> pageList(@RequestBody @Valid TaskPartRelQueryParam taskPartRelQueryParam) {
        return RestResponse.ok(taskPartRelService.getTaskPartRelPageList(taskPartRelQueryParam));
    }

    /**
     * 分页获取备件耗用详情列表
     */
    @ApiOperation("分页获取备件耗用详情列表")
    @GetMapping("/detailPageList")
    public RestResponse<PageResult<TaskPartRelReportDto>> detailPageList(@Valid TaskPartRelDetailQueryParam taskPartRelDetailQueryParam) {
        return RestResponse.ok(taskPartRelService.getTaskPartRelDetailPageList(taskPartRelDetailQueryParam));
    }

    /**
     * 导出备件耗用成本列表
     */
    @ApiOperation("导出备件耗用成本列表")
    @AuditLog(title = "备件耗用", desc = "导出备件耗用成本列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@RequestBody @Valid TaskPartRelQueryParam taskPartRelQueryParam, HttpServletResponse response) {
        taskPartRelQueryParam.setLimit(1000);
        PageResult<TaskPartRelReportDto> taskPartRelPageList = taskPartRelService.getTaskPartRelPageList(taskPartRelQueryParam);
        ExcelUtils<TaskPartRelReportDto> util = new ExcelUtils<>(TaskPartRelReportDto.class);
        util.exportExcel(taskPartRelPageList.getRecords(), "备件耗用成本", response);
    }
}
