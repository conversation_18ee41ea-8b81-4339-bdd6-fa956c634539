package cn.getech.ehm.task.service.impl;

import cn.getech.ehm.base.client.BaseServiceClient;
import cn.getech.ehm.base.dto.MaintPersonIdDto;
import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.equipment.client.EquipmentClient;
import cn.getech.ehm.equipment.dto.EquipmentInfoSearchDto;
import cn.getech.ehm.equipment.dto.EquipmentListDto;
import cn.getech.ehm.equipment.dto.EquipmentStructureDto;
import cn.getech.ehm.task.dto.task.info.MaintTaskStatisticsDto;
import cn.getech.ehm.task.dto.task.statistics.TaskStatisticsResultDto;
import cn.getech.ehm.task.dto.task.statistics.TaskStatisticsSearchDto;
import cn.getech.ehm.task.entity.MaintTask;
import cn.getech.ehm.task.entity.MaintTaskRepair;
import cn.getech.ehm.task.enums.TaskSourceType;
import cn.getech.ehm.task.enums.TaskStatusType;
import cn.getech.ehm.task.mapper.MaintTaskMapper;
import cn.getech.ehm.task.service.IMaintTaskRepairService;
import cn.getech.ehm.task.service.IMaintTaskStatisticsService;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MaintTaskStatisticsServiceImpl extends BaseServiceImpl<MaintTaskMapper, MaintTask> implements IMaintTaskStatisticsService {

    @Autowired
    EquipmentClient equipmentClient;
    @Autowired
    BaseServiceClient baseServiceClient;
    @Autowired
    IMaintTaskRepairService maintTaskRepairService;

    public List<String> dealLocationToEquipmentId(TaskStatisticsSearchDto param) {
        List<String> equipmentIds = Lists.newArrayList();
        EquipmentInfoSearchDto equipmentInfoSearchDto = new EquipmentInfoSearchDto();
        equipmentInfoSearchDto.setLocationIds(param.getEquipmentLocationIds());
        equipmentInfoSearchDto.setCategoryIds(param.getEquipmentCategoryIds());
        RestResponse<List<String>> equipmentIdsByEquipmentInfo = equipmentClient.getEquipmentIdsByParam(equipmentInfoSearchDto);
        if (!equipmentIdsByEquipmentInfo.isOk() || CollectionUtils.isEmpty(equipmentIdsByEquipmentInfo.getData())) {
            throw new GlobalServiceException(GlobalResultMessage.of("查找不到相关设备"));
        }
        equipmentIds = equipmentIdsByEquipmentInfo.getData();
        return equipmentIds;
    }

    public List<TaskStatisticsResultDto> byLocationId(TaskStatisticsSearchDto param) {
        List<String> equipmentIds = this.dealLocationToEquipmentId(param);
        int equipmentCountThisLocation = equipmentIds.size();
        RestResponse<Map<String, EquipmentListDto>> equipmentIdMapResult = equipmentClient.getListByIds(equipmentIds.toArray(new String[equipmentIds.size()]));
        if (!equipmentIdMapResult.isOk()) {
            throw new GlobalServiceException(GlobalResultMessage.of("查找不到该位置下的设备"));
        }
        Long minites = DateUtil.between(param.getStartTime(), param.getEndTime(), DateUnit.MINUTE);
        Map<String, EquipmentListDto> equipmentListDtoMap = equipmentIdMapResult.getData();
        List<MaintTask> maintTaskList = this.list(new QueryWrapper<MaintTask>().lambda()
                .select(MaintTask::getBeginMaintTime, MaintTask::getEndMaintTime, MaintTask::getEquipmentId)
                .ge(MaintTask::getCreateTime, param.getStartTime())
                .le(MaintTask::getCreateTime, param.getEndTime())
                .in(MaintTask::getEquipmentId, equipmentIds)
                .eq(MaintTask::getSourceType, TaskSourceType.BREAKDOWN.getValue())
                .in(CollectionUtils.isEmpty(param.getStatusFilter()), MaintTask::getStatus, TaskStatusType.CLOSED.getValue(), TaskStatusType.CHECK_ACCEPT.getValue())
                .eq(StringUtils.isNotBlank(param.getProcessType()), MaintTask::getProcessType, param.getProcessType())
                .in(CollectionUtils.isNotEmpty(param.getStatusFilter()), MaintTask::getStatus, param.getStatusFilter())
        );
        Map<String, List<MaintTask>> groupById = maintTaskList.stream().collect(Collectors.groupingBy(item -> item.getEquipmentId()));
        Map<String, BigDecimal> repairCostTimes = Maps.newHashMap();
        for (String equipmentId : groupById.keySet()) {
            List<MaintTask> taskListByEquipment = groupById.get(equipmentId);
            repairCostTimes.put(equipmentId, taskListByEquipment.stream()
                    .map(item -> {
                        Long between = DateUtil.between(item.getBeginMaintTime(), item.getEndMaintTime(), DateUnit.MINUTE);
                        return new BigDecimal(between);
                    }).reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        BigDecimal repairCostTotalTimes = new BigDecimal(0);
        for (String equipmentId : repairCostTimes.keySet()) {
            repairCostTotalTimes.add(repairCostTimes.get(equipmentId));
        }
        List<TaskStatisticsResultDto> resultDtoList = Lists.newArrayList();
        Integer maxCount = 2000;
        int i = 1;
        for (String equipmentId : groupById.keySet()) {
            if (i > maxCount) {
                break;
            }
            TaskStatisticsResultDto resultDto = new TaskStatisticsResultDto();
            EquipmentListDto equipmentListDto = equipmentListDtoMap.get(equipmentId);
            resultDto.setTitle(equipmentListDto != null ? equipmentListDto.getEquipmentCode() : "");
            BigDecimal repairCostTimesPer = repairCostTimes.get(equipmentId);
            //单台设备完好率
            resultDto.setValueOne(new BigDecimal(1).subtract(repairCostTimesPer.divide(new BigDecimal(minites), 4, RoundingMode.HALF_UP)).toString());
            resultDto.setValueOneRemark("单台设备完好率");
            //维修时常
            resultDto.setValueTwo(repairCostTimesPer.toString());
            resultDto.setValueTwoRemark("维修时常");
            //目标
            resultDto.setValueThree(param.getTargetValue());
            resultDto.setValueThreeRemark("目标");
            resultDtoList.add(resultDto);
            i++;
        }
        resultDtoList = resultDtoList.stream().sorted(Comparator.comparing(TaskStatisticsResultDto::getValueTwo, (a, b) -> {
            if (new BigDecimal(a).compareTo(new BigDecimal(b)) > 0) {
                return 1;
            } else {
                return -1;
            }
        }).reversed()).collect(Collectors.toList());
        return resultDtoList;
    }

    public List<TaskStatisticsResultDto> byTime(TaskStatisticsSearchDto param) {
        List<String> equipmentIds = this.dealLocationToEquipmentId(param);
        int equipmentCountThisLocation = equipmentIds.size();
        RestResponse<Map<String, EquipmentListDto>> equipmentIdMapResult = equipmentClient.getListByIds(equipmentIds.toArray(new String[equipmentIds.size()]));
        if (!equipmentIdMapResult.isOk()) {
            throw new GlobalServiceException(GlobalResultMessage.of("查找不到该位置下的设备"));
        }
        Map<String, EquipmentListDto> equipmentListDtoMap = equipmentIdMapResult.getData();
        List<MaintTask> maintTaskList = this.list(new QueryWrapper<MaintTask>().lambda()
                .ge(MaintTask::getCreateTime, param.getStartTime())
                .le(MaintTask::getCreateTime, param.getEndTime())
                .in(MaintTask::getEquipmentId, equipmentIds)
                .eq(MaintTask::getSourceType, TaskSourceType.BREAKDOWN.getValue())
                .in(CollectionUtils.isNotEmpty(param.getStatusFilter()), MaintTask::getStatus, param.getStatusFilter())
                .eq(StringUtils.isNotBlank(param.getProcessType()), MaintTask::getProcessType, param.getProcessType())
                .orderByAsc(MaintTask::getEndMaintTime));
        List<MaintTaskStatisticsDto> statisticsDtoList = Lists.newArrayList();
        for (MaintTask temp : maintTaskList) {
            MaintTaskStatisticsDto item = CopyDataUtil.copyObject(temp, MaintTaskStatisticsDto.class);
            item.setCreateTime(temp.getCreateTime());
            Date endMaintTime = item.getEndMaintTime();
            item.setEndMaintTimeTemp(DateUtil.format(endMaintTime, "yyyy/MM/dd"));
            statisticsDtoList.add(item);
        }
        Map<String, List<MaintTaskStatisticsDto>> collect = statisticsDtoList.stream().filter(item -> item.getEndMaintTime() != null).collect(Collectors.groupingBy(item -> item.getEndMaintTimeTemp()));
        List<TaskStatisticsResultDto> resultDtoList = Lists.newArrayList();
        Long days = DateUtil.between(param.getStartTime(), param.getEndTime(), DateUnit.DAY);
        for (int i = 0; i <= days; i++) {
            Date tempDate = DateUtil.offset(param.getStartTime(), DateField.DAY_OF_MONTH, i);
            String getKey = DateUtil.format(tempDate, "yyyy/MM/dd");
            List<MaintTaskStatisticsDto> maintTaskList1 = collect.getOrDefault(getKey, Lists.newArrayList());
            TaskStatisticsResultDto taskStatisticsResultDto = new TaskStatisticsResultDto();
            taskStatisticsResultDto.setTitle(getKey);
            //故障出现次数
            taskStatisticsResultDto.setValueOne(maintTaskList1 != null ? "" + maintTaskList1.size() : "0");
            taskStatisticsResultDto.setValueOneRemark("故障出现次数");
            final BigDecimal[] repaireCostTime = {new BigDecimal("0")};
            maintTaskList1.stream().forEach(item -> {
                long between = DateUtil.between(item.getCreateTime(), item.getEndMaintTime(), DateUnit.MINUTE);
                repaireCostTime[0] = repaireCostTime[0].add(new BigDecimal(between));
            });
            //维修时常
            taskStatisticsResultDto.setValueTwo(repaireCostTime[0].toString());
            taskStatisticsResultDto.setValueTwoRemark("维修时常");
            taskStatisticsResultDto.setSortNum(i);
            resultDtoList.add(taskStatisticsResultDto);
        }
        return resultDtoList;
    }

    public List<TaskStatisticsResultDto> byStaff(TaskStatisticsSearchDto param) {
        List<String> equipmentIds = this.dealLocationToEquipmentId(param);
        int equipmentCountThisLocation = equipmentIds.size();
        RestResponse<Map<String, EquipmentListDto>> equipmentIdMapResult = equipmentClient.getListByIds(equipmentIds.toArray(new String[equipmentIds.size()]));
        if (!equipmentIdMapResult.isOk()) {
            throw new GlobalServiceException(GlobalResultMessage.of("查找不到该位置下的设备"));
        }
        RestResponse<List<MaintPersonIdDto>> maintPersonIdDto = baseServiceClient.getMaintPersonIdDto();
        if (!maintPersonIdDto.isOk()) {
            throw new GlobalServiceException(GlobalResultMessage.of("查找不到维护人员信息"));
        }
        List<MaintPersonIdDto> maintPersonIdDtoData = maintPersonIdDto.getData();
        Map<String, String> maintPersonUidMap = maintPersonIdDtoData.stream().collect(Collectors.toMap(item -> item.getUid(), item -> item.getStaffId()));

        List<MaintPersonIdDto> maintPersonIdDtos = maintPersonIdDto.getData();
        List<MaintTask> maintTaskList = this.list(new QueryWrapper<MaintTask>().lambda()
                .ge(MaintTask::getCreateTime, param.getStartTime())
                .le(MaintTask::getCreateTime, param.getEndTime())
                .in(MaintTask::getEquipmentId, equipmentIds)
                .eq(MaintTask::getSourceType, TaskSourceType.BREAKDOWN.getValue())
                .in(CollectionUtils.isEmpty(param.getStatusFilter()), MaintTask::getStatus, TaskStatusType.CLOSED.getValue(), TaskStatusType.CHECK_ACCEPT.getValue())
                .in(CollectionUtils.isNotEmpty(param.getStatusFilter()), MaintTask::getStatus, param.getStatusFilter())
                .eq(StringUtils.isNotBlank(param.getProcessType()), MaintTask::getProcessType, param.getProcessType())
                .orderByAsc(MaintTask::getEndMaintTime));
        Map<String, List<MaintTask>> taskMapPerTeam = Maps.newHashMap();
        List<String> teamIds = maintTaskList.stream().filter(item -> ArrayUtil.isNotEmpty(item.getTeamIds())).flatMap(item -> Arrays.asList(item.getTeamIds()).stream()).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        RestResponse<Map<String, String>> teamNamesByIdResult = baseServiceClient.getTeamMapByIds(teamIds.toArray(new String[teamIds.size()]));

        if (!teamNamesByIdResult.isOk()) {
            throw new GlobalServiceException(GlobalResultMessage.of("查找不到维护班组信息"));
        }
        Map<String, String> teamNameMap = teamNamesByIdResult.getData();


        for (String teamId : teamIds) {
            log.debug("班组：{},{}", teamNameMap.get(teamId), teamId);
            RestResponse<List<String>> personIdsByTeamIds = baseServiceClient.getPersonIdsByTeamIds(new String[]{teamId});
            if (!personIdsByTeamIds.isOk()) {
                throw new GlobalServiceException(GlobalResultMessage.of("查找不到维护班组信息"));
            }
            List<String> personIdsByTeamId = personIdsByTeamIds.getData();
            Set<MaintTask> maintTaskListPerStaff = Sets.newHashSet();
            maintTaskList.stream().forEach(item -> {
                String handler = item.getHandler();
                String handlerMaintPersonId = maintPersonUidMap.get(handler);
                if (personIdsByTeamId.contains(handlerMaintPersonId)) {
                    maintTaskListPerStaff.add(item);
                    log.debug("taskId:{},uid:{}", item.getId(), handler);
                }

            });
            taskMapPerTeam.put(teamId, Lists.newArrayList(maintTaskListPerStaff.iterator()));
        }

        List<TaskStatisticsResultDto> resultDtoList = Lists.newArrayList();
        int i = 0;
        for (String teamId : teamIds) {
            List<MaintTask> maintTaskList1 = taskMapPerTeam.getOrDefault(teamId, Lists.newArrayList());
            TaskStatisticsResultDto taskStatisticsResultDto = new TaskStatisticsResultDto();
            taskStatisticsResultDto.setTitle(teamNameMap.get(teamId));
            i++;
            //故障出现次数
            taskStatisticsResultDto.setValueOne(maintTaskList1 != null ? "" + maintTaskList1.size() : "0");
            taskStatisticsResultDto.setValueOneRemark("故障出现次数");
            final BigDecimal[] repaireCostTime = {new BigDecimal("0")};
            maintTaskList1.stream().forEach(item -> {
                long between = DateUtil.between(item.getBeginMaintTime(), item.getEndMaintTime(), DateUnit.MINUTE);
                repaireCostTime[0] = repaireCostTime[0].add(new BigDecimal(between));
            });
            //维修时常
            taskStatisticsResultDto.setValueTwo(repaireCostTime[0].toString());
            taskStatisticsResultDto.setValueTwoRemark("维修时长");
            resultDtoList.add(taskStatisticsResultDto);
        }
        resultDtoList = resultDtoList.stream().sorted(Comparator.comparing(TaskStatisticsResultDto::getValueTwo, (a, b) -> {
            if (new BigDecimal(a).compareTo(new BigDecimal(b)) > 0) {
                return 1;
            } else {
                return -1;
            }
        }).reversed()).collect(Collectors.toList());
        return resultDtoList;
    }

    public List<TaskStatisticsResultDto> byStructure(TaskStatisticsSearchDto param) {
        List<String> equipmentIds = this.dealLocationToEquipmentId(param);
        int equipmentCountThisLocation = equipmentIds.size();
        RestResponse<Map<String, EquipmentListDto>> equipmentIdMapResult = equipmentClient.getListByIds(equipmentIds.toArray(new String[equipmentIds.size()]));
        if (!equipmentIdMapResult.isOk()) {
            throw new GlobalServiceException(GlobalResultMessage.of("查找不到该位置下的设备"));
        }
        Long minites = DateUtil.between(param.getStartTime(), param.getEndTime(), DateUnit.MINUTE);
        Map<String, EquipmentListDto> equipmentListDtoMap = equipmentIdMapResult.getData();
        List<MaintTask> maintTaskList = this.list(new QueryWrapper<MaintTask>().lambda()
                .select(MaintTask::getBeginMaintTime, MaintTask::getEndMaintTime, MaintTask::getEquipmentId, MaintTask::getId)
                .ge(MaintTask::getCreateTime, param.getStartTime())
                .le(MaintTask::getCreateTime, param.getEndTime())
                .in(MaintTask::getEquipmentId, equipmentIds)
                .eq(MaintTask::getSourceType, TaskSourceType.BREAKDOWN.getValue())
                .in(CollectionUtils.isEmpty(param.getStatusFilter()), MaintTask::getStatus, TaskStatusType.CLOSED.getValue(), TaskStatusType.CHECK_ACCEPT.getValue())
                .eq(StringUtils.isNotBlank(param.getProcessType()), MaintTask::getProcessType, param.getProcessType())
                .in(CollectionUtils.isNotEmpty(param.getStatusFilter()), MaintTask::getStatus, param.getStatusFilter())
        );
        Map<String, MaintTask> taskIdMap = maintTaskList.stream().collect(Collectors.toMap(item -> item.getId(), item -> item, (v1, v2) -> v1));
        List<String> taskIdList = maintTaskList.stream().map(item -> item.getId()).collect(Collectors.toList());
        Set<String> taskEquipmentIdSet = maintTaskList.stream().map(item -> item.getEquipmentId()).collect(Collectors.toSet());
        RestResponse<Map<String, EquipmentListDto>> listByIds = equipmentClient.getListByIds(taskEquipmentIdSet.toArray(new String[taskEquipmentIdSet.size()]));
        if (!listByIds.isOk()) {
            throw new GlobalServiceException(GlobalResultMessage.of("获取相关工单下的设备信息失败"));
        }
        Map<String, EquipmentListDto> equipmentMap = listByIds.getData();
        if (CollectionUtils.isEmpty(taskIdList)) {
            throw new GlobalServiceException(GlobalResultMessage.of("查找不到符合条件的工单"));
        }
        List<MaintTaskRepair> maintTaskRepairList = maintTaskRepairService.list(new QueryWrapper<MaintTaskRepair>().lambda().in(MaintTaskRepair::getTaskId, taskIdList));
        Map<String, List<MaintTaskRepair>> taskRepairMap = maintTaskRepairList.stream().collect(Collectors.groupingBy(item -> item.getTaskId()));
        Map<String, List<MaintTaskRepair>> positionRepairMap = Maps.newHashMap();
        maintTaskRepairList.stream().forEach(item -> {
            String[] faultPositionIds = item.getFaultStructureIds();
            if (ArrayUtil.isNotEmpty(faultPositionIds)) {
                for (String positionId : faultPositionIds) {
                    List<MaintTaskRepair> orDefault = positionRepairMap.getOrDefault(positionId, Lists.newArrayList());
                    orDefault.add(item);
                    positionRepairMap.put(positionId, orDefault);
                }
            }
        });
        if (CollectionUtils.isEmpty(positionRepairMap.keySet())) {
            throw new GlobalServiceException(GlobalResultMessage.of("无数据"));
        }
        List<TaskStatisticsResultDto> resultDtoList = Lists.newLinkedList();
        RestResponse<Map<String, String>> structureNameMap = equipmentClient.getStructureNameMap(positionRepairMap.keySet().toArray(new String[positionRepairMap.keySet().size()]));
        if (!structureNameMap.isOk()) {
            throw new GlobalServiceException(GlobalResultMessage.of("获取设备结构名称失败"));
        }
        Map<String, String> structureMap = structureNameMap.getData();
        RestResponse<List<EquipmentStructureDto>> listByStructureId = equipmentClient.getListByStructureId(Lists.newArrayList(positionRepairMap.keySet().iterator()));
        if (!listByStructureId.isOk()) {
            throw new GlobalServiceException(GlobalResultMessage.of("获取设备结构信息失败"));
        }
        List<EquipmentStructureDto> data = listByStructureId.getData();
        List<String> structureParentIds = data.stream().filter(item -> StringUtils.isNotBlank(item.getParentId())).map(item -> item.getParentId()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(structureParentIds)) {
            RestResponse<List<EquipmentStructureDto>> structureParentResult = equipmentClient.getListByStructureId(structureParentIds);
            if (!structureParentResult.isOk()) {
                throw new GlobalServiceException(GlobalResultMessage.of("获取设备结构信息失败"));
            }
            data.addAll(structureParentResult.getData());
        }
        Map<String, EquipmentStructureDto> structureDtoMap = data.stream().collect(Collectors.toMap(item -> item.getId(), item -> item));
        List<EquipmentStructureDto> collect = data.stream().filter(item -> CollectionUtils.isNotEmpty(item.getChildren())).flatMap(item -> item.getChildren().stream()).collect(Collectors.toList());
        Map<String, EquipmentStructureDto> structureChildMap = collect.stream().collect(Collectors.toMap(item -> item.getId(), item -> item));
        structureDtoMap.putAll(structureChildMap);
        for (String positionId : positionRepairMap.keySet()) {
            TaskStatisticsResultDto resultDto = new TaskStatisticsResultDto();
            List<MaintTaskRepair> maintTaskRepairPerPosition = positionRepairMap.get(positionId);
            BigDecimal maintCostTime = new BigDecimal("0");
            String equipmentName = "";
            for (MaintTaskRepair taskRepair : maintTaskRepairPerPosition) {
                MaintTask maintTask = taskIdMap.get(taskRepair.getTaskId());
                String equipmentId = maintTask.getEquipmentId();
                EquipmentListDto equipmentListDto = equipmentMap.get(equipmentId);
                equipmentName = equipmentListDto.getEquipmentName();
                long between = DateUtil.between(maintTask.getBeginMaintTime(), maintTask.getEndMaintTime(), DateUnit.MINUTE);
                maintCostTime = maintCostTime.add(new BigDecimal(between));
            }

            resultDto.setTitle(maintTaskRepairPerPosition != null ? structureMap.get(positionId) : "");
            //故障次数
            resultDto.setValueOne("" + maintTaskRepairPerPosition.size());
            resultDto.setValueOneRemark("故障次数");
            //维修时长
            resultDto.setValueTwo(maintCostTime.toString());
            resultDto.setValueTwoRemark("维修时长");
            resultDto.setValueThreeRemark("结构树");
            EquipmentStructureDto equipmentStructureDto = structureDtoMap.get(positionId);
            log.info("structureType:{}", equipmentStructureDto);
            if (equipmentStructureDto != null && equipmentStructureDto.getType() == 1 && param.getStructureType().contains(1)) {
                resultDto.setValueThree(equipmentName + "/" + equipmentStructureDto.getName());
                resultDtoList.add(resultDto);
            } else if (equipmentStructureDto != null && equipmentStructureDto.getType() == 2 && param.getStructureType().contains(2)) {
                EquipmentStructureDto parenStructure = structureDtoMap.get(equipmentStructureDto.getParentId());
                if (ObjectUtil.isEmpty(parenStructure)) {
                    //log.info("拿不到结构信息：structureid:{},parentId:{},{}",positionId,equipmentStructureDto.getParentId(), JSON.toJSONString(structureDtoMap));
                    resultDto.setValueThree(equipmentName + "/" + equipmentStructureDto.getName());
                } else {
                    resultDto.setValueThree(equipmentName + "/" + equipmentStructureDto.getName() + "/" + parenStructure.getName());
                }

                resultDtoList.add(resultDto);
            }

        }
        resultDtoList = resultDtoList.stream().sorted(Comparator.comparing(TaskStatisticsResultDto::getValueTwo, (a, b) -> {
            if (new BigDecimal(a).compareTo(new BigDecimal(b)) > 0) {
                return 1;
            } else {
                return -1;
            }
        }).reversed()).collect(Collectors.toList());
        return resultDtoList;
    }

    public List<TaskStatisticsResultDto> byStructurePlato(TaskStatisticsSearchDto param) {
        List<String> equipmentIds = Collections.singletonList("6e37d34e6d054ff29438eea37f5fce27");//this.dealLocationToEquipmentId(param);
        List<MaintTask> maintTaskList = this.list(new QueryWrapper<MaintTask>().lambda()
                .select(MaintTask::getBeginMaintTime, MaintTask::getEndMaintTime, MaintTask::getEquipmentId, MaintTask::getId)
                .ge(MaintTask::getCreateTime, param.getStartTime())
                .le(MaintTask::getCreateTime, param.getEndTime())
                .in(MaintTask::getEquipmentId, equipmentIds)
                .eq(MaintTask::getSourceType, TaskSourceType.BREAKDOWN.getValue())
                .in(CollectionUtils.isEmpty(param.getStatusFilter()), MaintTask::getStatus, TaskStatusType.CLOSED.getValue(), TaskStatusType.CHECK_ACCEPT.getValue())
                .eq(StringUtils.isNotBlank(param.getProcessType()), MaintTask::getProcessType, param.getProcessType())
                .in(CollectionUtils.isNotEmpty(param.getStatusFilter()), MaintTask::getStatus, param.getStatusFilter())
        );
        Map<String, List<MaintTask>> taskEquipmentIdMap = maintTaskList.stream().collect(Collectors.groupingBy(MaintTask::getEquipmentId));
        List<String> equipmentIdList = maintTaskList.stream().map(item -> item.getEquipmentId()).collect(Collectors.toList());
        RestResponse<Map<String, EquipmentListDto>> equipmentInforMapResult = equipmentClient.getListByIds(equipmentIdList.toArray(new String[equipmentIdList.size()]));
        if (!equipmentInforMapResult.isOk()) {
            throw new GlobalServiceException(GlobalResultMessage.of("查找不到该位置下的设备"));
        }
        Map<String, EquipmentListDto> equipmentInfoListDtoMap = equipmentInforMapResult.getData();
        List<TaskStatisticsResultDto> resultDtoList = Lists.newLinkedList();
        for (String equipmentId : equipmentInfoListDtoMap.keySet()) {
            TaskStatisticsResultDto resultDto = new TaskStatisticsResultDto();
            EquipmentListDto equipmentInfoDto = equipmentInfoListDtoMap.get(equipmentId);
            List<MaintTask> maintTask = taskEquipmentIdMap.get(equipmentId);
            BigDecimal maintCostTime = new BigDecimal("0");
            for (MaintTask task : maintTask) {
                long between = DateUtil.between(task.getBeginMaintTime(), task.getEndMaintTime(), DateUnit.MINUTE);
                maintCostTime = maintCostTime.add(new BigDecimal(between));
            }
            resultDto.setTitle(equipmentInfoDto != null ? equipmentInfoDto.getEquipmentName() : "");
            //故障次数
            resultDto.setValueOne("" + maintTask.size());
            resultDto.setValueOneRemark("故障次数");
            //维修时长
            resultDto.setValueTwo(StringUtils.isNotBlank(maintCostTime.toString()) ? maintCostTime.toString() : "0");
            resultDto.setValueTwoRemark("维修时长");
            resultDtoList.add(resultDto);
        }
        resultDtoList = resultDtoList.stream().sorted(Comparator.comparing(TaskStatisticsResultDto::getValueTwo, (a, b) -> {
            if (new BigDecimal(a).compareTo(new BigDecimal(b)) > 0) {
                return 1;
            } else {
                return -1;
            }
        }).reversed()).collect(Collectors.toList());
        BigDecimal timeNow = new BigDecimal(0);
        BigDecimal timeTotal = resultDtoList.stream().map(item -> new BigDecimal(item.getValueTwo())).reduce(new BigDecimal(0), BigDecimal::add);
        for (TaskStatisticsResultDto temp : resultDtoList) {
            timeNow = timeNow.add(new BigDecimal(temp.getValueTwo()));
            if (timeTotal.equals(BigDecimal.ZERO)) {
                temp.setValueThree("0");
            } else {
                BigDecimal tempResult = timeNow.divide(timeTotal, 4, RoundingMode.HALF_UP);
                temp.setValueThree(tempResult.toString());
            }
            temp.setValueThreeRemark("故障柏拉值");
        }
        return resultDtoList;
    }

    public List<TaskStatisticsResultDto> byRepairTime(TaskStatisticsSearchDto param) {
        List<String> equipmentIds = this.dealLocationToEquipmentId(param);
        List<MaintTask> maintTaskList = this.list(new QueryWrapper<MaintTask>().lambda()
                .select(MaintTask::getBeginMaintTime, MaintTask::getEndMaintTime, MaintTask::getEquipmentId, MaintTask::getId)
                .ge(MaintTask::getCreateTime, param.getStartTime())
                .le(MaintTask::getCreateTime, param.getEndTime())
                .in(MaintTask::getEquipmentId, equipmentIds)
                .eq(MaintTask::getSourceType, TaskSourceType.BREAKDOWN.getValue())
                .in(CollectionUtils.isEmpty(param.getStatusFilter()), MaintTask::getStatus, TaskStatusType.CLOSED.getValue(), TaskStatusType.CHECK_ACCEPT.getValue())
                .eq(StringUtils.isNotBlank(param.getProcessType()), MaintTask::getProcessType, param.getProcessType())
                .in(CollectionUtils.isNotEmpty(param.getStatusFilter()), MaintTask::getStatus, param.getStatusFilter())
        );
        Map<String, List<MaintTask>> taskEquipmentIdMap = maintTaskList.stream().collect(Collectors.groupingBy(MaintTask::getEquipmentId));
        List<String> equipmentIdList = maintTaskList.stream().map(item -> item.getEquipmentId()).collect(Collectors.toList());
        RestResponse<Map<String, EquipmentListDto>> equipmentInforMapResult = equipmentClient.getListByIds(equipmentIdList.toArray(new String[equipmentIdList.size()]));
        if (!equipmentInforMapResult.isOk()) {
            throw new GlobalServiceException(GlobalResultMessage.of("查找不到该位置下的设备"));
        }
        Map<String, EquipmentListDto> equipmentInfoListDtoMap = equipmentInforMapResult.getData();
        List<TaskStatisticsResultDto> resultDtoList = Lists.newLinkedList();
        Long totalHour = DateUtil.between(param.getStartTime(), param.getEndTime(), DateUnit.MINUTE);
        for (String equipmentId : equipmentInfoListDtoMap.keySet()) {
            TaskStatisticsResultDto resultDto = new TaskStatisticsResultDto();
            EquipmentListDto equipmentInfoDto = equipmentInfoListDtoMap.get(equipmentId);
            List<MaintTask> maintTask = taskEquipmentIdMap.get(equipmentId);
            BigDecimal hourPerEquipment = new BigDecimal("0");
            for (MaintTask task : maintTask) {
                if (task.getBeginMaintTime().before(param.getStartTime())) {
                    task.setBeginMaintTime(param.getStartTime());
                }
                if (task.getEndMaintTime().after(param.getEndTime())) {
                    task.setEndMaintTime(param.getEndTime());
                }
                long between = DateUtil.between(task.getBeginMaintTime(), task.getEndMaintTime(), DateUnit.MINUTE);
                hourPerEquipment = hourPerEquipment.add(new BigDecimal(between));
            }
            resultDto.setTitle(equipmentInfoDto != null ? equipmentInfoDto.getEquipmentName() : "");
            //故障次数
            resultDto.setValueOne("" + maintTask.size());
            resultDto.setValueOneRemark("故障次数");
            //维修时长
            BigDecimal availabilityNum = new BigDecimal("1").subtract(hourPerEquipment.divide(new BigDecimal(totalHour), 4, RoundingMode.HALF_UP));
            resultDto.setValueTwo(StringUtils.isNotBlank(availabilityNum.toString()) ? availabilityNum.toString() : "0");
            resultDto.setValueTwoRemark("完好率");
            resultDtoList.add(resultDto);
        }
        resultDtoList = resultDtoList.stream().sorted(Comparator.comparing(TaskStatisticsResultDto::getValueTwo, (a, b) -> {
            if (new BigDecimal(a).compareTo(new BigDecimal(b)) > 0) {
                return 1;
            } else {
                return -1;
            }
        })).collect(Collectors.toList());
        return resultDtoList;
    }

    public List<TaskStatisticsResultDto> analysis(TaskStatisticsSearchDto param) {
        List<TaskStatisticsResultDto> resultDtoList = Lists.newLinkedList();
        List<String> equipmentIds = this.dealLocationToEquipmentId(param);
        List<MaintTask> maintTaskList = this.list(new QueryWrapper<MaintTask>().lambda()
                .select(MaintTask::getBeginMaintTime, MaintTask::getEndMaintTime, MaintTask::getEquipmentId, MaintTask::getId, MaintTask::getJobLevel, MaintTask::getStatus)
                .ge(MaintTask::getCreateTime, param.getStartTime())
                .le(MaintTask::getCreateTime, param.getEndTime())
                .in(MaintTask::getEquipmentId, equipmentIds)
                .eq(param.getSourceType() != null, MaintTask::getSourceType, param.getSourceType())
                .eq(param.getJobLevel() != null, MaintTask::getJobLevel, param.getJobLevel())
                .eq(param.getTaskType() != null, MaintTask::getType, param.getTaskType())
                .eq(StringUtils.isNotBlank(param.getProcessType()), MaintTask::getProcessType, param.getProcessType())
                .in(CollectionUtils.isNotEmpty(param.getStatusFilter()), MaintTask::getStatus, param.getStatusFilter())
        );

        Map<String, List<MaintTask>> taskEquipmentIdMap = maintTaskList.stream().collect(Collectors.groupingBy(MaintTask::getEquipmentId));
        List<String> equipmentIdList = maintTaskList.stream().map(item -> item.getEquipmentId()).collect(Collectors.toList());
        RestResponse<Map<String, EquipmentListDto>> equipmentInforMapResult = equipmentClient.getListByIds(equipmentIdList.toArray(new String[equipmentIdList.size()]));
        if (!equipmentInforMapResult.isOk()) {
            throw new GlobalServiceException(GlobalResultMessage.of("查找不到该位置下的设备"));
        }
        Map<String, EquipmentListDto> equipmentInfoListDtoMap = equipmentInforMapResult.getData();
        List<EquipmentListDto> equipmentList = equipmentInfoListDtoMap.values().stream().collect(Collectors.toList());
        Map<String, List<MaintTask>> keyList = this.getKeyList(param, maintTaskList, equipmentList);
        for (String key : keyList.keySet()) {
            log.info("key:{}", key);
            List<MaintTask> taskList = keyList.get(key);
            TaskStatisticsResultDto resultDto = new TaskStatisticsResultDto();
            resultDto.setTitle(key);
            List<MaintTask> finishList = taskList.stream().filter(item -> finishStatusList.contains(item.getStatus())).collect(Collectors.toList());
            resultDto.setValueOne("" + taskList.size());
            resultDto.setValueOneRemark("总数");
            resultDto.setValueTwo("" + finishList.size());
            resultDto.setValueTwoRemark("完成数");
            resultDto.setValueThree("" + (taskList.size() - finishList.size()));
            resultDto.setValueThreeRemark("未完成数");
            resultDto.setValueFour("" + new BigDecimal(finishList.size() * 100).divide(new BigDecimal(taskList.size()), 2, RoundingMode.HALF_UP).toPlainString());
            resultDto.setValueFourRemark("完成率");
            if (param.getAnalysisType() == 1) {
                resultDto.setJobLevel(new Integer(key));
            }
            resultDtoList.add(resultDto);
        }
        return resultDtoList;
    }


    public Map<String, List<MaintTask>> getKeyList(TaskStatisticsSearchDto param, List<MaintTask> maintTaskList, List<EquipmentListDto> equipmentList) {
        Map<String, List<MaintTask>> collect = Maps.newHashMap();
        //根据维保类型
        if (param.getAnalysisType() == 1) {
            collect = maintTaskList.stream().collect(Collectors.groupingBy(item -> "" + item.getJobLevel()));

        }
        //根据设备类型
        if (param.getAnalysisType() == 2) {
            Map<String, List<MaintTask>> taskByEquip = maintTaskList.stream().collect(Collectors.groupingBy(item -> item.getEquipmentId()));
            Map<String, List<EquipmentListDto>> equipByCategory = equipmentList.stream().collect(Collectors.groupingBy(item -> {
                String categoryAllName = item.getCategoryAllName();
                String[] split = categoryAllName.split("/");
                if (split.length < 2) {
                    return split[0] + "(特)";
                } else {
                    return split[1];
                }

            }));
            for (String key : equipByCategory.keySet()) {
                List<EquipmentListDto> value = equipByCategory.get(key);
                List<MaintTask> taskList = Lists.newArrayList();
                for (EquipmentListDto item : value) {
                    List<MaintTask> task = taskByEquip.get(item.getEquipmentId());
                    taskList.addAll(task);
                }
                collect.put(key, taskList);
            }
        }
        //根据设备位置
        if (param.getAnalysisType() == 3) {
            Map<String, List<MaintTask>> taskByEquip = maintTaskList.stream().collect(Collectors.groupingBy(item -> item.getEquipmentId()));
            List<String> locationId = equipmentList.stream().map(item -> item.getLocationLayerCode()).map(item -> Arrays.asList(item.split("/"))).flatMap(item -> item.stream()).collect(Collectors.toList());
            RestResponse<Map<String, String>> locationMapByIds = equipmentClient.getLocationMapByIds(locationId.toArray(new String[locationId.size()]));
            if (!locationMapByIds.isOk()) {
                throw new GlobalServiceException(GlobalResultMessage.of("获取位置映射失败"));
            }
            Map<String, String> locationMap = locationMapByIds.getData();
            Map<String, List<EquipmentListDto>> equipByLocation = equipmentList.stream().collect(Collectors.groupingBy(item -> {
                String parentAllName = item.getLocationLayerCode();
                String[] split = parentAllName.split("/");
                if (split.length < 3) {
                    String name = locationMap.getOrDefault(split[0], "未知位置");
                    return name + "(特)";
                } else {
                    String name = locationMap.getOrDefault(split[2], "未知位置");
                    return name;
                }

            }));
            for (String key : equipByLocation.keySet()) {
                List<EquipmentListDto> value = equipByLocation.get(key);
                List<MaintTask> taskList = Lists.newArrayList();
                for (EquipmentListDto item : value) {
                    List<MaintTask> task = taskByEquip.get(item.getEquipmentId());
                    taskList.addAll(task);
                }
                collect.put(key, taskList);
            }
        }
        return collect;

    }

    public static List<Integer> finishStatusList = Arrays.asList(TaskStatusType.CHECK_ACCEPT.getValue(),
            TaskStatusType.CLOSED.getValue());


}
