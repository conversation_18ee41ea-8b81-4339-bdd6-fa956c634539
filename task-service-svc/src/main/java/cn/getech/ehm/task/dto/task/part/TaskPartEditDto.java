package cn.getech.ehm.task.dto.task.part;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 维护工单、备件修改
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "TaskPartEditDto", description = "维护工单、备件修改")
public class TaskPartEditDto {

    @ApiModelProperty(value = "关联表id")
    private String id;

    @ApiModelProperty(value = "备件id")
    private String partId;

    @ApiModelProperty(value = "计划数量")
    private Integer planQty;

    @ApiModelProperty(value = "实际数量")
    private Integer actualQty;
}