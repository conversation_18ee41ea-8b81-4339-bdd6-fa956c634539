package cn.getech.ehm.task.dto.screen;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2022-07-08
 */
@Data
@ApiModel("设备视角工作日历查询参数")
public class EquipmentTaskQueryParam {
    @ApiModelProperty("设备名")
    private String equipmentName;

    @ApiModelProperty("设备ids")
    private List<String> equipmentIds;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDate;
}
