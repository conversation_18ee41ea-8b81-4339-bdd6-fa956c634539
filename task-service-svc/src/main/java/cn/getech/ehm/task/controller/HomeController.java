package cn.getech.ehm.task.controller;

import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.equipment.client.EquipmentClient;
import cn.getech.ehm.equipment.dto.EquipChangeDto;
import cn.getech.ehm.task.dto.TodayTaskCountDto;
import cn.getech.ehm.task.dto.common.ProcessTaskDto;
import cn.getech.ehm.task.dto.defect.DefectInfoDto;
import cn.getech.ehm.task.dto.defect.DefectInfoQueryParam;
import cn.getech.ehm.task.dto.plan.MaintPlanDto;
import cn.getech.ehm.task.dto.plan.MaintPlanQueryParam;
import cn.getech.ehm.task.dto.screen.ManualRepairGroupCustomerDto;
import cn.getech.ehm.task.dto.screen.TimeQueryParam;
import cn.getech.ehm.task.dto.screen.WorkCalendarAppResult;
import cn.getech.ehm.task.dto.screen.WorkCalendarQueryParam;
import cn.getech.ehm.task.dto.screen.*;
import cn.getech.ehm.task.dto.task.info.MaintTaskDto;
import cn.getech.ehm.task.dto.task.info.MaintTaskQueryParam;
import cn.getech.ehm.task.dto.task.info.TaskCompleteDynamicDto;
import cn.getech.ehm.task.dto.task.info.TaskCountDto;
import cn.getech.ehm.task.entity.MaintPlan;
import cn.getech.ehm.task.enums.DefectStatusEnum;
import cn.getech.ehm.task.service.IDefectInfoService;
import cn.getech.ehm.task.service.IMaintTaskService;
import cn.getech.ehm.task.service.IMaintPlanService;
import cn.getech.ehm.part.client.PartClient;
import cn.getech.ehm.part.dto.InventoryOrderDto;
import cn.getech.ehm.part.dto.TransferOrderDto;
import cn.getech.ehm.task.service.IManualRepairService;
import cn.getech.poros.bpm.client.TaskServiceClient;
import cn.getech.poros.bpm.dto.BaseDto;
import cn.getech.poros.bpm.dto.task.TaskDto;
import cn.getech.poros.bpm.param.UserParam;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import cn.getech.poros.framework.common.context.PorosContextHolder;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 工作台首页控制器
 *
 * <AUTHOR>
 * @since 2020-08-13
 */
@RestController
@RequestMapping("/home")
@Api(tags = "工作台首页服务接口")
@Slf4j
public class HomeController {

    @Value("${flow.judge.code:E0001}")
    private String judgeCode;
    @Value("${flow.transfer.code:E0002}")
    private String transferCode;

    @Value("${flow.change.code:E0003}")
    private String changeCode;

    @Value("${flow.maint.code:E0004}")
    private String maintCode;

    @Value("${flow.maint.plan:E0005}")
    private String planCode;

    @Value("${flow.maint.remote:E0006}")
    private String remoteCode;

    @Autowired
    @Lazy
    private IMaintTaskService maintTaskService;
    @Autowired
    @Lazy
    private IMaintPlanService maintPlanService;
    @Autowired
    private TaskServiceClient taskServiceClient;
    @Autowired
    private EquipmentClient equipmentClient;
    @Autowired
    private PartClient partClient;
    @Autowired
    @Lazy
    private IManualRepairService manualRepairService;
    @Autowired
    private ResourceBundleMessageSource messageSource;
    @Autowired
    private IDefectInfoService defectInfoService;

    @ApiOperation("获取今日工单数量")
    @GetMapping("/todayTaskQty")
    //@Permission("maint:plan:list")
    public RestResponse<Integer> getTodayTaskQty() {
        return RestResponse.ok(maintTaskService.getTodayTaskQty());
    }

    @ApiOperation("获取超期工单数量")
    @GetMapping("/overdueTaskQty")
    //@Permission("maint:plan:list")
    public RestResponse<Integer> getOverdueTaskQty() {
        return RestResponse.ok(maintTaskService.getOverdueTaskQty());
    }

    @ApiOperation("获取今日已完成工单数量")
    @GetMapping("/todayCompletedTaskQty")
    //@Permission("maint:plan:list")
    public RestResponse<Integer> getTodayCompletedTaskQty() {
        return RestResponse.ok(maintTaskService.getTodayCompletedTaskQty());
    }

    /**
     * 获取待办列表(待派单、待接单、待处理、待评价)
     */
    @ApiOperation("获取待办列表(待派单、待接单、待处理、待评价)")
    @GetMapping("/taskTodoList")
    //@Permission("maint:plan:list")
    public RestResponse<List<MaintTaskDto>> getTaskTodoList() {
        return RestResponse.ok(maintTaskService.getTaskTodoList());
    }

    /**
     * 获取工单完成动态（返回最近5条）
     */
    @ApiOperation("获取工单完成动态（返回最近5条）")
    @GetMapping("/taskCompleteDynamic")
    //@Permission("maint:plan:list")
    public RestResponse<List<TaskCompleteDynamicDto>> getTaskCompleteDynamic() {
        return RestResponse.ok(maintTaskService.getTaskCompleteDynamic());
    }

    /**
     * 获取工单总览列表(待派单、待接单、待处理、待评价)
     */
    @ApiOperation("获取工单总览列表(待派单、待接单、待处理、待评价)")
    @GetMapping("/taskOverview/{status}")
    //@Permission("maint:plan:list")
    public RestResponse<List<MaintTaskDto>> getTaskOverview(
            @ApiParam(name = "status", value = "状态(0待派单1待接单2待执行3开始确认4安全确认中5已确认6执行中7执行中已挂起8待验收9正常关闭10异常关闭)",
                    required = true) @PathVariable Integer status) {
        return RestResponse.ok(maintTaskService.getTaskOverview(status));
    }

    /**
     * 获取已评价工单统计
     */
    @ApiOperation("获取已评价工单统计")
    @GetMapping("/taskEvaluated")
    //@Permission("maint:plan:list")
    public RestResponse<List<Map<Integer, Integer>>> getTaskEvaluated() {
        return RestResponse.ok(maintTaskService.getTaskEvaluated());
    }

    /**
     * 获取待办列表
     */
    @ApiOperation("获取待办列表")
    @GetMapping("/getTodoList")
    public RestResponse<BaseDto<ProcessTaskDto>> getTodoList(@RequestParam(defaultValue = "1") int pageNo, @RequestParam(defaultValue = "10") int limit) {
        UserParam userParam = new UserParam();
        userParam.setUid(PorosContextHolder.getCurrentUser().getUid());
        userParam.setPageNo(pageNo);
        userParam.setLimit(limit);
        RestResponse<BaseDto<TaskDto>> restResponse = taskServiceClient.getAssigneeList(userParam);
        if (restResponse.isOk()) {
            BaseDto<TaskDto> taskDtoBaseDto = restResponse.getData();
            BaseDto<ProcessTaskDto> processTaskDtoBaseDto = new BaseDto<>();
            if (taskDtoBaseDto.getRecords() != null) {
                List<ProcessTaskDto> processTaskDtos = restResponse.getData().getRecords().stream().map(record -> {
                    String processInstanceId = record.getProcessInstanceDTO().getProcessInstanceId();
                    String definitionKey = record.getProcessInstanceDTO().getDifinitionKey();
                    ProcessTaskDto processTaskDto = new ProcessTaskDto();
                    if (maintCode.equalsIgnoreCase(definitionKey)) {
                        MaintTaskDto maintTask = maintTaskService.getByProcessInstanceId(processInstanceId);
                        if (maintTask != null) {
                            BeanUtils.copyProperties(record, processTaskDto);
                            processTaskDto.setId(maintTask.getId());
                            processTaskDto.setCode(maintTask.getCode());
                            processTaskDto.setType(maintTask.getType());
                            processTaskDto.setStatus(maintTask.getStatus());
                        }
                    } else if (judgeCode.equalsIgnoreCase(definitionKey)) {
                        RestResponse<InventoryOrderDto> res = partClient.getInventoryOrderDtoByProcessInstanceId(processInstanceId);
                        if (res.isOk()) {
                            InventoryOrderDto data = res.getData();
                            if (data != null) {
                                BeanUtils.copyProperties(record, processTaskDto);
                                processTaskDto.setId(data.getId());
                                processTaskDto.setCode(data.getCode());
                                processTaskDto.setType(6);
                                switch (data.getStatus()) {
                                    case 1:
                                        processTaskDto.setStatus(9);
                                        break;
                                    case 3:
                                        processTaskDto.setStatus(-9);
                                        break;
                                    default:
                                        processTaskDto.setStatus(10);
                                }
                            }
                        }
                    } else if (transferCode.equalsIgnoreCase(definitionKey)) {
                        RestResponse<TransferOrderDto> res = partClient.getTransferOrderDtoByProcessInstanceId(processInstanceId);
                        if (res.isOk()) {
                            TransferOrderDto data = res.getData();
                            if (data != null) {
                                BeanUtils.copyProperties(record, processTaskDto);
                                processTaskDto.setId(data.getId());
                                processTaskDto.setCode(data.getCode());
                                processTaskDto.setType(5);
                                switch (data.getStatus()) {
                                    case 1:
                                        processTaskDto.setStatus(9);
                                        break;
                                    case 3:
                                        processTaskDto.setStatus(-9);
                                        break;
                                    default:
                                        processTaskDto.setStatus(10);
                                }
                            }
                        }
                    }else if (planCode.equalsIgnoreCase(definitionKey)) {
                        LambdaQueryWrapper<MaintPlan> queryWrapper = Wrappers.lambdaQuery();
                        queryWrapper.eq(MaintPlan::getProcessInstanceId, processInstanceId);
                        List<MaintPlan> list = maintPlanService.list(queryWrapper);
                        if (CollUtil.isNotEmpty(list) && list.size() == 1) {
                            MaintPlan maintPlan = list.get(0);
                            BeanUtils.copyProperties(record, processTaskDto);
                            processTaskDto.setId(maintPlan.getId());
                            processTaskDto.setCode("");
                            processTaskDto.setType(7);
                            switch (maintPlan.getStatus()) {
                                case 2:
                                    processTaskDto.setStatus(9);
                                    break;
                                case 3:
                                    processTaskDto.setStatus(-9);
                                    break;
                                default:
                                    processTaskDto.setStatus(10);
                            }
                        }
                    } else if (changeCode.equalsIgnoreCase(definitionKey)) {
                        RestResponse<EquipChangeDto> res = equipmentClient.getEquipChangeByProcessInstanceId(processInstanceId);
                        if (res.isOk()) {
                            EquipChangeDto dto = res.getData();
                            if (dto != null) {
                                BeanUtils.copyProperties(record, processTaskDto);
                                processTaskDto.setId(dto.getId());
                                processTaskDto.setCode(dto.getLocationName());
                                processTaskDto.setType(8);
                                switch (dto.getStatus()) {
                                    case 0:
                                        processTaskDto.setStatus(9);
                                        break;
                                    case 2:
                                        processTaskDto.setStatus(-9);
                                        break;
                                    default:
                                        processTaskDto.setStatus(10);
                                }
                            }
                        }
                    } else {
                        // todo 更多流程
                    }
                    return processTaskDto;
                }).filter(dto -> dto.getStatus() != null).collect(Collectors.toList());
                processTaskDtoBaseDto.setRecords(processTaskDtos);
                processTaskDtoBaseDto.setCurrent(taskDtoBaseDto.getCurrent());
                processTaskDtoBaseDto.setPages(taskDtoBaseDto.getPages());
                processTaskDtoBaseDto.setSize(taskDtoBaseDto.getSize());
                processTaskDtoBaseDto.setTotal(taskDtoBaseDto.getTotal());
                return RestResponse.ok(processTaskDtoBaseDto);
            } else {
                return RestResponse.ok(new BaseDto<>());
            }
        } else {
            log.error("调用流程引擎远程接口失败," + restResponse.getMsg());
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("process_error", null, LocaleContextHolder.getLocale())));
        }
    }

    /**
     * 客户端待办列表
     */
    @ApiOperation("客户端待办列表")
    @GetMapping("/clientTodoList")
    public RestResponse<BaseDto<ProcessTaskDto>> clientTodoList(@RequestParam(defaultValue = "1") int pageNo, @RequestParam(defaultValue = "10") int limit) {
        UserParam userParam = new UserParam();
        userParam.setDefinitionKey(remoteCode);
        userParam.setUid(PorosContextHolder.getCurrentUser().getUid());
        userParam.setPageNo(pageNo);
        userParam.setLimit(limit);
        log.info("调用工作流 ->" + JSONObject.toJSON(userParam));
        RestResponse<BaseDto<TaskDto>> restResponse = taskServiceClient.getAssigneeList(userParam);
        log.info("工作流返回数据->" + JSONObject.toJSON(restResponse));
        if (restResponse.isOk()) {
            BaseDto<TaskDto> taskDtoBaseDto = restResponse.getData();
            BaseDto<ProcessTaskDto> processTaskDtoBaseDto = new BaseDto<>();
            if (taskDtoBaseDto.getRecords() != null) {
                List<ProcessTaskDto> processTaskDtos = restResponse.getData().getRecords().stream().map(record -> {
                    String processInstanceId = record.getProcessInstanceDTO().getProcessInstanceId();
                    ProcessTaskDto processTaskDto = new ProcessTaskDto();
                    MaintTaskDto maintTask = maintTaskService.getByProcessInstanceId(processInstanceId);
                    if (maintTask != null) {
                        BeanUtils.copyProperties(record, processTaskDto);
                        processTaskDto.setId(maintTask.getId());
                        processTaskDto.setCode(maintTask.getCode());
                        processTaskDto.setType(StaticValue.NINE);
                        processTaskDto.setStatus(maintTask.getStatus());
                    }
                    return processTaskDto;
                }).filter(dto -> dto.getStatus() != null).collect(Collectors.toList());
                processTaskDtoBaseDto.setRecords(processTaskDtos);
                processTaskDtoBaseDto.setCurrent(taskDtoBaseDto.getCurrent());
                processTaskDtoBaseDto.setPages(taskDtoBaseDto.getPages());
                processTaskDtoBaseDto.setSize(taskDtoBaseDto.getSize());
                processTaskDtoBaseDto.setTotal(taskDtoBaseDto.getTotal());
                return RestResponse.ok(processTaskDtoBaseDto);
            } else {
                return RestResponse.ok(new BaseDto<>());
            }
        } else {
            log.error("调用流程引擎远程接口失败," + restResponse.getMsg());
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("process_error", null, LocaleContextHolder.getLocale())));
        }
    }

    @ApiOperation("查询大屏诊断排名前五的数据")
    @PostMapping("/getManualRepairGroupCustomerDto")
    @AuditLog(title = "大屏", desc = "查询大屏诊断排名前五的数据", businessType = BusinessType.QUERY)
    public RestResponse<ManualRepairGroupCustomerDto> getManualRepairGroupCustomerDto(@RequestBody TimeQueryParam timeQueryParam) {
        return RestResponse.ok(manualRepairService.getManualRepairGroupCustomerDto(timeQueryParam));
    }


    @ApiOperation("获取工作日历")
    @PostMapping("/getWorkCalendar")
    public RestResponse getCalcuelate(@RequestBody @Valid WorkCalendarQueryParam param) {
        return RestResponse.ok(maintTaskService.getWorkCalendar(param));
    }

    @ApiOperation("获取工作日历")
    @PostMapping("/getWorkCalendar/app")
    public RestResponse<List<WorkCalendarAppResult>> getCalcuelateOfApp(@RequestBody @Valid WorkCalendarQueryParam param) {
        return RestResponse.ok(maintTaskService.getWorkCalendarOfApp(param));
    }

    @ApiOperation("获取人员视图工作日历")
    @PostMapping("/user/getWorkCalendar")
    public RestResponse<LinkedHashMap<String, List<UserCalendarResult>>> getUserCalcuelate(@RequestBody @Valid UserCalendarQueryParam param) {
        return RestResponse.ok(maintTaskService.getUserCalcuelate(param));
    }

    @ApiOperation("获取设备视图工作日历")
    @PostMapping("/equipment/getWorkCalendar")
    public RestResponse<LinkedHashMap<String, List<EquipmentCalendarResult>>> getEquipmentCalcuelate(@RequestBody @Valid EquipmentCalendarQueryParam param) {
        return RestResponse.ok(maintTaskService.getEquipmentCalcuelate(param));
    }


    @ApiOperation("获取待办-维保计划")
    @PostMapping("/get/todo/plan")
    public RestResponse<PageResult<MaintPlanDto>> getTodoOfPlan(@RequestBody MaintPlanQueryParam param) {
        return RestResponse.ok(maintPlanService.getTodoOfPlan(param));
    }

    @ApiOperation("获取待办-工单")
    @PostMapping("/get/todo/task")
    public RestResponse<PageResult<MaintTaskDto>> getTodoOfTask(@RequestBody MaintTaskQueryParam param) {
        return RestResponse.ok(maintTaskService.getTodoOfTask(param));
    }

    @ApiOperation("获取待办-缺陷")
    @PostMapping("/get/todo/defect")
    public RestResponse<PageResult<DefectInfoDto>> getTodoOfDefect(@RequestBody DefectInfoQueryParam param) {
        param.setDefectStatusArray(new Integer[]{DefectStatusEnum.NODEAL.getValue(), DefectStatusEnum.DEALING.getValue()});
        return RestResponse.ok(defectInfoService.pageDto(param, 3));
    }

    @ApiOperation("工作台-今日任务概览")
    @GetMapping("/todayTask")
    @ApiImplicitParam(name = "ourTask", value = "我的工单", dataType = "boolean", defaultValue = "false")
    public RestResponse<TodayTaskCountDto> todayTask(@RequestParam(required = false, defaultValue = "false") Boolean ourTask) {
        return RestResponse.ok(maintTaskService.todayTask(ourTask));
    }

    @ApiOperation("工作台-任务总览")
    @GetMapping("/getCount")
    public RestResponse<List<TaskCountDto>> getWorkbenchCount(){
        return RestResponse.ok(maintTaskService.getWorkbenchCount());
    }
}
