package cn.getech.ehm.task.service.impl;

import cn.getech.ehm.task.dto.*;
import cn.getech.ehm.task.entity.ActOvertimeInfo;
import cn.getech.ehm.task.handler.ActivitiHandler;
import cn.getech.ehm.task.mapper.ActOvertimeInfoMapper;
import cn.getech.ehm.task.service.IActOvertimeInfoService;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.ResultCode;
import cn.getech.poros.framework.common.bean.BaseEntity;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * <pre>
 * 节点时限记录 服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2023-03-15
 */
@Slf4j
@Service
public class ActOvertimeInfoServiceImpl extends BaseServiceImpl<ActOvertimeInfoMapper, ActOvertimeInfo> implements IActOvertimeInfoService {

    @Autowired
    private ActOvertimeInfoParamMapper actOvertimeInfoParamMapper;

    @Override
    public PageResult<ActOvertimeInfoDto> pageDto(ActOvertimeInfoQueryParam actOvertimeInfoQueryParam) {
        Wrapper<ActOvertimeInfo> wrapper = getPageSearchWrapper(actOvertimeInfoQueryParam);
        PageResult<ActOvertimeInfoDto> result = actOvertimeInfoParamMapper.pageEntity2Dto(page(actOvertimeInfoQueryParam, wrapper));

        return Optional.ofNullable(result).orElse(new PageResult<>());
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveByParam(ActOvertimeInfoAddParam actOvertimeInfoAddParam) {
        ActOvertimeInfo actOvertimeInfo = actOvertimeInfoParamMapper.addParam2Entity(actOvertimeInfoAddParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, actOvertimeInfo);
        return save(actOvertimeInfo);
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean updateByParam(ActOvertimeInfoEditParam actOvertimeInfoEditParam) {
        ActOvertimeInfo actOvertimeInfo = actOvertimeInfoParamMapper.editParam2Entity(actOvertimeInfoEditParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, actOvertimeInfo);
        return updateById(actOvertimeInfo);
    }


    @Override
    public ActOvertimeInfoDto getDtoById(Long id) {
        return actOvertimeInfoParamMapper.entity2Dto((ActOvertimeInfo) this.getById(id));
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveDtoBatch(List<ActOvertimeInfoDto> rows) {
        return saveBatch(actOvertimeInfoParamMapper.dtoList2Entity(rows));
    }

    private Wrapper<ActOvertimeInfo> getPageSearchWrapper(ActOvertimeInfoQueryParam actOvertimeInfoQueryParam) {
        LambdaQueryWrapper<ActOvertimeInfo> wrapper = Wrappers.<ActOvertimeInfo>lambdaQuery();
        wrapper.eq(StringUtils.isNotBlank(actOvertimeInfoQueryParam.getProcInsId()), ActOvertimeInfo::getProcInsId, actOvertimeInfoQueryParam.getProcInsId());
        Date now = new Date();
        //默认只显示已超时得超时项
        wrapper.apply("((task_end_time < NOW() and task_real_time IS NULL ) or (task_real_time > task_end_time))");
        if (BaseEntity.class.isAssignableFrom(ActOvertimeInfo.class)) {
            wrapper.orderByDesc(ActOvertimeInfo::getUpdateTime, ActOvertimeInfo::getCreateTime);
        }
        return wrapper;
    }
}
