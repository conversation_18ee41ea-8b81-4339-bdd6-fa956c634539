package cn.getech.ehm.task.handler;

import cn.getech.ehm.base.client.BaseServiceClient;
import cn.getech.ehm.base.dto.*;
import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.common.context.UserContextHolder;
import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.equipment.client.EquipmentClient;
import cn.getech.ehm.equipment.client.PorosClient;
import cn.getech.ehm.equipment.dto.*;
import cn.getech.ehm.task.entity.DefectInfo;
import cn.getech.ehm.task.entity.MaintTaskPart;
import cn.getech.ehm.task.entity.MaintTaskRepair;
import cn.getech.ehm.task.service.IDefectInfoService;
import cn.getech.ehm.task.service.IMaintTaskPartService;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.permission.client.PorosSecGrantClient;
import cn.getech.poros.permission.client.PorosSecOrgClient;
import cn.getech.poros.permission.client.PorosSecStaffClient;
import cn.getech.poros.permission.dto.PorosSecStaffDto;
import cn.getech.poros.permission.dto.RoleDto;
import cn.getech.poros.permission.dto.SecStaffUidParam;
import cn.getech.poros.permission.dto.StaffBaseInfoDto;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CommonGetHandler {
    @Autowired
    private EquipmentClient equipmentClient;

    @Autowired
    private BaseServiceClient baseServiceClient;
    @Autowired
    private PorosSecStaffClient porosSecStaffClient;

    @Autowired
    private IMaintTaskPartService maintTaskPartService;
    @Autowired
    private IDefectInfoService defectInfoService;
    @Autowired
    private PorosSecGrantClient porosSecGrantClient;
    @Autowired
    private PorosSecOrgClient porosSecOrgClient;

    @Autowired
    private PorosClient porosClient;

    public Map<String, DefectInfo> getDefectListById(List<String> defectId) {
        if (CollectionUtils.isEmpty(defectId)) {
            return Maps.newHashMap();
        }
        List<DefectInfo> list = defectInfoService.listByIds(defectId);
        Map<String, DefectInfo> collect = list.stream().collect(Collectors.toMap(DefectInfo::getId, item -> item, (v1, v2) -> v2));
        return collect;
    }

    public EquipmentListDto getEquipmentInfoById(String equipmentId) {
        if (StringUtils.isEmpty(equipmentId)) {
            return null;
        }
        RestResponse<Map<String, EquipmentListDto>> listByIds = equipmentClient.getListByIds(equipmentId.split(","));
        if (listByIds.isOk()) {
            Map<String, EquipmentListDto> data = listByIds.getData();
            return data.getOrDefault(equipmentId, null);
        } else {
            throw new GlobalServiceException(GlobalResultMessage.of("查询设备失败"));
        }
    }

    public Map<String, EquipmentListDto> getEquipmentByIds(List<String> equipmentIds) {
        if (CollectionUtils.isEmpty(equipmentIds)) {
            return Maps.newHashMap();
        }
        RestResponse<Map<String, EquipmentListDto>> listByIds = equipmentClient.getListByIds(equipmentIds.toArray(new String[equipmentIds.size()]));
        if (listByIds.isOk()) {
            return listByIds.getData();
        } else {
            log.error("远程调用equipment-service出错");
        }
        return Maps.newHashMap();
    }

    public Map<String, MaintTeamDto> getTeamById(List<String> teamId) {
        if (CollectionUtils.isEmpty(teamId)) {
            return Maps.newHashMap();
        }
        RestResponse<List<MaintTeamDto>> maintTeamListByIds = baseServiceClient.getMaintTeamListByIds(teamId.toArray(new String[teamId.size()]));
        if (maintTeamListByIds.isOk()) {
            List<MaintTeamDto> data = maintTeamListByIds.getData();
            Map<String, MaintTeamDto> collect = data.stream().collect(Collectors.toMap(item -> item.getId(), item -> item, (v1, v2) -> v2));
            return collect;
        } else {
            log.error("查询班组失败");
        }
        return Maps.newHashMap();
    }

    public String getTeamName(Map<String, MaintTeamDto> teamById, String[] teamIds) {
        MaintTeamDto maintTeamDto = teamById.get(ArrayUtil.isNotEmpty(teamIds) ? teamIds[0] : "");
        return maintTeamDto != null ? maintTeamDto.getName() : "";
    }

    public EquipmentListDto getEquipment(Map<String, EquipmentListDto> equipmentById, String equipmentId) {
        EquipmentListDto equipmentListDto = equipmentById.get(equipmentId);
        if (equipmentListDto != null) {
            return equipmentListDto;
        }
        return new EquipmentListDto();
    }

    public MaintTaskRepair getRepairByTaskId(Map<String, MaintTaskRepair> repairByTaskId, String id) {
        MaintTaskRepair maintTaskRepair = repairByTaskId.get(id);
        if (maintTaskRepair != null) {
            return maintTaskRepair;
        } else {
            return new MaintTaskRepair();
        }
    }

    public Map<String, List<MaintTaskPart>> getPartByTaskId(List<String> taskIdList) {
        if (CollectionUtils.isEmpty(taskIdList)) {
            return Maps.newHashMap();
        }
        List<MaintTaskPart> maintTaskPartList = maintTaskPartService.list(new QueryWrapper<MaintTaskPart>().lambda().in(MaintTaskPart::getTaskId, taskIdList));
        Map<String, List<MaintTaskPart>> collect = maintTaskPartList.stream().collect(Collectors.groupingBy(item -> item.getTaskId()));
        return collect;
    }

    public void getPart(Map<String, List<MaintTaskPart>> partByTaskId, String taskId) {
        List<MaintTaskPart> maintTaskPartList = partByTaskId.get(taskId);
        if (CollectionUtils.isNotEmpty(maintTaskPartList)) {

        }
    }

    public Map<String, FaultKnowledgeResDto> getFaultPhemones(List<String> phenomenonId) {
        if (CollectionUtils.isEmpty(phenomenonId)) {
            return Maps.newHashMap();
        }
        FaultKnowledgeSearchDto searchDto = new FaultKnowledgeSearchDto();
        searchDto.setType(StaticValue.ONE);
        searchDto.setIds(phenomenonId);
        RestResponse<Map<String, FaultKnowledgeResDto>> restResponse = baseServiceClient.faultKnowledgeMap(searchDto);
        if (restResponse.isOk()) {
            Map<String, FaultKnowledgeResDto> data = restResponse.getData();
            return data;
        } else {
            throw new GlobalServiceException(GlobalResultMessage.of("查询故障失败"));
        }
    }

    public Map<String, FaultKnowledgeResDto> getFaultReason(List<String> phenomenonId) {
        if (CollectionUtils.isEmpty(phenomenonId)) {
            return Maps.newHashMap();
        }
        FaultKnowledgeSearchDto searchDto = new FaultKnowledgeSearchDto();
        searchDto.setType(StaticValue.TWO);
        searchDto.setIds(phenomenonId);
        RestResponse<Map<String, FaultKnowledgeResDto>> restResponse = baseServiceClient.faultKnowledgeMap(searchDto);
        if (restResponse.isOk()) {
            Map<String, FaultKnowledgeResDto> data = restResponse.getData();
            return data;
        } else {
            throw new GlobalServiceException(GlobalResultMessage.of("查询故障失败"));
        }
    }

    public Map<String, FaultKnowledgeResDto> getFaultMeasure(List<String> phenomenonId) {
        if (CollectionUtils.isEmpty(phenomenonId)) {
            return Maps.newHashMap();
        }
        FaultKnowledgeSearchDto searchDto = new FaultKnowledgeSearchDto();
        searchDto.setType(StaticValue.THREE);
        searchDto.setIds(phenomenonId);
        RestResponse<Map<String, FaultKnowledgeResDto>> restResponse = baseServiceClient.faultKnowledgeMap(searchDto);
        if (restResponse.isOk()) {
            Map<String, FaultKnowledgeResDto> data = restResponse.getData();
            return data;
        } else {
            throw new GlobalServiceException(GlobalResultMessage.of("查询故障失败"));
        }
    }

    public String getFaultMeasure(Map<String, FaultKnowledgeResDto> faultPhemones, String[] ids, String other) {
        List<FaultKnowledgeResDto> faultKnowledgeResDtos = Lists.newArrayList();
        StringBuilder sb = new StringBuilder();
        if (ArrayUtil.isNotEmpty(ids)) {
            for (String temp : ids) {
                FaultKnowledgeResDto faultKnowledgeResDto = faultPhemones.get(temp);
                if (faultKnowledgeResDto != null) {
                    sb.append(faultKnowledgeResDto.getName() + ",");
                }
            }
        }

        if (StringUtils.isNotBlank(other)) {
            sb.append(other);
        }
        return sb.toString();
    }

    public String getFaultReason(Map<String, FaultKnowledgeResDto> faultPhemones, String[] ids, String other) {
        List<FaultKnowledgeResDto> faultKnowledgeResDtos = Lists.newArrayList();
        StringBuilder sb = new StringBuilder();
        if (ArrayUtil.isNotEmpty(ids)) {
            for (String temp : ids) {
                FaultKnowledgeResDto faultKnowledgeResDto = faultPhemones.get(temp);
                if (faultKnowledgeResDto != null) {
                    sb.append(faultKnowledgeResDto.getName() + ",");
                }
            }
        }
        if (StringUtils.isNotBlank(other)) {
            sb.append(other);
        }
        return sb.toString();
    }

    public String getFaultPhemones(Map<String, FaultKnowledgeResDto> faultPhemones, String[] ids, String other) {
        List<FaultKnowledgeResDto> faultKnowledgeResDtos = Lists.newArrayList();
        StringBuilder sb = new StringBuilder();
        if (ArrayUtil.isNotEmpty(ids)) {
            for (String temp : ids) {
                FaultKnowledgeResDto faultKnowledgeResDto = faultPhemones.get(temp);
                if (faultKnowledgeResDto != null) {
                    sb.append(faultKnowledgeResDto.getName() + ",");
                }
            }
        }
        if (StringUtils.isNotBlank(other)) {
            sb.append(other);
        }
        return sb.toString();
    }

    public Map<String, StaffBaseInfoDto> getNames(List<String> uids) {
        RestResponse<Map<String, StaffBaseInfoDto>> baseInfoMap = porosSecStaffClient.getBaseInfoMap(uids);
        if (baseInfoMap.isOk()) {
            Map<String, StaffBaseInfoDto> data = baseInfoMap.getData();
            return data;
        } else {
            return Maps.newHashMap();
        }
    }

    public String getNames(Map<String, StaffBaseInfoDto> nameMap, String uid) {
        StaffBaseInfoDto orDefault = nameMap.get(uid);
        if (orDefault != null) {
            return orDefault.getName();
        }
        return "";
    }

    public Map<String, String> getCategoryNameMap() {
        Map result = Maps.newHashMap();
        RestResponse<Map<String, String>> categoryAllNameMap = equipmentClient.getCategoryAllNameMap();
        if (categoryAllNameMap.isOk()) {
            Map<String, String> data = categoryAllNameMap.getData();
            result = data;
        } else {
            throw new GlobalServiceException(GlobalResultMessage.of("获取设备位置失败"));
        }
        return result;
    }

    public Map<String, String> getUidNameMap(List<String> uids) {
        Map<String, String> uidMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(uids)) {
            String uidStrs = StringUtils.join(uids, StringPool.COMMA);
            RestResponse<Map<String, String>> uidResponse = porosSecStaffClient.getMap(uidStrs);
            if (uidResponse.isOk()) {
                uidMap = uidResponse.getData();
            } else {
                log.error("获取用户名称失败");
            }
        }
        return uidMap;
    }

    /**
     * 获取故障现象/原因/措施
     *
     * @param ids
     * @param type
     * @return
     */
    public Map<String, FaultKnowledgeResDto> getFaultKnowledgeMap(List<String> ids, Integer type) {
        Map<String, FaultKnowledgeResDto> faultKnowledgeMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(ids)) {
            FaultKnowledgeSearchDto searchDto = new FaultKnowledgeSearchDto();
            searchDto.setType(type);
            searchDto.setIds(ids);
            RestResponse<Map<String, FaultKnowledgeResDto>> restResponse = baseServiceClient.faultKnowledgeMap(searchDto);
            if (restResponse.isOk()) {
                faultKnowledgeMap = restResponse.getData();
            } else {
                log.error("获取故障现象/原因/措施失败");
            }
        }
        return faultKnowledgeMap;
    }

    public Map<String, DictionaryItemDto> getDictMap(String dictType) {
        RestResponse<Map<String, DictionaryItemDto>> baseServiceClientItemMapByCode = baseServiceClient.getItemMapByCode(dictType);
        if (!baseServiceClientItemMapByCode.isOk()) {
            log.info("连接base-service获取{}字典表失败", dictType);
            return Maps.newHashMap();
        } else {
            return baseServiceClientItemMapByCode.getData();
        }
    }

    public Map<String, String> getDictNameMap(String dictType) {
        RestResponse<Map<String, DictionaryItemDto>> baseServiceClientItemMapByCode = baseServiceClient.getItemMapByCode(dictType);
        if (!baseServiceClientItemMapByCode.isOk()) {
            log.info("连接base-service获取{}字典表失败", dictType);
            return Maps.newHashMap();
        } else {
            return baseServiceClientItemMapByCode.getData().values().stream().collect(Collectors.toMap(DictionaryItemDto::getValue, DictionaryItemDto::getName));
        }
    }

    public Map<String, String> getUidNameMap(String uidStrs) {
        Map<String, String> uidMap = null;
        RestResponse<Map<String, String>> uidResponse = porosSecStaffClient.getMap(uidStrs);
        if (uidResponse.isOk()) {
            uidMap = uidResponse.getData();
        } else {
            log.error("获取用户名称失败");
        }
        return null != uidMap ? uidMap : Maps.newHashMap();
    }

    public Map<String, String> getAllUserNameMap() {
        Map<String, String> map = Maps.newHashMap();
        RestResponse<List<PorosSecStaffDto>> restResponse = porosClient.getUserList(new PorosStaffSearchDto());
        if (restResponse.isOk()) {
            map = restResponse.getData().stream().collect(Collectors.toMap(PorosSecStaffDto::getName, PorosSecStaffDto::getUid, (v1, v2) -> v1));
        } else {
            log.error("获取租户下用户失败");
        }
        return map;
    }

    /**
     * 获取设备当前权限
     *
     * @return
     */
    public BuildInfoSearchDto getCurrentUserInfoIds() {
        RestResponse<BuildInfoSearchDto> authRes = equipmentClient.getCurrentUserInfoIds();
        if (!authRes.isOk()) {
            log.error("获取设备权限失败");
        } else {
            return authRes.getData();
        }
        return null;

    }

    /**
     * 根据角色id获取角色信息
     *
     * @param roleIds
     * @return
     */
    public Map<String, RoleDto> getRoleMap(List<String> roleIds) {
        if (CollectionUtils.isNotEmpty(roleIds)) {
            RestResponse<List<RoleDto>> roleRes = porosSecGrantClient.getByRoleIds(roleIds);
            if (roleRes.isOk()) {
                return roleRes.getData().stream().collect(Collectors.toMap(RoleDto::getId, v -> v, (v1, v2) -> v1));
            } else {
                log.error("获取角色信息失败");
            }
        }
        return Collections.emptyMap();
    }

    /**
     * 根据角色id获取用户信息
     *
     * @param roleId
     * @return
     */
    public List<PorosSecStaffDto> getUserByRoleId(String roleId) {
        if (StringUtils.isNotBlank(roleId)) {
            RestResponse<List<PorosSecStaffDto>> res = porosSecGrantClient.listUserByRoleId(roleId);
            if (res.isOk()) {
                return res.getData();
            } else {
                log.error("获取角色下用户信息失败");
            }
        }
        return Collections.emptyList();
    }

    /**
     * 根据用户uid获取对应信息
     *
     * @param uids
     * @return
     */
    public List<PorosSecStaffDto> getStaffMap(List<String> uids) {
        if (CollectionUtils.isNotEmpty(uids)) {
            SecStaffUidParam secStaffUidParam = new SecStaffUidParam();
            secStaffUidParam.setUids(uids);
            RestResponse<List<PorosSecStaffDto>> staffRes = porosSecStaffClient.findUserByUidsPost(secStaffUidParam);
            if (staffRes.isOk()) {
                return staffRes.getData();
            } else {
                log.error("获取用户失败");
            }
        }
        return new ArrayList<>();
    }

    /**
     * 获取设备基础信息
     *
     * @param searchDto
     * @return
     */
    public Map<String, EquipmentSummaryDto> getEquipmentSummaryMap(EquipmentInfoSearchDto searchDto) {
        RestResponse<Map<String, EquipmentSummaryDto>> restResponse = equipmentClient.getSummaryMap(searchDto);
        if (!restResponse.isOk()) {
            log.error("获取设备信息失败");
            return Maps.newHashMap();
        } else {
            return restResponse.getData();
        }
    }

    /**
     * 根据班组id获取班组下人员id集合
     *
     * @param teamIds
     * @return
     */
    public List<String> getMaintainerIdByTeamIds(List<String> teamIds) {
        RestResponse<List<String>> personIdsByTeamIds = baseServiceClient.getPersonIdsByTeamIds(teamIds.toArray(new String[teamIds.size()]));
        if (!personIdsByTeamIds.isOk()) {
            log.error("查询班组失败");
        } else {
            return personIdsByTeamIds.getData();
        }
        return null;
    }

    /**
     * 根据人员id获取uid集合
     *
     * @param maintainerIds
     * @return
     */
    public List<String> getUidsByMaintainerIds(List<String> maintainerIds) {
        RestResponse<List<String>> uidsByMaintainerIds = baseServiceClient.getUidsByIds(maintainerIds.toArray(new String[maintainerIds.size()]));
        if (!uidsByMaintainerIds.isOk()) {
            log.error("查询人员失败");
        } else {
            return uidsByMaintainerIds.getData();
        }
        return null;
    }

    public List<String> getUidsOfDept() {
        UserBaseInfo userBaseInfo = UserContextHolder.getContext().getUserBaseInfo();
        List<String> allOrgCode = userBaseInfo.getAllOrgCode();
        List<String> finalList = Lists.newArrayList();
        finalList.add(userBaseInfo.getUid());
        for (String orgCode : allOrgCode) {
            RestResponse<List<PorosSecStaffDto>> member = porosSecOrgClient.getMember(orgCode);
            if (member.isOk() && CollectionUtils.isNotEmpty(member.getData())) {
                List<String> collect = member.getData().stream().map(item -> item.getUid()).collect(Collectors.toList());
                finalList.addAll(collect);
            }
        }
        return finalList;
    }

    public List<String> getLocationIdsOf() {
        UserBaseInfo userBaseInfo = UserContextHolder.getContext().getUserBaseInfo();
        List<String> allOrgCode = userBaseInfo.getAllOrgCode();
        List<String> finalList = Lists.newArrayList();
        finalList.add(userBaseInfo.getUid());
        for (String orgCode : allOrgCode) {
            RestResponse<List<PorosSecStaffDto>> member = porosSecOrgClient.getMember(orgCode);
            if (member.isOk() && CollectionUtils.isNotEmpty(member.getData())) {
                List<String> collect = member.getData().stream().map(item -> item.getUid()).collect(Collectors.toList());
                finalList.addAll(collect);
            }
        }
        return finalList;
    }

    public List<String> getUidOfMaintainerIds(String[] maintainers) {
        RestResponse<List<String>> restResponse = baseServiceClient.getUidsByIds(maintainers);
        if (restResponse.isOk()) {
            return restResponse.getData();
        } else {
            log.error(restResponse.getMsg());
            throw new GlobalServiceException(GlobalResultMessage.of("没有获取到维保人员对应的用户id"));
        }
    }

    public List<String> getUidOfMaintTeamIds(String[] teamIds) {
        TeamPersonUidDto temp = new TeamPersonUidDto();
        temp.setTeamIds(teamIds);
        RestResponse<List<String>> restResponse = baseServiceClient.getUidsByTeamOrPersonIds(temp);
        if (restResponse.isOk()) {
            return restResponse.getData();
        } else {
            log.error(restResponse.getMsg());
            throw new GlobalServiceException(GlobalResultMessage.of("没有获取到维保班组对应的用户id"));
        }
    }
}
