package cn.getech.ehm.task.controller;


import io.swagger.annotations.*;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import org.springframework.beans.factory.annotation.Autowired;
import cn.getech.poros.framework.common.annotation.Permission;
import cn.getech.poros.framework.common.api.PageResult;
import org.apache.commons.collections4.CollectionUtils;
import cn.getech.ehm.task.dto.MaintTaskDefectQueryParam;
import cn.getech.ehm.task.dto.MaintTaskDefectAddParam;
import cn.getech.ehm.task.dto.MaintTaskDefectEditParam;
import cn.getech.ehm.task.dto.MaintTaskDefectDto;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.utils.ExcelUtils;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotEmpty;

import cn.getech.ehm.task.service.IMaintTaskDefectService;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 工单下缺陷记录控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@RestController
@RequestMapping("/maintTaskDefect")
@Api(tags = "工单下缺陷记录服务接口")
public class MaintTaskDefectController {

    @Autowired
    private IMaintTaskDefectService maintTaskDefectService;

//    /**
//     * 分页获取工单下缺陷记录列表
//     */
//    @ApiOperation("分页获取工单下缺陷记录列表")
//    @PostMapping("/list")
//    //@Permission("maint:task:defect:list")
//    public RestResponse<PageResult<MaintTaskDefectDto>> pageList(@RequestBody MaintTaskDefectQueryParam maintTaskDefectQueryParam) {
//        return RestResponse.ok(maintTaskDefectService.pageDto(maintTaskDefectQueryParam));
//    }

    /**
     * 新增工单下缺陷记录
     */
    @ApiOperation("新增工单下缺陷记录")
    @AuditLog(title = "工单下缺陷记录", desc = "新增工单下缺陷记录", businessType = BusinessType.INSERT)
    @PostMapping
    //@Permission("maint:task:defect:update")
    public RestResponse<Boolean> add(@RequestBody @Valid MaintTaskDefectAddParam maintTaskDefectAddParam) {
        return RestResponse.ok(maintTaskDefectService.saveByParam(maintTaskDefectAddParam));
    }

    /**
     * 修改工单下缺陷记录
     */
    @ApiOperation(value = "修改工单下缺陷记录")
    @AuditLog(title = "工单下缺陷记录", desc = "修改工单下缺陷记录", businessType = BusinessType.UPDATE)
    @PutMapping
    //@Permission("maint:task:defect:update")
    public RestResponse<Boolean> update(@RequestBody @Valid MaintTaskDefectAddParam maintTaskDefectEditParam) {
        return RestResponse.ok(maintTaskDefectService.saveByParam(maintTaskDefectEditParam));
    }

//    /**
//     * 根据id删除工单下缺陷记录
//     */
//    @ApiOperation(value = "根据id删除工单下缺陷记录")
//    @AuditLog(title = "工单下缺陷记录", desc = "工单下缺陷记录", businessType = BusinessType.DELETE)
//    @DeleteMapping("/{ids}")
//    //@Permission("maint:task:defect:delete")
//    public RestResponse<Boolean> delete(@PathVariable("ids") @NotEmpty String[] ids) {
//        return RestResponse.ok(maintTaskDefectService.removeByIds(Arrays.asList(ids)));
//    }

    /**
     * 根据id获取工单下缺陷记录
     */
    @ApiOperation(value = "根据id获取工单下缺陷记录")
    @GetMapping(value = "/{id}")
    //@Permission("maint:task:defect:list")
    public RestResponse<MaintTaskDefectDto> get(@PathVariable String id) {
        return RestResponse.ok(maintTaskDefectService.getDtoById(id));
    }

//    /**
//     * 导出工单下缺陷记录列表
//     */
//    @ApiOperation(value = "导出工单下缺陷记录列表")
//    @AuditLog(title = "工单下缺陷记录", desc = "导出工单下缺陷记录列表", businessType = BusinessType.EXPORT)
//    @GetMapping("/export")
//    // @Permission("maint:task:defect:export")
//    public void excelExport(@Valid MaintTaskDefectQueryParam maintTaskDefectQueryParam, HttpServletResponse response) {
//        PageResult<MaintTaskDefectDto> pageResult = maintTaskDefectService.pageDto(maintTaskDefectQueryParam);
//        ExcelUtils<MaintTaskDefectDto> util = new ExcelUtils<>(MaintTaskDefectDto. class);
//
//        util.exportExcel(pageResult.getRecords(), "工单下缺陷记录", response);
//    }
//
//    /**
//     * Excel导入工单下缺陷记录
//     */
//    @ApiOperation(value = "Excel导入工单下缺陷记录")
//    @AuditLog(title = "工单下缺陷记录", desc = "Excel导入工单下缺陷记录", businessType = BusinessType.INSERT)
//    @PostMapping("/import")
//    //@Permission("maint:task:defect:import")
//    public RestResponse<Boolean> excelImport(@RequestParam("file") MultipartFile file) {
//        ExcelUtils<MaintTaskDefectDto> util = new ExcelUtils<>(MaintTaskDefectDto. class);
//        List<MaintTaskDefectDto> rows = util.importExcel(file);
//        if (CollectionUtils.isEmpty(rows)) {
//            return RestResponse.failed();
//        }
//        return RestResponse.ok(maintTaskDefectService.saveDtoBatch(rows));
//    }

}
