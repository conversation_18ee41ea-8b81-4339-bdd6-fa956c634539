package cn.getech.ehm.task.dto.task.info;

import cn.getech.ehm.base.dto.AttachmentClientDto;
import cn.getech.ehm.task.dto.task.assist.MaintTaskAssistListDto;
import cn.getech.ehm.common.mp.handler.StringArrayTypeHandler;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

import java.util.Date;
import java.util.List;


/**
 * 维护工单主表 返回数据模型
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "MaintTaskDto", description = "维护工单主表返回数据模型")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MaintTaskDto {

    @ApiModelProperty(value = "工单id")
    private String id;

    @ApiModelProperty(value = "工单编号")
    private String code;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "维保类型")
    private String jobType;

    @ApiModelProperty(value = "维保类型名称")
    private String jobTypeName;

    @ApiModelProperty("维保等级")
    private Integer jobLevel;

    @ApiModelProperty("维保等级名称")
    private String jobLevelName;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "影响程度")
    private String influence;

    @ApiModelProperty(value = "影响程度名称")
    private String influenceName;

    @ApiModelProperty(value = "故障影响")
    private String[] faultInfluences;

    @ApiModelProperty(value = "状态名称")
    private String statusName;

    @ApiModelProperty(value = "工单类型(1故障2维保3缺陷单)")
    private Integer type;

    @ApiModelProperty(value = "工单类型名称")
    private String typeName;

    @ApiModelProperty(value = "工单来源")
    private Integer sourceType;


    @ApiModelProperty(value = "计划单/故障单/缺陷单id")
    private String sourceId;

    @ApiModelProperty(value = "计划单名称")
    private String sourceName;

    @ApiModelProperty(value = "工单来源名称")
    private String sourceTypeName;

    @ApiModelProperty(value = "紧急程度/优先程度")
    private String urgency;

    @ApiModelProperty(value = "紧急程度名称")
    private String urgencyName;

    @ApiModelProperty(value = "专业/工单类别")
    private String major;

    @ApiModelProperty(value = "专业/工单类别")
    private String majorName;

    @ApiModelProperty(value = "设备编码")
    private String equipmentCode;

    @ApiModelProperty(value = "维护设备id")
    private String equipmentId;

    @ApiModelProperty(value = "维护设备名称")
    private String equipmentName;

    @ApiModelProperty(value = "维护设备位置")
    private String equipmentLocation;

    @ApiModelProperty(value = "维护设备类型Id")
    private String equipmentCategoryId;

    @ApiModelProperty(value = "维护设备类型")
    private String equipmentCategory;

    @ApiModelProperty(value = "维护设备状态")
    private Integer equipmentRunningStatus;

    @ApiModelProperty(value = "设备路径")
    private String equipmentParentAllName;

    @ApiModelProperty(value = "故障现象/作业标准")
    private String content;

    @ApiModelProperty(value = "计划维护时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date planMaintTime;

    @ApiModelProperty(value = "维护人员id集合")
    @TableField(typeHandler = StringArrayTypeHandler.class)
    private String[] staffIds;

    @ApiModelProperty(value = "维护人员名称集合")
    private String staffNames;

    @ApiModelProperty(value = "维护人员集合")
    private List<NameDetailDto> staffDtos;

    @ApiModelProperty(value = "维护班组id集合")
    @TableField(typeHandler = StringArrayTypeHandler.class)
    private String[] teamIds;

    @ApiModelProperty(value = "维护班组名称集合")
    private String teamNames;

    @ApiModelProperty(value = "维护人员集合")
    private List<NameDetailDto> teamDtos;

    @ApiModelProperty(value = "人员和班组组合人员ids")
    private String allStaffIds;

    @ApiModelProperty(value = "是否安全确认")
    private Boolean confirm;

    @ApiModelProperty(value = "流程实例id")
    private String processInstanceId;

    @ApiModelProperty(value = "工作流任务id")
    private String activityId;

    @ApiModelProperty(value = "派单员")
    private String[] dispatchHandler;

    @ApiModelProperty(value = "是否可以改派")
    private Boolean canTransform = false;

    @ApiModelProperty(value = "创建人标识")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "更新人标识")
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value = "审核人员", hidden = true)
    @TableField(typeHandler = StringArrayTypeHandler.class)
    private String[] reviewer;

    @ApiModelProperty(value = "已审核人员", hidden = true)
    @TableField(typeHandler = StringArrayTypeHandler.class)
    private String[] alreadyReviewer;

    @ApiModelProperty(value = "是否能审核")
    private Boolean canAudit = false;

    @ApiModelProperty(value = "首张图片id")
    private String picId;

    @ApiModelProperty(value = "图片url")
    private String picUrl;

    @ApiModelProperty(value = "关闭原因")
    private String closeReason;

    @ApiModelProperty("派单时间")
    private Date sendTaskDate;

    @ApiModelProperty("接单时间")
    private Date recTaskDate;

    @ApiModelProperty("派单截止时间")
    private Date sendTaskDeadlineDate;

    @ApiModelProperty("接单截止时间")
    private Date recTaskDeadlineDate;

    @ApiModelProperty("工单截止时间")
    private Date taskDeadlineDate;

    @ApiModelProperty("是否超时")
    private Boolean isOverTime;

    @ApiModelProperty("完工附件")
    @TableField(typeHandler = StringArrayTypeHandler.class)
    private String[] finishFileIds;

    @ApiModelProperty("维保对象id")
    private String sourcePlanEquipmentId;

    @ApiModelProperty("验收人id")
    private String[] accepterId;

    @ApiModelProperty("验收人名称")
    private String[] accepterName;

    private String handler;

    @ApiModelProperty("业务类型")
    private String bussinessType;

    @ApiModelProperty("区域")
    private String areaType;

    @ApiModelProperty("工序")
    private String processType;

    @ApiModelProperty("完工附件")
    private List<AttachmentClientDto> finishFileList;

    @ApiModelProperty("点检异常数量")
    private Integer ecErrorNum;

    @ApiModelProperty("点检异常处理方式")
    private Integer ecErrorResult;

    @ApiModelProperty("点检异常处理结果")
    private String ecErrorDealContent;

    @ApiModelProperty("ec错误关联工单id")
    private String ecErrorTaskId;

    @ApiModelProperty("ec错误关联工单号")
    private String ecErrorTaskCode;

    @ApiModelProperty("ec错误关联工单名称")
    private String ecErrorTaskName;;

    @ApiModelProperty("启动时间")
    private Date startTime;

    @ApiModelProperty("实际停机开始时间")
    private Date beginDowntime;

    @ApiModelProperty("实际停机结束时间")
    private Date endDowntime;

    @ApiModelProperty("工单开始处理时间")
    private Date beginMaintTime;

    @ApiModelProperty("工单结束处理时间")
    private Date endMaintTime;

    @ApiModelProperty("辅助人员信息")
    private List<MaintTaskAssistListDto>  assistPeopleList;

    @ApiModelProperty("工单耗时")
    private String maintTimeCost;

    @ApiModelProperty("停机耗时")
    private String downTimeCost;

    @ApiModelProperty("是否已经安全确认结束")
    private Integer secureConfirmed;

    @ApiModelProperty("缺陷单id")
    private String defectId;

    @ApiModelProperty("缺陷状态")
    private Integer defectStatus;

    @TableField(typeHandler = StringArrayTypeHandler.class)
    private String[] uids;

    @ApiModelProperty("挂起原因")
    private String hangUpReason;

    @ApiModelProperty("是否生成了缺陷")
    private Boolean defectFlag;

//    @ApiModelProperty(value = "LOTO标志1是2否")
//    private Integer lotoFlag;

    @ApiModelProperty("损坏原因")
    private String damageReason;

    @ApiModelProperty("是否有挂起")
    private Boolean hasHangUp;

    @ApiModelProperty("退回原因")
    private String returnReason;

    @ApiModelProperty("重新打开原因")
    private String reopenReason;

    @ApiModelProperty("重新打开次数")
    private Integer reopenCount;

    @ApiModelProperty(value = "来源工单id")
    private String sourceTaskId;

    @ApiModelProperty(value = "来源工单code")
    private String sourceTaskCode;
}