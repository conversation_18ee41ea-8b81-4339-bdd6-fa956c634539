package cn.getech.ehm.task.service;

import cn.getech.ehm.task.entity.TaskNotifyConfig;
import cn.getech.poros.framework.common.service.IBaseService;
import cn.getech.ehm.task.dto.TaskNotifyConfigQueryParam;
import cn.getech.ehm.task.dto.TaskNotifyConfigAddParam;
import cn.getech.ehm.task.dto.TaskNotifyConfigEditParam;
import cn.getech.ehm.task.dto.TaskNotifyConfigDto;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <pre>
 * 工单通知配置 服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2022-11-01
 */
public interface ITaskNotifyConfigService extends IBaseService<TaskNotifyConfig> {

        /**
         * 分页查询，返回Dto
         *
         * @param taskNotifyConfigQueryParam
         * @return
         */
        PageResult<TaskNotifyConfigDto> pageDto(TaskNotifyConfigQueryParam taskNotifyConfigQueryParam);

        /**
         * 保存
         * @param taskNotifyConfigAddParam
         * @return
         */
        boolean saveByParam(TaskNotifyConfigAddParam taskNotifyConfigAddParam);

        /**
         * 根据id查询，转dto
         * @param id
         * @return
         */
        TaskNotifyConfigDto getDtoById(Long id);

        /**
         * 批量保存
         * @param rows
         */
        boolean saveDtoBatch(List<TaskNotifyConfigDto> rows);

        /**
         * 更新
         * @param taskNotifyConfigEditParam
         */
        boolean updateByParam(TaskNotifyConfigEditParam taskNotifyConfigEditParam);

        public TaskNotifyConfig getDefault();
}