package cn.getech.ehm.task.dto.task.info;

import cn.getech.poros.framework.common.param.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 维护工单主表 分页查询参数对象
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MaintTask查询", description = "维护工单主表查询参数")
public class MaintTaskQueryParam extends PageParam {

    @ApiModelProperty(value = "工单编号/名称")
    private String codeOrName;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "状态")
    private Integer[] statusArr;

    @ApiModelProperty(value = "紧急程度")
    private String urgency;

    @ApiModelProperty(value = "来源(1故障报修2维保计划)")
    private Integer sourceType;

    @ApiModelProperty(value = "来源(1故障报修2维保计划)")
    private Integer[] sourceTypes;

    @ApiModelProperty(value = "专业/工单类别")
    private String major;

    @ApiModelProperty(value = "设备编码/名称")
    private String equipmentCodeOrName;

    @ApiModelProperty(value = "设备名称")
    private String equipmentName;

    @ApiModelProperty(value = "设备编码")
    private String equipmentCode;

    @ApiModelProperty(value = "维护设备id")
    private String equipmentId;

    @ApiModelProperty(value = "设备位置Id")
    private String locationId;

    @ApiModelProperty(value = "设备位置Ids")
    private List<String> locationIds;

    @ApiModelProperty(value = "设备类型Id")
    private String categoryId;

    @ApiModelProperty(value = "设备类型Ids")
    private List<String> categoryIds;

    @ApiModelProperty(value = "维护设备多选查询ids")
    private List<String> equipmentIds;

    @ApiModelProperty(value = "设备运行状态")
    private Integer equipmentRunningStatus;

    @ApiModelProperty(value = "故障/维保内容")
    private String content;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "接单人")
    private String handler;

    @ApiModelProperty(value = "接单开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginRecTakeTime;

    @ApiModelProperty(value = "接单结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endRecTakeTime;

    @ApiModelProperty(value = "创建开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginCreateTime;

    @ApiModelProperty(value = "创建结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endCreateTime;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "更新开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date beginUpdateTime;

    @ApiModelProperty(value = "更新结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date endUpdateTime;

    @ApiModelProperty(value = "维护人员")
    private String staffName;

    @ApiModelProperty(value = "故障单/维保单id")
    private String sourceId;

    @ApiModelProperty(value = "工单类型(1故障2维保3缺陷单)", required = true)
    private Integer type;

    @ApiModelProperty(value = "工单类型(1故障2维保3缺陷单)")
    private Integer[] types;

    private List<String> overTimeType;

    @ApiModelProperty("是否点检异常工单")
    private Boolean isEcError;

    @ApiModelProperty("点检处理方式筛选")
    private Integer[] ecErrorResults;

    @ApiModelProperty("最终班组人员")
    private String[] staffId;

    @ApiModelProperty("是否超期")
    private Boolean isOverTime;

    @ApiModelProperty("是否完成")
    private Boolean isComplete;

    @ApiModelProperty("区域列表")
    private List<String> areaTypes;

    @ApiModelProperty("工序列表")
    private List<String> processTypes;

    @ApiModelProperty("我的工单")
    private Boolean ourTask;

    @ApiModelProperty("工作今日工单状态0全部1已完成2未完成")
    private Integer todayTaskStatus;

    @ApiModelProperty("维保等级")
    private Integer jobLevel;

    @ApiModelProperty("维保类型")
    private String jobType;

    @ApiModelProperty(value = "排序[属性:排序(ASC升序DESC降序)]", example = "createTime:ASC")
    private Map<String, String> sortPros;

    @ApiModelProperty("关闭原因")
    private String closeReason;

    @ApiModelProperty("损坏原因")
    private String damageReason;

    @ApiModelProperty("是否有挂起")
    private Boolean hasHangUp;
}
