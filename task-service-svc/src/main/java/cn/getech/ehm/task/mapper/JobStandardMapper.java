package cn.getech.ehm.task.mapper;

import cn.getech.ehm.task.dto.job.JobStandardDto;
import cn.getech.ehm.task.dto.job.JobStandardListDto;
import cn.getech.ehm.task.dto.job.JobStandardQueryParam;
import cn.getech.ehm.task.entity.JobStandard;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 作业标准mapper
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
@Repository
public interface JobStandardMapper extends BaseMapper<JobStandard> {

    /**
     * 分页查询
     * @param page
     * @param param
     * @return
     */
    IPage<JobStandardListDto> getPageList(Page<JobStandardQueryParam> page,
                                          @Param("param") JobStandardQueryParam param);

    /**
     * 根据id获取对象
     * @param id
     * @return
     */
    JobStandardDto getDtoById(@Param("id") String id);
}
