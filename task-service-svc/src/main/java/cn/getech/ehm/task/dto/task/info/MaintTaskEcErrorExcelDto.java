package cn.getech.ehm.task.dto.task.info;

import cn.getech.poros.framework.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * MaintTaskOvertimeExcelDto
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "MaintTaskEcErrorExcelDto", description = "MaintTaskEcErrorExcelDto")
public class MaintTaskEcErrorExcelDto {

    @ApiModelProperty(value = "序号")
    @Excel(name = "序号", cellType = Excel.ColumnType.STRING)
    private String taskNo;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @Excel(name = "结束时间", cellType = Excel.ColumnType.STRING)
    private String updateTimeStr;

    @ApiModelProperty(value = "工单名称/编号")
    @Excel(name = "工单名称/编号", cellType = Excel.ColumnType.STRING)
    private String nameStr;

    @ApiModelProperty("处理人")
    @Excel(name = "处理人", cellType = Excel.ColumnType.STRING)
    private String handlerName;

    @ApiModelProperty(value = "设备名称")
    @Excel(name = "设备名称", cellType = Excel.ColumnType.STRING)
    private String equipmentName;

    @ApiModelProperty(value = "设备编码")
    @Excel(name = "设备编码", cellType = Excel.ColumnType.STRING)
    private String equipmentCode;

    @ApiModelProperty(value = "设备类型")
    @Excel(name = "设备类型", cellType = Excel.ColumnType.STRING)
    private String equipmentCategoryName;

    @ApiModelProperty(value = "异常处理")
    @Excel(name = "异常处理", cellType = Excel.ColumnType.STRING)
    private String ecErrorResultName;

    @ApiModelProperty(value = "作业分类")
    @Excel(name = "作业分类", cellType = Excel.ColumnType.STRING)
    private String maintTaskItemCategoryStr;

    @Excel(name = "作业内容", cellType = Excel.ColumnType.STRING)
    private String maintTaskItemContent;

    @Excel(name = "基准目标", cellType = Excel.ColumnType.STRING)
    private String benchmark;

    @Excel(name = "区间", cellType = Excel.ColumnType.STRING)
    private String targetStr;

    @Excel(name = "作业结果", cellType = Excel.ColumnType.STRING)
    private String result;

    @ApiModelProperty("描述")
    @Excel(name = "描述", cellType = Excel.ColumnType.STRING)
    private String description;
}