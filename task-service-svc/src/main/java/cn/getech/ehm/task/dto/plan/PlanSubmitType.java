package cn.getech.ehm.task.dto.plan;

import lombok.Getter;
import lombok.Setter;

/**
 * 维保提交任务类型
 * <AUTHOR>
 * @date 2020-09-01 20:17:42
 **/
public enum PlanSubmitType {

    /**
     * 发布
     */
    SUBMIT(0),
    /**
     * 审核通过
     */
    CONFIRM(1),
    /**
     * 驳回
     */
    REJECT(2),
    /**
     * 驳回再次提交
     */
    AGAIN_SUBMIT(3),
    /**
     * 作废
     */
    TRASH(-1);

    @Getter
    @Setter
    private int code;

    PlanSubmitType(int code){
        this.code = code;
    }

    public static PlanSubmitType valueOf(int code){
        for (PlanSubmitType submitType: PlanSubmitType.values()){
            if(submitType.getCode() == code){
                return submitType;
            }
        }
        return null;
    }

}
