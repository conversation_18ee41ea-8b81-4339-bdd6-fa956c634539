package cn.getech.ehm.task.service;

import cn.getech.ehm.task.dto.ParameterCategoryDto;
import cn.getech.ehm.task.dto.plan.*;
import cn.getech.ehm.task.dto.task.info.MaintTaskPlanAddDto;
import cn.getech.ehm.task.entity.MaintPlan;
import cn.getech.ehm.task.entity.MaintTask;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.service.IBaseService;

import java.util.List;
import java.util.Map;

/**
 * 维护计划 服务类
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
public interface IMaintPlanService extends IBaseService<MaintPlan> {

        /**
         * 分页查询，返回Dto
         *
         * @param maintPlanQueryParam
         * @return
         */
        PageResult<MaintPlanListDto> pageDto(MaintPlanQueryParam maintPlanQueryParam);

        /**
         * 保存
         *
         * @param addParam
         * @return 主键ID
         */
        String saveByParam(MaintPlanAddParam addParam);

        /**
         * 根据id查询，转dto
         *
         * @param id
         * @return
         */
        MaintPlanDto getDtoById(String id);

        /**
         * 更新
         *
         * @param maintPlanEditParam
         */
        boolean updateByParam(MaintPlanEditParam maintPlanEditParam);

        /**
         * 释放工单
         *
         * @param id 计划id
         * @return
         */
        boolean release(String id);

        /**
         * 获取位置、类型下设备id集合
         *
         * @param locationId
         * @param categoryId
         * @return
         */
        List<String> getEquipmentIds(String locationId, String categoryId);

        /**
         * 定时创建单
         *
         * @param synMaintPlanDto
         * @return
         */
        List<MaintTaskPlanAddDto> createTaskOrder (SynMaintPlanDto synMaintPlanDto);

        /**
         * 以上次日期创建单
         *
         * @param synMaintPlanDto
         * @return
         */
        List<MaintTaskPlanAddDto> createLastTaskOrder( SynMaintPlanDto synMaintPlanDto, List<String> equipmentIds);

        /**
         * app获取维护计划列表
         *
         * @param equipmentId
         * @return
         */
        PageResult<MaintPlanAppDto> planPageList(String equipmentId);

        /**
         * 根据id删除
         *
         * @param ids
         * @return
         */
        boolean removeByIds(String[] ids);

        /**
         * 根据字段查询数据库中存在的条数
         *
         * @param tableName
         * @param columnName
         * @param value
         * @return
         */
        Integer getCount(String tableName, String columnName, String value);

        /**
         * 维护计划流程审核提交
         *
         * @param auditParam
         * @return
         */
        Boolean submitProcessTask(PlanAuditParam auditParam);

        /**
         * 获取需要开单的计划单详情
         *
         * @return
         */
        Map<String, SynMaintPlanDto> getSynListByIds(List<String> planIds);

        /**
         * 获取详情
         *
         * @param planId
         * @return
         */
        SynMaintPlanDto getSynById(String planId);

        /**
         * 根据流程实例ID获取计划表单
         *
         * @param processInstanceId
         * @return
         */
        MaintPlanDto getMaintPlanDtoByProcessInstanceId(String processInstanceId);


        /**
         * 获取计划维保对象已经使用的作业标准id集合
         *
         * @param standardIds
         * @return
         */
        List<String> getUsedStandardIds(List<String> standardIds);

        PageResult<MaintPlanDto> getTodoOfPlan(MaintPlanQueryParam param);

        public void getStaffByScheduleDate(MaintTask maintTask);

        /**
         * iot参数推数后校验是否生成cbm
         * @param dto
         * @return
         */
        Boolean createCbmOrder(ParameterCategoryDto dto);

        Boolean markEnabled(PlanEnableEditParam param);

        public void markDeletedBatch(String id);

        /**
         * 构造维护班组人员
         * @param dto
         * @param addDto
         * @return
         */
        void buildTeamPerson(TriggerTimeDto dto, MaintTaskPlanAddDto addDto);

        /**
         * 工作台-维保计划预警
         * @param type
         * @return
         */
        String getStatisticsOfPlanEndCount(String type);
}