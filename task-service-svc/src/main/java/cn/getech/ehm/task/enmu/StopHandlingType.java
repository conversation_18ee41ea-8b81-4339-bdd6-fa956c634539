package cn.getech.ehm.task.enmu;

import cn.getech.ehm.common.dto.EnumListDto;
import java.util.ArrayList;
import java.util.List;

/**
 * 设备停机处理方式枚举
 * <AUTHOR>
 * @date 2020/3/3
 */
public enum StopHandlingType {
    NORMAL(1, "正常释放工单"),
    NOT_ADD_TASK(2, "不释放工单");


    StopHandlingType(int value, String name) {
        this.value = value;
        this.name = name;
    }

    private int value;

    private String name;

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static List<EnumListDto> getList(){
        List<EnumListDto> enumListDtos = new ArrayList<>();
        for(StopHandlingType stopHandlingType : StopHandlingType.values()){
            EnumListDto enumListDto = new EnumListDto();
            enumListDto.setValue(stopHandlingType.value);
            enumListDto.setName(stopHandlingType.name);
            enumListDtos.add(enumListDto);
        }
        return enumListDtos;
    }
}

