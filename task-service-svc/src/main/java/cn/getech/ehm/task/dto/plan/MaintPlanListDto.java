package cn.getech.ehm.task.dto.plan;

import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.common.mp.handler.StringArrayTypeHandler;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

/**
 * 维护计划列表
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "MaintPlanListDto", description = "维护计划列表返回数据模型")
public class MaintPlanListDto {

    @ApiModelProperty(value = "计划id")
    private String id;

    @ApiModelProperty(value = "计划名称")
    private String name;

    @ApiModelProperty(value = "维保类别")
    private String jobType;

    @ApiModelProperty(value = "紧急程度/优先程度")
    private String urgency;

    @ApiModelProperty(value = "专业")
    private String major;

    @ApiModelProperty(value = "工单数量")
    private Integer taskCount = StaticValue.ZERO;

    @ApiModelProperty(value = "0 待发布 1 已发布 2 发布审批中 3 发布驳回")
    private Integer status;

    @ApiModelProperty(value = "流程实例id")
    private String processInstanceId;

    @ApiModelProperty(value = "工作流任务id")
    private String activityId;

    @ApiModelProperty(value = "作业标准id")
    private String standardId;

    @ApiModelProperty(value = "作业标准名称")
    private String standardName;

    @ApiModelProperty(value = "上次维保时间")
    private Date lastMaintTime;

    @ApiModelProperty(value = "下次维保时间")
    private Date nextMaintTime;

    @ApiModelProperty(value = "是否故障单自动转换")
    private Boolean repairAuto;

    //截止天数
    private Integer deadlineDays;

    @ApiModelProperty("使用状态0-已停用,1-已启用")
    private Integer enabled;

    @ApiModelProperty("设备类型-权限划分用")
    private String equipType;

    @ApiModelProperty("设备类型-权限划分用")
    private String equipTypeName;

}