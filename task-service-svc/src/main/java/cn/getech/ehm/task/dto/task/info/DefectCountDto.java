package cn.getech.ehm.task.dto.task.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 工单统计
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "DefectCountDto", description = "缺陷单app数量统计")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DefectCountDto {

    @ApiModelProperty(value = "唯一标识")
    private String uniqueMark;

    @ApiModelProperty(value = "图标名称")
    private String iconName;

    @ApiModelProperty(value = "图标数量")
    private Integer iconCount;
}