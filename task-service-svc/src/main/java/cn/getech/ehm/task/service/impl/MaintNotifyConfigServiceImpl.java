package cn.getech.ehm.task.service.impl;

import cn.getech.ehm.task.entity.MaintNotifyConfig;
import cn.getech.ehm.task.mapper.MaintNotifyConfigMapper;
import cn.getech.ehm.task.service.IMaintNotifyConfigService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.ehm.task.dto.MaintNotifyConfigQueryParam;
import cn.getech.ehm.task.dto.MaintNotifyConfigAddParam;
import cn.getech.ehm.task.dto.MaintNotifyConfigEditParam;
import cn.getech.ehm.task.dto.MaintNotifyConfigParamMapper;
import cn.getech.ehm.task.dto.MaintNotifyConfigDto;
import cn.getech.poros.framework.common.bean.BaseEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.ResultCode;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import java.util.List;
import cn.hutool.core.util.ArrayUtil;



/**
 * <pre>
 *  服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2025-02-10
 */
@Slf4j
@Service
public class MaintNotifyConfigServiceImpl extends BaseServiceImpl<MaintNotifyConfigMapper, MaintNotifyConfig> implements IMaintNotifyConfigService {

    @Autowired
    private MaintNotifyConfigParamMapper maintNotifyConfigParamMapper;

    @Override
    public PageResult<MaintNotifyConfigDto> pageDto(MaintNotifyConfigQueryParam maintNotifyConfigQueryParam) {
        Wrapper<MaintNotifyConfig> wrapper = getPageSearchWrapper(maintNotifyConfigQueryParam);
        PageResult<MaintNotifyConfigDto> result = maintNotifyConfigParamMapper.pageEntity2Dto(page(maintNotifyConfigQueryParam, wrapper));

        return Optional.ofNullable(result).orElse(new PageResult<>());
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveByParam(MaintNotifyConfigAddParam maintNotifyConfigAddParam) {
        MaintNotifyConfig maintNotifyConfig = maintNotifyConfigParamMapper.addParam2Entity(maintNotifyConfigAddParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,maintNotifyConfig);
        return save(maintNotifyConfig);
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean updateByParam(MaintNotifyConfigEditParam maintNotifyConfigEditParam) {
        MaintNotifyConfig maintNotifyConfig = maintNotifyConfigParamMapper.editParam2Entity(maintNotifyConfigEditParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,maintNotifyConfig);
        return updateById(maintNotifyConfig);
    }


    @Override
    public MaintNotifyConfigDto getDtoById(Long id) {
        return maintNotifyConfigParamMapper.entity2Dto((MaintNotifyConfig) this.getById(id));
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveDtoBatch(List<MaintNotifyConfigDto> rows) {
        return saveBatch(maintNotifyConfigParamMapper.dtoList2Entity(rows));
    }

    private Wrapper<MaintNotifyConfig> getPageSearchWrapper(MaintNotifyConfigQueryParam maintNotifyConfigQueryParam) {
        LambdaQueryWrapper<MaintNotifyConfig> wrapper = Wrappers.<MaintNotifyConfig>lambdaQuery();
        wrapper.like(StringUtils.isNotBlank(maintNotifyConfigQueryParam.getId()),
                     MaintNotifyConfig::getId, maintNotifyConfigQueryParam.getId());
        if(BaseEntity.class.isAssignableFrom(MaintNotifyConfig.class)){
            wrapper.orderByDesc(MaintNotifyConfig::getUpdateTime,MaintNotifyConfig::getCreateTime);
        }
        return wrapper;
    }
}
