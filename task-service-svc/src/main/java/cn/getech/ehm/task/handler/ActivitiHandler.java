package cn.getech.ehm.task.handler;

import cn.getech.ehm.common.context.UserContext;
import cn.getech.ehm.common.context.UserContextHolder;
import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.task.dto.activiti.AuditActivitiServiceResult;
import cn.getech.ehm.task.dto.activiti.PartTaskAuditParam;
import cn.getech.poros.bpm.client.ProcessServiceClient;
import cn.getech.poros.bpm.client.TaskServiceClient;
import cn.getech.poros.bpm.dto.BaseDto;
import cn.getech.poros.bpm.dto.task.ProcessTaskDTO;
import cn.getech.poros.bpm.dto.task.TaskDto;
import cn.getech.poros.bpm.param.UserParam;
import cn.getech.poros.bpm.param.task.ProcessTaskParam;
import cn.getech.poros.bpm.param.task.TaskCompleteParam;
import cn.getech.poros.bpm.param.task.TaskOptionParam;
import cn.getech.poros.bpm.param.task.TaskRejectParam;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.framework.common.context.PorosContextHolder;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Service
@Slf4j
@RefreshScope
public class ActivitiHandler {
    @Autowired
    private TaskServiceClient taskServiceClient;
    @Autowired
    private ProcessServiceClient processServiceClient;
    @Autowired
    private ResourceBundleMessageSource messageSource;

    @Value("${heroin.env.isdev:false}")
    public Boolean isDev;

    public final static String PROC_DEF_KEY_PLAN = "E0005";

    public final static String PROC_DEF_KEY_TASK = "E0004";

    @Transactional
    public void submitActiviti(TaskCompleteParam taskCompleteParam, String taskId) {
        RestResponse<Object> completeTask = taskServiceClient.completeTask(taskCompleteParam);
        log.info("流程节点完成任务提交参数{}响应{}", JSON.toJSONString(taskCompleteParam), JSON.toJSONString(completeTask));
        if (!completeTask.isOk() && !isDev) {
            log.error("流程引擎调用失败,taskId:{},error:{} ", taskId, completeTask.getMsg());
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("process_error", null, LocaleContextHolder.getLocale())));
        }
    }


    @Transactional
    public AuditActivitiServiceResult submitActivitiPass(PartTaskAuditParam taskAuditParam) {
        AuditActivitiServiceResult auditActivitiServiceResult = new AuditActivitiServiceResult();
        // 同意
        // 发起任务完成流程
        TaskCompleteParam taskCompleteParam = new TaskCompleteParam();
        taskCompleteParam.setTaskId(taskAuditParam.getActivityId());
        Map variables = Maps.newHashMap();
        variables.putAll(taskAuditParam.getVariables() != null ? taskAuditParam.getVariables() : Maps.newHashMap());
        variables.put("result", true);
        taskCompleteParam.setVariables(variables);
        taskCompleteParam.setComment(taskAuditParam.getComment());
        RestResponse<Object> completeTask = taskServiceClient.completeTask(taskCompleteParam);
        log.info("流程节点完成任务提交参数{}响应{}", JSON.toJSONString(taskCompleteParam), JSON.toJSONString(completeTask));
        if (!completeTask.isOk() && !isDev) {
            log.error("流程引擎调用失败, " + completeTask.getMsg());
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("process_error", null, LocaleContextHolder.getLocale())));
        }
        auditActivitiServiceResult = this.getProcessTaskUserId(taskAuditParam.getProcessInstanceId());
        return auditActivitiServiceResult;
    }

    @Transactional
    public AuditActivitiServiceResult submitActivitiFail(PartTaskAuditParam taskAuditParam) {
        AuditActivitiServiceResult auditActivitiServiceResult = new AuditActivitiServiceResult();
        // 驳回
        // 发起任务完成流程
        TaskCompleteParam taskCompleteParam = new TaskCompleteParam();
        taskCompleteParam.setTaskId(taskAuditParam.getActivityId());
        Map variables = Maps.newHashMap();
        variables.putAll(taskAuditParam.getVariables() != null ? taskAuditParam.getVariables() : Maps.newHashMap());
        variables.put("result", false);
        taskCompleteParam.setVariables(variables);
        taskCompleteParam.setComment(taskAuditParam.getComment());
        RestResponse<Object> completeTask = taskServiceClient.completeTask(taskCompleteParam);
        log.info("流程节点完成任务提交参数{}响应{}", JSON.toJSONString(taskCompleteParam), JSON.toJSONString(completeTask));
        if (!completeTask.isOk() && !isDev) {
            log.error("流程引擎调用失败, " + completeTask.getMsg());
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("process_error", null, LocaleContextHolder.getLocale())));
        }
        auditActivitiServiceResult = this.getProcessTaskUserId(taskAuditParam.getProcessInstanceId());
        return auditActivitiServiceResult;
    }

    @Transactional
    public AuditActivitiServiceResult rejectActiviti(PartTaskAuditParam taskAuditParam) {
        AuditActivitiServiceResult auditActivitiServiceResult = new AuditActivitiServiceResult();
        // 驳回
        // 发起任务驳回流程
        TaskRejectParam taskRejectParam = new TaskRejectParam();
        taskRejectParam.setTaskId(taskAuditParam.getActivityId());
        taskRejectParam.setVariables(taskAuditParam.getVariables());
        taskRejectParam.setComment(taskAuditParam.getComment());
        RestResponse<Object> rejectTask = taskServiceClient.rejectTask(taskRejectParam);
        log.info("流程节点驳回任务提交参数{}响应{}", JSON.toJSONString(taskRejectParam), JSON.toJSONString(rejectTask));
        if (!rejectTask.isOk() && !isDev) {
            log.error("流程引擎调用失败, " + rejectTask.getMsg());
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("process_error", null, LocaleContextHolder.getLocale())));
        }
        auditActivitiServiceResult = this.getProcessTaskUserId(taskAuditParam.getProcessInstanceId());
        return auditActivitiServiceResult;
    }

//    public StartActivitiServiceResult startProcess(Integer opType, String serviceType, String other) {
//        StartActivitiServiceResult startActivitiServiceResult = new StartActivitiServiceResult();
//        ProcessStartParam processStartParam = new ProcessStartParam();
//        processStartParam.setProcessDefinitionKey(ActivitiServiceConfig.chooseProcessDefKey("" + opType, "" + serviceType));
//        processStartParam.setProcessInstanceName(
//                StrUtil.join(StringPool.COLON, ActivitiServiceConfig.chooseProcessDefName("" + opType, "" + serviceType), other));
//        processStartParam.setUid(PorosContextHolder.getCurrentUser().getUid());
//        processStartParam.setLevel(StaticValue.ONE);
//        Map setAssigners = Maps.newHashMap();
//        setAssigners.put("task1-assigners", "" + PorosContextHolder.getCurrentUser().getUid());
//        processStartParam.setSetAssignees(setAssigners);
//        RestResponse<String> restResponse = processServiceClient.startProcess(processStartParam);
//        log.info("流程引擎请求参数:{}, 返回参数:{}", JSONUtil.toJsonStr(processStartParam), JSONUtil.toJsonStr(restResponse));
//        if (restResponse.isOk() && !isDev) {
//            String processInstanceId = restResponse.getData();
//            startActivitiServiceResult.setProcessInstanceId(processInstanceId);
//            AuditActivitiServiceResult auditActivitiServiceResult = this.getProcessTaskUserId(processInstanceId);
//            startActivitiServiceResult.setProcessUser(auditActivitiServiceResult.getProcessUser());
//            startActivitiServiceResult.setTaskId(auditActivitiServiceResult.getTaskId());
//            return startActivitiServiceResult;
//        } else if (isDev) {
//            log.info("当前为测试结果，实际不调用工作流引擎");
//            startActivitiServiceResult.setProcessInstanceId("" + other);
//            startActivitiServiceResult.setProcessUser(PorosContextHolder.getCurrentUser().getUid());
//            return startActivitiServiceResult;
//        } else {
//            log.error("流程引擎调用失败, " + restResponse.getMsg());
//            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("process_error", null, LocaleContextHolder.getLocale())));
//        }
//    }

    public Boolean delete(String taskId) {
        if (StringUtils.isEmpty(taskId)) {
            log.error("删除工作流失败：id为空");
            return false;
        }
        TaskOptionParam taskOptionParam = new TaskOptionParam();
        taskOptionParam.setTaskId(taskId);
        taskOptionParam.setComment("删除任务");
        RestResponse<Object> objectRestResponse = taskServiceClient.trashTask(taskOptionParam);
        if (objectRestResponse.isOk()) {
            log.info("删除工作流成功：{}", JSON.toJSONString(objectRestResponse.getData()));
            return true;
        } else {
            log.error("删除工作流失败：{}", JSON.toJSONString(objectRestResponse));
            return false;
        }
    }

    public void deleteByProcessId(String processId, UserBaseInfo userBaseInfo) {
        UserContextHolder.defaultContext();
        ;
        UserContextHolder.switchContext(userBaseInfo);
        this.deleteByProcessId(processId);
    }

    public void deleteByProcessId(String processId) {
        AuditActivitiServiceResult processTaskUserId = this.getProcessTaskUserId(processId);
        String taskId = "";
        if (processTaskUserId != null && StringUtils.isNotEmpty(processTaskUserId.getTaskId())) {
            taskId = processTaskUserId.getTaskId();
        }
        if (StringUtils.isEmpty(taskId)) {
            log.error("删除工作流失败：taskId为空");
            return;
        }
        TaskOptionParam taskOptionParam = new TaskOptionParam();
        taskOptionParam.setTaskId(taskId);
        taskOptionParam.setComment("删除任务");
        RestResponse<Object> objectRestResponse = taskServiceClient.trashTask(taskOptionParam);
        if (objectRestResponse.isOk()) {
            log.info("删除工作流成功：{}", JSON.toJSONString(objectRestResponse.getData()));
        } else {
            log.error("删除工作流失败：{}", JSON.toJSONString(objectRestResponse));
        }
    }

    public void deleteByProcessIdManage(String processId) {
        UserContextHolder.defaultContext();
        if (StringUtils.isEmpty(processId)) {
            return;
        }
        //RestResponse<Boolean> booleanRestResponse = processServiceClient.trashProcess(processId);
        //log.info("result:{}", booleanRestResponse.getData());
    }

    public AuditActivitiServiceResult getProcessTaskUserId(String processInstanceId) {
        ProcessTaskParam processTaskParam = new ProcessTaskParam();
        processTaskParam.setProcessInstanceId(processInstanceId);
        RestResponse<List<ProcessTaskDTO>> response = taskServiceClient.getUserTaskByProcessId(processTaskParam);
        if (!response.isOk() && !isDev) {
            log.error("流程引擎调用失败, " + response.getMsg());
            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("process_error", null, LocaleContextHolder.getLocale())));
        }
        //log.info("审批人列表：{}, 当前用户：{}", response.getData(), PorosContextHolder.getCurrentUser().getUid());
        //请求是成功的
        if (response.isOk() && !isDev) {
            if (CollectionUtils.isNotEmpty(response.getData())) {
                List<String> candidateUids = response.getData().get(0).getCandidateUids();
                return AuditActivitiServiceResult.builder()
                        .processUser(JSONUtil.toJsonStr(response.getData().get(0).getCandidateUids()))
                        .processUserArray(candidateUids.toArray(new String[candidateUids.size()]))
                        .taskId(response.getData().get(0).getTaskId())
                        .taskName(response.getData().get(0).getActivityName())
                        .build();
            } else {
                //没有后续任务
                return AuditActivitiServiceResult.builder()
                        .processUser("")
                        .taskId("")
                        .build();
            }
        } else if (isDev) {
            log.info("当前为测试结果，实际不调用工作流引擎");
            return AuditActivitiServiceResult.builder()
                    .processUser(PorosContextHolder.getCurrentUser().getUid())
                    .taskId("test-task-id")
                    .build();


        } else {
            throw new GlobalServiceException(GlobalResultMessage.of("获取下一节点审批人失败,请尝试重新审批"));
        }
    }

    public BaseDto<TaskDto> getTodoList(IPage page, String procDefKey) {
        UserParam userParam = new UserParam();
        userParam.setUid(PorosContextHolder.getCurrentUser().getUid());
        userParam.setPageNo(Integer.valueOf("" + page.getCurrent()));
        userParam.setLimit(Integer.valueOf("" + page.getSize()));
        userParam.setProcessStatus(2);
        userParam.setDefinitionKey(procDefKey);
        log.info("获取待办列表，参数：{}", JSON.toJSONString(userParam));
        RestResponse<BaseDto<TaskDto>> assigneeList = taskServiceClient.getAssigneeList(userParam);
        log.info("获取待办列表，结果：{}", JSON.toJSONString(assigneeList));
        if (assigneeList.isOk()) {
            return assigneeList.getData();
        } else {
            return new BaseDto<TaskDto>();
        }
    }

//    public String getProcessTaskId(String processInstanceId) {
//        if (StringUtils.isBlank(processInstanceId)) {
//            //log.info("未找到流程实例id");
//            return null;
//        }
//        UserBaseInfo user = PorosContextHolder.getCurrentUser();
//        if (null == user) {
//            throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("user_error", null, LocaleContextHolder.getLocale())));
//        }
//        String uid = user.getUid();
//        ProcessTaskParam processTaskParam = new ProcessTaskParam();
//        processTaskParam.setProcessInstanceId(processInstanceId);
//        log.info("流程实例id:" + processInstanceId + ",uid:" + uid);
//        RestResponse<List<ProcessTaskDTO>> restResponse = taskServiceClient.getUserTaskByProcessId(processTaskParam);
//        log.info("流程引擎返回值为：" + JSON.toJSONString(restResponse));
//        if (restResponse.isOk()) {
//            List<ProcessTaskDTO> dtos = restResponse.getData();
//            if (CollectionUtils.isNotEmpty(dtos)) {
//                //目前直接取第一个就行，后期有并行task，在处理列表
//                ProcessTaskDTO processTaskIdDto = dtos.get(0);
//                List<String> candidateUids = processTaskIdDto.getCandidateUids();
//                //如果当前工作流执行人包含当前登录用户，返回任务id
//                if (CollectionUtils.isNotEmpty(candidateUids) && candidateUids.contains(uid)) {
//                    return processTaskIdDto.getTaskId();
//                }
//            }
//        } else {
//            log.error("当前用户在当前工单没有处理事项");
//            //throw new GlobalServiceException(GlobalResultMessage.of(messageSource.getMessage("find_info_error", null, LocaleContextHolder.getLocale())));
//        }
//        return null;
//    }

    public static void dealContext(String currentUid, String tenantId) {
        UserContextHolder.defaultContext();
        UserContext context = UserContextHolder.getContext();
        UserBaseInfo userBaseInfo = context.getUserBaseInfo();
        userBaseInfo.setUid(currentUid);
        userBaseInfo.setTenantId(tenantId);
        UserContextHolder.switchContext(userBaseInfo);
    }
}
