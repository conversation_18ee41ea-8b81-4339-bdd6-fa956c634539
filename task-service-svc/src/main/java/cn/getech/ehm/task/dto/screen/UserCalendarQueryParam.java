package cn.getech.ehm.task.dto.screen;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2022-07-07
 */
@Data
@ApiModel("人员视角工作日历查询参数")
public class UserCalendarQueryParam {
    @ApiModelProperty("用户名")
    private String keyword;

    @ApiModelProperty("app用户名")
    private String userName;

    @ApiModelProperty("是否是自己")
    private Boolean isMyHandler;

    @ApiModelProperty("月份")
    @JsonFormat(pattern = "yyyy-MM", timezone = "GMT+8")
    @NotNull
    public Date searchDate;

    @ApiModelProperty(value = "维护设备位置ids")
    private List<String> equipmentLocationIds;

}
