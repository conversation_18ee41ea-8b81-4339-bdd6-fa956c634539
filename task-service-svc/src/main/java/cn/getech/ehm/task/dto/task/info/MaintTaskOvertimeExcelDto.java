package cn.getech.ehm.task.dto.task.info;

import cn.getech.poros.framework.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * MaintTaskOvertimeExcelDto
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "MaintTaskOvertimeExcelDto", description = "MaintTaskOvertimeExcelDto")
public class MaintTaskOvertimeExcelDto {
    @ApiModelProperty(value = "序号")
    @Excel(name = "序号", cellType = Excel.ColumnType.STRING)
    private String taskNo;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @Excel(name = "更新时间", cellType = Excel.ColumnType.STRING)
    private String updateTimeStr;

    @ApiModelProperty(value = "工单名称/编号")
    @Excel(name = "工单名称/编号", cellType = Excel.ColumnType.STRING)
    private String nameStr;

    @ApiModelProperty(value = "工单状态")
    @Excel(name = "工单状态", cellType = Excel.ColumnType.STRING)
    private String statusName;

    @ApiModelProperty(value = "工单类型")
    @Excel(name = "工单类型", cellType = Excel.ColumnType.STRING)
    private String typeName;

    @ApiModelProperty("处理人")
    @Excel(name = "处理人", cellType = Excel.ColumnType.STRING)
    private String handlerName;

    @ApiModelProperty(value = "设备名称")
    @Excel(name = "设备名称", cellType = Excel.ColumnType.STRING)
    private String equipmentName;

    @ApiModelProperty(value = "设备编码")
    @Excel(name = "设备编码", cellType = Excel.ColumnType.STRING)
    private String equipmentCode;

    @ApiModelProperty(value = "设备类型")
    @Excel(name = "设备类型", cellType = Excel.ColumnType.STRING)
    private String equipmentCategoryName;

    @ApiModelProperty(value = "超时内容")
    @Excel(name = "超时内容", cellType = Excel.ColumnType.STRING)
    private String overTimeContent;
}