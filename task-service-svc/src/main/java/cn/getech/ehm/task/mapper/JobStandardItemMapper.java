package cn.getech.ehm.task.mapper;

import cn.getech.ehm.task.dto.job.ItemCountDto;
import cn.getech.ehm.task.entity.JobStandardItem;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

/**
 * 作业项目mapper
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
@Repository
public interface JobStandardItemMapper extends BaseMapper<JobStandardItem> {

    /**
     * 获取作业标准时间统计
     * @param standardIds
     * @return
     */
    List<ItemCountDto> getCountList(@Param("standardIds") List<String> standardIds);
}
