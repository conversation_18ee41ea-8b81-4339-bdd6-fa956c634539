package cn.getech.ehm.task.dto.plan;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 维护计划启用开关参数
 *
 * <AUTHOR>
 * @since  2022-09-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "维护计划启用开关参数", description = "维护计划启用开关参数")
public class PlanEnableEditParam extends ApiParam {
    @ApiModelProperty("id集合")
    @NotEmpty(message = "id不能为空")
    private List<String> idList;

    @ApiModelProperty("0-停用, 1-启用")
    @NotNull(message = "操作不能为空")
    private Integer enabled;
}
