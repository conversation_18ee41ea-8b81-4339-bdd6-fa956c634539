package cn.getech.ehm.task.service.feign;

import cn.getech.ehm.task.dto.task.history.MaintTaskHistoryDto;
import cn.getech.ehm.task.entity.PermissionMenu;
import cn.getech.poros.framework.common.api.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-08-21 16:40:20
 **/
@FeignClient(name = "poros-bpmengine-v2", path = "/api/poros-bpmengine-v2")
public interface LocalTaskClient {

    @GetMapping(value = "/task/getTaskRecordList")
    RestResponse<List<MaintTaskHistoryDto>> getTaskRecordList(@RequestParam("processInstanceId") String processInstanceId);

}
