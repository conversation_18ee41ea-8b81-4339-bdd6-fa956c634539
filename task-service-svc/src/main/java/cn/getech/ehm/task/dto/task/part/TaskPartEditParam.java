package cn.getech.ehm.task.dto.task.part;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;


/**
 * 工单备件修改
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "TaskPartEditParam", description = "工单备件修改")
public class TaskPartEditParam extends ApiParam {

    @ApiModelProperty(value = "工单id")
    private String taskId;

    @ApiModelProperty(value = "备件集合")
    List<TaskPartEditDto> parts;
}