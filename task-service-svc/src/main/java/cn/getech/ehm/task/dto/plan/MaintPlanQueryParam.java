package cn.getech.ehm.task.dto.plan;

import cn.getech.poros.framework.common.param.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 维护计划 分页查询参数对象
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MaintPlan查询", description = "维护计划查询参数")
public class MaintPlanQueryParam extends PageParam {

    @ApiModelProperty(value = "计划名称")
    private String name;

    @ApiModelProperty(value = "维保类别")
    private String jobType;

    @ApiModelProperty(value = "紧急程度/优先程度")
    private String urgency;

    @ApiModelProperty(value = "专业")
    private String major;

    @ApiModelProperty(value = "0 待发布 1 已发布 2 发布审批中 3 发布驳回")
    private Integer status;

    @ApiModelProperty(value = "排序[属性:排序(ASC升序DESC降序)]", example = "createTime:ASC")
    private Map<String, String> sortPros;

    @ApiModelProperty(value = "排序sql", hidden = true)
    private String sortValue;

    @ApiModelProperty(value = "设备ID")
    private String equipmentId;

    @ApiModelProperty(value = "作业标准ID")
    private String standardId;

    @ApiModelProperty(value = "创建开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginCreateTime;

    @ApiModelProperty(value = "创建结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endCreateTime;

    @ApiModelProperty(value = "释放工单开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginTriggerTime;

    @ApiModelProperty(value = "释放工单结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTriggerTime;

    @ApiModelProperty(value = "运行开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginPlanMaintTime;

    @ApiModelProperty(value = "运行结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endPlanMaintTime;

    public List<String> planIds;

    @ApiModelProperty("使用状态")
    private Integer enabled;

    private List<String> uidsOfDept;

    @ApiModelProperty(value = "设备类型")
    private String equipType;
}
