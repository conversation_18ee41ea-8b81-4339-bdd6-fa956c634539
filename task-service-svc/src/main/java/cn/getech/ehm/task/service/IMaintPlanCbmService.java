package cn.getech.ehm.task.service;

import cn.getech.ehm.task.dto.plan.CbmTriggerMainDto;
import cn.getech.ehm.task.entity.MaintPlanCbm;
import cn.getech.poros.framework.common.service.IBaseService;

import java.util.List;
import java.util.Map;

/**
 * cbm计划详情 服务类
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
public interface IMaintPlanCbmService extends IBaseService<MaintPlanCbm> {

    /**
     * 更新cbm触发器
     * @param cbmTriggerMainDtos
     * @param planId
     * @return
     */
    Boolean saveByParam(List<CbmTriggerMainDto> cbmTriggerMainDtos, String planId, Boolean add);

    /**
     * 根据计划单获取cbm单触发器详情
     * @param planId
     * @return
     */
    List<CbmTriggerMainDto> getCbmDetail(String planId);

    /**
     * 满足触发器的计划单
     * @param planIds
     * @return
     */
    List<String> satisfyTriggerPlanId(List<String> planIds, String categoryParamId, Map<String, Double> paramValueMap);
}