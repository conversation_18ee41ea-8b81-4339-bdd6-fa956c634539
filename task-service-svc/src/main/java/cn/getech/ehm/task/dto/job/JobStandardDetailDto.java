package cn.getech.ehm.task.dto.job;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 作业标准列表
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "JobStandardListDto", description = "作业标准列表")
public class JobStandardDetailDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "描述")
    private String remark;
}