package cn.getech.ehm.task.entity;

import cn.getech.poros.framework.common.bean.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 作业票
 *
 * <AUTHOR>
 * @since 2020-08-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("job_ticket")
public class JobTicket extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 是否安全确认
     */
    @TableField("confirm")
    private Boolean confirm;

    /**
     * 是否显示工况/风险信息
     */
    @TableField(value = "show_information")
    private Boolean showInformation;

    /**
     * 是否显示作业票类型
     */
    @TableField(value = "show_ticket_type")
    private Boolean showTicketType;

    /**
     * 是否开启故障单/缺陷单派单人工审批
     */
    @TableField(value = "repair_task_audit")
    private Boolean repairTaskAudit;

    /**
     * 是否删除(0false1true)
     */
    @TableField("deleted")
    private Integer deleted;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;
}
