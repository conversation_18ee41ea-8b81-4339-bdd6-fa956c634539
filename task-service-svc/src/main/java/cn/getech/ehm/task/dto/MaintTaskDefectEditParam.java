package cn.getech.ehm.task.dto;

import cn.getech.poros.framework.common.param.ApiParam;
import cn.getech.poros.framework.common.param.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

/**
 * <pre>
 * 缺陷记录 编辑参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2025-03-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MaintTaskDefect编辑", description = "缺陷记录编辑参数")
public class MaintTaskDefectEditParam extends ApiParam {

    @ApiModelProperty(value = "")
    private Integer deleted;

    @ApiModelProperty(value = "")
    private String tenantId;

    @ApiModelProperty(value = "设备id")
    private String equipmentId;

    @ApiModelProperty(value = "缺陷名称")
    private String defectName;

    @ApiModelProperty(value = "缺陷内容")
    private String defectContent;

    @ApiModelProperty(value = "影响描述")
    private String affectContent;

    @ApiModelProperty(value = "缺陷种类")
    private Integer defectType;

    @ApiModelProperty(value = "专业类别")
    private Integer majorType;

    @ApiModelProperty(value = "现场图片/视频")
    private String[] liveMediaIds;

    @ApiModelProperty(value = "缺陷状态")
    private Integer defectStatus;

    @ApiModelProperty(value = "验收状态")
    private Integer checkStatus;

    @ApiModelProperty(value = "截止日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "处理人ids")
    private String dealPersonIds;

    @ApiModelProperty(value = "建议处理方案")
    private String suggestDealContent;

    @ApiModelProperty(value = "工单id")
    private String maintTaskId;

    @ApiModelProperty(value = "工单code")
    private String maintTaskCode;

    @ApiModelProperty(value = "验收说明")
    private String checkExplain;

    @ApiModelProperty(value = "实际处理方案")
    private String realDealContent;

    @ApiModelProperty(value = "关闭原因")
    private String closeReason;

    @ApiModelProperty(value = "处理时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date dealDate;

    @ApiModelProperty(value = "关闭时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date closeDate;

    @ApiModelProperty(value = "来源工单id")
    private String sourceTaskId;

    @ApiModelProperty(value = "缺陷原因")
    private String defectReason;

    @ApiModelProperty(value = "来源工单编码")
    private String sourceTaskCode;

    @ApiModelProperty(value = "驳回原因")
    private String rejectReason;

}
