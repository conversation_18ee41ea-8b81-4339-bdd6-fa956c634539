package cn.getech.ehm.task.dto.task.ticket;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * 工单作业票
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "TaskTicketDto", description = "工单作业票")
public class TaskTicketDto {

    @ApiModelProperty(value = "工单id")
    private String taskId;

    @ApiModelProperty(value = "是否显示工况/风险信息")
    private Boolean showInformation;

    @ApiModelProperty(value = "工况/风险信息")
    private String ticketInformation;

    @ApiModelProperty(value = "是否显示作业票类型")
    private Boolean showTicketType;

    @ApiModelProperty(value = "已选择作业票类型")
    private String[] ticketTypes;

    /**
     * 编辑的情况下是人员uid/name的格式
     */
    @ApiModelProperty(value = "审核人员")
    private String[] reviewer;

    @ApiModelProperty(value = "已审核人员")
    private String[] alreadyReviewer;

    @ApiModelProperty(value = "是否能审核")
    private Boolean canAudit = false;

    @ApiModelProperty(value = "工作票附件")
    private String[] ticketMediaIds;

    @ApiModelProperty(value = "作业票内容集合")
    private List<TaskTicketItemDto> itemDtos;
}