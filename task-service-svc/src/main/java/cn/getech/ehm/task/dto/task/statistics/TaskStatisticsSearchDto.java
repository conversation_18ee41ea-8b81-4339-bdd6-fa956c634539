package cn.getech.ehm.task.dto.task.statistics;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;


/**
 * 维护工单主表 返回数据模型
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@ApiModel(value = "TaskStatisticsSearchDto", description = "TaskStatisticsSearchDto")
public class TaskStatisticsSearchDto {

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "维护设备位置ids")
    private List<String> equipmentLocationIds;

    @ApiModelProperty(value = "维护班组id")
    private String[] teamIds;

    private String targetValue;

    @ApiModelProperty("状态筛选")
    private List<String> statusFilter;

    @ApiModelProperty("工序类型")
    private String processType;

    @ApiModelProperty("设备类型")
    private List<String> equipmentCategoryIds;

    @ApiModelProperty("零部件搜索类型")
    private List<Integer> structureType = Arrays.asList(1,2);

    @ApiModelProperty("工单类型")
    private Integer taskType;

    @ApiModelProperty("工单来源类型")
    private Integer sourceType;

    @ApiModelProperty("统计类型")
    private Integer analysisType;

    private Integer jobLevel;
}