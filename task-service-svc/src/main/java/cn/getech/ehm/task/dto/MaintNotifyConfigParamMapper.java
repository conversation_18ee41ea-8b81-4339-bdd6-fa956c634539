package cn.getech.ehm.task.dto;

import cn.getech.poros.framework.common.bean.BaseEntity;
import cn.getech.ehm.task.entity.MaintNotifyConfig;
import org.mapstruct.Mapper;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <pre>
 *  参数实体映射
 * </pre>
 *
 * <AUTHOR>
 * @date 2025-02-10
 */
@Mapper(componentModel = "spring",imports = {BaseEntity.class})
public interface  MaintNotifyConfigParamMapper{

    /**
     * 新增参数转换为实体
     *
     * @param maintNotifyConfigAddParam
     * @return
     */
    MaintNotifyConfig addParam2Entity(MaintNotifyConfigAddParam maintNotifyConfigAddParam);

    /**
     * 编辑参数转换为实体
     * @param maintNotifyConfigEditParam
     * @return
     */
    MaintNotifyConfig editParam2Entity(MaintNotifyConfigEditParam maintNotifyConfigEditParam);

    /**
     * 实体转换为Dto
     * @param maintNotifyConfig
     * @return
     */
    MaintNotifyConfigDto entity2Dto(MaintNotifyConfig maintNotifyConfig);

    /**
     * 分页实体转DTO
     * @param page
     * @return
     */
    PageResult<MaintNotifyConfigDto> pageEntity2Dto(PageResult<MaintNotifyConfig> page);


    /**
     * dto集合转entity集合
     * @param rows
     * @return
     */
    List<MaintNotifyConfig> dtoList2Entity(List<MaintNotifyConfigDto> rows);

}
