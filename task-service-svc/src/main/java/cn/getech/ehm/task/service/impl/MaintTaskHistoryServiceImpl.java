package cn.getech.ehm.task.service.impl;

import cn.getech.ehm.common.exception.GlobalResultMessage;
import cn.getech.ehm.common.exception.GlobalServiceException;
import cn.getech.ehm.task.dto.task.history.*;
import cn.getech.ehm.task.entity.MaintTask;
import cn.getech.ehm.task.entity.MaintTaskHistory;
import cn.getech.ehm.task.enums.TaskStatusType;
import cn.getech.ehm.task.handler.ActivitiTaskNotifyHandler;
import cn.getech.ehm.task.mapper.MaintTaskHistoryMapper;
import cn.getech.ehm.task.service.IMaintTaskHistoryService;
import cn.getech.ehm.task.service.feign.LocalTaskClient;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.api.ResultCode;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.poros.permission.client.PorosSecStaffClient;
import cn.getech.poros.permission.dto.PorosSecStaffDto;
import cn.hutool.core.lang.UUID;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * <pre>
 * 工单历史 服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2025-02-05
 */
@Slf4j
@Service
public class MaintTaskHistoryServiceImpl extends BaseServiceImpl<MaintTaskHistoryMapper, MaintTaskHistory> implements IMaintTaskHistoryService {

    @Autowired
    private MaintTaskHistoryParamMapper maintTaskHistoryParamMapper;
    @Autowired
    private PorosSecStaffClient porosSecStaffClient;
    @Autowired
    private LocalTaskClient localTaskClient;
    @Autowired
    @Lazy
    private ActivitiTaskNotifyHandler activitiTaskNotifyHandler;

    @Override
    public PageResult<MaintTaskHistoryDto> pageDto(MaintTaskHistoryQueryParam maintTaskHistoryQueryParam) {
        Wrapper<MaintTaskHistory> wrapper = getPageSearchWrapper(maintTaskHistoryQueryParam);
        PageResult<MaintTaskHistoryDto> result = maintTaskHistoryParamMapper.pageEntity2Dto(page(maintTaskHistoryQueryParam, wrapper));
        result.getRecords().stream().forEach(item -> item.setStatusChange(TaskStatusType.getNameByValue(item.getOldStatus()) + "->" + TaskStatusType.getNameByValue(item.getNewStatus())));
        if (CollectionUtils.isEmpty(result.getRecords())) {
            log.info("未找到审批历史，尝试通过旧版本接口查找");
            RestResponse<List<MaintTaskHistoryDto>> taskRecordList = localTaskClient.getTaskRecordList(maintTaskHistoryQueryParam.getProcessInstanceId());
            if (taskRecordList.isOk()) {
                result.setRecords(taskRecordList.getData());
            }
        }
        return Optional.ofNullable(result).orElse(new PageResult<>());
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveByParam(MaintTaskHistoryAddParam maintTaskHistoryAddParam) {
        MaintTaskHistory maintTaskHistory = maintTaskHistoryParamMapper.addParam2Entity(maintTaskHistoryAddParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, maintTaskHistory);
        return save(maintTaskHistory);
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean updateByParam(MaintTaskHistoryEditParam maintTaskHistoryEditParam) {
        MaintTaskHistory maintTaskHistory = maintTaskHistoryParamMapper.editParam2Entity(maintTaskHistoryEditParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR, maintTaskHistory);
        return updateById(maintTaskHistory);
    }


    @Override
    public MaintTaskHistoryDto getDtoById(Long id) {
        return maintTaskHistoryParamMapper.entity2Dto((MaintTaskHistory) this.getById(id));
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveDtoBatch(List<MaintTaskHistoryDto> rows) {
        return saveBatch(maintTaskHistoryParamMapper.dtoList2Entity(rows));
    }

    private Wrapper<MaintTaskHistory> getPageSearchWrapper(MaintTaskHistoryQueryParam maintTaskHistoryQueryParam) {
        LambdaQueryWrapper<MaintTaskHistory> wrapper = Wrappers.<MaintTaskHistory>lambdaQuery();
        wrapper.eq(StringUtils.isNotBlank(maintTaskHistoryQueryParam.getProcessInstanceId()), MaintTaskHistory::getProcessInstanceId, maintTaskHistoryQueryParam.getProcessInstanceId());
        wrapper.eq(StringUtils.isNotBlank(maintTaskHistoryQueryParam.getMaintTaskId()), MaintTaskHistory::getMaintTaskId, maintTaskHistoryQueryParam.getMaintTaskId());
        wrapper.like(StringUtils.isNotBlank(maintTaskHistoryQueryParam.getId()),
                MaintTaskHistory::getId, maintTaskHistoryQueryParam.getId());
        wrapper.orderByDesc(MaintTaskHistory::getSort, MaintTaskHistory::getCreateTime);
        return wrapper;
    }


    public String initFirstNode(UserBaseInfo userBaseInfo, String taskId) {
        MaintTaskHistory maintTaskHistory = new MaintTaskHistory();
        maintTaskHistory.setId(UUID.fastUUID().toString(true));
        maintTaskHistory.setStartTime(new Date());
        maintTaskHistory.setEndTime(new Date());
        maintTaskHistory.setOperator("提交");
        maintTaskHistory.setActivityId(UUID.fastUUID().toString(true));
        maintTaskHistory.setActivityName("起草");
        maintTaskHistory.setTaskStatus("1");
        maintTaskHistory.setTaskId(UUID.fastUUID().toString(true));
        String processInstanceId = maintTaskHistory.getId();
        maintTaskHistory.setProcessInstanceId(processInstanceId);
        maintTaskHistory.setAssigneeUid(userBaseInfo.getUid());
        maintTaskHistory.setAssigneeName(userBaseInfo.getName());
        maintTaskHistory.setMaintTaskId(taskId);
        maintTaskHistory.setSort(0);
        this.save(maintTaskHistory);
        return maintTaskHistory.getProcessInstanceId();
    }

    public String insertDoneNode(String processInstanceId, UserBaseInfo userBaseInfo, String taskId, int oldStatus, int newStatus, String operator, String comment) {
        //this.update(new UpdateWrapper<MaintTaskHistory>().lambda().eq(MaintTaskHistory::getMaintTaskId, taskId).set(MaintTaskHistory::getTaskStatus, "1"));
        List<MaintTaskHistory> existList = this.list(new QueryWrapper<MaintTaskHistory>().lambda().eq(MaintTaskHistory::getMaintTaskId, taskId).orderByDesc(MaintTaskHistory::getSort));
        if (CollectionUtils.isNotEmpty(existList)) {
            this.update(new UpdateWrapper<MaintTaskHistory>().lambda().eq(MaintTaskHistory::getMaintTaskId, taskId).set(MaintTaskHistory::getTaskStatus, "1"));
        }
        MaintTaskHistory maintTaskHistory = new MaintTaskHistory();
        maintTaskHistory.setId(UUID.fastUUID().toString(true));
        maintTaskHistory.setStartTime(new Date());
        maintTaskHistory.setEndTime(new Date());
        maintTaskHistory.setOperator(operator);
        maintTaskHistory.setActivityId(UUID.fastUUID().toString(true));
        maintTaskHistory.setActivityName("" + TaskStatusType.getNameByValue(newStatus));
        maintTaskHistory.setTaskStatus("0");
        maintTaskHistory.setTaskId(maintTaskHistory.getId());
        maintTaskHistory.setProcessInstanceId(processInstanceId);
        maintTaskHistory.setAssigneeUid(userBaseInfo.getUid());
        maintTaskHistory.setAssigneeName(userBaseInfo.getName());
        maintTaskHistory.setMaintTaskId(taskId);
        maintTaskHistory.setSort(existList.size());
        maintTaskHistory.setOldStatus(oldStatus + "");
        maintTaskHistory.setNewStatus(newStatus + "");
        maintTaskHistory.setComment(comment);
        this.save(maintTaskHistory);
        return maintTaskHistory.getId();
    }

    public String insertTrashStatusNode(String processInstanceId, UserBaseInfo userBaseInfo, String taskId, int oldStatus, int newStatus, String operator) {
        List<MaintTaskHistory> existList = this.list(new QueryWrapper<MaintTaskHistory>().lambda().eq(MaintTaskHistory::getMaintTaskId, taskId).orderByDesc(MaintTaskHistory::getSort));
        if (CollectionUtils.isNotEmpty(existList)) {
            this.update(new UpdateWrapper<MaintTaskHistory>().lambda().eq(MaintTaskHistory::getMaintTaskId, taskId).set(MaintTaskHistory::getTaskStatus, "1"));
        } else {
            return "";
        }
        MaintTaskHistory oldNode = existList.get(existList.size() - 2);
        int count = existList.size();
        MaintTaskHistory maintTaskHistory = new MaintTaskHistory();
        maintTaskHistory.setId(UUID.fastUUID().toString(true));
        maintTaskHistory.setStartTime(new Date());
        maintTaskHistory.setEndTime(new Date());
        maintTaskHistory.setOperator(operator);
        maintTaskHistory.setActivityId(UUID.fastUUID().toString(true));
        maintTaskHistory.setActivityName("" + TaskStatusType.getNameByValue(newStatus));
        maintTaskHistory.setTaskStatus("0");
        maintTaskHistory.setTaskId(maintTaskHistory.getId());
        maintTaskHistory.setProcessInstanceId(processInstanceId);
        maintTaskHistory.setAssigneeUid(oldNode.getAssigneeUid());
        maintTaskHistory.setAssigneeName(oldNode.getAssigneeName());
        maintTaskHistory.setMaintTaskId(taskId);
        maintTaskHistory.setSort(count);
        maintTaskHistory.setOldStatus(oldStatus + "");
        maintTaskHistory.setNewStatus(newStatus + "");
        this.save(maintTaskHistory);
        return maintTaskHistory.getId();
    }

    public String insertRejectStatusNode(String processInstanceId, UserBaseInfo userBaseInfo, String taskId, int oldStatus, int newStatus, String operator) {
        List<MaintTaskHistory> existList = this.list(new QueryWrapper<MaintTaskHistory>().lambda().eq(MaintTaskHistory::getMaintTaskId, taskId).orderByDesc(MaintTaskHistory::getSort));
        if (CollectionUtils.isNotEmpty(existList)) {
            this.update(new UpdateWrapper<MaintTaskHistory>().lambda().eq(MaintTaskHistory::getMaintTaskId, taskId).set(MaintTaskHistory::getTaskStatus, "1"));
        }
        MaintTaskHistory oldNode = existList.get(existList.size() - 2);
        int count = existList.size();
        MaintTaskHistory maintTaskHistory = new MaintTaskHistory();
        maintTaskHistory.setId(UUID.fastUUID().toString(true));
        maintTaskHistory.setStartTime(new Date());
        maintTaskHistory.setEndTime(new Date());
        maintTaskHistory.setOperator(operator);
        maintTaskHistory.setActivityId(UUID.fastUUID().toString(true));
        maintTaskHistory.setActivityName("" + TaskStatusType.getNameByValue(newStatus));
        maintTaskHistory.setTaskStatus("0");
        maintTaskHistory.setTaskId(maintTaskHistory.getId());
        maintTaskHistory.setProcessInstanceId(processInstanceId);
        maintTaskHistory.setAssigneeUid(oldNode.getAssigneeUid());
        maintTaskHistory.setAssigneeName(oldNode.getAssigneeName());
        maintTaskHistory.setMaintTaskId(taskId);
        maintTaskHistory.setSort(count);
        maintTaskHistory.setOldStatus(oldStatus + "");
        maintTaskHistory.setNewStatus(newStatus + "");
        this.save(maintTaskHistory);
        return maintTaskHistory.getId();
    }

    public MaintTaskHistory insertStatusNode(String processInstanceId, UserBaseInfo userBaseInfo, String taskId, int oldStatus, int newStatus, List<String> uids, String operator) {
        List<MaintTaskHistory> existList = this.list(new QueryWrapper<MaintTaskHistory>().lambda().eq(MaintTaskHistory::getMaintTaskId, taskId).orderByDesc(MaintTaskHistory::getSort));
        if (CollectionUtils.isNotEmpty(existList)) {
            this.update(new UpdateWrapper<MaintTaskHistory>().lambda().eq(MaintTaskHistory::getMaintTaskId, taskId).set(MaintTaskHistory::getTaskStatus, "1"));
        }
        List<PorosSecStaffDto> data = Lists.newArrayList();
        if (CollectionUtils.isEmpty(uids)) {
            uids = Lists.newArrayList();
        } else {
            RestResponse<List<PorosSecStaffDto>> list = porosSecStaffClient.getList(String.join(",", uids));
            if (!list.isSuccess()) {
                throw new GlobalServiceException(new GlobalResultMessage("获取人员信息失败"));
            }
            data = list.getData();
        }
        int count = existList.size();
        MaintTaskHistory maintTaskHistory = new MaintTaskHistory();
        maintTaskHistory.setId(UUID.fastUUID().toString(true));
        maintTaskHistory.setStartTime(new Date());
        maintTaskHistory.setEndTime(new Date());
        maintTaskHistory.setOperator(operator);
        maintTaskHistory.setActivityId(UUID.fastUUID().toString(true));
        maintTaskHistory.setActivityName("" + TaskStatusType.getNameByValue(newStatus));
        maintTaskHistory.setTaskStatus("0");
        maintTaskHistory.setTaskId(maintTaskHistory.getId());
        maintTaskHistory.setProcessInstanceId(processInstanceId);
        maintTaskHistory.setAssigneeUid(String.join(",", data.stream().map(item -> item.getUid()).collect(Collectors.toList())));
        maintTaskHistory.setAssigneeName(String.join(",", data.stream().map(item -> item.getName()).collect(Collectors.toList())));
        maintTaskHistory.setMaintTaskId(taskId);
        maintTaskHistory.setSort(count);
        maintTaskHistory.setOldStatus(oldStatus + "");
        maintTaskHistory.setNewStatus(newStatus + "");
        this.save(maintTaskHistory);
        activitiTaskNotifyHandler.send(taskId, maintTaskHistory, userBaseInfo);
        return maintTaskHistory;
    }

    public MaintTaskHistory insertStatusNode(String processInstanceId, UserBaseInfo userBaseInfo, MaintTask maintTask, int oldStatus, int newStatus, List<String> uids, String operator) {
        List<MaintTaskHistory> existList = this.list(new QueryWrapper<MaintTaskHistory>().lambda().eq(MaintTaskHistory::getMaintTaskId, maintTask.getId()).orderByDesc(MaintTaskHistory::getSort));
        if (CollectionUtils.isNotEmpty(existList)) {
            this.update(new UpdateWrapper<MaintTaskHistory>().lambda().eq(MaintTaskHistory::getMaintTaskId, maintTask.getId()).set(MaintTaskHistory::getTaskStatus, "1"));
        }
        int count = existList.size();
        List<PorosSecStaffDto> data = Lists.newArrayList();
        if (CollectionUtils.isEmpty(uids)) {
            uids = Lists.newArrayList();
        } else {
            RestResponse<List<PorosSecStaffDto>> list = porosSecStaffClient.getList(String.join(",", uids));
            if (!list.isSuccess()) {
                throw new GlobalServiceException(new GlobalResultMessage("获取人员信息失败"));
            }
            data = list.getData();
        }
        MaintTaskHistory maintTaskHistory = new MaintTaskHistory();
        maintTaskHistory.setId(UUID.fastUUID().toString(true));
        maintTaskHistory.setStartTime(new Date());
        maintTaskHistory.setEndTime(new Date());
        maintTaskHistory.setOperator(operator);
        maintTaskHistory.setActivityId(UUID.fastUUID().toString(true));
        maintTaskHistory.setActivityName("" + TaskStatusType.getNameByValue(newStatus));
        maintTaskHistory.setTaskStatus("0");
        maintTaskHistory.setTaskId(maintTaskHistory.getId());
        maintTaskHistory.setProcessInstanceId(processInstanceId);
        maintTaskHistory.setAssigneeUid(String.join(",", data.stream().map(item -> item.getUid()).collect(Collectors.toList())));
        maintTaskHistory.setAssigneeName(String.join(",", data.stream().map(item -> item.getName()).collect(Collectors.toList())));
        maintTaskHistory.setMaintTaskId(maintTask.getId());
        maintTaskHistory.setSort(count);
        maintTaskHistory.setOldStatus(oldStatus + "");
        maintTaskHistory.setNewStatus(newStatus + "");
        this.save(maintTaskHistory);
        activitiTaskNotifyHandler.send(maintTask, maintTaskHistory, userBaseInfo);
        return maintTaskHistory;
    }
}
