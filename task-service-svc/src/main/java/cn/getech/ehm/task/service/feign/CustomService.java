package cn.getech.ehm.task.service.feign;

import cn.getech.poros.bpm.dto.task.ProcessTaskDTO;
import cn.getech.poros.bpm.param.task.ProcessTaskParam;
import cn.getech.poros.framework.common.api.RestResponse;
import com.dtflys.forest.annotation.Body;
import com.dtflys.forest.annotation.Header;
import com.dtflys.forest.annotation.Request;

import java.util.List;

public interface CustomService {


    @Request(
            url = "http://172.40.168.4:31895/api/poros-bpmengine-v2/task/getUserTaskByProcessId",
            type = "POST",
            headers = {"Accept: */*", "connection:Keep-Alive", "Content-Type:application/json"}
    )
    RestResponse<List<ProcessTaskDTO>> getUserTaskByProcessId(@Header("Authorization")String token, @Body ProcessTaskParam processTaskParam);

}
