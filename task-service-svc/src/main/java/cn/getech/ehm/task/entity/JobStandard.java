package cn.getech.ehm.task.entity;

import cn.getech.poros.framework.common.bean.BaseEntity;
import cn.getech.ehm.common.mp.handler.StringArrayTypeHandler;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

/**
 * 作业标准
 *
 * <AUTHOR>
 * @since 2020-08-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("job_standard")
public class JobStandard extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 设备类型id集合
     */
    @TableField(value = "info_category_ids", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] infoCategoryIds;

    /**
     * 设备位置id集合
     */
    @TableField(value = "info_location_ids", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] infoLocationIds;

    /**
     * 专业类别
     */
    @TableField(value = "majors", jdbcType = JdbcType.VARCHAR, typeHandler = StringArrayTypeHandler.class)
    private String[] majors;

    /**
     * 作业类别
     */
    @TableField("job_type")
    private String jobType;

    /**
     * 是否发布
     */
    @TableField("published")
    private Boolean published;

    /**
     * 是否停机
     */
    @TableField("stopped")
    private Boolean stopped;

    /**
     * 是否删除(0false1true)
     */
    @TableField("deleted")
    private Integer deleted;

    /**
     * 使用状态1使用中2未使用
     */
    @TableField("status")
    private Integer status;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    private String equipType;
}
