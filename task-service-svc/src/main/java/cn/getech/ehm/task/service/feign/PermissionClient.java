package cn.getech.ehm.task.service.feign;

import cn.getech.ehm.task.entity.PermissionMenu;
import cn.getech.poros.framework.common.api.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2020-08-21 16:40:20
 **/
@FeignClient(name = "poros-permission", path = "/api/poros-permission")
public interface PermissionClient {

    /**
     * 获取菜单权限列表
     * @param sysCode
     * @return
     */
    @GetMapping(value = "/menu/menus")
    RestResponse<PermissionMenu> getMenuAndCode(@RequestParam("sysCode") String sysCode);

}
