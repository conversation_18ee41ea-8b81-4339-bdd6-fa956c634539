package cn.getech.ehm.task.service;

import cn.getech.ehm.task.dto.shield.MaintTaskShieldDto;
import cn.getech.ehm.task.entity.MaintTaskShield;
import cn.getech.poros.framework.common.service.IBaseService;

/**
 * 工单屏蔽
 */
public interface IMaintTaskShieldService extends IBaseService<MaintTaskShield> {

    /**
     * 更新
     */
    Boolean updateByParam(MaintTaskShieldDto dto);

    /**
     * 根据id查询，转dto
     */
    MaintTaskShieldDto getDto();
}