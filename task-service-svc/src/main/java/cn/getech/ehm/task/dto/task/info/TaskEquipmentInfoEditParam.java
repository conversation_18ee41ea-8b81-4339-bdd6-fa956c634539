package cn.getech.ehm.task.dto.task.info;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 维护工单编辑参数
 *
 * <AUTHOR>
 * @date 2020-07-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "TaskEquipmentInfoEditParam", description = "维护工单编辑设备信息")
public class TaskEquipmentInfoEditParam extends ApiParam {

    @ApiModelProperty(value = "工单id")
    private String id;

    @ApiModelProperty(value = "工单名称")
    private String name;

    @ApiModelProperty(value = "来源类型")
    private Integer sourceType;

    @ApiModelProperty(value = "设备id")
    private String equipmentId;

    @ApiModelProperty(value = "新设备id")
    private String newEquipmentId;

    @ApiModelProperty("是否变更(0false1true)")
    private Integer changed;

    @ApiModelProperty("变更原因")
    private String remark;

    @ApiModelProperty("事前沟通人id")
    private String communicationerId;

    @ApiModelProperty("事前沟通人名称")
    private String communicationerName;
}
