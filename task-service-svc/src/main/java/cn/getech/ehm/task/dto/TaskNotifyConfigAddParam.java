package cn.getech.ehm.task.dto;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;

/**
 * <pre>
 * 工单通知配置 新增参数
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-11-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "TaskNotifyConfig新增", description = "工单通知配置新增参数")
public class TaskNotifyConfigAddParam extends ApiParam {

    @ApiModelProperty(value = "")
    private String createBy;
    @ApiModelProperty(value = "")
    private String updateBy;
    @ApiModelProperty(value = "")
    private Date createTime;
    @ApiModelProperty(value = "")
    private Date updateTime;
    @ApiModelProperty(value = "")
    private String remark;
}