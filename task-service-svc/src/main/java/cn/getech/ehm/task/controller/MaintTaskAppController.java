package cn.getech.ehm.task.controller;

import cn.getech.ehm.common.constant.StaticValue;
import cn.getech.ehm.common.util.CopyDataUtil;
import cn.getech.ehm.iot.client.ParameterClient;
import cn.getech.ehm.task.dto.MaintTaskFirstPageDto;
import cn.getech.ehm.task.dto.plan.MaintPlanAppDto;
import cn.getech.ehm.task.dto.repair.*;
import cn.getech.ehm.task.dto.task.info.*;
import cn.getech.ehm.task.service.IMaintPlanService;
import cn.getech.ehm.task.service.IMaintTaskService;
import cn.getech.ehm.task.service.IManualRepairService;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import cn.getech.poros.framework.common.bean.UserBaseInfo;
import cn.getech.poros.framework.common.context.PorosContextHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import java.util.List;

/**
 * 工单App
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
@RestController
@RequestMapping("/maintTaskApp")
@Api(tags = "工单App服务接口")
@Slf4j
public class MaintTaskAppController {

    @Autowired
    private IMaintTaskService maintTaskService;
    @Autowired
    private IManualRepairService manualRepairService;
    @Autowired
    private IMaintPlanService maintPlanService;

    /**
     * app获取故障报修列表
     */
    @ApiOperation("app分页获取故障报修列表")
    @PostMapping("/repairPageList")
    //@Permission("manual:repair:list")
    public RestResponse<PageResult<ManualRepairAppDto>> repairPageList(@RequestBody @Valid RepairAppQueryParam repairAppQueryParam){
        //我的报修需要过滤，设备概览相关不需要过滤
        if(null == repairAppQueryParam.getIsRelEquipment() || repairAppQueryParam.getIsRelEquipment() == StaticValue.ZERO) {
            UserBaseInfo userInfo = PorosContextHolder.getCurrentUser();
            Assert.notNull(userInfo, "current user is null");
            repairAppQueryParam.setCreateBy(userInfo.getUid());
        }
        return RestResponse.ok(manualRepairService.appManualPageDto(repairAppQueryParam));
    }

    /**
     * 新增故障报修
     */
    @ApiOperation("新增故障报修")
    @AuditLog(title = "故障报修",desc = "新增故障报修",businessType = BusinessType.INSERT)
    @PostMapping("/repairAdd")
    //@Permission("manual:repair:update")
    public RestResponse<Boolean> repairAdd(@RequestBody @Valid ManualRepairAddParam manualRepairAddParam) {
        return RestResponse.ok(manualRepairService.saveManualRepair(manualRepairAddParam));
    }

    /**
     * app获取维护计划列表
     */
    @ApiOperation("app获取维护计划列表")
    @GetMapping("/planPageList")
    public RestResponse<PageResult<MaintPlanAppDto>> getPlanPageList(@Valid String equipmentId){
        return RestResponse.ok(maintPlanService.planPageList(equipmentId));
    }

    /**
     * 分页获取工单列表
     */
    @ApiOperation("分页获取工单列表")
    @PostMapping("/taskPageList")
    public RestResponse<PageResult<MaintTaskAppPageDto>> taskPageList(@RequestBody MaintTaskQueryAppParam queryAppParam){
        return RestResponse.ok(maintTaskService.appPageDto(queryAppParam));
    }

    /**
     * 获取工单各个状态数量
     */
    @ApiOperation("获取工单各个状态数量")
    @GetMapping("/getCount")
    //@Permission("maint:task:list")
    public RestResponse<List<TaskCountDto>> getCount(@RequestParam Boolean ourTask){
        return RestResponse.ok(maintTaskService.getCount(ourTask));
    }

    @ApiOperation("获取工单各个状态数量")
    @GetMapping("/getCount/type")
    //@Permission("maint:task:list")
    public RestResponse<List<TaskCountDto>> getCountOfType(@RequestParam Boolean ourTask,@RequestParam String type){
        return RestResponse.ok(maintTaskService.getCount(ourTask,type));
    }

    /**
     * app首页获取设备维护
     */
    @ApiOperation("app首页获取设备维护工单，设备告警")
    @PostMapping("/getFirstPage")
    @ApiImplicitParam(name="size",value="条数",dataType="int", paramType = "query")
    //@Permission("manual:repair:list")
    public RestResponse<List<MaintTaskFirstPageDto>> getFirstPage(@RequestParam(value = "size", required = false) Integer size){
        if(null == size){
            size = StaticValue.TWO;
        }
        MaintTaskQueryAppParam queryParam = new MaintTaskQueryAppParam();
        queryParam.setPageNo(StaticValue.ONE);
        queryParam.setLimit(size);
        PageResult<MaintTaskAppPageDto> maintTaskPageDtoPageResult = maintTaskService.maintTaskFirstPage(queryParam);
        List<MaintTaskAppPageDto> maintTaskPageDtos = maintTaskPageDtoPageResult.getRecords();
        return RestResponse.ok(CopyDataUtil.copyList(maintTaskPageDtos, MaintTaskFirstPageDto.class));
    }

    /**
     * 首页获取工单
     */
    @ApiOperation("首页获取设备维护工单列表(更多)")
    @PostMapping("/maintTaskFirstPage")
    public RestResponse<PageResult<MaintTaskAppPageDto>> maintTaskFirstPage(@RequestBody @Valid MaintTaskQueryAppParam queryParam){
        return RestResponse.ok(maintTaskService.maintTaskFirstPage(queryParam));
    }

    @ApiOperation("离线缓存")
    @GetMapping("/offlineCache")
    @ApiImplicitParams({
            @ApiImplicitParam(name="id",value="巡检任务id",dataType="String", paramType = "query")
    })
    public RestResponse<MaintTaskCacheDto> offlineCache(@RequestParam String id){
        return RestResponse.ok(maintTaskService.offlineCache(id));
    }

    @ApiOperation("离线提交")
    @PostMapping("/offlineCacheSubmit")
    public RestResponse<Boolean> offlineCacheSubmit(@RequestBody MaintTaskCacheEditDto dto){
        return RestResponse.ok(maintTaskService.offlineCacheSubmit(dto));
    }

    @ApiOperation("离线提交校验")
    @GetMapping("/offlineCacheSubmitCheck")
    public RestResponse<Boolean> offlineCacheSubmitCheck(@RequestParam String id){
        return RestResponse.ok(maintTaskService.offlineCacheSubmitCheck(id));
    }
}
