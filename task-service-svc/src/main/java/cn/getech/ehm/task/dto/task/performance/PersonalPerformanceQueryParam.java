package cn.getech.ehm.task.dto.task.performance;

import cn.getech.poros.framework.common.param.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 个人绩效 分页查询参数对象
 *
 * <AUTHOR>
 * @date 2021-01-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "PersonPerformance查询", description = "个人绩效分页查询参数对象")
public class PersonalPerformanceQueryParam extends PageParam {

    @ApiModelProperty(value = "用户Ids")
    private List<String> uids;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "开始时间")
    Date beginTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "结束时间")
    Date endTime;

    @ApiModelProperty(value = "班组id")
    List<String> teamIds;

}
