package cn.getech.ehm.task.dto.task.process;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 维护工单处理提交参数
 * <AUTHOR>
 */
@Data
@ApiModel(value = "TaskSubmitParam", description = "维护工单处理提交参数")
public class TaskSubmitParam {

    @ApiModelProperty(value = "提交类型 0:派单1:接单2:改派3:开始安全确认4提交确认审核5:安全确认" +
            "6安全确认驳回7:开始执行8:挂起9执行完成10验收11异常关闭12转缺陷13派单审批通过14一级保养关闭")
    private int submitType;

    @ApiModelProperty(value = "工作流任务ID")
    private String activityId;

    @ApiModelProperty(value = "工单ID")
    private String taskId;

    @ApiModelProperty(value = "维护人员id集合")
    private String[] staffIds;

    @ApiModelProperty(value = "维护班组id集合")
    private String[] teamIds;

    @ApiModelProperty(value = "备注/审批意见")
    private String remark;

    @ApiModelProperty(value = "是否自由扫码", hidden = true)
    private Boolean freeTask = false;

    private Boolean forceSubmit = false;


}