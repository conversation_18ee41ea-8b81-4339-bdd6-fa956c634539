package cn.getech.ehm.task.controller;

import cn.getech.ehm.task.dto.ManualRepairAddDto;
import cn.getech.ehm.task.dto.repair.*;
import cn.getech.poros.framework.common.annotation.CreateByAndUpdateBy;
import cn.getech.poros.framework.common.utils.ExcelUtils;
import io.swagger.annotations.*;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import org.springframework.beans.factory.annotation.Autowired;
import cn.getech.poros.framework.common.api.PageResult;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.ehm.task.service.IManualRepairService;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;

/**
 * 报修控制器
 *
 * <AUTHOR>
 * @since 2020-08-06
 */
@RestController
@RequestMapping("/manualRepair")
@Api(tags = "报修服务接口")
public class ManualRepairController {

    @Autowired
    private IManualRepairService manualRepairService;

    /**
     * 分页获取人工报修列表
     */
    @ApiOperation("分页获取人工报修列表")
    @PostMapping("/list")
    //@Permission("manual:repair:list")
    public RestResponse<PageResult<ManualRepairListDto>> pageList(@RequestBody @Valid ManualRepairQueryParam manualRepairQueryParam) {
        return RestResponse.ok(manualRepairService.manualPageDto(manualRepairQueryParam));
    }

    /**
     * 维保计划获取报修列表
     */
    @ApiOperation("维保计划获取报修列表")
    @PostMapping("/detailList")
    //@Permission("manual:repair:list")
    public RestResponse<PageResult<ManualRepairDetailDto>> detailList(@RequestBody DetailQueryParam queryParam) {
        return RestResponse.ok(manualRepairService.detailList(queryParam));
    }

    /**
     * 导出人工报修列表
     */
    @ApiOperation(value = "导出人工报修列表")
    @AuditLog(title = "人工报修",desc = "导出人工报修列表",businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void excelExport(@RequestBody @Valid ManualRepairQueryParam queryParam, HttpServletResponse response){
        ExcelUtils<ManualRepairExcelDto> util = new ExcelUtils<>(ManualRepairExcelDto.class);

        util.exportExcel(manualRepairService.manualExcelDto(queryParam), "故障报修列表",response);
    }

    /**
     * 新增故障报修
     */
    @ApiOperation("新增故障报修")
    @AuditLog(title = "故障报修", desc = "新增故障报修", businessType = BusinessType.INSERT)
    @PostMapping
    //@Permission("manual:repair:update")
    public RestResponse<Boolean> add(@RequestBody @Valid ManualRepairAddParam manualRepairAddParam) {
        return RestResponse.ok(manualRepairService.saveManualRepair(manualRepairAddParam));
    }

    /**
     * 修改故障报修
     */
    @ApiOperation("修改故障报修")
    @AuditLog(title = "故障报修", desc = "修改故障报修", businessType = BusinessType.UPDATE)
    @PutMapping
    //@Permission("manual:repair:update")
    public RestResponse<Boolean> edit(@RequestBody @Valid ManualRepairEditParam manualRepairEditParam) {
        return RestResponse.ok(manualRepairService.editManualRepair(manualRepairEditParam));
    }

    /**
     * 根据id获取人工报修
     */
    @ApiOperation(value = "根据id获取人工报修")
    @GetMapping(value = "/{id}")
    @CreateByAndUpdateBy(value = true)
    //@Permission("manual:repair:list")
    public RestResponse<ManualRepairDto> get(@PathVariable String id) {
        return RestResponse.ok(manualRepairService.getManualDtoById(id));
    }

    /**
     * 获取故障单关联的设备ids
     */
    @ApiOperation(value = "获取故障单关联的设备ids")
    @GetMapping(value = "/getUsedInfoIds")
    public RestResponse<List<String>> getUsedInfoIds() {
        return RestResponse.ok(manualRepairService.getUsedInfoIds());
    }

    /**
     * 告警新增故障报修
     */
    @ApiOperation("告警新增故障报修")
    @AuditLog(title = "故障报修", desc = "告警新增故障报修", businessType = BusinessType.INSERT)
    @PostMapping(value = "/saveWarnRepair")
    public RestResponse<Boolean> saveWarnRepair(@RequestBody @Valid ManualRepairAddDto addDto) {
        return RestResponse.ok(manualRepairService.saveWarnRepair(addDto));
    }

    /**
     * 校验当天设备参数是否有生成故障单
     */
    @ApiOperation("校验当天设备参数是否有生成故障单")
    @GetMapping(value = "/checkHaveRepair")
    public RestResponse<Boolean> checkHaveRepair(@RequestParam("infoParamId") String infoParamId) {
        return RestResponse.ok(manualRepairService.checkHaveRepair(infoParamId));
    }

    @ApiOperation("scada告警新增故障报修")
    @PostMapping(value = "/scadaSaveWarnRepair")
    public RestResponse<Boolean> scadaSaveWarnRepair(@RequestBody List<ManualRepairAddDto> addDtos) {
        return RestResponse.ok(manualRepairService.scadaSaveWarnRepair(addDtos));
    }
}
