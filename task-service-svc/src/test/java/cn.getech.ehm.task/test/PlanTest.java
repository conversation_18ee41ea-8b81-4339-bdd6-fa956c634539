package cn.getech.ehm.task.test;

import cn.getech.ehm.common.context.UserContextHolder;
import cn.getech.ehm.task.Application;
import cn.getech.ehm.task.service.IMaintPlanCbmService;
import cn.getech.ehm.task.service.IMaintTaskService;
import cn.getech.ehm.task.service.IPlanEquipmentTimeService;
import cn.getech.ehm.task.service.ITriggerTimeService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR>
 * @date 2021-03-18 19:15:53
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Application.class})
@Slf4j
public class PlanTest {
    @Autowired
    private IPlanEquipmentTimeService planEquipmentTimeService;
    @Autowired
    private ITriggerTimeService triggerTimeService;
    @Autowired
    private IMaintPlanCbmService cbmService;
    @Autowired
    private IMaintTaskService maintTaskService;
    @Autowired
    @Qualifier("schedulerExecutorService")
    private ExecutorService codeService;

    @Test
    public void createTriggerTimeTest() {
        UserContextHolder.defaultContext();
        String planId = "b0842b307d88cdf36c0352003d1c8cde";
        planEquipmentTimeService.createTriggerTime(planId);
    }

    @Test
    public void canCreateTaskDtos() {
        triggerTimeService.canCreateTaskDtos();
    }

    @Test
    public void createCbm() {
        List<String> planIds = new ArrayList<>();
        planIds.add("9b5f56337a73081a01a14c17d5ddf384");
        planIds.add("10eae7fe35587ad260fc59d3af55c738");
        Map<String, Double> paramMap = new HashMap<>();
        paramMap.put("23a528b1499e9358e00a2fb79984f2cc", 15.0);
        List<String> createTaskPlanIds = cbmService.satisfyTriggerPlanId(planIds, "23a528b1499e9358e00a2fb79984f2cc", paramMap);

    }

    @Test
    public void test() {
        for (int i = 0; i < 1000; i++) {
            CompletableFuture.runAsync(() -> {
                String test20241226 = maintTaskService.getMaxCode("TEST20241226");
                log.info("code:{}", test20241226);
            }, codeService);
        }
    }
}
