SET FOREIGN_KEY_CHECKS=0;

ALTER TABLE `ehm`.`maint_plan` ADD COLUMN `bussiness_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '业务类型';

ALTER TABLE `ehm`.`maint_plan` ADD COLUMN `area_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '区域' ;

ALTER TABLE `ehm`.`maint_plan` ADD COLUMN `process_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工序' ;

ALTER TABLE `ehm`.`maint_task` ADD COLUMN `bussiness_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '业务类型' ;

ALTER TABLE `ehm`.`maint_task` ADD COLUMN `area_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '区域' ;

ALTER TABLE `ehm`.`maint_task` ADD COLUMN `process_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工序' ;

SET FOREIGN_KEY_CHECKS=1;