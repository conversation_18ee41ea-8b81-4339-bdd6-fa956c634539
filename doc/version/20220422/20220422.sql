SET
FOREIGN_KEY_CHECKS=0;

ALTER TABLE equipment_special_manager DROP COLUMN project_code;
ALTER TABLE `ehm`.`equipment_info_special`
    ADD COLUMN `duty_user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '责任人' AFTER `update_time`;

ALTER TABLE `ehm`.`equipment_info_special`
    ADD COLUMN `duty_user_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '责任人' AFTER `duty_user_id`;

ALTER TABLE `ehm`.`equipment_info_special`
    ADD COLUMN `deadline_days` int(10) NULL DEFAULT 15 COMMENT '提前提醒天数' AFTER `duty_user_name`;

ALTER TABLE `ehm`.`equipment_special_manager`
    ADD COLUMN `id_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '身份证号' AFTER `name`;

ALTER TABLE `ehm`.`equipment_special_manager`
    ADD COLUMN `deadline_days` int(10) NULL DEFAULT NULL COMMENT '提前预警天数' AFTER `update_time`;

ALTER TABLE `ehm`.`equipment_special_manager`
    ADD COLUMN `certificate_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '证书名称' AFTER `deadline_days`;

ALTER TABLE `ehm`.`equipment_special_manager`
    ADD COLUMN `certificate_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '证书类型' AFTER `certificate_name`;

ALTER TABLE `ehm`.`equipment_structure` MODIFY COLUMN `tenant_id` varchar (32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '租户id' AFTER `sort`;

ALTER TABLE `ehm`.`maint_plan`
    ADD COLUMN `deadline_days` int(4) NULL DEFAULT NULL COMMENT '截止天数' AFTER `update_time`;

ALTER TABLE `ehm`.`maint_task`
    ADD COLUMN `send_task_date` datetime NULL DEFAULT NULL COMMENT '派单时间' AFTER `remark`;

ALTER TABLE `ehm`.`maint_task`
    ADD COLUMN `send_task_deadline_date` datetime NULL DEFAULT NULL COMMENT '派单截止时间' AFTER `send_task_date`;

ALTER TABLE `ehm`.`maint_task`
    ADD COLUMN `rec_task_date` datetime NULL DEFAULT NULL COMMENT '接单时间' AFTER `send_task_deadline_date`;

ALTER TABLE `ehm`.`maint_task`
    ADD COLUMN `rec_task_deadline_date` datetime NULL DEFAULT NULL COMMENT '接单截止时间' AFTER `rec_task_date`;

CREATE TABLE `ehm`.`maint_task_config`
(
    `id`               varchar(32) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
    `create_by`        varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
    `update_by`        varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
    `create_time`      datetime NULL DEFAULT NULL,
    `update_time`      datetime NULL DEFAULT NULL,
    `deleted`          tinyint(4) NULL DEFAULT 0,
    `remark`           varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
    `tenant_id`        varchar(32) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
    `create_user_name` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
    `task_type`        varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT '工单类型',
    `config_type`      tinyint(4) NULL DEFAULT NULL COMMENT '配置类型',
    `config_content`   varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT '配置内容',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = latin1 COLLATE = latin1_swedish_ci COMMENT = '工单配置信息' ROW_FORMAT = Dynamic;

ALTER TABLE `ehm`.`manual_repair`
    ADD COLUMN `deadline_date` datetime NULL DEFAULT NULL COMMENT '截止日期' AFTER `update_time`;

SET
FOREIGN_KEY_CHECKS=1;