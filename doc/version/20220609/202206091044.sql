create table maint_task_assist_person
(
	id varchar(32) not null comment 'id',
	task_id varchar(64) not null comment '工单id',
	user_id varchar(64) null comment '辅助人员id',
	user_name varchar(64) null comment '辅助人员名称',
	description varchar(128) null comment '参与说明',
	time_mode int not null comment '工时计算方式',
	start_time datetime null comment '参与开始时间',
	end_time datetime null comment '参与结束时间',
	work_hours decimal(10,2) null comment '工时',
	create_by varchar(64) null comment '创建人',
	update_by varchar(64) null comment '更新者',
	create_time datetime not null,
	update_Time datetime not null,
	remark varchar(255) null comment '备注',
    tenant_id varchar(64) null comment '租户id',
	constraint Maint_task_assist_person_pk
		primary key (id)
)
comment '工单辅助人员表';


